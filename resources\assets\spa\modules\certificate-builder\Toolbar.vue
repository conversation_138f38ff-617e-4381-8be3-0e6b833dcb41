<template>
    <div
        class="toolbar"
        :class="'certificate-toolbar sticky top-px z-50 mx-1 flex w-full justify-between gap-4 rounded-md border-b border-gray-300 bg-white p-2 shadow'"
    >
        <div class="flex items-center gap-1">
            <Button variant="icon" class="h-8 w-8" @click="undoAction" :disabled="!canUndo"
                ><IconArrowCounterclockwise24Regular class="h-5 w-5"
            /></Button>
            <Button variant="icon" class="h-8 w-8" @click="redoAction" :disabled="!canRedo"
                ><IconArrowClockwise24Regular class="h-5 w-5"
            /></Button>
        </div>
        <div class="flex flex-1 items-center justify-center gap-2">
            <template v-if="selectedNode?.getNode()?.attrs?.type === 'SHAPE'">
                <input
                    type="color"
                    v-model="shapeColor"
                    class="h-8 rounded-md"
                    @input="updateShape"
                    id="presetColors"
                />
                <!-- <div
                    class="tw-font-size flex items-center rounded-md border border-gray-300"
                    v-if="
                        !selectedNode?.getNode?.attrs?.variant.endsWith('-fill')
                    "
                >
                    <Button
                        variant="icon"
                        class="h-8 w-8"
                        @click="decreaseStroke"
                        ><IconLineHorizontal124Regular class="h-4 w-4"
                    /></Button>
                    <NumericTextBox
                        :style="{
                            width: '44px',
                            height: '32px',
                            borderRadius: 'none',
                        }"
                        :placeholder="'width'"
                        v-model="strokeWidth"
                        @change="updateShape"
                    ></NumericTextBox>
                    <Button
                        variant="icon"
                        class="h-8 w-8"
                        @click="increaseStroke"
                        ><IconAdd24Regular class="h-4 w-4"
                    /></Button>
                </div> -->
            </template>
            <template
                v-if="
                    selectedTextNode &&
                    ['TEXT', 'ATTRS'].includes(selectedTextNode.getNode().attrs.type)
                "
            >
                <!-- <div
                    class="flex items-center gap-2"
                    v-if="selectedTextNode.getNode().attrs.type === 'ATTRS'"
                >
                    <span class="text-xs">Height</span>
                    <NumericTextBox
                        :style="{
                            width: '80px',
                            height: '32px',
                            borderRadius: 'none',
                        }"
                        :placeholder="'height'"
                        v-model="height"
                        @change="updateText"
                    ></NumericTextBox>
                </div> -->
                <DropDownList
                    :style="{ width: '120px', height: '32px' }"
                    :data-items="fontsData"
                    :filterable="true"
                    @filterchange="filterChange"
                    v-model="fontFamily"
                    @change="applyFont"
                    :loading="loadingFont"
                ></DropDownList>

                <input
                    type="color"
                    v-model="fontColor"
                    class="h-8 rounded-md"
                    @input="updateText"
                    id="presetColors"
                />
                <div class="tw-font-size flex items-center rounded-md border border-gray-300">
                    <Button variant="icon" class="h-8 w-8" @click="decreaseFont"
                        ><IconLineHorizontal124Regular class="h-4 w-4"
                    /></Button>
                    <NumericTextBox
                        :style="{
                            width: '50px',
                            height: '32px',
                            borderRadius: 'none',
                        }"
                        :placeholder="'width'"
                        v-model="fontSize"
                        @change="updateText"
                    ></NumericTextBox>
                    <Button variant="icon" class="h-8 w-8" @click="increaseFont"
                        ><IconAdd24Regular class="h-4 w-4"
                    /></Button>
                </div>
                <ButtonGroup>
                    <KButton
                        :selected="fontStyle.includes('bold')"
                        :togglable="true"
                        :svg-icon="boldIcon"
                        @click="updateTextStyle('bold')"
                    >
                    </KButton>
                    <KButton
                        :selected="fontStyle.includes('italic')"
                        :togglable="true"
                        :svg-icon="italicIcon"
                        @click="updateTextStyle('italic')"
                    >
                    </KButton>
                    <KButton
                        :selected="textDecoration === 'underline'"
                        :togglable="true"
                        :svg-icon="underlineIcon"
                        @click="updateTextStyle('underline')"
                    >
                    </KButton>
                </ButtonGroup>
                <ButtonGroup>
                    <KButton
                        :selected="align === 'left'"
                        :togglable="true"
                        :svg-icon="alignLeftIcon"
                        @click="updateTextAlignment('left')"
                    >
                    </KButton>
                    <KButton
                        :selected="align === 'center'"
                        :togglable="true"
                        :svg-icon="alignCenterIcon"
                        @click="updateTextAlignment('center')"
                    >
                    </KButton>
                    <KButton
                        :selected="align === 'right'"
                        :togglable="true"
                        :svg-icon="alignRightIcon"
                        @click="updateTextAlignment('right')"
                    >
                    </KButton>
                </ButtonGroup>
            </template>
        </div>
        <div class="flex items-center justify-end gap-2">
            <!-- <Input
                v-model="certificateName"
                class="transition-all duration-300"
                placeholder="Untitled design"
                :class="inputFocus ? '!w-52' : '!w-40'"
                @focus="inputFocus = true"
                @blur="inputFocus = false"
            /> -->
            <!-- <p class="text-sm font-medium text-gray-700">{{ templateName }}</p> -->
            <Button v-if="certificateEmbedeUrl" variant="tertiary" size="sm" @click="previewHTML"
                >Preview</Button
            >
            <Button
                variant="primary"
                size="sm"
                @click="saveTemplate"
                :disabled="disabled"
                :loading="loaderStore.contextLoaders['button']"
                :loading-text="'Saving...'"
                >Save & Close</Button
            >
            <Button
                v-if="!isStudentCardTemplate"
                variant="secondary"
                size="sm"
                @click="saveAsTemplate"
                :disabled="disabled"
                >Save As New</Button
            >
        </div>
    </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted } from 'vue';
import { Button as KButton, ButtonGroup } from '@progress/kendo-vue-buttons';
import Button from '@spa/components/Buttons/Button.vue';
import {
    boldIcon,
    italicIcon,
    underlineIcon,
    alignLeftIcon,
    alignCenterIcon,
    alignRightIcon,
} from '@progress/kendo-svg-icons';
import { RadioGroup, NumericTextBox } from '@progress/kendo-vue-inputs';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { filterBy } from '@progress/kendo-data-query';
import { useLoaderStore } from '@spa/stores/modules/global-loader';

// import fonts from "./google-font.json";
import {
    IconArrowCounterclockwise24Regular,
    IconArrowClockwise24Regular,
    IconPanelLeftExpand24Regular,
    IconAdd24Regular,
    IconLineHorizontal124Regular,
} from '@iconify-prerendered/vue-fluent';
import { Input } from '@progress/kendo-vue-inputs';
const props = defineProps([
    'selectedTextNode',
    'selectedNode',
    'updateTextProperties',
    'deleteSelectedText',
    'updateShapeProperties',
    'certificateEmbedeUrl',
    'disabled',
    'canUndo',
    'canRedo',
    'templateName',
    'isStudentCardTemplate',
]);

const certificateFonts = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Courier New',
    'Georgia',
    'Verdana',
    'Trebuchet MS',

    'DejaVu Sans',
    'DejaVu Sans Condensed',
    'DejaVu Serif',
    'DejaVu Sans Mono',

    'Playfair Display',
    'Cinzel',
    'Cormorant Garamond',
    'Libre Baskerville',
    'Great Vibes',
    'Allura',
    'Dancing Script',
    'Pacifico',
    'Lato',
    'Roboto',
    'Open Sans',
    'Merriweather',
];

const loaderStore = useLoaderStore();

const lineHeights = [1, 1.15, 1.5, 2];
const loadingFont = ref(false);

const emit = defineEmits([
    'save',
    'preview',
    'undo',
    'redo',
    'toggle',
    'exportPng',
    'exportHtml',
    'saveAs',
]);

onMounted(() => {});

const fontFamily = ref('sans-serif');
const fontColor = ref('#000000');
const fontSize = ref(16);
const align = ref('left');
const textDecoration = ref('');
const fontStyle = ref('');
const fontsData = ref(certificateFonts);
const inputFocus = ref(false);
const shapeColor = ref('#000000');
const strokeWidth = ref(1);
const height = ref(null);
const certificateName = ref('');
const fontLineHeight = ref(1);

const updateText = () => {
    if (props.selectedTextNode) {
        props.updateTextProperties({
            fontFamily: fontFamily.value,
            fill: fontColor.value,
            fontSize: fontSize.value,
            align: align.value,
            textDecoration: textDecoration.value,
            fontStyle: fontStyle.value,
            height: height.value,
        });
    }
};

const updateShape = () => {
    if (!props.selectedNode) return;
    const node = props.selectedNode.getNode();
    if (node.attrs.variant.endsWith('-fill')) {
        props.updateShapeProperties({
            fill: shapeColor.value,
        });
    } else {
        props.updateShapeProperties({
            stroke: shapeColor.value,
            strokeWidth: strokeWidth.value,
        });
    }
};

const updateTextStyle = (style) => {
    if (!props.selectedTextNode) return;

    let currentFontStyle = props.selectedTextNode.getNode().attrs.fontStyle || '';

    if (style === 'bold') {
        currentFontStyle = currentFontStyle.includes('bold')
            ? currentFontStyle.replace('bold', '').trim()
            : currentFontStyle.includes('normal')
              ? currentFontStyle.replace('normal', 'bold').trim()
              : `${currentFontStyle} bold`.trim();
    } else if (style === 'italic') {
        currentFontStyle = currentFontStyle.includes('italic')
            ? currentFontStyle.replace('italic', '').trim()
            : `${currentFontStyle} italic`.trim();
    }

    if (style === 'underline') {
        textDecoration.value = textDecoration.value === 'underline' ? '' : 'underline';
    }
    fontStyle.value = currentFontStyle;
    props.updateTextProperties({
        fontFamily: fontFamily.value || 'sans-serif',
        fill: fontColor.value,
        fontSize: fontSize.value,
        align: align.value,
        textDecoration: textDecoration.value,
        fontStyle: currentFontStyle,
        height: height.value,
    });
};

const updateTextAlignment = (alignment) => {
    if (!props.selectedTextNode) return;
    align.value = alignment;
    props.updateTextProperties({
        fontFamily: fontFamily.value || 'sans-serif',
        fill: fontColor.value,
        fontSize: fontSize.value,
        align: align.value,
        textDecoration: textDecoration.value,
        fontStyle: fontStyle.value,
        height: height.value,
    });
};

const deleteText = () => {
    props.deleteSelectedText();
};

const filterChange = (event) => {
    fontsData.value = filterData(event.filter);
};

const filterData = (filter) => {
    const data = certificateFonts.slice();
    return filterBy(data, filter);
};

const applyFont = async (e) => {
    loadingFont.value = true;
    fontFamily.value = e.value;
    updateText();
    const fontName = fontFamily.value;
    const link = document.createElement('link');
    link.href = `https://fonts.googleapis.com/css2?family=${fontFamily.value.replace(/ /g, '+')}&display=swap`;
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    try {
        await Promise.all([
            document.fonts.load(`16px "${fontName}"`),
            new Promise((res) => setTimeout(res, 1000)),
        ]);
        updateText();
    } catch (err) {
        console.warn('Font load failed:', fontName);
    }

    loadingFont.value = false;
};

const updateTextFont = () => {
    if (props.selectedTextNode) {
        props.selectedTextNode.getNode().fontFamily(fontFamily.value);
        props.selectedTextNode.getNode().getLayer().batchDraw();
    }
};

const previewHTML = () => {
    emit('preview');
};

const saveTemplate = () => {
    emit('save');
};

const saveAsTemplate = () => {
    emit('saveAs');
};

const undoAction = () => {
    emit('undo');
};

const redoAction = () => {
    emit('redo');
};

const toggleSidebar = () => {
    emit('toggle');
};

const exportAsPng = () => {
    emit('exportPng');
};

const exportAsHtml = () => {
    emit('exportHtml');
};

const increaseFont = () => {
    fontSize.value = fontSize.value + 1;
    updateText();
};
const decreaseFont = () => {
    fontSize.value = fontSize.value - 1;
    updateText();
};

const increaseStroke = () => {
    strokeWidth.value = strokeWidth.value + 1;
    updateShape();
};
const decreaseStroke = () => {
    strokeWidth.value = strokeWidth.value - 1;
    updateShape();
};
watch(
    () => props.selectedTextNode,
    async (newNode) => {
        if (!newNode) return;
        const node = newNode.getNode();
        await nextTick();

        const attrs = node.attrs;

        if (attrs.fontFamily !== undefined) fontFamily.value = attrs.fontFamily;
        if (attrs.fill !== undefined) fontColor.value = attrs.fill;
        if (attrs.fontSize !== undefined) fontSize.value = attrs.fontSize;
        if (attrs.align !== undefined) align.value = attrs.align;
        if (attrs.fontStyle !== undefined) fontStyle.value = attrs.fontStyle;
        if (attrs.height !== undefined) height.value = attrs.height;
    }
);

watch(certificateName, (newValue) => {
    emit('update:certificateName', newValue);
});
</script>

<style scoped></style>
