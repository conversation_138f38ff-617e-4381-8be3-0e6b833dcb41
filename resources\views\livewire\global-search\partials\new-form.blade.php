<div class="relative text-gray-400 focus-within:text-gray-400" x-data="{ open: false, resultPanel: false }">
    <div class="pointer-events-none absolute inset-y-0 left-0 z-[1] flex items-center pl-3">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
                d="M10.5 5C13.5376 5 16 7.46243 16 10.5C16 11.8388 15.5217 13.0659 14.7266 14.0196L18.8536 18.1464C19.0488 18.3417 19.0488 18.6583 18.8536 18.8536C18.68 19.0271 18.4106 19.0464 18.2157 18.9114L18.1464 18.8536L14.0196 14.7266C13.0659 15.5217 11.8388 16 10.5 16C7.46243 16 5 13.5376 5 10.5C5 7.46243 7.46243 5 10.5 5ZM10.5 6C8.01472 6 6 8.01472 6 10.5C6 12.9853 8.01472 15 10.5 15C12.9853 15 15 12.9853 15 10.5C15 8.01472 12.9853 6 10.5 6Z"
                fill="#9CA3AF" />
        </svg>
    </div>
    <!-- <input id="findStudent" type="text" class="form-control search-icon-nav findStudent"> -->
    <input id="global-search-input-lw" name="search" x-on:focus="open=true; setTimeout(() => resultPanel=true, 500)"
        x-ref="searchEl" autocomplete="off" wire:model.live.debounce.250ms="form.keyword"
        x-bind:class="{ 'w-52 max-md:w-full': !open, 'w-[600px] max-md:w-[250px]': open }"
        class="header-search-input block rounded-md border border-transparent truncate bg-white py-2 pl-10 pr-3 leading-5 text-gray-400 transition-all duration-500 focus:bg-white focus:text-gray-900 focus:placeholder-gray-400 focus:outline-none focus:ring-0 sm:text-sm"
        placeholder="Search Student, Document or Task {{-- or Ctrl + J --}}" type="text" />
    <span wire:loading wire:target="form" class="absolute right-3 top-1/2 -translate-y-1/2 transform">
        <svg class="-ml-1 mr-3 h-5 w-5 animate-spin text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
            viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
            </circle>
            <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
        </svg>
    </span>
    <div id="" x-collapse.duration.200ms
        x-on:click.outside="if($event.target.id != 'global-search-input-lw') {resultPanel=false;setTimeout(() => {open=false; $refs['searchEl'].blur();}, 300)}"
        class="search-result animate__animated animate__fadeIn absolute right-0 top-full z-50 mt-1 max-h-[22rem] max-w-[600px] w-full  origin-top-right overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black ring-opacity-5 transition duration-500 ease-in-out focus:outline-none sm:text-sm"
        style="display: none" x-show="resultPanel">
        @if ($this->empty)
        <div class="mt-4 border bg-blue-50 p-2 text-primary-blue-500">
            <strong>{{ $form['keyword'] ? 'No records found.' : 'No recent records.' }}</strong>
        </div>
        @else
        <div class="">

            @include('livewire.global-search.partials.results')

        </div>
        @endif
    </div>
</div>