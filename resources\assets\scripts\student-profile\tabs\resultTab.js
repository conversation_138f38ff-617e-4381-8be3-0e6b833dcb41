$(document).ready(function () {
    let selectedUnits = [];
    let selectedUnitsIds = [];
});
var resultUnitGrid = true;
var resultConformationMessage =
    'All associated assessment results will automatically update to match the final result of the unit if it is not approved.';
var assessmentSyncToMoodleFromResultModalId = '#assessmentSyncToMoodleFromResultModal';
var unitGradeSyncFromMoodleModalId = '#unitGradeSyncFromMoodleModal';

function initializeResultTab() {
    $('#resultViewTabStrip').kendoTabStrip({
        animation: defaultOpenAnimation(),
    });

    $('#updateHigherEdUnitOutComeModal').kendoWindow(openCenterWindow('Update Final Outcome'));
    $('#updateResultCourseModal').kendoWindow(openCenterWindow('Move Courses results'));

    $('#resultUnitDetailsModal').kendoWindow(defaultWindowSlideFormat('Unit Details', 60));
    $('#assignBatchModal').kendoWindow(defaultWindowSlideFormat('Assign batch', 60));
    $('#transferToAnotherCourseModal').kendoWindow(
        defaultWindowSlideFormat('Transfer to another course', 60)
    );
    $('#bulkTransferToAnotherCourseModal').kendoWindow(
        defaultWindowSlideFormat('Bulk Transfer to another course', 60)
    );
    $('#unitOutComeModal').kendoWindow(defaultWindowSlideFormat('Update Unit Outcome', 60));
    $('#unitAvetmissModal').kendoWindow(defaultWindowSlideFormat('Update Avetmiss', 60));
    $('#reportToTCSIModal').kendoWindow(
        defaultWindowSlideFormat('TCSI for Lead and manage effective workplace relationships', 60)
    );
    $('#bulkAssignBatchModal').kendoWindow(defaultWindowSlideFormat('Bulk Assign batch', 60));
    $('#bulkUpdateUnitOutcomeModal').kendoWindow(
        defaultWindowSlideFormat('Bulk Update Unit outcome', 60)
    );

    $('#deleteSemesterUnitModal').kendoDialog({
        width: '400px',
        title: 'Delete',
        content:
            "Are you sure you want to delete this Assessment? <input type='hidden' name='id' id='deleteSemesterId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    deleteSemesterUnit(
                        $('#deleteSemesterUnitModal').find('#deleteSemesterId').val(),
                        $('#deleteSemesterUnitModal').find('#deleteSemesterId').attr('data-grid_id')
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog('#deleteSemesterUnitModal'),
        visible: false,
    });

    $(assessmentSyncToMoodleFromResultModalId).kendoDialog({
        width: '400px',
        title: 'Sync Assessment',
        content:
            "Are you sure you want to sync this assessment to Moodle? <input type='hidden' name='id' id='assignAssessmentTaskId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    assessmentSyncWithMoodleFromResultTab(
                        $(assessmentSyncToMoodleFromResultModalId)
                            .find('#assignAssessmentTaskId')
                            .val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenCenterDialog(assessmentSyncToMoodleFromResultModalId),
        visible: false,
    });
}

function manageResultTab() {
    if (selectedTabText === 'result') initializeResultTab();
    let tempSelectedDataArr = selectedDataArr;
    tempSelectedDataArr.is_higher_ed = isHigherEd;

    ajaxActionV2(
        'api/get-result-tab-data',
        'POST',
        tempSelectedDataArr,
        function (response) {
            let responseData = response.data.semesterData;
            $('.totalcourseAssesment').html(response.data.totalAssessmentCount);
            $('.totalCompletion').html(response.data.totalCompletion);
            let responseArr = {
                data: responseData,
            };
            $(document)
                .find('.resultSemesterViewForTab')
                .html(kendo.template($('#resultSemesterViewTemplateForTab').html())(responseArr));
            $(document)
                .find('#unitProgressbarInPercentageDiv')
                .html(kendo.template($('#unitProgressbarInPercentage').html())(response.data));
            $(document)
                .find('#resultHelp')
                .html(kendo.template($('#unitProgressbarTooltip').html())(response.data));
            toggleTabLoader('result', false);
            getUnitProgressTooltip();

            setTimeout(() => {
                $('.accordionResultForTab:first').trigger('click');
            }, 500);
        },
        false,
        function (flag) {
            toggleContentLoader('#studResultTab', flag);
        }
    );
    manageResultUnitList(tempSelectedDataArr);
}

function isFinalOutcomeEditable(dataItem) {
    return dataItem.is_result_lock == 0; // Editable only if is_result_lock is 0
}

function manageResultUnitList(tempSelectedDataArr) {
    if (resultUnitGrid) {
        // TODO:FLAG refresh
        resultUnitGrid = false;
        let resultTabUnitGrid = $('#resultTabUnitList')
            .kendoGrid({
                dataSource: customDataSourceForInlineV2(
                    'api/student-result-unit-data',
                    {
                        SN: { type: 'string', editable: false },
                        unit_id: { type: 'string', editable: false },
                        unit_code: { type: 'string', editable: false },
                        unit_name: { type: 'string', editable: false },
                        subject_attempt: { type: 'string', editable: false },
                        batch: { type: 'string', editable: false },
                        semester_term: { type: 'string', editable: false },
                        teacher_name: { type: 'string', editable: false },
                        course_activity_date: { type: 'date' },
                        final_outcome: {
                            type: 'string',
                            editable: !isHigherEd,
                        },
                        marks: { type: 'string', editable: false },
                        createdUnitDate: { type: 'date', editable: false },
                        start_date: { type: 'date' },
                        finish_date: { type: 'date' },
                        last_assessment_approved_date: { type: 'date' },
                        actual_end_date: { type: 'date' },
                        compentancy: { type: 'string' },
                    },
                    tempSelectedDataArr,
                    {},
                    'api/update-student-result-semester-data'
                ),
                pageable: customPageableArr(),
                sortable: true,
                resizable: true,
                filterable: {
                    messages: {
                        info: 'Filter',
                    },
                },
                detailInit: detailInit,
                change: onChange,
                detailExpand: function (e) {
                    e.sender.tbody.find('.k-detail-row').each(function (idx, item) {
                        if (item !== e.detailRow[0]) {
                            e.sender.collapseRow($(item).prev());
                        }
                    });
                },
                columns: [
                    {
                        selectable: true,
                        width: 44,
                        minResizableWidth: 44,
                        minWidth: 40,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div unit_#: id #'>#: SN #</div>",
                        field: 'SN',
                        title: 'SN',
                        minResizableWidth: 80,
                        width: 50,
                        minWidth: 30,
                        filterable: false,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_code #</div>",
                        field: 'unit_code',
                        title: 'Unit Code',
                        minResizableWidth: 80,
                        width: 80,
                        minwidth: 60,
                    },
                    {
                        template:
                            '<a data-course_id="#: course_id #" data-student_id="#: student_id #" data-subject_id="#: subject_id #" data-unit_id="#: unit_id #" id="resultUnitListExpand" class="resultUnitListExpandClass" href="javascript:void(0);" aria-label="Expand" tabindex="-1"><img src="' +
                            site_url +
                            'v2/img/plus_gray.svg" class="w-3 h-3"></a>',
                        width: 36,
                        minWidth: 30,
                        filterable: false,
                    },
                    {
                        // template:"<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div truncate' title='#: unit_name #'>#: unit_name #</div><div class='flex items-center text-sm leading-5 font-normal text-gray-500 action-div'>#: totalAssessmentCount # Assessments</div>",
                        template:
                            "<div class='flex gap-1 items-center'>" +
                            "<div class='truncate'>" +
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div truncate' title='#: unit_name #'>#: unit_name #</div>" +
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-500 action-div'>#: totalAssessmentCount # Assessments</div>" +
                            '</div>' +
                            "# if(is_result_lock == '1') { #" + // Check if is_result_lock is true
                            "<div class='glob-tooltip flex-10' title='This result has been locked. Unable to edit'>" +
                            "<svg xmlns='http://www.w3.org/2000/svg' width='20px' height='20px' viewBox='0 0 24 24'>" +
                            "<path fill='currentColor' d='M12 15.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3M8 6a4 4 0 1 1 8 0v1h.75A3.25 3.25 0 0 1 20 10.25v7.5A3.25 3.25 0 0 1 16.75 21h-9.5A3.25 3.25 0 0 1 4 17.75v-7.5A3.25 3.25 0 0 1 7.25 7H8zm4-2.5A2.5 2.5 0 0 0 9.5 6v1h5V6A2.5 2.5 0 0 0 12 3.5m-4.75 5a1.75 1.75 0 0 0-1.75 1.75v7.5c0 .966.784 1.75 1.75 1.75h9.5a1.75 1.75 0 0 0 1.75-1.75v-7.5a1.75 1.75 0 0 0-1.75-1.75z'></path>" +
                            '</svg>' +
                            '</div>' +
                            '# } #' +
                            '</div>',
                        field: 'unit_name',
                        title: 'Unit Name',
                        minResizableWidth: 80,
                        width: '200px',
                        minWidth: 180,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div truncate' title='#: subject_attempt #'>#: subject_attempt #</div>",
                        field: 'subject_attempt',
                        title: 'Attempt',
                        minResizableWidth: 80,
                        width: 100,
                        minWidth: 40,
                        filterable: false,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((batch !=null) ? batch :'--') #</div>",
                        field: 'batch',
                        title: 'Batch',
                        minResizableWidth: 80,
                        width: 100,
                        minWidth: 80,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: semester_term #</div>",
                        field: 'semester_term',
                        title: 'Semester',
                        minResizableWidth: 80,
                        width: 80,
                        minWidth: 60,
                    },
                    {
                        template: function (dataItem) {
                            return manageTeacherProfilePic('', dataItem.teacher_name);
                        },
                        field: 'teacher_name',
                        title: 'Trainer Name',
                        minResizableWidth: 80,
                        width: '150px',
                        minWidth: 100,
                    },
                    // {
                    //     template:
                    //         "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(createdUnitDate, displayDateFormatJS) # </div>",
                    //     field: "createdUnitDate",
                    //     title: "Created Date",
                    //     minResizableWidth: 80,
                    //     width: 120,
                    //     minWidth: 60
                    // },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((start_date != null)?kendo.toString(start_date, displayDateFormatJS):'--') # </div>",
                        field: 'start_date',
                        title: 'Start Date',
                        minResizableWidth: 80,
                        format: '{0:dd/MM/yyyy}',
                        editor: datePickerForInlineEdit,
                        width: 120,
                        minWidth: 60,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((finish_date != null)?kendo.toString(finish_date, displayDateFormatJS):'--') # </div>",
                        field: 'finish_date',
                        title: 'Finish Date',
                        minResizableWidth: 100,
                        format: '{0:dd/MM/yyyy}',
                        editor: datePickerForInlineEdit,
                        width: 120,
                        minWidth: 100,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: (last_assessment_approved_date !=null)? kendo.toString(last_assessment_approved_date, displayDateFormatJS):'--' # </div>",
                        field: 'last_assessment_approved_date',
                        title: 'Activity End Date',
                        minResizableWidth: 100,
                        format: '{0:dd/MM/yyyy}',
                        editor: datePickerForInlineEdit,
                        width: 140,
                        minWidth: 100,
                    },
                    // {
                    //     template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(actual_end_date, displayDateFormatJS) # </div>",
                    //     field: "actual_end_date",
                    //     title: "ACT. END DATE",
                    //     format: "{0:dd/MM/yyyy}",
                    //     editor: datePickerForInlineEdit,
                    // },
                    {
                        template:
                            "<div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'><span class='flex items-center text-xs leading-5 font-normal text-green-800 action-div' title='#: final_outcome1 #'>#: (final_outcome != null)?final_outcome:'--' #</span></div>",
                        field: 'final_outcome',
                        title: 'Final Outcome',
                        minResizableWidth: 100,
                        editor: dropDownForFinalOutcome,
                        width: 120,
                        minWidth: 100,
                        filterable: {
                            multi: true,
                            //when serverPaging of the Grid is enabled, dataSource should be provided for all the Filterable Multi Check widgets
                            dataSource: [
                                { final_outcome: 'Continuing Enrolment' },
                                { final_outcome: 'Continuing' },
                                { final_outcome: 'Pass' },
                                { final_outcome: 'Pass Conceded' },
                                { final_outcome: 'Fail' },
                                { final_outcome: 'Withdrawn' },
                            ],
                        },
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((marks !=null) ? marks :'--') #</div>",
                        field: 'marks',
                        title: 'Marks',
                        minResizableWidth: 80,
                        minWidth: 60,
                        hidden: !isHigherEd,
                    },
                    {
                        // template: '<a class="expand-row" href="javascript:void(0);" aria-label="Expand" tabindex="-1"><span class="k-icon k-i-arrow-chevron-down"></span></a>',
                        headerTemplate:
                            "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                        field: 'action',
                        title: 'Action',
                        minResizableWidth: 80,
                        filterable: false,
                        sortable: false,
                        width: 80,
                        minWidth: 60,
                        command: [
                            {
                                name: 'edit',
                                visible: function (dataItem) {
                                    return !dataItem.is_course_complete; // Hide edit button if course is complete
                                },
                                iconClass: 'k-icon k-i-edit',
                                className: 'result-tooltip',
                            },
                            {
                                template: `<button class='k-button k-button-icontext unitGradeSyncFromMoodleBtn summary-action' title='Sync all grades for this unit from moodle'><span class='k-icon'><img class='w-full h-full' src='${site_url}/v2/img/arrow-sync.svg'/></span></button>`,
                            },
                            {
                                //template: `<button class='k-button action-result-semester result-tooltip' title='View all action' ><span class='k-icon'><img class='w-full h-full' src='${site_url}v2/img/horizontal-dot.svg'></span></button>`,
                                template:
                                    "<a role='button' class='action-result-semester k-button k-button-icontext'><span class='k-icon k-i-more-horizontal'></span></a>",
                                field: 'action',
                                title: 'Action',
                                width: 80,
                            },
                        ],
                    },
                ],
                filterMenuInit: function (e) {
                    console.log('e.field', e);
                    // if (e.field === "unit_code") {
                    const menu = e.container;
                    const ascBtn = $(
                        `<button class="px-3 py-2 text-gray-700 flex items-center hover:bg-gray-100">
                            <span><img src="${site_url}v2/icons/arrow-up.svg" alt="Arrow up" /></span>
                            <span>Sort Ascending</span>
                        </button>`
                    );
                    const descBtn = $(
                        `<button class="px-3 py-2 text-gray-700 flex items-center hover:bg-gray-100"><span><img src="${site_url}v2/icons/arrow-down.svg" alt="Arrow up" /></span><span>Sort Descending</span></button>`
                    );

                    ascBtn.on('click', function () {
                        $('#grid').data('kendoGrid').dataSource.sort({
                            field: e.field,
                            dir: 'asc',
                        });
                    });

                    descBtn.on('click', function () {
                        $('#grid').data('kendoGrid').dataSource.sort({
                            field: e.field,
                            dir: 'desc',
                        });
                    });

                    menu.prepend(descBtn).prepend(ascBtn);
                    // }
                },
                save: function (e) {
                    if (
                        ['C', 'NYC', 'CT', 'RPL'].includes(e.model.final_outcome) &&
                        !e.model.last_assessment_approved_date
                    ) {
                        notificationDisplay(
                            'Activity End Date is required when final outcome is C, NYC, CT, or RPL.',
                            '',
                            'error'
                        );
                        e.preventDefault(); // Stop saving
                        return false;
                    }

                    if (
                        e.model.final_outcome !== undefined &&
                        e.model.is_result_lock != '1' &&
                        (e.model.final_outcome == 'C' || e.model.final_outcome == 'NYC')
                    ) {
                        e.preventDefault(); // Prevent auto-saving
                        // Display confirmation popup
                        if (confirm(resultConformationMessage)) {
                            // Proceed with saving the data if confirmed
                            e.sender.saveChanges(); // Manually trigger the save
                        }
                    }
                },
                editable: {
                    mode: 'inline',
                    createAt: 'top',
                    update: function (e) {
                        return !e.model.is_course_complete; // Disable editing if course is complete
                    },
                },
                noRecords: noRecordTemplate(),
                dataBound: function (e) {
                    togglePagination('#resultTabUnitList');
                    $(
                        '.bultkReportToTcsi, .bulkAssignBatchBtn, .bulkUpdateUnitOutcome, .bulkUpdateUnitAvetmissOutcome, .bulkUpdateResultCourse'
                    ).attr('disabled', true);
                    // this.tbody.find("tr").each(function() {
                    //     var dataItem = $("#resultTabUnitList").data("kendoGrid").dataItem(this);
                    //     if (dataItem.is_course_complete) {
                    //         $(this).find(".action-result-semester").hide(); // Hide button for complete courses
                    //         // $(this).find("input[type='checkbox']").prop("disabled", true);
                    //         $("#resultViewTabStrip .k-checkbox").prop("disabled", true);
                    //     }else{
                    //         $("#resultViewTabStrip .k-checkbox").prop("disabled", false);
                    //     }
                    // });
                    this.tbody.find('.k-grid-edit').attr('title', 'Edit');
                    getActionTooltip('.result-tooltip');

                    //TODO:: manage sync icon according to moodle connect or not (use requestEnd)
                    //this.tbody.find(".unitGradeSyncFromMoodleBtn").toggle(window.isMoodleConnected);

                    /*this.tbody.find(".unitGradeSyncFromMoodleBtn").each(function () {
                    let resultTabUnitGrid = $("#resultTabUnitList").data("kendoGrid");
                    let dataItem = resultTabUnitGrid.dataItem($(this).closest("tr"));
                    $(this).attr("title", `Sync all grades for unit (${dataItem.unit_code}) from Moodle`);
                    $(this).hide();
                });*/

                    this.tbody
                        .find('.unitGradeSyncFromMoodleBtn')
                        .off('click')
                        .on('click', function (e) {
                            unitGradeSyncFromMoodleHandler(e, '#resultTabUnitList');
                        });
                },
            })
            .data('kendoGrid');

        manageMoodleSyncIcon(resultTabUnitGrid);

        setColumnMinWidth('#resultTabUnitList');
        customGridHtml('#resultTabUnitList');
    } else {
        refreshGrid('#resultTabUnitList', tempSelectedDataArr);
    }

    function refreshUnitViewGrid() {
        refreshGrid('#resultTabUnitList', selectedDataArr);

        // Refresh other tab data
        $('.accordionResultForTab:first').trigger('click');
    }

    function detailInit(e) {
        kendo.ui.progress($('.k-detail-row .k-detail-cell'), true);

        let dataArr1 = {
            id: e.data.id,
            course_id: e.data.course_id,
            student_id: e.data.student_id,
            unit_id: e.data.unit_id,
            subject_id: e.data.subject_id,
            student_course_id: selectedStudCourseID,
            is_higher_ed: isHigherEd,
            batch: e.data.batch,
        };
        let detailGrid = $(
            `<div class='grid-animation'>
                <div class='result-skeleton flex items-center justify-center relative min-h-[100px]'>
                    <div class='vue-simple-spinner animate-spin'
                        style='margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 32px; height: 32px;'>
                    </div>
                </div>
            <div/>
            <div class='result-disclaimer text-xs text-red-500' style='display: none;'>**The final outcome is only updated when all the assessments below are approved.</div>`
        )
            .appendTo(e.detailCell)
            .kendoGrid({
                dataSource: customDataSourceForInline(
                    'api/get-assessment-details',
                    {
                        task_name: { type: 'string', editable: false },
                        competency: { type: 'string', editable: !isHigherEd },
                        marks: { type: 'string', editable: isHigherEd },
                        due_date: { type: 'date', editable: false },
                        moodle_status: { type: 'string', editable: false },
                        moodle_synced_at: { type: 'date', editable: false },
                    },
                    dataArr1,
                    {},
                    'api/update-assessment-details',
                    '',
                    refreshUnitViewGrid,
                    true
                ),
                sortable: true,
                resizable: true,
                columns: [
                    {
                        template:
                            "<div class='action-div text-primary-blue-500'>#: task_name #</div>",
                        field: 'task_name',
                        // title: "TASK NAME"
                    },
                    {
                        template:
                            "<div><span class='text-xs font-normal leading-5 text-left'>Outcome</span> <div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'>   <div class='text-center text-green-800 text-xs font-normal leading-tight tracking-wide'>#: competency #</div></div></div>",
                        field: 'competency',
                        // title: "Outcome",
                        editor: dropDownForCompetency,
                        hidden: isHigherEd,
                    },
                    {
                        template:
                            "<div><span class='text-xs font-normal leading-5 text-left'>Marks</span> <div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'>   <div class='text-center text-green-800 text-xs font-normal leading-tight tracking-wide'>#: marks #</div></div></div>",
                        field: 'marks',
                        hidden: !isHigherEd,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-500 action-div'><span class='text-xs font-normal leading-5 text-left'>Assignment Due Date  </span>  <span class='m-1 text-gray-700'> #: convertJsDateFormat(due_date) # </span></div>",
                        field: 'due_date',
                        //format: "{0:dd/MM/yyyy}",
                        editor: datePickerForFinalOutCome,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-500 action-div space-x-2'><span class='text-xs font-normal leading-5 text-left'>Moodle Status</span><span class='m-1 text-gray-700'>#: (moodleData === false) ? 'N/A' : (moodleData.assessment_moodle_sync_status ?? 'Not Sync') #</span></div>",
                        //title: "Moodle Status",
                        field: 'moodle_status',
                        sortable: false,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-500 action-div space-x-2'><span class='text-xs font-normal leading-5 text-left'>Sync At</span><span class='m-1 text-gray-700'>#: (moodleData !== false && moodleData.assessment_moodle_synced_at !== '') ? moodleData.assessment_moodle_synced_at : 'N/A' #</span></div>",
                        //title: "Sync At",
                        field: 'moodle_synced_at',
                        sortable: false,
                    },
                    {
                        // template: '<a class="expand-row" href="javascript:void(0);" aria-label="Expand" tabindex="-1"><span class="k-icon k-i-arrow-chevron-down"></span></a>',
                        headerTemplate:
                            "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                        field: 'action',
                        // title: "ACTION",
                        filterable: false,
                        sortable: false,
                        //command: ["edit"],
                        command: [
                            {
                                name: 'edit',
                                iconClass: 'k-icon k-i-edit',
                                className: 'summary-action',
                            },
                            {
                                template: `<button class='k-button k-button-icontext assessmentSyncToMoodleBtn summary-action' title='Add activity to moodle'><span class='k-icon'><img class='w-full h-full' src='${site_url}/v2/img/arrow-sync.svg'/></span></button>`,
                            },
                        ],
                    },
                ],
                editable: 'inline',
                noRecords: noRecordTemplate(),
                detailExpand: function (e) {
                    // Add the k-state-expanded class when a row is expanded
                    e.masterRow.addClass('k-state-expanded');
                },
                detailCollapse: function (e) {
                    // Remove the k-state-expanded class when a row is collapsed
                    e.masterRow.removeClass('k-state-expanded');
                },
                dataBound: function (e) {
                    $(document).find('.result-skeleton').hide();
                    $(e.detailCell).show();
                    let length = e.sender._data.length;
                    if (length > 0) {
                        $(document).find('.result-disclaimer').show();
                    }
                },
                edit: function (e) {
                    if (e.model.is_locked) {
                        e.container.find("input[name='marks']").prop('disabled', true);
                    }
                },
            })
            .data('kendoGrid');

        toggleMoodleColumnsAndIconGrid(detailGrid);

        $(e.detailCell).find('.k-grid-header').hide();
    }

    $('#resultTabUnitList').kendoTooltip({
        filter: 'td .action-result-semester',
        position: 'bottom left',
        showOn: 'click',
        width: 224,
        showAfter: 100,
        content: function (e) {
            let dataItem = $('#resultTabUnitList')
                .data('kendoGrid')
                .dataItem(e.target.closest('tr'));
            return kendo.template($('#unitActionTemplate').html())({
                dataItems: dataItem,
                id: dataItem.id,
                subject_id: dataItem.subject_id,
                course_id: dataItem.course_id,
                semester_id: dataItem.semester_id,
                unit_name: dataItem.unit_name,
                unit_code: dataItem.unit_code,
                unit_id: dataItem.unit_id,
                batch: dataItem.batch,
                is_course_complete: dataItem.is_course_complete,
                student_id: dataItem.student_id,
                is_higher_ed: dataItem.is_higher_ed,
                mark_outcome: dataItem.mark_outcome,
                //is_moodle_connect: dataItem.mark_outcome,
            });
        },
        show: function (e) {
            e.sender.popup.element.find('.k-callout').remove();
            e.sender.popup.element.addClass('tw-popup--top-right tw-tooltip');
            e.sender.popup.wrapper.css({
                right: '64px',
                left: 'unset',
            });
        },
    });

    $(unitGradeSyncFromMoodleModalId).kendoDialog({
        width: '400px',
        title: 'Sync From Moodle',
        //content: "Are you sure you want to sync all assessment grades for this unit from Moodle? <input type='hidden' name='id' id='unitId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    unitGradeSyncFromMoodleForResultTab(
                        $(unitGradeSyncFromMoodleModalId).find('#unitId').val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenCenterDialog(unitGradeSyncFromMoodleModalId),
        visible: false,
    });
}

function manageMoodleSyncIcon(resultTabUnitGrid) {
    resultTabUnitGrid.dataSource.bind('requestEnd', function (e) {
        if (e.response) {
            const isMoodleConnected = e.response.data.isMoodleConnect;
            window.detailIsMoodleConnected = isMoodleConnected;
        }
    });
    resultTabUnitGrid.bind('dataBound', function () {
        this.tbody.find('.unitGradeSyncFromMoodleBtn').toggle(window.detailIsMoodleConnected);
        if (window.detailIsMoodleConnected) {
            this.tbody.find('.unitGradeSyncFromMoodleBtn').each(function () {
                let dataItem = resultTabUnitGrid.dataItem($(this).closest('tr'));
                $(this).attr(
                    'title',
                    `Sync all grades for unit (${dataItem.unit_code}) from Moodle`
                );
            });
        }
    });
}

function manageResultData2(dataArr1, resultGrid) {
    $('.' + resultGrid)
        .kendoGrid({
            dataSource: customDataSourceForInline(
                'api/student-result-data',
                {
                    subject_name: { type: 'string', editable: false },
                    course_activity_date: { type: 'date' },
                    final_outcome: { type: 'string' },
                    actual_end_date: { type: 'date' },
                    compentancy: { type: 'string' },
                },
                dataArr1,
                {},
                'api/student-result-data-update',
                'api/student-result-data-delete'
            ),
            // pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    template: "<div data-id='#: id #' data-subject='#: subject_name #'></div>",
                    field: 'id',
                    hidden: true,
                },
                {
                    template:
                        "<div data-id='#: id #' data-subject='#: subject_name #' class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: subject_name #</div>",
                    field: 'subject_name',
                    title: 'Subject Name',
                    width: '33%',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(course_activity_date, displayDateFormatJS) # </div>",
                    field: 'course_activity_date',
                    title: 'Course Activity Date',
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForFinalOutCome,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((final_outcome !=null) ? final_outcome :'--') #</div>",
                    field: 'final_outcome',
                    title: 'Final Outcome',
                    editor: dropDownForFinalOutcome,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(start_date, displayDateFormatJS) # </div>",
                    field: 'start_date',
                    title: 'Start Date',
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForFinalOutCome1,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(finish_date, displayDateFormatJS) # </div>",
                    field: 'finish_date',
                    title: 'Finish Date',
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForFinalOutCome1,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(actual_end_date, displayDateFormatJS) # </div>",
                    field: 'actual_end_date',
                    title: 'Actual End Date',
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForFinalOutCome1,
                },
                {
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                    field: 'action',
                    title: 'Action',
                    filterable: false,
                    sortable: false,
                    width: '150px',
                    command: [
                        'edit',
                        'destroy',
                        {
                            template:
                                "<button class='k-button k-button-icontext btn-history'><span class='k-icon'><img class='w-full h-full' src='" +
                                site_url +
                                "/v2/img/Ellipse.svg' ></span></button>",
                        },
                    ],
                },
            ],
            editable: 'inline',
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                togglePagination('.' + resultGrid);
            },
        })
        .on('click', '.btn-history', function (e) {
            // let treelist = $(e.delegateTarget).data("kendoGrid");
            let id = $(e.currentTarget)
                .closest('tr')
                .find('td:first-child')
                .find('div')
                .attr('data-id');
            let subject_name = $(e.currentTarget)
                .closest('tr')
                .find('td:first-child')
                .find('div')
                .attr('data-subject');
            ajaxActionV2(
                'api/get-stud-subject-enroll-history',
                'POST',
                { student_subject_enrolment_id: id },
                function (response) {
                    kendoWindowOpen('#currentCourseHistoryModal');
                    $('#currentCourseHistoryModal')
                        .prev()
                        .find('.k-window-title')
                        .text(subject_name);
                    let currentCourseHistoryTemplate = kendo.template(
                        $('#currentCourseHistoryTemplate').html()
                    )(response.data);
                    $(document).find('#currentCourseHistoryDiv').html(currentCourseHistoryTemplate);
                }
            );
        });
}

function manageResultDataForResultTab(dataArr1, resultGrid) {
    let resultTabUnitGrid = $('.' + resultGrid)
        .kendoGrid({
            dataSource: customDataSourceForInlineV2(
                'api/student-result-semester-data',
                {
                    SN: { type: 'string', editable: false },
                    unit_id: { type: 'string', editable: false },
                    unit_code: { type: 'string', editable: false },
                    unit_name: { type: 'string', editable: false },
                    subject_attempt: { type: 'string', editable: false },
                    batch: { type: 'string', editable: false },
                    semester_term: { type: 'string', editable: false },
                    teacher_name: { type: 'string', editable: false },
                    course_activity_date: { type: 'date' },
                    final_outcome: { type: 'string', editable: !isHigherEd },
                    marks: { type: 'string', editable: false },
                    createdUnitDate: { type: 'date', editable: false },
                    start_date: { type: 'date' },
                    finish_date: { type: 'date' },
                    last_assessment_approved_date: { type: 'date' },
                    actual_end_date: { type: 'date' },
                    compentancy: { type: 'string' },
                },
                dataArr1,
                {},
                'api/update-student-result-semester-data',
                'api/delete-student-result-data-semester'
            ),
            // pageable: customPageableArr(),
            detailInit: detailInitSemesterView,
            detailExpand: function (e) {
                e.sender.tbody.find('.k-detail-row').each(function (idx, item) {
                    if (item !== e.detailRow[0]) {
                        e.sender.collapseRow($(item).prev());
                    }
                });
            },
            // detailTemplate: kendo.template($("#semesterAssessmentsTemplate").html()),
            sortable: true,
            resizable: true,
            columns: [
                // {
                //     selectable: true,
                //     width: "50px",
                // },
                {
                    template:
                        '<div class="flex items-center text-sm leading-5 font-normal text-gray-600 action-div" data-id="#: id #">#: SN #</div>',
                    field: 'SN',
                    title: 'SN',
                    minResizableWidth: 50,
                    width: '50px',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_code #</div>",
                    field: 'unit_code',
                    title: 'Unit Code',
                    minResizableWidth: 80,
                },
                {
                    template:
                        '<a data-course_id="#: course_id #" data-student_id="#: student_id #" data-subject_id="#: subject_id #" data-unit_id="#: unit_id #" class="resultSemesterListExpand resultSemesterListExpandClass" href="javascript:void(0);" aria-label="Expand" tabindex="-1"><img src="' +
                        site_url +
                        'v2/img/plus_gray.svg" ></a>',
                    width: '40px',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_name #</div>",
                    field: 'unit_name',
                    title: 'Unit Name',
                    minResizableWidth: 80,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: subject_attempt #</div>",
                    field: 'subject_attempt',
                    title: 'Attempt',
                    minResizableWidth: 80,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((batch !=null) ? batch :'--') #</div>",
                    field: 'batch',
                    title: 'Batch',
                    minResizableWidth: 80,
                },
                {
                    template: function (dataItem) {
                        return manageTeacherProfilePic('', dataItem.teacher_name);
                    },
                    field: 'teacher_name',
                    title: 'Trainer Name',
                    minResizableWidth: 80,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(createdUnitDate, displayDateFormatJS) # </div>",
                    field: 'createdUnitDate',
                    title: 'Created Date',
                    minResizableWidth: 80,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((start_date != null)?kendo.toString(start_date, displayDateFormatJS):'--') #</div>",
                    field: 'start_date',
                    title: 'Start Date',
                    minResizableWidth: 80,
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForInlineEdit,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((finish_date != null)?kendo.toString(finish_date, displayDateFormatJS):'--') # </div>",
                    field: 'finish_date',
                    title: 'Finish Date',
                    minResizableWidth: 80,
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForInlineEdit,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: (last_assessment_approved_date !=null)? kendo.toString(last_assessment_approved_date, displayDateFormatJS):'-' # </div>",
                    field: 'last_assessment_approved_date',
                    title: 'Activity End Date',
                    minResizableWidth: 100,
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForInlineEdit,
                    width: 140,
                    minWidth: 100,
                },
                // {
                //     template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(actual_end_date, displayDateFormatJS) # </div>",
                //     field: "actual_end_date",
                //     title: "ACT. END DATE",
                //     format: "{0:dd/MM/yyyy}",
                //     editor: datePickerForInlineEdit,
                // },
                {
                    template:
                        "<div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'><span class='flex items-center text-xs leading-5 font-normal text-green-800 action-div' title='#: final_outcome1 #'>#: (final_outcome != null)?final_outcome:'--' #</span></div>",
                    field: 'final_outcome',
                    title: 'Final Outcome',
                    minResizableWidth: 80,
                    editor: dropDownForFinalOutcome,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((marks !=null) ? marks :'--') #</div>",
                    field: 'marks',
                    title: 'Marks',
                    minResizableWidth: 80,
                    hidden: !isHigherEd,
                },
                {
                    // template: '<a class="expand-row" href="javascript:void(0);" aria-label="Expand" tabindex="-1"><span class="k-icon k-i-arrow-chevron-down"></span></a>',
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                    field: 'action',
                    title: 'Action',
                    minResizableWidth: 80,
                    filterable: false,
                    sortable: false,
                    command: [
                        'edit',
                        {
                            template: `<button class='k-button k-button-icontext unitGradeSyncFromMoodleBtn summary-action' title='Sync all grades for this unit from moodle'><span class='k-icon'><img class='w-full h-full' src='${site_url}/v2/img/arrow-sync.svg'/></span></button>`,
                        },
                        {
                            template:
                                "<a role='button' class='action-result-semester k-button k-button-icontext'><span class='k-icon k-i-more-horizontal'></span></a>",
                        },
                    ],
                },
            ],
            editable: 'inline',
            save: function (e) {
                if (
                    e.model.final_outcome !== undefined &&
                    e.model.is_result_lock != '1' &&
                    (e.model.final_outcome == 'C' || e.model.final_outcome == 'NYC')
                ) {
                    e.preventDefault(); // Prevent auto-saving
                    // Display confirmation popup
                    if (confirm(resultConformationMessage)) {
                        // Proceed with saving the data if confirmed
                        e.sender.saveChanges(); // Manually trigger the save
                    }
                }
            },
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                togglePagination('.' + resultGrid);

                //TODO:: manage sync icon according to moodle connect or not (use requestEnd)
                //this.tbody.find(".unitGradeSyncFromMoodleBtn").toggle(window.isMoodleConnected);

                /*this.tbody.find(".unitGradeSyncFromMoodleBtn").each(function () {
                    let resultTabUnitGrid = $("." + resultGrid).data("kendoGrid");
                    let dataItem = resultTabUnitGrid.dataItem($(this).closest("tr"));
                    $(this).attr("title", `Sync all grades for unit (${dataItem.unit_code}) from Moodle`);
                });*/

                this.tbody
                    .find('.unitGradeSyncFromMoodleBtn')
                    .off('click')
                    .on('click', function (e) {
                        unitGradeSyncFromMoodleHandler(e, '.' + resultGrid);
                    });
            },
        })
        .on('click', '.btn-history', function (e) {
            // let treelist = $(e.delegateTarget).data("kendoGrid");
            let id = $(e.currentTarget)
                .closest('tr')
                .find('td:first-child')
                .find('div')
                .attr('data-id');
            let subject_name = $(e.currentTarget)
                .closest('tr')
                .find('td:first-child')
                .find('div')
                .attr('data-subject');
            ajaxActionV2(
                'api/get-stud-subject-enroll-history',
                'POST',
                { student_subject_enrolment_id: id },
                function (response) {
                    kendoWindowOpen('#currentCourseHistoryModal');
                    $('#currentCourseHistoryModal')
                        .prev()
                        .find('.k-window-title')
                        .text(subject_name);
                    let currentCourseHistoryTemplate = kendo.template(
                        $('#currentCourseHistoryTemplate').html()
                    )(response.data);
                    $(document).find('#currentCourseHistoryDiv').html(currentCourseHistoryTemplate);
                }
            );
        })
        .data('kendoGrid');

    manageMoodleSyncIcon(resultTabUnitGrid);

    function detailInitSemesterView(e) {
        kendo.ui.progress($('.k-detail-row .k-detail-cell'), true);
        dataArr1 = {
            id: e.data.id,
            course_id: e.data.course_id,
            student_id: e.data.student_id,
            unit_id: e.data.unit_id,
            subject_id: e.data.subject_id,
            student_course_id: selectedStudCourseID,
            is_higher_ed: isHigherEd,
            batch: e.data.batch,
        };
        let detailGrid = $('<div/>')
            .appendTo(e.detailCell)
            .kendoGrid({
                dataSource: customDataSourceForInline(
                    'api/get-assessment-details',
                    {
                        task_name: { type: 'string', editable: false },
                        competency: { type: 'string', editable: !isHigherEd },
                        marks: { type: 'string', editable: isHigherEd },
                        due_date: { type: 'date', editable: false },
                    },
                    dataArr1,
                    {},
                    'api/update-assessment-details',
                    '',
                    null,
                    true
                ),
                // pageable: customPageableArr(),
                detailInit: detailInitSemesterView,
                detailExpand: function (e) {
                    e.sender.tbody.find('.k-detail-row').each(function (idx, item) {
                        if (item !== e.detailRow[0]) {
                            e.sender.collapseRow($(item).prev());
                        }
                    });
                },
                // detailTemplate: kendo.template($("#semesterAssessmentsTemplate").html()),
                sortable: true,
                resizable: true,
                columns: [
                    {
                        template: "<div class='action-div'>#: task_name #</div>",
                        field: 'task_name',
                        // title: "TASK NAME"
                    },
                    {
                        template:
                            "<div><span class='text-xs font-normal leading-5 text-left'>Outcome</span> <div class='w-24 h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'>   <div class='text-center text-green-800 text-xs font-normal leading-tight tracking-wide'>#: competency #</div></div></div>",
                        field: 'competency',
                        // title: "Outcome",
                        editor: dropDownForCompetency,
                        hidden: isHigherEd,
                    },
                    {
                        template:
                            "<div><span class='text-xs font-normal leading-5 text-left'>Marks</span> <div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'>   <div class='text-center text-green-800 text-xs font-normal leading-tight tracking-wide'>#: marks #</div></div></div>",
                        field: 'marks',
                        hidden: !isHigherEd,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'><span class='text-xs font-normal leading-5 text-left'>Assignment Due Date  </span>  <span class='m-1'> #: convertJsDateFormat(due_date) # </span></div>",
                        field: 'due_date',
                        //format: "{0:dd/MM/yyyy}",
                        editor: datePickerForFinalOutCome,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-500 action-div space-x-2'><span class='text-xs font-normal leading-5 text-left'>Moodle Status</span><span class='m-1 text-gray-700'>#: (moodleData === false) ? 'N/A' : (moodleData.assessment_moodle_sync_status ?? 'Not Sync') #</span></div>",
                        field: 'moodle_status',
                        //title: "Moodle Status",
                        minResizableWidth: 100,
                        sortable: false,
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-500 action-div space-x-2'><span class='text-xs font-normal leading-5 text-left'>Sync At</span><span class='m-1 text-gray-700'>#: (moodleData !== false && moodleData.assessment_moodle_synced_at !== '') ? moodleData.assessment_moodle_synced_at : 'N/A' #</span></div>",
                        field: 'moodle_synced_at',
                        //title: "Sync At",
                        minResizableWidth: 100,
                        sortable: false,
                    },
                    {
                        // template: '<a class="expand-row" href="javascript:void(0);" aria-label="Expand" tabindex="-1"><span class="k-icon k-i-arrow-chevron-down"></span></a>',
                        headerTemplate:
                            "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                        field: 'action',
                        // title: "ACTION",
                        filterable: false,
                        sortable: false,
                        //command: ["edit"],
                        command: [
                            {
                                name: 'edit',
                                iconClass: 'k-icon k-i-edit',
                                className: 'summary-action',
                            },
                            {
                                template: `<button class='k-button k-button-icontext assessmentSyncToMoodleBtn summary-action' title='Add activity to moodle'><span class='k-icon'><img class='w-full h-full' src='${site_url}/v2/img/arrow-sync.svg'/></span></button>`,
                            },
                        ],
                    },
                ],
                editable: 'inline',
                noRecords: noRecordTemplate(),
                dataBound: function (e) {
                    togglePagination('.' + resultGrid);
                },
                edit: function (e) {
                    if (e.model.is_locked) {
                        e.container.find("input[name='marks']").prop('disabled', true);
                    }
                },
            })
            .on('click', '.btn-history', function (e) {
                // let treelist = $(e.delegateTarget).data("kendoGrid");
                let id = $(e.currentTarget)
                    .closest('tr')
                    .find('td:first-child')
                    .find('div')
                    .attr('data-id');
                let subject_name = $(e.currentTarget)
                    .closest('tr')
                    .find('td:first-child')
                    .find('div')
                    .attr('data-subject');
                ajaxActionV2(
                    'api/get-stud-subject-enroll-history',
                    'POST',
                    { student_subject_enrolment_id: id },
                    function (response) {
                        kendoWindowOpen('#currentCourseHistoryModal');
                        $('#currentCourseHistoryModal')
                            .prev()
                            .find('.k-window-title')
                            .text(subject_name);
                        let currentCourseHistoryTemplate = kendo.template(
                            $('#currentCourseHistoryTemplate').html()
                        )(response.data);
                        $(document)
                            .find('#currentCourseHistoryDiv')
                            .html(currentCourseHistoryTemplate);
                    }
                );
            })
            .data('kendoGrid');

        toggleMoodleColumnsAndIconGrid(detailGrid);

        $(e.detailCell).find('.k-grid-header').hide();
        // $(e.detailCell).appendTo("<div class='sss'>dddddddddd</div>");
        console.log('e.detailCell', e.detailCell);
    }

    $('.' + resultGrid).kendoTooltip({
        filter: 'td .action-result-semester',
        position: 'bottom left',
        showOn: 'click',
        showAfter: 100,
        width: 224,
        content: function (e) {
            let dataItem = $('.' + resultGrid)
                .data('kendoGrid')
                .dataItem(e.target.closest('tr'));

            return kendo.template($('#semesterActionTemplate').html())({
                id: dataItem.id,
                subject_id: dataItem.subject_id,
                course_id: dataItem.course_id,
                semester_id: dataItem.semester_id,
                is_course_complete: dataItem.is_course_complete,
                unit_code: dataItem.unit_code,
                is_higher_ed: dataItem.is_higher_ed,
            });
        },
        show: function (e) {
            e.sender.popup.element.find('.k-callout').remove();
            e.sender.popup.element.addClass('tw-popup--top-right tw-tooltip');
            e.sender.popup.wrapper.css({
                right: '64px',
                left: 'unset',
            });
        },
    });
}

function dropDownForCompetency(container, options) {
    var isEditable = options.model.is_locked == 0;
    $(
        '<input name="competency" required data-text-field="text" data-value-field="value" data-bind="value:' +
            options.field +
            '"/>'
    )
        .appendTo(container)
        .kendoDropDownList({
            autoBind: true,
            dataTextField: 'text',
            dataValueField: 'value',
            dataSource: getDropdownDataSource('get-constant-data', {
                action: 'arrAssessmentCompetency',
            }),
            enable: isEditable,
        });
}

function manageTeacherProfilePic(profile_pic, nameStr) {
    // Manage user name with profile picture or default 2 characters
    let html = '';
    if (profile_pic == '') {
        let displayName = 'NA';
        if (typeof nameStr !== undefined && nameStr != null) {
            let name = nameStr.toUpperCase().split(/\s+/);
            displayName =
                name.length >= 2 ? name[0].charAt(0) + name[1].charAt(0) : name[0].substring(0, 2);
        } else {
            nameStr = 'N/A';
        }
        html =
            "<div class='flex items-center  space-x-2 studentNameDiv'><div class='user-profile-pic h-7 w-7 flex justify-center items-center rounded-full bg-primary-blue-500'><span class='text-xs leading-6 font-medium'>" +
            displayName +
            "</span></div>&nbsp;<span  class='text-sm leading-5 text-gray-600 action-div hover:text-primary-blue-500'>" +
            nameStr +
            '</span></div>';
    } else {
        html =
            "<div class='flex items-center  space-x-2 studentNameDiv'><img class='h-7 w-7 rounded-full flex' src='" +
            profile_pic +
            "' alt=''>&nbsp;<span class=' text-sm leading-5 font-medium text-gray-600 action-div hover:text-primary-blue-500'>" +
            nameStr +
            '</span></div>';
    }
    return html;
}

function manageDeleteButtonSemesterUnit(gridId) {
    let html = '';
    // html = '<a role="button" class="k-button k-button-icontext deleteSemester" data-grid_id="'+ gridId +'"  ><span class="k-icon k-i-close"></span>Delete</a>';
    html = `<a role="button" class="k-button k-button-iconext deleteSemester" data-grid_id="${gridId}">
            <img src="${site_url}v2/img/icon-delete.svg" class="w-4 h-4">
        </a>`;
    return html;
}

function setAccordion() {
    const accordionHeader = document.querySelectorAll('.accordion-header');
    accordionHeader.forEach((header) => {
        header.addEventListener('click', function () {
            const accordionContent = header.parentElement.querySelector('.accordion-contentv2');
            let accordionMaxHeight = accordionContent.style.maxHeight;
            $(accordionContent).slideToggle('fast');
        });
    });
}

function tcsiStudentForm(enrollId, subject_id, course_id, formDataValue) {
    ajaxActionV2('api/get-tcsi-student-unit-enroll-form-list', 'POST', {}, function (response) {
        var tcsiStudentUnitEnrollmentInformationId = $('#tcsiStudentUnitEnrollmentInformation')
            .html('')
            .off('submit')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                type: 'group',
                layout: 'grid',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'mode_attendance',
                        label: 'Mode of Attendance(E329)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Mode of Attendance',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.modeOfAttendance,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                    },
                    {
                        field: 'student_status_code',
                        label: 'Student Status Code(E490)',
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Student Status Code',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.arrStudStatusCode,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        colSpan: 6,
                    },
                    {
                        field: 'unit_of_study_status_code',
                        label: 'Unit of study status code(E355)',
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Unit of study status code(E355)',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.arrUnitOfStudyStatus,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        attributes: {
                            disabled: true,
                        },
                        colSpan: 6,
                    },
                    {
                        field: 'subject_tuition_fee',
                        label: 'Amount Charged(E384)',
                        colSpan: 6,
                        editor: customFloatNumberInput,
                        attributes: { placeholder: 'Enter Amount Charged' },
                        //validation: { required: true },
                    },
                    {
                        field: 'paid_amount_or_upfront',
                        label: 'Amount Paid Upfront(E381)',
                        colSpan: 6,
                        editor: customFloatNumberInput,
                        attributes: {
                            placeholder: 'Enter Amount Paid Upfront',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'eftsl_value',
                        label: 'EFTSL Value(E339)',
                        colSpan: 6,
                        className: 'threeDigitInput',
                        editor: customFloatNumberInput,
                        attributes: { placeholder: 'Enter EFTSL Value' },
                        //validation: { required: true },
                    },
                    {
                        field: 'census_date',
                        editor: 'DatePicker',
                        label: 'Unit of Study Census Date(E489)',
                        colSpan: 6,
                        //validation: { required: true },
                        editorOptions: { format: dateFormatFrontSideJS },
                        attributes: {
                            placeholder: 'Select Unit of Study Census Date',
                        },
                    },
                    {
                        field: 'subject_completion_date',
                        editor: 'DatePicker',
                        label: 'Subject Completion Date/Unit of Study Outcome Date(E601)',
                        colSpan: 6,
                        //validation: { required: true },
                        editorOptions: { format: dateFormatFrontSideJS },
                        attributes: {
                            placeholder: 'Select Subject Completion Date',
                        },
                    },
                    {
                        field: 'loan_fee',
                        label: 'Loan Fee(E529)',
                        colSpan: 6,
                        editor: customNumberInput,
                        attributes: { placeholder: 'Enter Loan Fee' },
                        //validation: { required: true },
                    },
                    {
                        field: 'help_loan_amount',
                        label: 'Help Loan Amount(E558)',
                        colSpan: 6,
                        editor: customNumberInput,
                        attributes: {
                            placeholder: 'Enter Help Loan Amount',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'remission_reason',
                        label: 'Remission Reason Code(E446)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Remission Reason Code',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.arrRemissionReasonCode,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'higher_degree_research_end_user_engagement_code',
                        label: 'Higher Degree By Research End User Engagement Code',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel:
                                'Select Higher Degree By Research End User Engagement Code',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.arrStudStatusCode,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'max_stud_contribution',
                        label: 'Maximum Student Contribution Code(E392)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Maximum Student Contribution Code',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.arrStudStatusCode,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'work_experience_industry',
                        label: 'Work Experience in Industry Code(E337)',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Work Experience in Industry Code',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.arrWorkExpIndustryCode,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'summer_winter_school',
                        label: 'Work Experience in Winter School',
                        colSpan: 6,
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Work Experience in Winter School',
                            dataSource: {
                                schema: { data: 'data' },
                                data: response.data.arrStudStatusCode,
                            },
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                        },
                        //validation: { required: true },
                    },
                    {
                        field: 'credit_point',
                        label: 'Credit Point',
                        colSpan: 6,
                        editor: customNumberInput,
                        attributes: { placeholder: 'Enter Credit Point' },
                        //validation: { required: true },
                    },
                    {
                        field: 'academic_organisational_unit',
                        label: 'Academic Organisational Unit',
                        colSpan: 6,
                        editor: customNumberInput,
                        attributes: {
                            placeholder: 'Enter Academic Organisational Unit',
                        },
                        //validation: { required: true },
                    },
                ],
                buttonsTemplate: setWindowFooterTemplate('Save Changes'),
                submit: function (ev) {
                    ev.preventDefault();
                    saveTcsiStudentEnrollmentForm(
                        '#tcsiStudentUnitEnrollmentInformation',
                        '#reportToTCSIModal',
                        enrollId,
                        subject_id,
                        course_id
                    );
                },
            });
        if (formDataValue) {
            tcsiStudentUnitEnrollmentInformationId.data('kendoForm').setOptions({
                formData: {
                    mode_attendance: formDataValue.mode_attendance,
                    student_status_code: formDataValue.student_status_code,
                    subject_tuition_fee: formDataValue.subject_tuition_fee,
                    paid_amount_or_upfront: formDataValue.paid_amount_or_upfront,
                    census_date: formDataValue.census_date,
                    subject_completion_date: formDataValue.subject_completion_date,
                    loan_fee: formDataValue.loan_fee,
                    unit_of_study_status_code: formDataValue.unit_of_study_status_code,
                    eftsl_value: formDataValue.eftsl_value,
                    help_loan_amount: formDataValue.help_loan_amount,
                    remission_reason: formDataValue.remission_reason,
                    higher_degree_research_end_user_engagement_code:
                        formDataValue.higher_degree_research_end_user_engagement_code,
                    max_stud_contribution: formDataValue.max_stud_contribution,
                    work_experience_industry: formDataValue.work_experience_industry,
                    summer_winter_school: formDataValue.summer_winter_school,
                    credit_point: formDataValue.credit_point,
                    academic_organisational_unit: formDataValue.academic_organisational_unit,
                },
            });
        }
    });
}

function saveTcsiStudentEnrollmentForm(formId, modalId, id, subject_id, course_id) {
    let unitIds = $('input[name="unitId[]"]')
        .map(function () {
            return this.value;
        })
        .get();
    if (formId.length > 0) {
        let selectedDataArr = {
            unitIds: unitIds.length > 0 ? unitIds : [id],
            college_id: collegeId,
            student_id: studentId,
            stud_subject_enroll_id: id,
            subject_id: subject_id,
            course_id: course_id,
        };
        let dataArr = getSerializeFormArray(formId, selectedDataArr);
        ajaxActionV2(
            'api/save-tcsi-student-unit-enrollment-form',
            'POST',
            dataArr,
            function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    $(modalId).data('kendoWindow').close();
                    reloadGrid('#resultTabUnitList');
                }
            }
        );
    }
}

function setSelectedUnits(selectedUnits) {
    let filterHtml = '';
    if (selectedUnits.length > 0) {
        selectedUnits.filter(function (arr) {
            let unitLabel = getUnitLabelTextFormat(arr['unitCode'], arr['unitName']);
            filterHtml += `<div class="inline-flex items-center justify-center space-x-2 px-2 py-1 m-1 bg-gray-100 rounded-lg">
                    <span class="text-xs leading-none text-center text-gray-800">${unitLabel}</span>
                    <input hidden name="unitId[]" value="${arr['id']}">
                    <span class="cursor-pointer k-icon k-i-close clear_selected_units_for_tcsi text-blue-500" data-filter-id="${arr['id']}" data-key="${arr['unitName']}"></span>
                </div>`;
        });
    } else {
        $('.selectedUnitsDiv').hide();
        $('.selectedAvetmissUnitsDiv').hide();
    }
    return filterHtml;
}

function setSingleSelectedUnit(unitCode = '', unitName = '') {
    let filterHtml = '';
    let unitLabel = getUnitLabelTextFormat(unitCode, unitName);
    if (unitLabel.length > 0) {
        $('.selectedUnitsDiv').show();
        $('.selectedAvetmissUnitsDiv').show();
        return `<div class="inline-flex items-center justify-center p-2 bg-gray-100 rounded-lg">
                    <span class="text-xs leading-none text-center text-gray-800">${unitLabel}</span>
                </div>`;
    }
    return filterHtml;
}

function getUnitLabelTextFormat(unitCode = '', unitName = '') {
    let unitText = '';
    if (unitCode || unitName) {
        unitText = unitCode && unitName ? `${unitCode} : ${unitName}` : unitCode || unitName;
    }
    return unitText;
}

function onChange(e) {
    if (e.sender.editable) {
        e.sender.cancelRow();
    }
    selectedUnits = [];
    selectedUnitsIds = [];
    checkIsCourseComplete = [];
    var rows = e.sender.select();
    rows.each(function (e) {
        var grid = $('#resultTabUnitList').data('kendoGrid');
        var dataItem = grid.dataItem(this);

        selectedUnits.push({
            id: dataItem.id,
            unitCode: dataItem.unit_code,
            unitName: dataItem.unit_name,
            start_date: dataItem.start_date,
            finish_date: dataItem.finish_date,
            final_outcome: dataItem.final_outcome,
        });
        selectedUnitsIds.push(dataItem.id);

        if (!dataItem.is_course_complete) {
            checkIsCourseComplete.push(dataItem.id);
        }
    });

    $(
        '.bultkReportToTcsi, .bulkAssignBatchBtn, .bulkUpdateUnitOutcome, .bulkUpdateUnitAvetmissOutcome, .bulkUpdateResultCourse'
    ).attr('disabled', selectedUnits.length === 0);
    $('.bulkAssignBatchBtn, .bulkUpdateUnitOutcome').attr(
        'disabled',
        checkIsCourseComplete.length === 0
    );
}

function transferToAnotherCourseForm(enrollId, subject_id, course_id, semester_id, formDataValue) {
    setStudentInfoInDiv('#studentInfoTransferToAnotherCourse');
    let postDataArr = {
        student_course_id: selectedStudCourseID,
        student_id: studentId,
        id: enrollId,
        subject_id: subject_id,
        course_id: course_id,
        semester_id: semester_id,
        is_higher_ed: isHigherEd,
    };
    ajaxActionV2(
        'api/get-transfer-to-another-course-form-dropdown-list',
        'POST',
        postDataArr,
        function (response) {
            $('#transferToAnotherCourseModal')
                .find('#subjectNameLableTransferToAnotherCourse')
                .text(response.data.subjectUnitNameDetail[0].subject_name);
            $('#transferToAnotherCourseModal')
                .find('#unitNameLableTransferToAnotherCourse')
                .text(response.data.subjectUnitNameDetail[0].unit_name);
            $('#transferToAnotherCourseModal')
                .find('#finalOutcomeLableTransferToAnotherCourse')
                .text(formDataValue.final_outcome == 'C' ? 'CT' : 'Enrolled');
            let isEditable = formDataValue.is_result_lock == 0;
            var transferToAnotherCourseInformationForm = $(
                '#transferToAnotherCourseInformationForm'
            )
                .html('')
                .kendoForm({
                    orientation: 'vertical',
                    validatable: defaultErrorTemplate(),
                    type: 'group',
                    layout: 'grid',
                    grid: { cols: 12, gutter: 16 },
                    items: [
                        {
                            field: 'transfer_to_student_course_id',
                            label: 'Transfer to Course',
                            editor: 'DropDownList',
                            editorOptions: {
                                optionLabel: 'Select Transfer to Course',
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrTransferToCourse,
                                },
                                select: function (e) {
                                    if (e.dataItem) {
                                        let postArr = {
                                            student_course_id: e.dataItem.Id,
                                        };
                                        setStratDateAndEndDate(postArr);
                                    }
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            colSpan: 6,
                        },
                        {
                            field: 'activity_start_date',
                            editor: 'DatePicker',
                            label: 'Start Date',
                            colSpan: 6,
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            validation: {
                                required: true,
                            },
                        },
                        {
                            field: 'activity_finish_date',
                            editor: 'DatePicker',
                            label: 'Finish Date',
                            colSpan: 6,
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'last_assessment_approved_date',
                            editor: 'DatePicker',
                            label: 'Activity End Date',
                            colSpan: 6,
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            validation: { required: true },
                        },
                    ],
                    buttonsTemplate: setWindowFooterTemplate('Save Changes'),
                    submit: function (ev) {
                        ev.preventDefault();
                        saveTransferToAnotherCourseForm(
                            '#transferToAnotherCourseInformationForm',
                            '#transferToAnotherCourseModal',
                            enrollId,
                            subject_id,
                            course_id
                        );
                    },
                });

            if (formDataValue) {
                transferToAnotherCourseInformationForm.data('kendoForm').setOptions({
                    formData: {
                        activity_start_date: formDataValue.activity_start_date,
                        activity_finish_date: formDataValue.activity_finish_date,
                        last_assessment_approved_date: formDataValue.last_assessment_approved_date,
                    },
                });
            }
        }
    );
}

function assignBatchForm(enrollId, subject_id, course_id, semester_id, formDataValue) {
    setStudentInfoInDiv('#studentInfoSubjectOutcome');
    console.log('semester_id', semester_id);
    let postDataArr = {
        student_course_id: selectedStudCourseID,
        student_id: studentId,
        id: enrollId,
        subject_id: subject_id,
        course_id: course_id,
        semester_id: semester_id,
        is_higher_ed: isHigherEd,
    };
    ajaxActionV2(
        'api/get-student-outcome-form-dropdown-list',
        'POST',
        postDataArr,
        function (response) {
            $('#assignBatchModal')
                .find('#subjectNameLable')
                .text(response.data.subjectUnitNameDetail[0].subject_name);
            $('#assignBatchModal')
                .find('#unitNameLable')
                .text(response.data.subjectUnitNameDetail[0].unit_name);
            let isEditable = formDataValue.is_result_lock == 0;
            var assignBatchInformationForm = $('#assignBatchInformationForm')
                .html('')
                .kendoForm({
                    orientation: 'vertical',
                    validatable: defaultErrorTemplate(),
                    type: 'group',
                    layout: 'grid',
                    grid: { cols: 12, gutter: 16 },
                    items: [
                        {
                            field: 'semester_id',
                            label: 'Semester',
                            colSpan: 6,
                            editor: 'DropDownList',
                            attributes: {
                                disabled: semester_id == 0 ? false : true,
                            },
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrSemester,
                                },
                                select: function (e) {
                                    if (e.dataItem) {
                                        let postArr = {
                                            semester_id: e.dataItem.Id,
                                        };
                                        setDropdownList('term', 'communication-term-data', postArr);
                                        let postArr2 = {
                                            subject_id: $('#assignBatchModal')
                                                .find('#subject_id')
                                                .val(),
                                            semester_id: e.dataItem.Id,
                                        };
                                        setDropdownListIdName(
                                            'batch',
                                            'get-course-batch',
                                            postArr2
                                        );
                                    }
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'term',
                            label: 'Term',
                            editor: 'DropDownList',
                            attributes: {
                                disabled: semester_id == 0 ? false : true,
                            },
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrTerm,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            colSpan: 6,
                        },
                        {
                            field: 'batch',
                            label: 'Batch',
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrBatch,
                                },
                                select: function (e) {
                                    let tempSelectedDataArr = selectedDataArr;
                                    tempSelectedDataArr.is_higher_ed = isHigherEd;
                                    tempSelectedDataArr.batch = e.dataItem.Id;

                                    ajaxActionV2(
                                        'api/get-batch-date-info',
                                        'POST',
                                        tempSelectedDataArr,
                                        function (response) {
                                            // Set Start-date end-date value
                                            const responseData = response.data;
                                            let form = $(
                                                '#assignBatchInformationForm'
                                            ).getKendoForm();
                                            form.editable.options.model.set(
                                                'activity_start_date',
                                                responseData.start_week
                                            );
                                            form.editable.options.model.set(
                                                'activity_finish_date',
                                                responseData.end_week
                                            );
                                        }
                                    );
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            colSpan: 6,
                        },
                        {
                            field: 'activity_start_date',
                            editor: 'DatePicker',
                            label: 'Start Date',
                            colSpan: 6,
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            attributes: {
                                disabled: !isEditable,
                            },
                            validation: {
                                required: true,
                                validateDate: function (input) {
                                    if (input.attr('id') == 'activity_start_date') {
                                        return validDate(input.val());
                                    }
                                    if (input.attr('id') == 'activity_finish_date') {
                                        return validDate(input.val());
                                    } else {
                                        return true;
                                    }
                                },
                            },
                        },
                        {
                            field: 'activity_finish_date',
                            editor: 'DatePicker',
                            label: 'Finish Date',
                            colSpan: 6,
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            attributes: {
                                disabled: !isEditable,
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'last_assessment_approved_date',
                            editor: 'DatePicker',
                            label: 'Activity End Date',
                            colSpan: 6,
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            attributes: {
                                disabled: !isEditable,
                            },
                            // validation: { required: true },
                        },
                        {
                            field: 'study_reason',
                            label: 'Study Reason',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrStudyreasonData,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'venue_location',
                            label: 'Venue/Training Location',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrVenau,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'funding_source_state',
                            label: 'Funding Source State',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrFundingSource,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'funding_source_nat',
                            label: 'Funding Source Nat',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrFundingSourceNat,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'final_outcome',
                            label: 'Finale Outcome',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                //dataSource: getDropdownDataSource('get-constant-data', {'action': 'arrFinalOutcome', 'college_id': collegeId, 'is_higher_ed': isHigherEd}),
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrSelectFinalOutcome,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                            attributes: {
                                disabled: !isEditable,
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'outcome_identifier',
                            label: 'Outcome Identifier – Training organization :',
                            colSpan: 6,
                            editor: customNumberInput,
                            validation: { required: true },
                        },
                        {
                            field: 'comment',
                            label: 'Comments/Remarks',
                            colSpan: 12,
                            editor: 'TextArea',
                            editorOptions: { rows: 5 },
                            validation: { required: true },
                        },
                    ],
                    buttonsTemplate: setWindowFooterTemplate('Save Changes'),
                    submit: function (ev) {
                        ev.preventDefault();
                        saveAssignBatchForm(
                            '#assignBatchInformationForm',
                            '#assignBatchModal',
                            enrollId,
                            subject_id,
                            course_id
                        );
                    },
                });

            if (formDataValue) {
                assignBatchInformationForm.data('kendoForm').setOptions({
                    formData: {
                        semester_id: formDataValue.semester_id,
                        term: formDataValue.term,
                        batch: formDataValue.batch,
                        activity_start_date: formDataValue.activity_start_date,
                        activity_finish_date: formDataValue.activity_finish_date,
                        last_assessment_approved_date: formDataValue.last_assessment_approved_date,
                        study_reason: formDataValue.study_reason,
                        funding_source_state: formDataValue.funding_source_state,
                        funding_source_nat: formDataValue.funding_source_nat,
                        final_outcome: isHigherEd
                            ? formDataValue.mark_outcome
                            : formDataValue.final_outcome,
                        outcome_identifier: formDataValue.outcome_identifier,
                        comment: formDataValue.comment,
                        venue_location: formDataValue.vanue_location,
                    },
                });
            }
        }
    );
}

function setStratDateAndEndDate(postArr) {
    ajaxActionV2('api/get-student-course-info', 'POST', postArr, function (response) {
        console.log('response.data.start_date', response.data.start_date);
        let form = $('#transferToAnotherCourseInformationForm').getKendoForm();
        form.editable.options.model.set('activity_start_date', response.data.start_date);
        form.editable.options.model.set('activity_finish_date', response.data.finish_date);
        form.editable.options.model.set('last_assessment_approved_date', response.data.finish_date);
    });
}

function saveAssignBatchForm(formId, modalId, id, subject_id, course_id) {
    let unitIds = $('input[name="unitId[]"]')
        .map(function () {
            return this.value;
        })
        .get();
    if (formId.length > 0) {
        let selectedDataArr = {
            unitIds: unitIds,
            college_id: collegeId,
            student_id: studentId,
            stud_subject_enroll_id: id,
            subject_id: subject_id,
            course_id: course_id,
            is_higher_ed: isHigherEd,
        };

        let disabledFields = $(document).find('#assignBatchModal').find('[disabled]');
        disabledFields.prop('disabled', false);
        let dataArr = getSerializeFormArray('#assignBatchModal', selectedDataArr);

        disabledFields.prop('disabled', true);
        if (dataArr.final_outcome == 'C' || dataArr.final_outcome == 'NYC') {
            if (!confirm(resultConformationMessage)) {
                return false;
            }
        }
        ajaxActionV2('api/save-subject-outcome-form', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $(modalId).data('kendoWindow').close();
                reloadGrid('#resultTabUnitList');
                if ($('#selectedUnitSubjectGrid').data('kendoGrid')) {
                    reloadGrid('#selectedUnitSubjectGrid');
                }
                let dataArr1 = {
                    student_id: studentId,
                    is_higher_ed: isHigherEd,
                };
                dataArr1['student_course_id'] = $('.accordionResultForTab.is-open').attr(
                    'data-student-course-id'
                );
                dataArr1['semester_id'] = $('.accordionResultForTab.is-open').attr('data-semester');
                dataArr1['term_id'] = $('.accordionResultForTab.is-open').attr('data-term');
                let resultGrid = $('.accordionResultForTab.is-open').attr('data-grid-id');
                var grid = $('.' + resultGrid).data('kendoGrid');
                // Check if the grid exists
                if (grid) {
                    // Destroy the Kendo Grid instance
                    grid.destroy();
                }
                manageResultDataForResultTab(dataArr1, resultGrid);
            }
        });
    }
}
function saveTransferToAnotherCourseForm(formId, modalId, id, subject_id, course_id) {
    if (formId.length > 0) {
        let selectedDataArr = {
            college_id: collegeId,
            student_id: studentId,
            id: id,
            student_course_id: selectedStudCourseID,
            subject_id: subject_id,
            course_id: course_id,
            is_higher_ed: isHigherEd,
        };

        let dataArr = getSerializeFormArray(
            '#transferToAnotherCourseInformationForm',
            selectedDataArr
        );
        console.log('dataArr after serialization:', dataArr);

        ajaxActionV2('api/transfer-result-to-another-course', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $(modalId).data('kendoWindow').close();
            }
        });
    }
}

function unitOutComeForm(enrollId, subject_id, course_id, semester_id, formDataValue) {
    let postDataArr = {
        student_id: studentId,
        id: enrollId,
        subject_id: subject_id,
        course_id: course_id,
        semester_id: semester_id,
        is_higher_ed: isHigherEd,
    };

    ajaxActionV2(
        'api/get-unit-outcome-form-dropdown-list',
        'POST',
        postDataArr,
        function (response) {
            let isEditable = formDataValue.is_result_lock == 0;

            var unitOutComeInformation = $('#unitOutComeInformation')
                .html('')
                .kendoForm({
                    orientation: 'vertical',
                    type: 'group',
                    layout: 'grid',
                    grid: { cols: 12, gutter: 16 },
                    items: [
                        {
                            field: 'study_from',
                            editor: 'DatePicker',
                            attributes: {
                                readOnly: true,
                                placeholder: 'Study From ',
                            },
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            label: 'Study Period',
                            colSpan: 6,
                            validation: { required: true },
                        },
                        {
                            field: 'study_to',
                            editor: 'DatePicker',
                            attributes: {
                                readOnly: true,
                                placeholder: 'Study To',
                            },
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            label: 'To',
                            colSpan: 6,
                            validation: { required: true },
                        },
                        {
                            field: 'schedule_hours',
                            label: 'Unit Schedule Hours :',
                            attributes: { readOnly: true },
                            colSpan: 6,
                            editor: 'TextBox',
                            validation: { required: true },
                        },
                        {
                            field: 'delivery_mode',
                            label: 'Unit Delivery Mode',
                            colSpan: 6,
                            editor: 'DropDownList',
                            // attributes: { readOnly: true },
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrDeliveryMode,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'tution_fee',
                            label: 'Tuition Fee :',
                            colSpan: 6,
                            attributes: { readOnly: true },
                            editor: 'TextBox',
                            validation: { required: true },
                        },
                        {
                            field: 'amount_paid',
                            label: 'Amount Paid/Fee Paid Upfront:',
                            colSpan: 6,
                            attributes: { readOnly: true },
                            editor: 'TextBox',
                            validation: { required: true },
                        },
                        {
                            field: 'competency_date',
                            label: 'Unit Competency Date:',
                            colSpan: 6,
                            editor: 'DatePicker',
                            editorOptions: {
                                format: dateFormatFrontSideJS,
                            },
                            validation: { required: true },
                        },
                        {
                            field: 'compentency',
                            label: 'Compentency',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrSelectFinalOutcome,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'marks',
                            label: 'Marks',
                            colSpan: 6,
                        },
                        {
                            field: 'attended_hour',
                            label: 'Attended Hours (WD only):',
                            colSpan: 6,
                            attributes: { readOnly: true },
                            editor: 'TextBox',
                            validation: { required: true },
                        },
                        {
                            field: 'comment',
                            label: 'Comment:',
                            colSpan: 12,
                            editor: 'TextArea',
                            editorOptions: { rows: 5 },
                            validation: { required: true },
                        },
                    ],
                    buttonsTemplate: setWindowFooterTemplate('Save Changes'),
                    submit: function (ev) {
                        ev.preventDefault();
                        saveUnitOutcomeForm(
                            '#unitOutComeInformation',
                            '#unitOutComeModal',
                            enrollId,
                            subject_id,
                            course_id
                        );
                    },
                });

            setTimeout(() => {
                // for HigherEd compantnacy is disable otherwise check result is lock
                $('#unitOutComeInformation')
                    .find('#compentency')
                    .data('kendoDropDownList')
                    .enable(response.data.isHigherEd ? false : isEditable);
                if (response.data.isHigherEd) {
                    $('#unitOutComeInformation')
                        .find('#marks-form-label')
                        .closest('.k-form-field')
                        .show();
                } else {
                    $('#unitOutComeInformation')
                        .find('#marks-form-label')
                        .closest('.k-form-field')
                        .hide();
                }
            }, 500);

            if (formDataValue) {
                unitOutComeInformation.data('kendoForm').setOptions({
                    formData: {
                        study_from: formDataValue.study_from,
                        study_to: formDataValue.study_to,
                        schedule_hours: formDataValue.schedule_hours,
                        delivery_mode: formDataValue.delivery_mode,
                        tution_fee: formDataValue.tution_fee,
                        amount_paid: formDataValue.amount_paid,
                        competency_date: formDataValue.competency_date,
                        compentency: formDataValue.final_outcome,
                        marks: formDataValue.marks,
                        attended_hour: formDataValue.attended_hour,
                        comment: formDataValue.comment,
                    },
                });
            }
        }
    );
}

function saveUnitOutcomeForm(formId, modalId, id, subject_id, course_id) {
    let unitIds = $('input[name="unitId[]"]')
        .map(function () {
            return this.value;
        })
        .get();
    if (formId.length > 0) {
        let selectedDataArr = {
            unitIds: unitIds,
            college_id: collegeId,
            student_id: studentId,
            stud_subject_enroll_id: id,
            subject_id: subject_id,
            course_id: course_id,
        };
        let disabledFields = $(document).find('#unitOutComeInformation').find('[disabled]');
        disabledFields.prop('disabled', false);
        let dataArr = getSerializeFormArray(formId, selectedDataArr);
        disabledFields.prop('disabled', true);

        if (dataArr.compentency == 'C' || dataArr.compentency == 'NYC') {
            if (!confirm(resultConformationMessage)) {
                return false;
            }
        }
        ajaxActionV2('api/save-unit-outcome-form', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $(modalId).data('kendoWindow').close();
                reloadGrid('#resultTabUnitList');

                if (
                    $('#bulkUpdateUnitOutcomeModal')
                        .find('#selectedUnitSubjectGrid')
                        .data('kendoGrid')
                ) {
                    $('#bulkUpdateUnitOutcomeModal')
                        .find('#selectedUnitSubjectGrid')
                        .data('kendoGrid')
                        .refresh();
                    $('#bulkUpdateUnitOutcomeModal')
                        .find('#selectedUnitSubjectGrid')
                        .data('kendoGrid')
                        .dataSource.read();
                }

                let dataArr1 = {
                    student_id: studentId,
                    is_higher_ed: isHigherEd,
                };
                dataArr1['student_course_id'] = $('.accordionResultForTab.is-open').attr(
                    'data-student-course-id'
                );
                dataArr1['semester_id'] = $('.accordionResultForTab.is-open').attr('data-semester');
                dataArr1['term_id'] = $('.accordionResultForTab.is-open').attr('data-term');
                let resultGrid = $('.accordionResultForTab.is-open').attr('data-grid-id');
                var grid = $('.' + resultGrid).data('kendoGrid');
                // Check if the grid exists
                if (grid) {
                    // Destroy the Kendo Grid instance
                    grid.destroy();
                }
                manageResultDataForResultTab(dataArr1, resultGrid);
            }
        });
    }
}

function avetmissForm(enrollId, subject_id, course_id, semester_id, formDataValue) {
    ajaxActionV2(
        'api/get-avetmiss-form-dropdown-list',
        'POST',
        {
            student_id: studentId,
            id: enrollId,
            subject_id: subject_id,
            course_id: course_id,
            semester_id: semester_id,
            student_course_id: selectedStudCourseID,
            delivery_mode: formDataValue ? formDataValue.delivery_mode : '',
        },
        function (response) {
            var unitAvetmissInformation = $('#unitAvetmissInformation')
                .html('')
                .off('submit')
                .kendoForm({
                    validatable: defaultErrorTemplate(),
                    orientation: 'vertical',
                    type: 'group',
                    layout: 'grid',
                    grid: { cols: 12, gutter: 16 },
                    items: [
                        {
                            field: 'funding_source_state',
                            label: 'Funding Source State',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrFundingSource,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'funding_source_nat',
                            label: 'Funding Source National',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                select: function (e) {
                                    if (e.dataItem.Id == '13') {
                                        $('#unitAvetmissModal')
                                            .find('#funding_identifier')
                                            .prop('required', true);
                                    } else {
                                        $('#unitAvetmissModal')
                                            .find('#funding_identifier')
                                            .prop('required', false);
                                    }
                                },
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrFundingSourceNat,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'funding_identifier',
                            label: 'Specific Funding Identifier',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrSpecificFunding,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'study_reason',
                            label: 'Study Reason',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrStudyreasonData,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'course_commencing_id',
                            label: 'Course Commencing ID',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrCourseCommencing,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'training_contract_id',
                            label: 'Training Contract ID',
                            colSpan: 6,
                            editor: customNumberInput,
                            validation: { required: true },
                        },
                        {
                            field: 'apprenticeship_client_id',
                            label: 'Apprenticeship Client ID',
                            colSpan: 6,
                            editor: customNumberInput,
                            validation: { required: true },
                        },
                        {
                            field: 'vet_in_school',
                            label: 'VET in School Flag',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrVETInSchoolFlag,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'fee_exemption_id',
                            label: 'Fee Exemption Type ID',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrFeeExemptionType,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'purchase_contract_id',
                            label: 'Purchase contract Id',
                            colSpan: 6,
                            editor: customNumberInput,
                            validation: { required: true },
                        },
                        {
                            field: 'con_schedule_id',
                            label: ' Purchasing Contract Schedule ID',
                            colSpan: 6,
                            editor: customNumberInput,
                            validation: { required: true },
                        },
                        {
                            field: 'booking_id',
                            label: 'Booking ID',
                            colSpan: 6,
                            editor: customNumberInput,
                            validation: { required: true },
                        },
                        {
                            field: 'course_site_id',
                            label: 'Course Site ID',
                            colSpan: 6,
                            editor: customNumberInput,
                            validation: { required: true },
                        },
                        {
                            field: 'delivery_mode',
                            label: 'Delivery Mode',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                select: function (e) {
                                    if (e.dataItem) {
                                        onChangeDeliveryMode(e.dataItem.Id);
                                    }
                                },
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrDeliveryMode,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                        {
                            field: 'predominant_delivery_mode',
                            label: 'Predominant Delivery Mode',
                            colSpan: 6,
                            editor: 'DropDownList',
                            editorOptions: {
                                dataSource: {
                                    schema: { data: 'data' },
                                    data: response.data.arrPredominantDeliveryMode,
                                },
                                dataValueField: 'Id',
                                dataTextField: 'Name',
                            },
                        },
                    ],
                    buttonsTemplate: setWindowFooterTemplate('Save Changes'),
                    submit: function (ev) {
                        ev.preventDefault();
                        saveAvetmissForm(
                            '#unitAvetmissInformation',
                            '#unitAvetmissModal',
                            enrollId,
                            subject_id,
                            course_id
                        );
                    },
                });

            $('#unitAvetmissInformation')
                .find('#delivery_mode')
                .data('kendoDropDownList')
                .value(response.data.defaultSelected.delivery_mode);
            if (formDataValue) {
                unitAvetmissInformation.data('kendoForm').setOptions({
                    formData: {
                        funding_source_state: formDataValue.funding_source_state,
                        funding_source_nat: formDataValue.funding_source_nat,
                        funding_identifier: formDataValue.funding_identifier,
                        study_reason: formDataValue.study_reason,
                        course_commencing_id: formDataValue.course_commencing_id,
                        training_contract_id: formDataValue.training_contract_id,
                        apprenticeship_client_id: formDataValue.apprenticeship_client_id,
                        con_schedule_id: formDataValue.con_schedule_id,
                        vet_in_school: formDataValue.vet_in_school,
                        purchase_contract_id: formDataValue.purchase_contract_id,
                        fee_exemption_id: formDataValue.fee_exemption_id,
                        booking_id: formDataValue.booking_id,
                        course_site_id: formDataValue.course_site_id,
                        delivery_mode: formDataValue.delivery_mode,
                        predominant_delivery_mode: formDataValue.predominant_delivery_mode,
                    },
                });
                if (!formDataValue.predominant_delivery_mode) {
                    var dropdownlist = $('#predominant_delivery_mode').data('kendoDropDownList');
                    dropdownlist.select(0);
                }
            } else {
                var dropdownlist = $('#predominant_delivery_mode').data('kendoDropDownList');
                dropdownlist.select(0);
            }
        }
    );
}

function saveAvetmissForm(formId, modalId, id, subject_id, course_id) {
    let unitIds = $('input[name="unitId[]"]')
        .map(function () {
            return this.value;
        })
        .get();
    if (formId.length > 0) {
        let selectedDataArr = {
            unitIds: unitIds,
            college_id: collegeId,
            student_id: studentId,
            stud_subject_enroll_id: id,
            subject_id: subject_id,
            course_id: course_id,
            is_higher_ed: isHigherEd,
        };
        let dataArr = getSerializeFormArray(formId, selectedDataArr);
        ajaxActionV2('api/save-avetmiss-form', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $(modalId).data('kendoWindow').close();
                reloadGrid('#resultTabUnitList');
            }
        });
    }
}

function deleteSemesterUnit(primaryID, gridId) {
    if (primaryID > 0) {
        ajaxActionV2(
            'api/delete-student-result-data-semester',
            'POST',
            { id: primaryID },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                refreshGrid2('.' + gridId);
                // paymentsCardUpdate();
            }
        );
    }
}

function onChangeDeliveryMode(value) {
    $('#predominant_delivery_mode').kendoDropDownList({
        dataTextField: 'Name',
        dataValueField: 'Id',
        dataBound: function (e) {
            this.select(0);
            this.trigger('change');
            this.trigger('select');
        },
        dataSource: getDropdownDataSource('get-predominant-delivery-mode', {
            delivery_mode: value,
        }),
    });
}

function setStudentInfoInDiv(divId) {
    ajaxActionV2(
        'api/get-student-details-data',
        'POST',
        { student_course_id: selectedStudCourseID },
        function (response) {
            let studentInfoDiv = kendo.template($('#commonDivForStudentInfo').html())({
                data: response.data[0],
            });
            $(document).find(divId).html(studentInfoDiv);
        }
    );
}

function setDropdownList(fieldID, api_url, postArr = {}) {
    $('#' + fieldID).kendoDropDownList({
        autoWidth: true,
        dataTextField: 'text',
        dataValueField: 'value',
        filter: 'contains',
        dataSource: getDropdownDataSource(api_url, postArr),
    });
}

function setDropdownListIdName(fieldID, api_url, postArr = {}) {
    $('#' + fieldID).kendoDropDownList({
        autoWidth: true,
        dataTextField: 'Name',
        dataValueField: 'Id',
        filter: 'contains',
        dataSource: getDropdownDataSource(api_url, postArr),
    });
    var batchDropdownlist = $('#batch').data('kendoDropDownList');
    if (batchDropdownlist) {
        batchDropdownlist.bind('select', function (e) {
            var dataItem = this.dataItem(e.item);
            let tempSelectedDataArr = {
                ...selectedDataArr,
                is_higher_ed: isHigherEd,
                batch: dataItem.Id,
            };
            ajaxActionV2(
                'api/get-batch-date-info',
                'POST',
                tempSelectedDataArr,
                function (response) {
                    const { start_week, end_week } = response.data;
                    let form = $('#assignBatchInformationForm').getKendoForm();
                    form.editable.options.model.set('activity_start_date', start_week);
                    form.editable.options.model.set('activity_finish_date', end_week);
                }
            );
        });
    }
}

function onSelect() {
    console.log('s');
}

function hideItem(count) {
    console.log('count', count);
    if (count == '0.00%') {
        return 'hidden';
    }
}

function getUnitProgressTooltip() {
    var unitProgressTt = $('#unitProgressHelp')
        .kendoTooltip({
            filter: '.icon-help',
            width: 130,
            position: 'bottom',
            autoHide: true,
            content: kendo.template($('#resultHelp').html()),
            show: function (e) {
                e.sender.popup.wrapper.css({
                    width: '130px',
                    right: '32px',
                    left: 'unset',
                });
            },
        })
        .data('kendoTooltip');
    return unitProgressTt;
}

function getStudnetCourseForUnitV2(selectedUnits) {
    selectedDataArr.selectedUnits = selectedUnits;
    let unitList = kendo.template($('#unitListTemplate').html())({ selectedUnits });
    $('#unitList').html(unitList);
    ajaxActionV2('api/get-enrolled-course', 'POST', selectedDataArr, function (response) {
        $('#updateBulkResultCourseForm')
            .html('')
            .off('submit')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                type: 'group',
                layout: 'grid',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'newStudentCourseId',
                        label: 'Select Course',
                        colSpan: 12,
                        editor: 'DropDownList',
                        editorOptions: {
                            dataValueField: 'id',
                            dataTextField: 'course_title',
                            dataSource: response.data.enrolledCourse,
                        },
                    },
                ],
                buttonsTemplate:
                    '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 fixed border-t bg-white px-6">\n' +
                    '<div class="float-right flex space-x-4 items-center justify-end">\n' +
                    '<button type="button" data-modal-name="bulkTransferToAnotherCourseModal" class="closeModal flex items-center justify-center w-24 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300 hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">\n' +
                    '<p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>\n' +
                    '</button>\n' +
                    '<button type="button" class="submitBulkResultUpdate flex items-center justify-center h-8 px-3 py-2 bg-primary-blue-500 shadow rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
                    '<p class="text-sm font-medium leading-4 text-white">Update</p></button>\n' +
                    '</div>\n' +
                    '</div>',
            });

        kendoWindowOpen('#bulkTransferToAnotherCourseModal');
    });
}
function getStudnetCourseForUnit() {
    ajaxActionV2('api/get-enrolled-course', 'POST', selectedDataArr, function (response) {
        $('#updateResultCourseForm')
            .html('')
            .off('submit')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                type: 'group',
                layout: 'grid',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'newStudentCourseId',
                        label: 'Select Course',
                        colSpan: 12,
                        editor: 'DropDownList',
                        editorOptions: {
                            dataValueField: 'id',
                            dataTextField: 'course_title',
                            dataSource: response.data.enrolledCourse,
                        },
                    },
                ],
                buttonsTemplate:
                    '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end px-6 py-2">\n' +
                    '<div class="float-right flex space-x-4 items-center justify-end">\n' +
                    '<button type="button" data-modal-name="updateResultCourseModal" class="closeModal flex items-center justify-center w-24 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300 hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">\n' +
                    '<p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>\n' +
                    '</button>\n' +
                    '<button type="button" class="submitresultUpdate flex items-center justify-center h-8 px-3 py-2 bg-primary-blue-500 shadow rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
                    '<p class="text-sm font-medium leading-4 text-white">Update</p></button>\n' +
                    '</div>\n' +
                    '</div>',
            });

        kendoWindowOpen('#updateResultCourseModal');
    });
}

$(document).on('click', '.accordionResult', function (e) {
    e.preventDefault();
    $(this).toggleClass('is-open');
    let content = this.nextElementSibling;
    let dataArr1 = { student_id: studentId };
    dataArr1['student_course_id'] = $(this).attr('data-student-course-id');
    dataArr1['semester_id'] = $(this).attr('data-semester');
    dataArr1['term_id'] = $(this).attr('data-term');
    let resultGrid = $(this).attr('data-grid-id');
    // var resultGridFlag = true;
    if (content.style.maxHeight) {
        content.style.maxHeight = null;
    } else {
        manageResultData(dataArr1, resultGrid);
        setTimeout(() => {
            content.style.maxHeight = content.scrollHeight + 'px';
        }, 1000);
    }
});

$(document).on('click', '.accordionResultForTab', function (e) {
    e.preventDefault();
    $(this).toggleClass('is-open');
    let content = this.nextElementSibling;
    let dataArr1 = { student_id: studentId, is_higher_ed: isHigherEd };
    dataArr1['student_course_id'] = $(this).attr('data-student-course-id');
    dataArr1['semester_id'] = $(this).attr('data-semester');
    dataArr1['term_id'] = $(this).attr('data-term');
    let resultGrid = $(this).attr('data-grid-id');
    var resultGridFlag = true;
    let gridObject = $('.' + resultGrid).data('kendoGrid');
    console.log('is', gridObject);
    if (gridObject) {
        console.log(gridObject.dataSource.total());
    }
    manageResultDataForResultTab(dataArr1, resultGrid);
    $(content).slideToggle('fast');
});

$(document).on('click', '#resultUnitListExpand', function (e) {
    e.preventDefault();
    $('.resultUnitListExpandClass')
        .find('img')
        .attr('src', site_url + 'v2/img/plus_gray.svg')
        .addClass('w-3 h-3');

    $(this).parent().parent().find('td.k-hierarchy-cell').find('a').click();
    isExpand = $(this).parent().parent().find('.k-hierarchy-cell').attr('aria-expanded');
    if (isExpand == 'true') {
        $(this)
            .find('img')
            .attr('src', site_url + 'v2/img/minus-gray.svg');
    } else {
        $('.k-detail-row').remove();
        $(this)
            .find('img')
            .attr('src', site_url + 'v2/img/plus_gray.svg');
    }
});

$(document).on('click', '.resultSemesterListExpand', function (e) {
    $('.resultSemesterListExpandClass')
        .find('img')
        .attr('src', site_url + 'v2/img/plus_gray.svg');

    $(this).parent().parent().find('td.k-hierarchy-cell').find('a').click();
    isExpand = $(this).parent().parent().find('.k-hierarchy-cell').attr('aria-expanded');
    if (isExpand == 'true') {
        $(this)
            .find('img')
            .attr('src', site_url + 'v2/img/minus-gray.svg');
    } else {
        $('.k-detail-row').remove();
        $(this)
            .find('img')
            .attr('src', site_url + 'v2/img/plus_gray.svg');
    }
});

$('body').on('click', '.viewUnitDetails', function (e) {
    e.preventDefault();
    let dataArr1 = {
        id: $(this).attr('data-id'),
        course_id: $(this).attr('data-course_id'),
        student_id: $(this).attr('data-student_id'),
        unit_id: $(this).attr('data-unit_id'),
        subject_id: $(this).attr('data-subject_id'),
        batch: $(this).attr('data-batch'),
        student_course_id: selectedStudCourseID,
        is_higher_ed: isHigherEd,
    };
    ajaxActionV2('api/get-assessment-details', 'POST', dataArr1, function (response) {
        let resultUnitDetails = kendo.template($('#resultUnitDetailsTemplate').html())({
            data: response.data.detail[0],
            isHigherEd: isHigherEd,
            assessmentData: response.data.data,
        });
        $(document).find('#resultUnitDetailsData').html(resultUnitDetails);
        kendoWindowOpen('#resultUnitDetailsModal');
        setAccordion();
    });
    // kendoWindowOpen("#resultUnitDetailsModal");
});

$('body').on('click', '.reportToTcsi', function (e) {
    e.preventDefault();

    $('.selectedUnitsDiv').hide();
    $('#selectedUnitsList').html('');

    var enrollId = $(this).attr('data-id');
    var subject_id = $(this).attr('data-subject_id');
    var course_id = $(this).attr('data-course_id');
    var unitCode = $(this).attr('data-unit-code');
    var unitName = $(this).attr('data-unit-name');

    ajaxActionV2(
        'api/get-tcsi-student-unit-enrollment-data',
        'POST',
        { id: enrollId },
        function (response) {
            $('#selectedUnitsList').html(setSingleSelectedUnit(unitCode, unitName));
            if (response.data) {
                tcsiStudentForm(enrollId, subject_id, course_id, response.data);
            } else {
                tcsiStudentForm(enrollId, subject_id, course_id, '');
            }
            kendoWindowOpen('#reportToTCSIModal');
        }
    );
});

$('body').on('click', '.bultkReportToTcsi', function (e) {
    e.preventDefault();
    if (typeof selectedUnits !== 'undefined' && selectedUnits.length != 0) {
        if (selectedUnits) {
            $('.selectedUnitsDiv').show();
            $('#selectedUnitsList').html(setSelectedUnits(selectedUnits));
            tcsiStudentForm('', '', '', '');
            kendoWindowOpen('#reportToTCSIModal');
        }
    } else {
        $('.selectedUnitsDiv').hide();
        notificationDisplay('Pleas Select Unit', '', 'error');
    }
});

$('body').on('click', '.clear_selected_units_for_tcsi', function (e) {
    e.preventDefault();
    let unitId = $(this).attr('data-filter-id');
    $(document)
        .find('.unit_' + unitId)
        .parents('tr')
        .find('input')
        .click();
    $('#selectedUnitsList').html(setSelectedUnits(selectedUnits));
    $('#selectedUnitsListForAvetmiss').html(setSelectedUnits(selectedUnits));
});

$('body').on('click', '.updateAssignBatchBtn', function (e) {
    e.preventDefault();
    var enrollId = $(this).attr('data-id');
    var subject_id = $(this).attr('data-subject_id');
    var course_id = $(this).attr('data-course_id');
    var semester_id = $(this).attr('data-semester_id');
    var is_course_complete = $(this).attr('data-is_course_complete');

    if (is_course_complete == 'true') {
        notificationDisplay(
            'The selected action cannot be performed the course has been completed.',
            '',
            'error'
        );
        return false;
    }
    $('#assignBatchModal').find('#subject_id').val(subject_id);
    ajaxActionV2('api/get-student-subject-outcome', 'POST', { id: enrollId }, function (response) {
        if (response.data) {
            assignBatchForm(enrollId, subject_id, course_id, semester_id, response.data);
        } else {
            assignBatchForm(enrollId, subject_id, course_id, semester_id, '');
        }
        kendoWindowOpen('#assignBatchModal');
        $('.selectedUnitsDiv').hide();
    });
});
$('body').on('click', '.transferToAnotherCourse', function (e) {
    e.preventDefault();
    var enrollId = $(this).attr('data-id');
    var subject_id = $(this).attr('data-subject_id');
    var course_id = $(this).attr('data-course_id');
    var semester_id = $(this).attr('data-semester_id');
    var is_course_complete = $(this).attr('data-is_course_complete');

    if (is_course_complete == 'true') {
        notificationDisplay(
            'The selected action cannot be performed the course has been completed.',
            '',
            'error'
        );
        return false;
    }
    $('#transferToAnotherCourseModal').find('#subject_id').val(subject_id);
    ajaxActionV2('api/get-student-subject-outcome', 'POST', { id: enrollId }, function (response) {
        if (response.data) {
            transferToAnotherCourseForm(
                enrollId,
                subject_id,
                course_id,
                semester_id,
                response.data
            );
        } else {
            transferToAnotherCourseForm(enrollId, subject_id, course_id, semester_id, '');
        }
        kendoWindowOpen('#transferToAnotherCourseModal');
        $('.selectedUnitsDiv').hide();
    });
});

$('body').on('click', '.updateUnitOutCome', function (e) {
    e.preventDefault();
    var enrollId = $(this).attr('data-id');
    var subject_id = $(this).attr('data-subject_id');
    var course_id = $(this).attr('data-course_id');
    var semester_id = $(this).attr('data-semester_id');
    var is_course_complete = $(this).attr('data-is_course_complete');

    if (is_course_complete == 'true') {
        notificationDisplay(
            'The selected action cannot be performed the course has been completed.',
            '',
            'error'
        );
        return false;
    }

    $('#unit_code').text($(this).attr('data-unit_code'));
    setStudentInfoInDiv('#studentInfoUnitOutcome');
    ajaxActionV2('api/get-student-unit-outcome', 'POST', { id: enrollId }, function (response) {
        if (response.data) {
            unitOutComeForm(enrollId, subject_id, course_id, semester_id, response.data);
        } else {
            unitOutComeForm(enrollId, subject_id, course_id, semester_id, '');
        }
        kendoWindowOpen('#unitOutComeModal');
        $('.selectedUnitsDiv').hide();
    });
});

$('body').on('click', '.updateUnitAvetmiss', function (e) {
    e.preventDefault();

    $('.selectedAvetmissUnitsDiv').hide();
    $('#selectedUnitsListForAvetmiss').html('');

    var enrollId = $(this).attr('data-id');
    var subject_id = $(this).attr('data-subject_id');
    var course_id = $(this).attr('data-course_id');
    var semester_id = $(this).attr('data-semester_id');
    var unitCode = $(this).attr('data-unit-code');
    var unitName = $(this).attr('data-unit-name');

    ajaxActionV2('api/get-student-avetmiss', 'POST', { id: enrollId }, function (response) {
        $('#selectedUnitsListForAvetmiss').html(setSingleSelectedUnit(unitCode, unitName));
        if (response.data) {
            avetmissForm(enrollId, subject_id, course_id, semester_id, response.data);
        } else {
            avetmissForm(enrollId, subject_id, course_id, semester_id, '');
        }
        kendoWindowOpen('#unitAvetmissModal');
    });
});

$('body').on('click', '.bulkUpdateUnitAvetmissOutcome', function (e) {
    e.preventDefault();
    if (typeof selectedUnits !== 'undefined' && selectedUnits.length != 0) {
        if (selectedUnits) {
            $('.selectedAvetmissUnitsDiv').show();
            $('#selectedUnitsListForAvetmiss').html(setSelectedUnits(selectedUnits));
            avetmissForm('', '', '', '');
            kendoWindowOpen('#unitAvetmissModal');
        }
    } else {
        $('.selectedAvetmissUnitsDiv').hide();
        notificationDisplay('Pleas Select Unit', '', 'error');
    }
});

$('body').on('click', '.bulkAssignBatchBtn', function (e) {
    e.preventDefault();
    if (typeof selectedUnits !== 'undefined' && selectedUnits.length != 0) {
        if (selectedUnits) {
            setStudentInfoInDiv('#studentInfoBulkUpdateAssignBatch');
            $('#selectedUnitSubjectGrid').kendoGrid({
                dataSource: customDataSource(
                    'api/get-selected-unit-subject-data',
                    {},
                    { unitIds: selectedUnitsIds, is_higher_ed: isHigherEd }
                ),
                pageable: customPageableArr(),
                sortable: true,
                resizable: true,
                columns: [
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_name #</div>",
                        field: 'unit_name',
                        title: 'Unit Name',
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: start_date # - #: finish_date #</div>",
                        field: 'start_date',
                        title: 'ACTIVITY DATE',
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: final_outcome #</div>",
                        field: 'final_outcome',
                        title: 'Final Outcome',
                    },
                    {
                        template:
                            "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: updated_by #</div>",
                        field: 'Updated',
                        title: 'updated',
                    },
                    {
                        template:
                            '<button type="button" class="editSubjectOutcomebtn" data-id="#: id #"data-subject_id="#: subject_id #"data-course_id="#: course_id #"data-semester_id="#: semester_id #"  ><span class="k-icon k-i-edit k-icon-edit"></span></button>',
                        headerTemplate:
                            "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                        field: 'action',
                        title: 'ACTION',
                        filterable: false,
                        sortable: false,
                    },
                ],
                noRecords: noRecordTemplate(),
                dataBound: function (e) {
                    togglePagination('#selectedUnitSubjectGrid');
                },
            });
            kendoWindowOpen('#bulkAssignBatchModal');
        }
    } else {
        notificationDisplay('Pleas Select Unit', '', 'error');
    }
});

$('body').on('click', '.bulkUpdateResultCourse', function (e) {
    e.preventDefault();
    if (typeof selectedUnits !== 'undefined' && selectedUnits.length != 0) {
        if (selectedUnits) {
            const ids = selectedUnits.map((item) => item.id).join(',');
            $('#enrollmentId').val(ids);
            getStudnetCourseForUnitV2(selectedUnits);
        }
    } else {
        notificationDisplay('Pleas Select Unit', '', 'error');
    }
});

$('body').on('click', '.bulkUpdateUnitOutcome', function (e) {
    e.preventDefault();
    if (typeof selectedUnits !== 'undefined' && selectedUnits.length != 0) {
        if (selectedUnits) {
            setStudentInfoInDiv('#studentInfoBulkUpdateUnitOutcome');
            $('#bulkUpdateUnitOutcomeModal')
                .find('#selectedUnitSubjectGrid')
                .kendoGrid({
                    dataSource: customDataSource(
                        'api/get-selected-unit-subject-data',
                        {},
                        { unitIds: selectedUnitsIds, is_higher_ed: isHigherEd }
                    ),
                    pageable: customPageableArr(),
                    sortable: true,
                    resizable: true,
                    columns: [
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_name #</div>",
                            field: 'unit_name',
                            title: 'Unit Name',
                        },
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: start_date_unit # - #: finish_date_unit #</div>",
                            field: 'start_date',
                            title: 'ACTIVITY DATE',
                        },
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: final_outcome #</div>",
                            field: 'final_outcome',
                            title: 'Final Outcome',
                        },
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: updated_by #</div>",
                            field: 'Updated',
                            title: 'updated',
                        },
                        {
                            template:
                                '<button type="button" class="editUnitOutcomeBtn" data-id="#: id #" data-unit_code="#: unit_code #" data-subject_id="#: subject_id #" data-course_id="#: course_id #" data-semester_id="#: semester_id #"  ><span class="k-icon k-i-edit k-icon-edit"></span></button>',
                            headerTemplate:
                                "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                            field: 'action',
                            title: 'ACTION',
                            filterable: false,
                            sortable: false,
                        },
                    ],
                    noRecords: noRecordTemplate(),
                });
            kendoWindowOpen('#bulkUpdateUnitOutcomeModal');
        }
    } else {
        notificationDisplay('Pleas Select Unit', '', 'error');
    }
});

$('body').on('click', '.editSubjectOutcomebtn', function (e) {
    e.preventDefault();
    var enrollId = $(this).attr('data-id');
    var subject_id = $(this).attr('data-subject_id');
    var course_id = $(this).attr('data-course_id');
    var semester_id = $(this).attr('data-semester_id');
    $('#assignBatchModal').find('#subject_id').val(subject_id);

    ajaxActionV2('api/get-student-subject-outcome', 'POST', { id: enrollId }, function (response) {
        if (response.data) {
            assignBatchForm(enrollId, subject_id, course_id, semester_id, response.data);
        } else {
            assignBatchForm(enrollId, subject_id, course_id, semester_id, '');
        }
        kendoWindowOpen('#assignBatchModal');
    });
});

$('body').on('click', '.editUnitOutcomeBtn', function (e) {
    e.preventDefault();
    var enrollId = $(this).attr('data-id');
    var subject_id = $(this).attr('data-subject_id');
    var course_id = $(this).attr('data-course_id');
    var semester_id = $(this).attr('data-semester_id');
    $('#unit_code').text($(this).attr('data-unit_code'));
    setStudentInfoInDiv('#studentInfoUnitOutcome');
    ajaxActionV2('api/get-student-unit-outcome', 'POST', { id: enrollId }, function (response) {
        if (response.data) {
            unitOutComeForm(enrollId, subject_id, course_id, semester_id, response.data);
        } else {
            unitOutComeForm(enrollId, subject_id, course_id, semester_id, '');
        }
        kendoWindowOpen('#unitOutComeModal');
    });
});

$('body').on('click', '.deleteSemester', function (e) {
    e.preventDefault();
    var tr = $(e.target).closest('tr').find('td:first-child').next().next().find('div'); //get the row for deletion
    let primaryID = tr.attr('data-id');
    $('#deleteSemesterUnitModal').data('kendoDialog').open();
    $('#deleteSemesterUnitModal').find('#deleteSemesterId').val(primaryID);
    let gridId = $(this).attr('data-grid_id');
    $('#deleteSemesterUnitModal').find('#deleteSemesterId').attr('data-grid_id', gridId);
});

$('body').on('change', '#term', function (e) {
    e.preventDefault();
    let postArr = {
        subject_id: $('#assignBatchModal').find('#subject_id').val(),
        semester_id: $('#semester_id').data('kendoDropDownList').value(),
    };
    setDropdownListIdName('batch', 'get-course-batch', postArr);
});

$('body').on('click', '.generateCertificateResultTabBtn', function (e) {
    e.preventDefault();
    $('.embed-content').hide();
    $('.enroll-certificate-preview-placeholder').show();
    $('#subject_enrollment_id').val('');
    $('.studentEnrollDocumentsPreview').attr('src', '');
    ajaxActionV2('api/get-course-tab-data', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;
        let responseArr = {
            data: responseData.courseSummary.currentCourseSummary,
            getResultCalculationMethod: responseData.courseSummary.getResultCalculationMethod,
            studentDetails: responseData.courseSummary.studentDetails,
        };

        $(document)
            .find('.currentCourseSummary')
            .html(kendo.template($('#currentCourseSummaryTemplate').html())(responseArr));
        $(document)
            .find('.studentCourseDetailHeader')
            .html(kendo.template($('#studentCourseDetailHeaderTemplate').html())(responseArr));

        $('#generate_certificate_list').kendoDropDownList({
            filter: 'contains',
            optionLabel: 'Select',
            filterInput: {
                width: '100%',
            },
            dataTextField: 'Name',
            dataValueField: 'Id',
            dataType: 'json',
            // dataSource: getDropdownDataSource('get-constant-data', {'action': 'generateCertificateList'}),
            dataSource: {
                schema: { data: 'data' },
                data: responseData.generateCertificateList,
                // transport: getTransportReadOnly(apiUrl, postArr)
            },
            select: function (e) {
                if (e.dataItem.Id == 'C') {
                    $('.certificateDiv').removeClass('hidden');
                    $('.transcriptDiv').addClass('hidden');
                } else if (e.dataItem.Id == 'TOCA') {
                    $('.certificateDiv').addClass('hidden');
                    $('.transcriptDiv').removeClass('hidden');
                } else {
                    $('.certificateDiv').addClass('hidden');
                    $('.transcriptDiv').addClass('hidden');
                }
                generatePdf(e.dataItem.Id);
            },
            // value: value,
        });

        if (!$('#convert_course_status').data('kendoSwitch')) {
            $('#convert_course_status').kendoSwitch({
                size: 'medium',
                change: function (e) {
                    switchValueManage('#convert_course_status', e.checked);
                },
            });
        }
        if (!$('#include_failed_subject').data('kendoSwitch')) {
            $('#include_failed_subject').kendoSwitch({
                size: 'medium',
                change: function (e) {
                    switchValueManage('#include_failed_subject', e.checked);
                    generatePdf($('#generate_certificate_list').data('kendoDropDownList').value());
                },
            });
        }
        setTimeout(() => {
            $('.getGridOfCurrentCourseSummary .accordion:first').trigger('click');
            $('#completion_date').kendoDatePicker({
                format: dateFormatFrontSideJS,
                value: new Date(responseData.courseSummary.studentDetails[0].finish_date),
            });

            $('#generate_certificate_list').data('kendoDropDownList').value('');
            $('#generate_certificate_list').data('kendoDropDownList').enable(true);
        }, 500);
    });
    // setKendoDatePicker("#completion_date");
    setKendoDatePicker('#issued_date');

    kendoWindowOpen('#generateCertificateModal');
});

$('body').on('click', '.generateSOAForUnit', function (e) {
    e.preventDefault();
    let subjectID = $(this).data('id');

    ajaxActionV2('api/get-certificate-type', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;

        $('#subject_enrollment_id').val(subjectID);

        $('#generate_certificate_list').kendoDropDownList({
            filter: 'contains',
            optionLabel: 'Select',
            filterInput: {
                width: '100%',
            },
            dataTextField: 'Name',
            dataValueField: 'Id',
            dataType: 'json',
            dataSource: {
                schema: { data: 'data' },
                data: responseData.generateCertificateList, // Ensure data is accessed correctly
            },
            dataBound: function (e) {
                setTimeout(() => {
                    this.select(2);
                    this.trigger('change');
                    this.trigger('select');
                }, 2000);
            },
            select: function (e) {
                if (e.dataItem) {
                    if (e.dataItem.Id == 'C') {
                        $('.certificateDiv').removeClass('hidden');
                        $('.transcriptDiv').addClass('hidden');
                    } else if (e.dataItem.Id == 'TOCA') {
                        $('.certificateDiv').addClass('hidden');
                        $('.transcriptDiv').removeClass('hidden');
                    } else {
                        $('.certificateDiv').addClass('hidden');
                        $('.transcriptDiv').addClass('hidden');
                    }
                    generatePdf(e.dataItem.Id);
                }
                generatePdf('SOA');
            },
        });

        $('#generate_certificate_list').data('kendoDropDownList').enable(false);

        if (!$('#convert_course_status').data('kendoSwitch')) {
            $('#convert_course_status').kendoSwitch({
                size: 'medium',
                change: function (e) {
                    switchValueManage('#convert_course_status', e.checked);
                },
            });
        }
        if (!$('#include_failed_subject').data('kendoSwitch')) {
            $('#include_failed_subject').kendoSwitch({
                size: 'medium',
                change: function (e) {
                    switchValueManage('#include_failed_subject', e.checked);
                    generatePdf($('#generate_certificate_list').data('kendoDropDownList').value());
                },
            });
        }
        setTimeout(() => {
            $('.getGridOfCurrentCourseSummary .accordion:first').trigger('click');
            $('#completion_date').kendoDatePicker({
                format: dateFormatFrontSideJS,
                value: new Date(),
            });
        }, 500);
    });
    setKendoDatePicker('#issued_date');
    kendoWindowOpen('#generateCertificateModal');
});

$('body').on('click', '.updateResultCourse', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');

    $('#enrollmentId').val(primaryID);
    getStudnetCourseForUnit();
});

$('body').on('click', '.updateHigherEdUnitOutCome', function (e) {
    e.preventDefault();
    console.log('ss');
    let primaryID = $(this).attr('data-id');
    let mark_outcome = $(this).attr('data-mark_outcome');

    $('#finalOutcomeId').val(primaryID);
    ajaxActionV2('api/get-enroll-venue', 'POST', selectedDataArr, function (response) {
        $('#updateHigherEdUnitOutComeForm')
            .html('')
            .off('submit')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                type: 'group',
                layout: 'grid',
                grid: { cols: 12, gutter: 16 },
                items: [
                    {
                        field: 'finalOutcome',
                        label: 'Final Outcome',
                        colSpan: 12,
                        editor: 'DropDownList',
                        editorOptions: {
                            dataValueField: 'Id',
                            dataTextField: 'Name',
                            dataSource: response.data.markOutcomeData,
                        },
                    },
                ],
                buttonsTemplate:
                    '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end px-6 py-2">\n' +
                    '<div class="float-right flex space-x-4 items-center justify-end">\n' +
                    '<button type="button" data-modal-name="updateHigherEdUnitOutComeModal" class="closeModal flex items-center justify-center w-24 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300 hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">\n' +
                    '<p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>\n' +
                    '</button>\n' +
                    '<button type="button" class="submitHigherEdUnitOutComeForm flex items-center justify-center h-8 px-3 py-2 bg-primary-blue-500 shadow rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
                    '<p class="text-sm font-medium leading-4 text-white">Update</p></button>\n' +
                    '</div>\n' +
                    '</div>',
            });
        $('#updateHigherEdUnitOutComeForm')
            .data('kendoForm')
            .setOptions({
                formData: {
                    finalOutcome: mark_outcome,
                },
            });
        kendoWindowOpen('#updateHigherEdUnitOutComeModal');
    });
});

$('body').on('click', '.submitHigherEdUnitOutComeForm', function (e) {
    let dataArr = getSerializeFormArray('#updateHigherEdUnitOutComeModal', selectedDataArr);
    if (Array.isArray(dataArr.id)) {
        dataArr.id = parseInt(dataArr.id[dataArr.id.length - 1], 10);
    }

    // Check if 'finalOutcome' is an array and update it to the last value
    if (Array.isArray(dataArr.finalOutcome)) {
        dataArr.finalOutcome = parseInt(dataArr.finalOutcome[dataArr.finalOutcome.length - 1], 10);
    }
    console.log(dataArr);
    ajaxActionV2('api/save-highered-unit-outCome-form', 'POST', dataArr, function (response) {
        refreshGrid('#resultTabUnitList', selectedDataArr);
        $(document).find('#updateHigherEdUnitOutComeModal').getKendoWindow().close();
    });
});

$('body').on('click', '.submitresultUpdate', function (e) {
    let tempDataArr = {
        college_id: collegeId,
        student_id: studentId,
        student_course_id: selectedStudCourseID,
    };

    let dataArr = getSerializeFormArray('#updateResultCourseForm', tempDataArr);

    let primaryID = $('#enrollmentId').val();
    dataArr.primaryID = primaryID;
    ajaxActionV2('api/update-result-course', 'POST', dataArr, function (response) {
        notificationDisplay(response.message, '', response.status);
        if (response.status == 'error') {
            return false;
        }

        refreshGrid('#resultTabUnitList', selectedDataArr);
        $(document).find('#updateResultCourseModal').getKendoWindow().close();
    });
});
$('body').on('click', '.submitBulkResultUpdate', function (e) {
    let tempDataArr = {
        college_id: collegeId,
        student_id: studentId,
        student_course_id: selectedStudCourseID,
    };

    let dataArr = getSerializeFormArray('#updateBulkResultCourseForm', tempDataArr);

    let primaryID = $('#enrollmentId').val();
    dataArr.primaryID = primaryID;
    ajaxActionV2('api/update-result-course', 'POST', dataArr, function (response) {
        notificationDisplay(response.message, '', response.status);
        if (response.status == 'error') {
            return false;
        }

        refreshGrid('#resultTabUnitList', selectedDataArr);
        $(document).find('#bulkTransferToAnotherCourseModal').getKendoWindow().close();
    });
});

$('body').on('click', '.closeModal', function (e) {
    let modalName = $(this).attr('data-modal-name');
    $(document)
        .find('#' + modalName)
        .getKendoWindow()
        .close();
});

function assessmentSyncWithMoodleHandler(e, $gridElement) {
    e.preventDefault();
    let grid = $gridElement.data('kendoGrid');
    let tr = $(e.target).closest('tr');
    let dataItem = grid.dataItem(tr);

    let $modal = $(assessmentSyncToMoodleFromResultModalId);
    $modal.data('kendoDialog').open();
    $modal.find('#assignAssessmentTaskId').val(dataItem.id);
}

function assessmentSyncWithMoodleFromResultTab(assignAssessmentTaskId) {
    let tempSelectedDataArr = selectedDataArr;
    tempSelectedDataArr.assign_assessment_task_id = assignAssessmentTaskId;

    ajaxActionV2(
        'api/assessment-sync-with-moodle',
        'POST',
        tempSelectedDataArr,
        function (response) {
            notificationDisplay(response.message, '', response.status);
            /*if (response.data.moodleConnect == 1) {
            updateSyncDataTabView(response, "moodle", "course");
        }*/
        },
        true
    );
}

function toggleMoodleColumnsAndIconGrid(detailGrid) {
    detailGrid.dataSource.bind('requestEnd', function (e) {
        if (e.response) {
            const isMoodleConnected = e.response.data.isMoodleConnect;
            window.detailIsMoodleConnected = isMoodleConnected;

            ['moodle_status', 'moodle_synced_at'].forEach((column) => {
                isMoodleConnected ? detailGrid.showColumn(column) : detailGrid.hideColumn(column);
            });
        }
    });

    detailGrid.bind('dataBound', function () {
        this.tbody.find('.assessmentSyncToMoodleBtn').toggle(window.detailIsMoodleConnected);

        this.tbody
            .find('.assessmentSyncToMoodleBtn')
            .off('click')
            .on('click', function (e) {
                let $gridElement = $(e.target).closest("[data-role='grid']");
                assessmentSyncWithMoodleHandler(e, $gridElement);
            });
    });
}

function unitGradeSyncFromMoodleForResultTab(unitId) {
    let tempSelectedDataArr = selectedDataArr;
    tempSelectedDataArr.unit_id = unitId;

    ajaxActionV2(
        'api/unit-grade-sync-from-moodle',
        'POST',
        tempSelectedDataArr,
        function (response) {
            notificationDisplay(response.message, '', response.status);
        },
        true
    );
}

function unitGradeSyncFromMoodleHandler(e, gridId) {
    e.preventDefault();
    let grid = $(gridId).data('kendoGrid');
    let tr = $(e.target).closest('tr');
    let dataItem = grid.dataItem(tr);
    let unitId = dataItem.unit_id;
    let unitCode = dataItem.unit_code;

    let $unitModal = $(unitGradeSyncFromMoodleModalId);
    let newContent = `Are you sure you want to sync all assessment grades for <b>${unitCode}</b> from Moodle?
                      <input type='hidden' name='id' id='unitId' value='${unitId}' />`;
    $unitModal.data('kendoDialog').content(newContent);
    $unitModal.data('kendoDialog').open();
    //$unitModal.find("#unitId").val(unitId);
}
