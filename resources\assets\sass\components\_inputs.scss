.form-check [type='radio'] {
    display: inline-block;
    vertical-align: middle;
    flex-shrink: 0;
    &:checked {
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='8' cy='8' r='3'/%3E%3C/svg%3E");
        background-position: 50%;
        background-repeat: no-repeat;
        background-size: 1rem 1rem;
        border-color: transparent;
        background-origin: border-box;
    }
}

.tw-form {
    .k-checkbox + .k-label,
    .k-checkbox-wrap + .k-label,
    .k-checkbox + .k-checkbox-label,
    .k-checkbox-wrap + .k-checkbox-label {
        margin-inline-start: 0.75rem;
    }
}

textarea:focus-visible {
    outline: none;
}

.tagify__input {
    padding: 0;
    margin: 0;
}

.tw-daterange-popup {
    margin-top: 0.25rem;
    .k-calendar .k-calendar-view {
        width: fit-content;
    }
    .k-calendar.k-calendar-range {
        box-shadow: none;
    }

    &.k-popup .k-calendar {
        height: fit-content;
    }

    &__custom {
        .k-calendar .k-calendar-view {
            width: fit-content;
            flex-direction: column;
            max-height: 260px;
            overflow-y: auto;
            justify-content: flex-start;
        }
    }
    .k-calendar.k-calendar-range .k-range-start {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
    }

    .k-calendar.k-calendar-range .k-range-end {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
    }

    .k-calendar .k-range-start,
    .k-calendar .k-range-end,
    .k-calendar .k-range-mid {
        background-color: var(--color-primary-blue-500);
        .k-link {
            color: white;
        }

        &.k-calendar-td:hover .k-calendar-cell-inner,
        &.k-calendar-td:hover .k-link,
        &.k-calendar-td.k-hover .k-calendar-cell-inner,
        &.k-calendar-td.k-hover .k-link {
            background-color: var(--color-primary-blue-400);
            border: none;
        }
        &.k-calendar-td:focus .k-calendar-cell-inner,
        &.k-calendar-td:focus .k-link,
        &.k-calendar-td.k-focus .k-calendar-cell-inner,
        &.k-calendar-td.k-focus .k-link {
            box-shadow: none;
        }
    }

    & > &__daily,
    & > &__yearly,
    & > &__monthly {
        .k-calendar.k-calendar-range .k-range-start,
        .k-calendar.k-calendar-range .k-range-end {
            border-radius: 0.5rem;
        }
    }

    .k-calendar .k-range-start,
    .k-calendar .k-range-end,
    .k-calendar .k-range-mid {
        background-color: var(--color-primary-blue-500);
        .k-link {
            color: white;
        }

        &.k-calendar-td:hover .k-calendar-cell-inner,
        &.k-calendar-td:hover .k-link,
        &.k-calendar-td.k-hover .k-calendar-cell-inner,
        &.k-calendar-td.k-hover .k-link {
            background-color: var(--color-primary-blue-400);
            border: none;
        }
        &.k-calendar-td:focus .k-calendar-cell-inner,
        &.k-calendar-td:focus .k-link,
        &.k-calendar-td.k-focus .k-calendar-cell-inner,
        &.k-calendar-td.k-focus .k-link {
            box-shadow: none;
        }
    }

    & > &__monthly,
    & > &__yearly,
    & > &__3month,
    & > &__12month {
        .k-calendar-tbody {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        .k-month-calendar .k-calendar-td,
        .k-calendar-monthview .k-calendar-td,
        .k-year-calendar .k-calendar-td,
        .k-calendar-yearview .k-calendar-td,
        .k-century-calendar .k-calendar-td,
        .k-calendar-centuryview .k-calendar-td,
        .k-decade-calendar .k-calendar-td,
        .k-calendar-decadeview .k-calendar-td {
            --INTERNAL--kendo-calendar-cell-size: 68px;
            width: 68px;
            height: 40px;
        }
        .k-calendar-thead {
            display: none;
        }

        .k-calendar-header.k-hstack {
            justify-content: center;
            position: relative;
            margin-bottom: 1rem;
            .k-calendar-title {
                z-index: 1;
            }
            .k-spacer {
                display: none;
            }
            .k-calendar-nav {
                position: absolute;
                width: 100%;
                justify-content: space-between;
                .k-calendar-nav-today {
                    display: none;
                }
            }
        }
    }

    .k-month-calendar .k-calendar-td,
    .k-calendar-monthview .k-calendar-td {
        --INTERNAL--kendo-calendar-cell-size: 40px;
        width: 40px;
        height: 40px;
    }
}

/*Student Search Autocomplate*/

.autocomplateremove-margin {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}
/* globalsearch  */
.searchtext {
    /* Heading */
    width: 122px;
    height: 16px;
    /* text-xs / leading-4 / font-medium / uppercase / tracking-wider */
    /* font-family: 'Rubik'; */
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    /* identical to box height, or 133% */
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-feature-settings:
        'kern' off,
        'calt' off;
    /* gray/500 */
    color: #6b7280;
}

.required-field {
    color: red;
}

/* .images_2_upload_button{
    display: none
} */
.file_btn_border {
    width: 84%;
    border: 1px solid #dfe3e9 !important;
    /*border-color: #dfe3e9 !important;*/
}
.upload_file_btn {
    margin-left: -99px !important;
}
.number-only {
    padding: 7px;
    width: 100%;
    border-width: 1px;
    border-radius: 0.5rem;
    height: 2.25rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.errors {
    border: 1px solid #fca5a5 !important;
}

.header .k-autocomplete.findStudent {
    width: 385px;
    padding-left: 35px;
    min-height: 40px;
    display: flex;
    align-self: center;
}

.text-sm {
    font-size: 0.875rem;
}

.k-item.k-state-hover .k-state-default .text-sm {
    color: #fff !important;
}

.k-autocomplete > .k-clear-value {
    top: 46%;
}

.k-autocomplete .k-i-loading {
    top: 45%;
    transform: translateY(-50%);
}

.k-autocomplete.k-input-md .k-i-loading {
    top: unset;
    transform: unset;
    right: 0.25rem;
}

/* search auto complete */

.k-autocomplete {
    /* width: 385px; */
    /* padding-left: 35px; */
    min-height: 2.5rem;
    display: flex;
    align-self: center;
}
.k-autocomplete.findStudent {
    width: 385px;
    padding-left: 35px;
    min-height: 40px;
    display: flex;
    align-self: center;
}

.k-autocomplete .k-input {
    background-color: transparent !important;
}

.k-autocomplete .k-input.k-focus,
.k-autocomplete .k-input:focus {
    border-color: transparent;
    box-shadow: none;
}

/* DatePicker */

.k-picker-wrap .k-input[type='text'] {
    border-radius: 0.5rem;
    height: 35.28px !important;
    padding-inline: 0.75rem;
}

.k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

.k-label.k-form-label {
    color: #374151;
    font-weight: 500;
}

/* On click of grid list select box selected */
[type='checkbox']:checked:hover,
[type='checkbox']:checked:focus,
[type='radio']:checked:hover,
[type='radio']:checked:focus {
    border-color: var(--color-primary-blue-500);
    background-color: var(--color-primary-blue-500);
}

[type='text']:focus,
[type='email']:focus,
[type='password']:focus {
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
    border-color: var(--color-primary-blue-500) !important;
}

.k-input-inner {
    &[type='text']:focus,
    &[type='email']:focus,
    &[type='password']:focus {
        box-shadow: none;
        border-color: none;
    }
}

.tw-input-text ~ [data-lastpass-icon-root] {
    display: none !important;
}

.tw-datetime-picker {
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(209, 213, 219, 1);
    padding: 5px 13px;

    .k-dateinput {
        order: 2;
        font-size: 14px;
        color: rgba(107, 114, 128, 1);

        &:focus-within {
            box-shadow: none;
            border-color: none;
        }
    }

    .k-button {
        border: none;
        color: rgba(156, 163, 175, 1);
        width: unset;
        padding: 0px;

        .k-icon {
            font-size: 20px;
        }
    }
}

.tw-input-borderless {
    &.k-input.k-input-solid {
        border-width: 0;
    }
}

.tw-disable-password {
    .k-input.k-textbox {
        background-color: var(--color-gray-50);
        border-block: 1px solid var(--color-gray-300);
    }
    input {
        pointer-events: none;
    }

    .k-input-md .k-input-inner {
        padding-inline: 0 !important;
    }

    .k-form-buttons {
        display: none;
    }
}

.tw-form-invalid .k-textbox.k-input.k-input-solid.k-rounded-md {
    border-color: var(--color-red-300);
    color: var(--color-red-900);
    &:focus {
        border-color: var(--color-primary-blue-500);
        color: var(--color-gray-500);
    }
}

input::-ms-reveal,
input::-ms-clear {
    display: none;
}

.tw-phone-mask-input {
    display: flex;
    .k-dropdownlist.k-rounded-md {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        height: 2.375rem;
        width: 100px;
    }
    .k-input.k-rounded-md {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        height: 2.375rem;
    }
}

.tw-preset-picker {
    .k-list-content {
        max-height: calc(100vh - 300px) !important;
    }

    .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected {
        border-left: 3px solid var(--color-primary-blue-500);
    }

    .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected,
    .k-list.k-list-md .k-list-content .k-list-ul .k-selected.k-list-optionlabel {
        background-color: var(--color-primary-blue-50);
        border-radius: 0;

        &::after {
            display: none;
        }
    }

    &--user {
        .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected {
            border-left-width: 0;
        }
    }
}

.k-checkbox {
    &.tw-checkbox {
        &__default-cross {
            &::before {
                content: '\e11b';
                color: var(--color-gray-300);
                transform: scale(1) translate(-50%, -50%);
            }
            &:checked::before {
                content: '\e118';
                color: white;
            }
        }
    }
}

.tw-font-size {
    .k-numerictextbox:focus-within.k-input {
        box-shadow: none;
        border-color: var(--color-gray-300);
        border-radius: 0;
        border-block-width: 0;
    }
    .k-input.k-rounded-md {
        border-radius: 0;
    }

    .k-input-spinner {
        display: none;
    }
}

#filetringIntervention .select2-container {
    width: 100% !important;
}

.kendo-select-wrapper {
    .k-dropdown-wrap {
        background-color: white;
    }
}
