html {
    font-size: 16px !important;
}
/* .text-sm {
	font-size: 14px !important;
} */
table.table-custom thead tr th {
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    /* color: #7f8fa4; */
    color: #7f8fa4;
    font-weight: 500;
    padding: 12px 10px !important;
    white-space: nowrap;
}
.btn {
    border-radius: 25px;
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 0px solid transparent !important;
    padding: 9px 25px !important;
    font-size: 16px;
    /* font-weight: 700; */
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
    /* font-family: Muli; */
    font-weight: 500;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
}
.form-control {
    font-size: 16px !important;
    line-height: 24px !important;
    border-radius: 0;
    box-shadow: none;
    font-weight: 400 !important;
    border-color: #dfe3e9 !important;
    color: #354052 !important;
    height: 41px !important;
    border-radius: 4px !important;
    margin-bottom: 25px !important;
}
.selectric .label {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin: 0 0px 0 00px;
    height: 41px;
    font-size: 16px !important;
    text-align: left;
    line-height: 26px !important;
    box-shadow: none;
    font-weight: 400 !important;
    color: #354052 !important;
    padding: 6px 12px;
    padding-right: 35px;
}
.selectric-items li {
    display: block;
    padding: 10px 15px;
    font-weight: 400;
    color: #354052;
    cursor: pointer;
    border-bottom: 1px solid #dfe3e9 !important;
}
.dataTables_wrapper .paging_simple_numbers .paginate_button {
    background: #fff !important;
    color: rgba(53, 64, 82, 0.5) !important;
    font-size: 14px !important;
    line-height: 20px !important;
    text-align: center !important;
    font-weight: 400 !important;
    border: 0px;
    margin: 0;
    width: 36px;
}
b,
strong {
    font-weight: 500;
}
.sidebar-menu > li > a {
    /* padding: 0.5rem; */
    display: flex;
    color: rgb(208, 226, 255);
}
.column-navbar.navbar-custom-menu.data-setting {
    position: absolute;
    right: 12px;
    top: 0px;
    z-index: 9;
}
.main-header {
    position: relative;
    max-height: 100px;
    z-index: 0;
}
.sidebar-menu a:hover,
.sidebar-menu a:active,
.sidebar-menu a:focus {
    outline: none;
    text-decoration: none;
    color: #fff;
}
.sidebar-menu > li {
    position: relative;
    margin: 0;
    padding: 0;
}
.sidebar-menu .treeview-menu > li > a {
    padding: 8px 5px 8px 33px;
    display: block;
    font-size: 14px;
    position: relative;
}
.sidebar-menu .treeview-menu > li > a > .fa {
    position: absolute;
    left: 20px;
    top: 11px;
}
.sidebar-menu .treeview-menu > li > .second-level > li > a {
    padding: 8px 5px 8px 34px !important;
}
.sidebar-menu .treeview-menu > li.active > a {
    color: #fff !important;
}
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: -85px;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 120px;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    /* border: 1px solid #ccc; */
    /* border: 1px solid rgba(0, 0, 0, 0.15); */
    border-radius: 6px;
}
.hidemenudropdown {
    display: none;
}
.open > .hidemenudropdown {
    display: block;
}
.hideprofiledropdown,
.isShowNotification {
    display: none;
}
.open > .hideprofiledropdown {
    display: block;
}
.open > .isShowNotification {
    display: block;
}
.nav-tabs-custom > .nav-tabs {
    z-index: 20;
}
.dropdown li a {
    font-weight: 400 !important;
    font-family: Muli;
    font-size: 14px;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.36;
    letter-spacing: normal;
    text-align: left;
    color: #354052;
}
.sidebar-menu .treeview-menu.menu-open {
    display: block;
}
/* Works on Firefox */
nav.leftsidebarmenu {
    overflow-y: scroll;
    /* scrollbar-color: rebeccapurple green; */
    /* scrollbar-width: thin; */
    scrollbar-width: thin;
    scrollbar-color: #374151 #1f2937;
}

/* Works on Chrome, Edge, and Safari */
nav.leftsidebarmenu::-webkit-scrollbar {
    width: 12px;
}

nav.leftsidebarmenu::-webkit-scrollbar-track {
    background: #1f2937;
}

nav.leftsidebarmenu::-webkit-scrollbar-thumb {
    background-color: #374151;
    border-radius: 20px;
    border: 3px solid #1f2937;
}
#customize p {
    margin-bottom: 0;
}
.toogleWidth {
    transition: width 0.2s;
}
.d-none {
    width: 50px;
}
/* ::-webkit-scrollbar {
    width: 0px;
} */
.d-none nav {
    width: 64rem;
}

.k-menu-scroll-wrapper {
    background-color: #1f2937 !important;
}

.sidebar-menu > li > a {
    /* padding: 0.5rem !important; */
    display: flex !important;
    color: rgb(208, 226, 255) !important;
}

.treeview-menu.second-level {
    background-color: #1f2937 !important;
}

.k-menu-group .k-item > .k-link:hover {
    background-color: transparent !important;
    color: rgb(208, 226, 255) !important;
}
.treeview-menu.second-level .menuli {
    margin-top: 5px !important;
}

.treeview-menu.second-level .menuli a i {
    margin-right: 5px !important;
}

.sidebar-menu .k-menu-scroll-button.k-scroll-up,
.sidebar-menu .k-menu-scroll-button.k-scroll-up {
    display: none !important;
}

.pull-right-container {
    position: absolute !important;
    right: 10px !important;
}

.sidebar-menu li > a > .pull-right-container {
    margin-top: 0px !important;
}
