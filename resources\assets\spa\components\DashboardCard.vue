<template>
    <div :class="cardClass" @click="handleClick">
        <div :class="titleClass">{{ cardTitle }}</div>
        <div class="flex flex-col flex-wrap items-start justify-start gap-1">
            <div :class="valueClass">{{ display }}</div>
            <div v-if="compare" class="flex items-center justify-start gap-2">
                <div class="flex space-x-1">
                    <icon
                        :name="getIcon"
                        :width="10"
                        :height="12"
                        :fill="getIconFillColor"
                        v-if="getIcon"
                    />
                    <div :class="additionalInfoClass">
                        {{ `${compare} ${comparedLabel}` }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { twMerge } from 'tailwind-merge';
export default {
    props: {
        cardTitle: {
            type: String,
            required: true,
        },
        display: {
            type: String,
            required: true,
        },
        compare: {
            type: [String, Boolean],
            default: null,
        },
        comparedLabel: {
            type: String,
            default: '',
        },
        status: {
            type: String, //increment/decrement
            default: 'neutral',
        },
        impression: {
            type: String, //positive/negative
            default: 'positive',
        },
        category: {
            type: String, //positive/negative
            default: '',
        },
        pt: {
            type: Object,
            default: {},
        },
    },
    computed: {
        cardClass() {
            return twMerge(
                'min-h-[7.5rem] w-full space-y-3 rounded-md bg-white p-4 border border-gray-200 grow shrink basis-0',
                this.pt.root
            );
        },
        titleClass() {
            return twMerge(
                'text-gray-700 text-sm font-normal leading-tight tracking-tight',
                this.pt.title
            );
        },
        valueClass() {
            return twMerge('text-gray-800 text-3xl font-normal leading-9', this.pt.content);
        },
        additionalInfoClass() {
            const commonClasses = 'text-xs font-normal leading-tight tracking-wide';
            if (this.impression == 'positive') {
                return `${commonClasses} text-emerald-500`;
            } else if (this.impression == 'negative') {
                return `${commonClasses} text-red-500`;
            } else {
                return `${commonClasses} text-gray-500`;
            }
        },
        getIcon() {
            if (this.status == 'neutral') return false;
            return this.status == 'increment' ? 'uparrow' : 'downarrow';
        },
        getIconFillColor() {
            return this.impression == 'positive' ? '#10B981' : '#EF4444';
        },
    },
    methods: {
        handleClick() {
            this.$emit('clicked');
        },
    },
};
</script>
