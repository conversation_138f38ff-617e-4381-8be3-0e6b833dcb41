<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\v2\Agent;
use App\Model\v2\BankInfo;
use App\Model\v2\ContractCode;
use App\Model\v2\ContractFundingSource;
use App\Model\v2\CourseSite;
use App\Model\v2\CoursesUpfrontFee;
use App\Model\v2\CourseTemplate;
use App\Model\v2\ElearningLink;
use App\Model\v2\ElicosDiscount;
use App\Model\v2\EmailTemplateDocuments;
use App\Model\v2\FailedEmails;
use App\Model\v2\Holiday;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\PaymentMode;
use App\Model\v2\PromotionPrice;
use App\Model\v2\ReportCategory;
use App\Model\v2\ReportLetterFileAttachment;
use App\Model\v2\Student;
use App\Model\v2\StudentAgentCommission;
use App\Model\v2\StudentAttendance;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPayment;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentInitialPaymentTransaction;
use App\Model\v2\StudentOfferDocuments;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\Timetable;
use App\Traits\CommonTrait;
use Auth;
use Config;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class OnboardSettingRepository extends CommonRepository
{
    use CommonTrait;

    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function getContractCodeData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'id',
            'contract_code',
            'description',
        ];

        $columns = [
            'contract_code' => 'contract_code',
            'description' => 'description',
        ];

        $query = ContractCode::from('rto_contract_code')->where('college_id', '=', $college_id)->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getContractFundingSourceData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rcfs.id',
            'code.contract_code as contract_code',
            'rcfs.state',
            'rcfs.funding_source',
            'rcfs.schedual_code',
            'rcfs.description',
        ];

        $columns = [
            'contract_code' => 'code.contract_code',
            'state' => 'rcfs.state',
            'funding_source' => 'rcfs.funding_source',
            'schedual_code' => 'rcfs.schedual_code',
            'description' => 'rcfs.description',
        ];

        $query = ContractFundingSource::from('rto_contract_funding_source as rcfs')
            ->join('rto_contract_code as code', 'code.id', '=', 'rcfs.contract_code_id')
            ->where('rcfs.college_id', '=', $college_id)
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        if (! $countOnly) {
            foreach ($result as $k => $row) {
                $statesName = Config::get('constants.arrContractState');
                $fundingSource = Config::get('constants.arrFundingSource');
                $result[$k]['state'] = $statesName[$row['state']];
                $result[$k]['funding_source'] = $fundingSource[$row['funding_source']];
            }
        }

        return $result;
    }

    public function getCourseSiteData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'id',
            'course_site_id',
            'is_active',
            'course_site_name',
        ];

        $columns = [
            'course_site_id' => 'course_site_id',
            'course_site_name' => 'course_site_name',
        ];

        $query = CourseSite::from('rto_course_site')->where('college_id', '=', $college_id)->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getElearningLinkData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rel.id',
            'ru.name as created_by',
            'rel.title',
            'rel.link',
            'rel.created_at as created_date',
        ];

        $columns = [
            'id' => 'rel.id',
            'created_by' => 'ru.name as created_by',
            'title' => 'rel.title',
            'link' => 'rel.link',
            'created_at' => 'rel.created_at as created_date',
        ];

        $query = ElearningLink::from('rto_elearning_link as rel')
            ->leftjoin('rto_users as ru', 'rel.created_by', '=', 'ru.id')
            ->where('rel.college_id', '=', $college_id)
        // ->orderBy('rel.id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function failedEmailData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'id',
            'sender',
            'receiver',
            'subject',
            'updated_at',
        ];

        $columns = [
            'sender' => 'sender',
            'receiver' => 'receiver',
            'subject' => 'subject',
            'updated_at' => 'updated_at',
        ];

        $query = FailedEmails::select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        if (! isset($post['sort']) && ! isset($post['sort'][0]['field'])) {
            $post['sort'][0]['field'] = 'updated_at';
            $post['sort'][0]['dir'] = 'desc';
        }
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function manageReportsData($request, $countOnly = false)
    {
        // $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rr.id',
            'rr.report_name',
            'rrc.category',
            'rrsc.sub_category_name',
            DB::raw('GROUP_CONCAT(rra.role_id SEPARATOR ", ") AS roles_id'),
            DB::raw('GROUP_CONCAT(role.role_name SEPARATOR ", ") AS access_roles'),
        ];

        $columns = [
            'report_name' => 'rr.report_name',
            'category' => 'rrc.category',
            'sub_category_name' => 'rrsc.sub_category_name',
            // 'access_roles' => DB::raw('GROUP_CONCAT(role.role_name SEPARATOR ", ") AS access_roles'),
        ];

        $query = ReportCategory::from('rto_report_category as rrc')
            ->join('rto_reports as rr', 'rr.report_category', '=', 'rrc.id')
            ->leftjoin('rto_reports_sub_category as rrsc', 'rrsc.id', '=', 'rr.sub_category_id')
            ->leftjoin('rto_report_access as rra', 'rra.report_id', '=', 'rr.id')
            ->leftjoin('rto_roles as role', 'role.id', '=', 'rra.role_id')
            ->groupBy('rr.id')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        // $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function bankInfoData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rbi.id',
            'rbi.campus_id',
            'rto_campus.name as campus',
            'rbi.branch_name',
            'rbi.bank_name',
            'rbi.acnt_name',
            'rbi.acnt_no',
            'rbi.bsb',
            'rbi.swift_code',
        ];

        $columns = [
            'campus' => 'rto_campus.name as campus',
            'branch_name' => 'branch_name',
            'bank_name' => 'bank_name',
            'acnt_name' => 'acnt_name',
            'acnt_no' => 'acnt_no',
            'bsb' => 'bsb',
            'swift_code' => 'swift_code',
        ];

        $query = BankInfo::from('rto_bank_info as rbi')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rbi.campus_id')
            ->where('rbi.college_id', '=', $college_id)
            ->orderBy('id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function studentReportLetterData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $courseId = $request->course_id;
        $semesterId = $request->semester_id;

        $columnArr = [
            'rs.id',
            'rs.generated_stud_id',
            'rs.first_name',
            'rs.student_type',
            'rs.email',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.status',
            DB::raw('group_concat(CONCAT(rc.course_code,":",rc.course_name))as course_name'),
            DB::raw('group_concat(CONCAT(rto_subject.subject_code,":",rto_subject.subject_name))as subject_group'),
            DB::raw('group_concat(CONCAT(rto_subject_unit.vet_unit_code,":",rto_subject_unit.unit_name))as unit_group'),
        ];

        $columns = [
            'generated_stud_id' => 'rs.generated_stud_id',
            'first_name' => 'rs.first_name',
            'student_type' => 'rs.student_type',
            'email' => 'rs.email',
            'course_name' => DB::raw('group_concat(CONCAT(rc.course_code,":",rc.course_name))as course_name'),
            'course_attempt' => 'rsc.course_attempt',
            'start_date' => 'rsc.start_date',
            'finish_date' => 'rsc.finish_date',
            'status' => 'rsc.status',
            'subject_group' => DB::raw('group_concat(CONCAT(rto_subject.subject_code,":",rto_subject.subject_name))as subject_group'),
            'unit_group' => DB::raw('group_concat(CONCAT(rto_subject_unit.vet_unit_code,":",rto_subject_unit.unit_name))as unit_group'),
        ];

        $query = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
            ->leftJoin('rto_student_courses as rsc', function ($join) {
                $join->on('rsc.student_id', '=', 'rsse.student_id');
                $join->on('rsc.course_id', '=', 'rsse.course_id');
            })
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rsse.unit_id')
            ->where('rsse.course_id', '=', $courseId)
            ->where('rsse.semester_id', '=', $semesterId)
            ->where('rs.college_id', '=', $college_id)
            ->where('rsse.final_outcome', '=', 'NYC')
            ->groupby(['rsse.student_id'])
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function publicHolidayData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rh.id',
            'rh.holiday_type_id as holiday_type',
            'rh.description',
            'rh.holiday_date',
            'rh.state_id as state',
        ];

        $columns = [
            'holiday_type' => 'rh.holiday_type_id as holiday_type',
            'description' => 'rh.description',
            'holiday_date' => 'rh.holiday_date',
            'state' => 'rh.state_id as state',
        ];

        $query = Holiday::from('rto_holiday as rh')
            ->where('rh.college_id', '=', $college_id)
            ->orderBy('id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function courseTemplateData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rct.id',
            'rct.template_name',
            'rct.course_id',
            'rc.course_code as course_code',
            'rc.course_name as course_name',
            'rct.no_of_core_subject',
            'rct.no_of_elective_subject',
            'rct.set_default',
            'rct.set_active',
        ];

        $columns = [
            'template_name' => 'rct.template_name',
            'course_code' => 'rc.course_code as course_code',
            'course_name' => 'rc.course_name as course_name',
            'no_of_core_subject' => 'rct.no_of_core_subject',
            'no_of_elective_subject' => 'rct.no_of_elective_subject',
            'set_default' => 'rct.set_default',
            'set_active' => 'rct.set_active',
        ];

        $query = CourseTemplate::from('rto_course_template as rct')
            ->leftjoin('rto_courses as rc', 'rct.course_id', '=', 'rc.id')
            ->where('rct.college_id', '=', $college_id)
            ->orderBy('rct.id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function uploadedEmailTemplateDocumentsData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'ret.id as template_id',
            'retd.id',
            'retd.file as file',
            'ru.name as added_by',
            'ret.created_at as date',
            DB::raw('DATE_FORMAT(ret.updated_at, "%d-%b-%Y") as uploaded_date'),
        ];

        $columns = [
        ];

        $query = EmailTemplateDocuments::from('rto_email_template_documents as retd')
            ->leftjoin('rto_email_template as ret', 'ret.id', '=', 'retd.email_template_id')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'ret.updated_by')
            ->where('ret.college_id', '=', $college_id)
            ->where('retd.email_template_id', '=', $request->email_template_id)
            ->orderBy('retd.id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        if (! $countOnly) {
            foreach ($result as $k => $row) {
                $filePath = Config::get('constants.uploadFilePath.Templates');
                $destinationPath = Helpers::changeRootPath($filePath, $row['template_id']);
                // $result[$k]['file_path'] = asset($destinationPath['view'] . $row['file']);
                $result[$k]['file_path'] = $this->getUploadedFileUrl($destinationPath['view'].$row['file']);

            }
        }

        return $result;
    }

    public function uploadedLetterTemplateDocumentsData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rlfa.id',
            'rlfa.letter_attachment as file',
            'rlfa.letter_id',
            'ru.name as added_by',
            'rlfa.created_at as date',
            DB::raw('DATE_FORMAT(rlfa.updated_at, "%d-%b-%Y") as uploaded_date'),
        ];

        $columns = [
        ];

        $query = ReportLetterFileAttachment::from('rto_letter_file_attachment as rlfa')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rlfa.updated_by')
            ->where('rlfa.college_id', '=', $college_id)
            ->where('rlfa.letter_id', '=', $request->letter_id)
            ->orderBy('rlfa.id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        if (! $countOnly) {
            foreach ($result as $k => $row) {
                $filePath = Config::get('constants.uploadFilePath.LetterFile');
                $destinationPath = Helpers::changeRootPath($filePath, $row['letter_id']);
                $result[$k]['file_path'] = $this->getUploadedFileUrl($destinationPath['view'].$row['file']);

            }
        }

        return $result;
    }

    public function generateAgentCommissionReport($data)
    {
        $query = StudentAgentCommission::from('rto_student_agent_commission as rsac')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsac.agent_id')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsac.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsac.course_id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rsac.student_id')
            ->leftjoin('rto_campus as rcamp', 'rcamp.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.transection_no', '=', 'rsac.transaction_no')
            ->leftjoin('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rsipt.initial_payment_detail_id')
            ->select('rcamp.name as campus_name', 'rs.first_name', 'rs.family_name', 'rs.generated_stud_id', 'rc.course_code', 'rsc.course_attempt', 'rsc.status', 'rsc.course_fee', 'ra.agency_name', 'rto_student_initial_payment_details.due_date', 'rto_student_initial_payment_details.upfront_fee_pay', 'rsipt.paid_amount as transaction_paid_amount', 'rc.course_name', 'rsac.*')
            ->where(DB::raw('rsac.commission_payable + rsac.gst_amount'), '!=', DB::raw('rsac.commission_paid'))
            ->where('rto_student_initial_payment_details.due_date', '>=', $data['from_date'])
            ->where('rto_student_initial_payment_details.due_date', '<=', $data['to_date'])
            ->groupBy('rsac.id')
            ->orderBy('rs.id', 'desc')
            ->get();

        return $query;
    }

    public function generateStudentInitialPaymentReport($data)
    {

        $query = StudentInitialPaymentDetails::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_initial_payment_details.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment_details.course_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_initial_payment_details.student_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_country', 'rto_students.nationality', '=', 'rto_country.id')
            ->leftjoin('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_student_courses.course_manager_id')
            ->select('rto_staff_and_teacher.first_name as manager_first_name',
                'rto_staff_and_teacher.last_name as manager_last_name',
                'rto_campus.name as campus_name', 'rto_students.id', 'rto_students.first_name',
                'rto_students.DOB', 'rto_students.family_name', 'rto_students.generated_stud_id',
                'rto_students.current_street_no', 'rto_students.current_street_name',
                'rto_students.current_city', 'rto_students.current_state', 'rto_students.current_postcode',
                'rto_students.current_home_phone', 'rto_students.email', 'rto_students.current_mobile_phone',
                'rto_country.nationality', 'rto_courses.course_name', 'rto_courses.course_code',
                'rto_student_courses.course_attempt', 'rto_student_courses.status', 'rto_student_courses.is_claim',
                'rto_student_courses.intake_date', 'rto_student_courses.start_date', 'rto_student_courses.finish_date',
                'rto_student_courses.course_fee', 'rto_agents.agency_name',
                'rto_student_initial_payment_details.upfront_fee_to_pay', 'rto_student_initial_payment_details.upfront_fee_pay',
                'rto_student_initial_payment_details.invoice_credit', 'rto_student_initial_payment_details.commission',
                'rto_student_initial_payment_details.gst_amount', 'rto_student_initial_payment_details.invoiced_start_date',
                'rto_student_initial_payment_details.paid_duration_text', 'rto_student_initial_payment_details.course_id', 'rto_student_initial_payment_details.student_id')
            ->where('rto_student_initial_payment_details.college_id', '=', $data['college_id'])
            ->where('rto_student_initial_payment_details.payment_status', '=', 'unpaid')
            ->where('rto_courses.course_type_id', '=', $data['course_type']);
        if ($data['course_id'] != 'All') {
            $query->where('rto_student_initial_payment_details.course_id', '=', $data['course_id']);
        }
        if ($data['status'] != 'All') {
            $query->where('rto_student_courses.status', '=', $data['status']);
        }
        $query->groupBy('rto_student_initial_payment_details.id')->get();

        return $query;
    }

    public function getCourseInvoiceDue($data)
    {
        $query = StudentInitialPaymentDetails::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment_details.course_id')
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_initial_payment_details.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_initial_payment_details.course_id');
            })
            ->where('rto_student_initial_payment_details.college_id', '=', $data['college_id'])
            ->where('rto_student_initial_payment_details.payment_status', '=', 'unpaid')
            ->where('rto_courses.course_type_id', '=', $data['course_type']);
        if ($data['course_id'] != 'All') {
            $query->where('rto_student_initial_payment_details.course_id', '=', $data['course_id']);
        }
        if ($data['status'] != 'All') {
            $query->where('rto_student_courses.status', '=', $data['status']);
        }
        $query->groupBy(['rto_student_initial_payment_details.student_id', 'rto_student_initial_payment_details.course_id']);
        $arrResult = $query->get([DB::raw('SUM(upfront_fee_to_pay) as total'), 'rto_student_initial_payment_details.course_id', 'rto_student_initial_payment_details.student_id'])->toArray();
        $arrReturn = [];
        foreach ($arrResult as $result) {
            $arrReturn[$result['course_id']][$result['student_id']] = $result['total'];
        }

        return $arrReturn;
    }

    public function generateTuitionFeeInvoiceReport($data)
    {
        $query = StudentInitialPaymentDetails::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_initial_payment_details.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment_details.course_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_initial_payment_details.student_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_country', 'rto_students.nationality', '=', 'rto_country.id')
            ->leftjoin('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_student_courses.course_manager_id')
            ->select('rto_staff_and_teacher.first_name as manager_first_name', 'rto_staff_and_teacher.last_name as manager_last_name',
                'rto_campus.name as campus_name', 'rto_students.id', 'rto_students.first_name', 'rto_students.DOB',
                'rto_students.family_name', 'rto_students.generated_stud_id', 'rto_students.current_street_no',
                'rto_students.current_street_name', 'rto_students.current_city', 'rto_students.current_state',
                'rto_students.current_postcode', 'rto_students.current_home_phone', 'rto_students.email',
                'rto_students.current_mobile_phone', 'rto_country.nationality', 'rto_courses.course_name',
                'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_student_courses.status',
                'rto_student_courses.is_claim', 'rto_student_courses.intake_date', 'rto_student_courses.start_date',
                'rto_student_courses.finish_date', 'rto_student_courses.course_fee', 'rto_agents.agency_name',
                'rto_student_initial_payment_details.upfront_fee_to_pay', 'rto_student_initial_payment_details.upfront_fee_pay',
                'rto_student_initial_payment_details.invoice_credit', 'rto_student_initial_payment_details.commission',
                'rto_student_initial_payment_details.gst_amount', 'rto_student_initial_payment_details.invoiced_start_date',
                'rto_student_initial_payment_details.paid_duration_text', 'rto_student_initial_payment_details.course_id', 'rto_student_initial_payment_details.student_id')
            ->where('rto_student_initial_payment_details.college_id', '=', $data['college_id'])
            ->where('rto_student_initial_payment_details.due_date', '>=', $data['from_date'])
            ->where('rto_student_initial_payment_details.due_date', '<=', $data['to_date'])
            ->groupBy(['rto_student_initial_payment_details.id'])
            ->get();

        return $query;
    }

    public function getAgentComissionPaid($data)
    {
        $sql = StudentInitialPaymentDetails::leftjoin('rto_student_initial_payment_transaction', 'rto_student_initial_payment_transaction.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->leftjoin('rto_student_agent_commission', 'rto_student_agent_commission.transaction_no', '=', 'rto_student_initial_payment_transaction.transection_no')
            ->select(DB::Raw('sum(rto_student_agent_commission.commission_paid) as commission_paid'), 'rto_student_initial_payment_details.id')
            ->where('rto_student_initial_payment_details.college_id', '=', $data['college_id'])
            ->where('rto_student_initial_payment_details.due_date', '>=', $data['from_date'])
            ->where('rto_student_initial_payment_details.due_date', '<=', $data['to_date'])
            ->groupBy(['rto_student_initial_payment_details.id']);

        return $sql->paginate('15')->pluck('commission_paid', 'id');
    }

    public function generatePaymentRefundReport($data)
    {
        $query = StudentInitialPaymentTransaction::leftjoin('rto_student_initial_payment_details', 'rto_student_initial_payment_details.id', '=', 'rto_student_initial_payment_transaction.initial_payment_detail_id')
            ->leftjoin('rto_student_agent_commission', 'rto_student_agent_commission.transaction_no', '=', 'rto_student_initial_payment_transaction.transection_no')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_initial_payment_details.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_initial_payment_transaction.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment_transaction.course_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_initial_payment_transaction.student_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_student_courses.course_manager_id')
            ->select('rto_staff_and_teacher.first_name as manager_first_name',
                'rto_staff_and_teacher.last_name as manager_last_name', 'rto_campus.name as campus_name',
                'rto_students.id', 'rto_students.first_name', 'rto_students.DOB', 'rto_students.family_name',
                'rto_students.generated_stud_id', 'rto_courses.course_name', 'rto_courses.national_code',
                'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_student_courses.status', 'rto_student_initial_payment_transaction.student_id',
                'rto_student_initial_payment_transaction.course_id', 'rto_student_initial_payment_transaction.transection_no', 'rto_student_initial_payment_transaction.initial_payment_detail_id',
                'rto_agents.agency_name', 'rto_student_initial_payment_transaction.agent_refunded', 'rto_student_initial_payment_transaction.agent_refund_date',
                'rto_student_initial_payment_transaction.refund_remarks', 'rto_student_initial_payment_transaction.student_net_receive', 'rto_student_initial_payment_transaction.student_administration_cost',
                'rto_student_agent_commission.GST_to_refund', 'rto_student_agent_commission.invoice_no',
                'rto_student_agent_commission.remarks as commission_remarks')
            ->where('rto_student_initial_payment_transaction.college_id', '=', $data['college_id'])
            ->where('rto_student_initial_payment_transaction.amount_refund', '!=', '0')
            ->where('rto_student_initial_payment_transaction.student_refund_date', '>=', $data['from_date'])
            ->where('rto_student_initial_payment_transaction.student_refund_date', '<=', $data['to_date'])
            ->groupBy(['rto_student_initial_payment_transaction.id'])
            ->get();

        return $query;
    }

    public function getPaymentModeData($data)
    {
        $result = [];
        $arrResult = PaymentMode::where('college_id', '=', $data['college_id'])->orderBy('id', 'DESC')->get()->toarray();
        for ($i = 0; $i < count($arrResult); $i++) {
            $result[$arrResult[$i]['id']] = $arrResult[$i]['name'];
        }
        $selectArray[''] = '--Select Mode--';

        return $selectArray + $result;
    }

    public function generateApplicationReport($data)
    {
        $sql = Student::leftjoin('rto_country', 'rto_country.id', '=', 'rto_students.nationality')
            ->select('application_reference_id', 'rto_students.created_at', 'first_name', 'family_name', 'current_situation', 'DOB', 'rto_country.nationality', 'gender', 'email', 'passport_no')
            ->where('rto_students.college_id', '=', $data['college_id'])
            ->where('rto_students.is_applicant', '=', '1');
        if ($data['value'] != '') {
            $sql->where('rto_students.application_reference_id', '=', $data['value']);
            $sql->orwhere('rto_students.first_name', '=', $data['value']);
            $sql->orwhere('rto_students.family_name', '=', $data['value']);
            $sql->orwhere('rto_students.passport_no', '=', $data['value']);
            if (strtotime($data['value'])) {
                $sql->orwhere('rto_students.DOB', '=', date('Y-m-d', strtotime($data['value'])));
            }
        }

        return $sql->get();
    }

    public function generateEnrollmentReport($data)
    {
        $sql = StudentCourses::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_users', 'rto_student_courses.created_by', '=', 'rto_users.id')
            ->select('rto_campus.name as campus_name', 'rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.generated_stud_id', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_student_courses.coe_name', 'rto_student_courses.status', 'rto_student_courses.intake_date', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_student_courses.created_at', 'rto_student_courses.updated_at')
            ->where('rto_student_courses.created_at', '>=', $data['from_date'])
            ->where('rto_student_courses.created_at', '<=', $data['to_date']);

        return $sql->where('rto_students.college_id', $data['college_id'])->get();
    }

    public function getAgentCommissionOwingDetails($data)
    {

        $sql = StudentInitialPayment::leftJoin('rto_student_initial_payment_details', function ($join) {
            $join->on('rto_student_initial_payment_details.student_id', '=', 'rto_student_initial_payment.student_id');
            $join->on('rto_student_initial_payment_details.course_id', '=', 'rto_student_initial_payment.course_id');
        })
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_initial_payment.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_initial_payment.course_id');
            })
            ->leftjoin('rto_student_initial_payment_transaction', 'rto_student_initial_payment_transaction.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->leftjoin('rto_student_agent_commission', 'rto_student_agent_commission.transaction_no', '=', 'rto_student_initial_payment_transaction.transection_no')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_initial_payment.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_initial_payment.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_initial_payment.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_initial_payment.created_at', '<=', $data['date'])
            ->select('rto_student_initial_payment.id', 'rto_student_initial_payment.tution_fee', 'rto_students.generated_stud_id', 'rto_agents.id as agents_id', 'rto_agents.agency_name', 'rto_campus.name as campus_name',
                'rto_courses.course_code', 'rto_student_courses.status', 'rto_student_courses.course_attempt', 'rto_student_courses.offer_id', 'rto_student_courses.agent_id', 'rto_student_courses.start_date', 'rto_student_courses.finish_date',
                DB::raw('sum(rto_student_initial_payment_details.upfront_fee_pay) as total_upfront_fee_pay'),
                DB::raw('sum(rto_student_initial_payment_details.upfront_fee_to_pay) as total_upfront_fee_to_pay'),
                DB::raw('sum(rto_student_agent_commission.commission_payable) as total_commission_payable'),
                DB::raw('sum(rto_student_agent_commission.gst_amount) as total_gst_amount'),
                DB::raw('sum(rto_student_agent_commission.commission_paid) as total_commission_paid'),
                DB::raw('sum(rto_student_initial_payment_transaction.amount_refund) as total_amount_refund')
            );
        $sql->groupBy('rto_student_initial_payment.id');

        return $sql->get();
    }

    public function getAgentConversionReport($data)
    {

        $sql = StudentCourses::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_agents.account_manager_id')
            ->leftjoin('rto_country', 'rto_country.id', '=', 'rto_agents.target_primary_country')
            ->where('rto_student_courses.course_type_id', '=', $data['course_type'])
            ->select('rto_staff_and_teacher.first_name', 'rto_staff_and_teacher.last_name', 'rto_agents.id', 'rto_agents.agency_name', 'rto_country.name as country_name', DB::raw('COUNT( rto_student_courses.`student_id`) AS total_students'), DB::raw('SUM(rto_student_courses.status = "Offered") AS total_students_offered'), DB::raw('SUM(rto_student_courses.status="Enrolled") AS total_students_enrolled'), DB::raw('SUM(IF(rto_student_courses.status = "Offered", total_weeks, 0)) AS total_weeks_offered'))
            ->where('rto_student_courses.start_date', '>=', $data['from_date'])
            ->where('rto_student_courses.start_date', '<=', $data['to_date'])
            ->where('rto_agents.college_id', $data['college_id']);
        if ($data['agent_id'] != 'All') {
            $sql->where('rto_student_courses.agent_id', '=', $data['agent_id']);
        }
        $sql->groupBy('rto_student_courses.agent_id');

        return $sql->get();
    }

    public function getAgentListByStatusAndCountry($data)
    {
        $sql = Agent::leftjoin('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_agents.account_manager_id')
            ->leftjoin('rto_agent_status', 'rto_agent_status.id', '=', 'rto_agents.status')
            ->leftjoin('rto_agent_profile_status', 'rto_agent_profile_status.agent_id', '=', 'rto_agents.id')
            ->where('rto_agents.college_id', $data['college_id'])
            ->where('rto_agents.status', $data['agent_status'])
            ->select('rto_staff_and_teacher.first_name', 'rto_staff_and_teacher.last_name', 'rto_agents.id', 'rto_agents.agency_name', 'rto_agents.contact_person', 'rto_agents.primary_email', 'rto_agents.telephone', 'rto_agents.mobile1', 'rto_agents.agency_name', 'rto_agents.office_ABN', 'rto_agents.office_address', 'rto_agents.office_country', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode', 'rto_agents.target_secondry_country', 'rto_agents.target_primary_country', 'rto_agents.target_primary_country', 'rto_agents.bank_name', 'rto_agents.bank_branch', 'rto_agents.bank_account_name', 'rto_agents.bank_account_number', 'rto_agents.BSB', 'rto_agents.bank_swift_code', 'rto_agents.bank_country', 'rto_agent_profile_status.end_date', 'rto_agent_status.status_type');
        if ($data['country_id'] != 'All') {
            $sql->where('rto_agents.target_primary_country', $data['country_id']);
        }

        return $sql->get();
    }

    public function getStudentCountByStatus($data)
    {
        $sql = StudentCourses::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_college_course_type', 'rto_college_course_type.id', '=', 'rto_student_courses.course_type_id')
            ->where('rto_courses.college_id', '=', $data['college_id'])
            ->select('rto_agents.id', 'rto_agents.agency_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_college_course_type.title as course_type', 'rto_student_courses.status', DB::raw('COUNT( rto_student_courses.`student_id`) AS total_students'))
            ->where('rto_student_courses.start_date', '>=', $data['from_date'])
            ->where('rto_student_courses.start_date', '<=', $data['to_date'])
            ->where('rto_student_courses.course_type_id', '=', $data['course_type']);

        if ($data['agent_id'] != 'All') {
            $sql->where('rto_student_courses.agent_id', '=', $data['agent_id']);
        }
        if ($data['status'] != 'All') {
            $sql->where('rto_student_courses.status', '=', $data['status']);
        }
        $sql->groupBy(['rto_student_courses.agent_id', 'rto_student_courses.status']);

        return $sql->get();
    }

    public function getStudentCountByCourse($data)
    {
        $sql = StudentCourses::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_college_course_type', 'rto_college_course_type.id', '=', 'rto_student_courses.course_type_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_courses.college_id', '=', $data['college_id'])
            ->where('rto_student_courses.status', '=', 'Enrolled')
            ->select('rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', DB::raw('COUNT( rto_student_courses.`student_id`) AS total_students'))
            ->where('rto_student_courses.course_type_id', '=', $data['course_type']);

        if ($data['course_id'] != 'All') {
            $sql->where('rto_student_courses.course_id', '=', $data['course_id']);
        }
        $sql->groupBy(['rto_student_courses.course_id']);

        return $sql->get();
    }

    public function getStudentOfferList($data)
    {
        $sql = StudentCourses::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_staff_and_teacher as staff1', 'staff1.id', '=', 'rto_agents.account_manager_id')
            ->leftjoin('rto_staff_and_teacher as staff2', 'staff2.id', '=', 'rto_student_courses.course_manager_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_country', 'rto_students.nationality', '=', 'rto_country.id')
            ->where('rto_courses.college_id', '=', $data['college_id'])
            ->where('rto_student_courses.offer_status', '!=', 'In Applicant')
            ->select('rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.email', 'rto_students.student_type', 'rto_students.student_type', 'rto_students.current_home_phone', 'rto_students.current_mobile_phone', 'rto_students.first_name', 'rto_students.family_name', 'rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_courses.tuition_fee', 'rto_courses.domestic_fee', 'rto_agents.agency_name', 'staff1.first_name as agent_manager_first_name', 'staff1.last_name as agent_manager_last_name', 'staff2.first_name as course_manager_first_name', 'staff2.last_name as course_manager_last_name', 'rto_country.nationality', 'rto_student_courses.*')
            ->where('rto_student_courses.created_at', '>=', $data['from_date'])
            ->where('rto_student_courses.created_at', '<=', $data['to_date']);

        return $sql->get();
    }

    public function getMissingDocuments($data)
    {
        $sql = StudentCourses::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_courses.college_id', '=', $data['college_id'])
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.student_type', 'rto_students.DOB', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_agents.agency_name', 'rto_student_courses.*')
            ->where('rto_student_courses.start_date', '>=', $data['from_date'])
            ->where('rto_student_courses.start_date', '<=', $data['to_date']);
        $arrResult = $sql->get()->toArray();
        $arrReturn = [];
        $arrDocuments = [];
        foreach ($arrResult as $result) {
            $arrRequiredDocuments = OfferDocumentChecklist::where('college_id', '=', $data['college_id'])->where('student_origin', '=', $result['student_type'])->where('is_active', '=', 1)->get();
            foreach ($arrRequiredDocuments as $document) {
                $findStudentOfferDocumentId = StudentOfferDocuments::where('rto_student_id', '=', $result['student_id'])
                    ->where('rto_offer_document_checklist_id', '=', $document->id)->get();
                if (empty($findStudentOfferDocumentId[0])) {
                    $arrReturn['offer_id'] = $result['offer_id'];
                    $arrReturn['generated_stud_id'] = $result['generated_stud_id'];
                    $arrReturn['name'] = $result['first_name'].' '.$result['family_name'];
                    $arrReturn['DOB'] = $result['DOB'];
                    $arrReturn['origin'] = $result['student_type'];
                    $arrReturn['course_code'] = $result['course_code'];
                    $arrReturn['course_name'] = $result['course_name'];
                    $arrReturn['status'] = $result['status'];
                    $arrReturn['stage'] = $result['status'] == 'Enrolled' ? 'Student' : 'Offer';
                    $arrReturn['start_date'] = $result['start_date'];
                    $arrReturn['intake_date'] = $result['intake_date'];
                    $arrReturn['agency_name'] = $result['agency_name'];
                    $arrReturn['student_id'] = $result['student_id'];
                    $arrReturn['document_name'] = $document->document_name;
                    $arrReturn['is_mandatory'] = $document->is_compulsory;
                    $arrDocuments[] = $arrReturn;
                }
            }
        }

        return $arrDocuments;
    }

    public function getStudentOfferListNotEnrolled($data)
    {
        $sql = StudentCourses::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_courses.college_id', '=', $data['college_id'])
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_courses.tuition_fee', 'rto_courses.domestic_fee', 'rto_agents.agency_name', 'rto_student_courses.*')
            ->where('rto_student_courses.start_date', '>=', $data['from_date'])
            ->where('rto_student_courses.start_date', '<=', $data['to_date']);

        return $sql->get();
    }

    public function getStudentListByIntakeDate($data)
    {
        $sql = StudentCourses::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_courses.college_id', '=', $data['college_id'])
                // ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_students.generated_stud_id', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_courses.tuition_fee', 'rto_courses.domestic_fee', 'rto_agents.agency_name', 'rto_student_courses.*')
            ->where('rto_student_courses.course_type_id', '=', $data['course_type']);

        if ($data['course_id'] != 'All') {
            $sql->where('rto_student_courses.course_id', '=', $data['course_id']);
        }
        if ($data['intake_date'] != '') {
            $sql->where('rto_student_courses.intake_date', '=', $data['intake_date']);
        }

        return $sql->get();
    }

    public function getStudentListCompletedCourse($data)
    {
        $sql = StudentCourses::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_courses.college_id', '=', $data['college_id'])
                // ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_campus.name as campus_name', 'rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_agents.agency_name', 'rto_student_courses.*')
            ->where('rto_student_courses.finish_date', '>=', $data['from_date'])
            ->where('rto_student_courses.course_type_id', '=', $data['course_type'])
            ->where('rto_student_courses.finish_date', '<=', $data['to_date']);

        if ($data['status'] != '') {
            $sql->where('rto_student_courses.status', '=', $data['status']);
        }

        return $sql->get();
    }

    public function getStudentsFailingConsecutiveSemester($data)
    {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
            })
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
            ->where('rto_student_subject_enrolment.course_id', '=', $data['course_id'])
            ->where('rto_student_subject_enrolment.semester_id', '=', $data['semester_id'])
            ->where('rto_semester.course_type_id', '=', $data['course_type'])
            ->where('rto_students.college_id', '=', $data['college_id'])
            ->groupby(['rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.semester_id'])
            ->select('rto_students.id as student_id', 'rto_students.email', 'rto_students.student_type', 'rto_students.generated_stud_id', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_student_courses.status', 'rto_semester.semester_name', DB::raw('SUM(rto_student_subject_enrolment.`final_outcome`="NYC") AS total_subjects_NYC'), DB::raw('COUNT(rto_student_subject_enrolment.subject_id) AS total_subjects'));
        $arrResult = $sql->get()->toArray();
        $arrReturn = [];
        foreach ($arrResult as $result) {
            $cntNyc = $result['total_subjects_NYC'];
            $totalSubjects = $result['total_subjects'];
            $halfSubjectCount = $totalSubjects / 2;
            if ($cntNyc >= $halfSubjectCount) {
                $arrReturnTmp = [];
                $arrReturnTmp['student_id'] = $result['generated_stud_id'];
                $arrReturnTmp['email'] = $result['email'];
                $arrReturnTmp['student_type'] = $result['student_type'];
                $arrReturnTmp['student_name'] = $result['first_name'].' '.$result['family_name'];
                $arrReturnTmp['semester_name'] = $result['semester_name'];
                $arrReturnTmp['course_name'] = $result['course_name'];
                $arrReturnTmp['course_code'] = $result['course_code'];
                $arrReturnTmp['course_attempt'] = $result['course_attempt'];
                $arrReturnTmp['start_date'] = date('d-m-Y', strtotime($result['start_date']));
                $arrReturnTmp['finish_date'] = date('d-m-Y', strtotime($result['finish_date']));
                $arrReturnTmp['status'] = $result['status'];
                $arrReturnTmp['total_subjects_NYC'] = $result['total_subjects_NYC'];
                $arrSubjectsTmp = $this->getStudentsFailedSubjects($result['student_id'], $data['course_id'], $data['semester_id']);
                // $arrReturnTmp['subjects'] = $arrSubjectsTmp;
                $arrReturn[] = $arrReturnTmp + $arrSubjectsTmp;
            }
        }

        return $arrReturn;
    }

    public function getStudentsFailedSubjects($studentId, $courseId, $semesterId)
    {
        $sql = StudentSubjectEnrolment::leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
            ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
            ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
            ->where('rto_student_subject_enrolment.final_outcome', '=', 'NYC')
            ->select('rto_subject.subject_code', 'rto_subject.subject_name', 'rto_subject_unit.unit_code', 'rto_subject_unit.unit_name');
        $arrResult = $sql->get()->toArray();

        $arrReturn = [];
        foreach ($arrResult as $result) {
            $arrReturnTmp = [];
            $arrReturnTmpSubject[] = $result['subject_code'].' : '.$result['subject_name'];
            $arrReturnTmpUnit[] = $result['unit_code'].' : '.$result['unit_name'];
        }
        $arrSubject['subject'] = array_values(array_unique($arrReturnTmpSubject));
        $arrUnit['unit'] = array_values(array_unique($arrReturnTmpUnit));
        $arrReturn = $arrSubject + $arrUnit;

        return $arrReturn;
    }

    public function getStudentsFailingOneSemester($data)
    {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
            })
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
            ->where('rto_student_subject_enrolment.course_id', '=', $data['course_id'])
            ->where('rto_student_subject_enrolment.semester_id', '=', $data['semester_id'])
            ->where('rto_semester.course_type_id', '=', $data['course_type'])
            ->where('rto_students.college_id', '=', $data['college_id'])
            ->groupby(['rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.semester_id'])
            ->select('rto_students.generated_stud_id', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_semester.semester_name', DB::raw('SUM(rto_student_subject_enrolment.`final_outcome`="NYC") AS total_subjects_NYC'), DB::raw('COUNT(rto_student_subject_enrolment.subject_id) AS total_subjects'));
        $arrResult = $sql->get()->toArray();
        $arrReturn = [];
        foreach ($arrResult as $result) {
            $cntNyc = $result['total_subjects_NYC'];
            $totalSubjects = $result['total_subjects'];
            $halfSubjectCount = $totalSubjects / 2;
            if ($cntNyc >= $halfSubjectCount) {
                $arrReturnTmp = [];
                $arrReturnTmp['student_id'] = $result['generated_stud_id'];
                $arrReturnTmp['student_name'] = $result['first_name'].' '.$result['family_name'];
                $arrReturnTmp['semester_name'] = $result['semester_name'];
                $arrReturnTmp['course_name'] = $result['course_name'];
                $arrReturnTmp['course_code'] = $result['course_code'];
                $arrReturnTmp['course_attempt'] = $result['course_attempt'];
                $arrReturnTmp['total_subjects_NYC'] = $result['total_subjects_NYC'];
                $arrReturn[] = $arrReturnTmp;
            }
        }

        return $arrReturn;
    }

    public function getAttendanceMissingList($data)
    {
        $sql = Timetable::leftjoin('rto_student_attendance', 'rto_student_attendance.timetable_id', '=', 'rto_timetable.id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_attendance.student_id')
            ->leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_student_attendance.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.course_id', '=', $data['course_id'])
            ->where('rto_student_courses.course_type_id', '=', $data['course_type'])
            ->where('rto_timetable.college_id', '=', $data['college_id'])
            ->where('rto_student_courses.start_date', '>=', $data['from_date'])
            ->where('rto_student_courses.finish_date', '<=', $data['to_date'])
            ->whereNotNull('rto_student_attendance.week_period')   // TODO:: GNG-1843
            ->groupBy('rto_student_attendance.student_id')
            ->select(['rto_student_attendance.student_id', 'rto_students.generated_stud_id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.email',
                'rto_students.student_type', 'rto_student_attendance.id', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.course_attempt', 'rto_student_courses.status',
                'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_campus.name as campus_name']);

        return $sql->get();
    }

    public function getStudentAttendanceByCourseClass($data)
    {
        $sql = StudentAttendance::leftjoin('rto_timetable', 'rto_timetable.id', '=', 'rto_student_attendance.timetable_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_attendance.student_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_timetable.subject_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_attendance.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_timetable.college_id', '=', $data['college_id'])
            ->where('rto_timetable.course_type_id', '=', $data['course_type'])
            ->where('rto_timetable.semester_id', '=', $data['semester_id'])
            ->where('rto_timetable.term', '=', $data['term'])
            ->where('rto_timetable.subject_id', '=', $data['subject_id'])
            ->where('rto_timetable.batch', '=', $data['batch'])
            ->where('rto_student_attendance.attendance_date', '=', $data['attendance_date'])
            ->groupBy(['rto_student_attendance.id'])
            ->select([
                'rto_students.generated_stud_id',
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_campus.name as campus_name',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.course_attempt',
                'rto_student_courses.status',
                'rto_timetable.start_time',
                'rto_timetable.finish_time',
                'rto_student_attendance.total_hours',
                'rto_student_attendance.student_id',
            ]);

        $arrResult = $sql->get()->toArray();
        $result = [];
        foreach ($arrResult as $attendance) {
            $result[$attendance['student_id']]['generated_stud_id'] = $attendance['generated_stud_id'];
            $result[$attendance['student_id']]['name'] = $attendance['first_name'].' '.$attendance['family_name'];
            $result[$attendance['student_id']]['course_code'] = $attendance['course_code'];
            $result[$attendance['student_id']]['course_name'] = $attendance['course_name'];
            $result[$attendance['student_id']]['course_attempt'] = $attendance['course_attempt'];
            $result[$attendance['student_id']]['status'] = $attendance['status'];
            $result[$attendance['student_id']]['start_date'] = $attendance['start_date'];
            $result[$attendance['student_id']]['finish_date'] = $attendance['finish_date'];
            $result[$attendance['student_id']]['campus_name'] = $attendance['campus_name'];
            $result[$attendance['student_id']]['total_hours'] = $attendance['total_hours'];
            $t1 = strtotime($attendance['finish_time']);
            $t2 = strtotime($attendance['start_time']);
            $diff = $t1 - $t2;
            $hours = $diff / (60 * 60);
            $result[$attendance['student_id']]['class_hour'] = $hours;
        }

        return $result;
    }

    public function getStudentContactUploadReport($data)
    {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
            ->where('rto_student_subject_enrolment.college_id', '=', $data['college_id'])
            ->where('rto_semester.course_type_id', '=', $data['course_type'])
            ->where('rto_student_subject_enrolment.semester_id', '=', $data['semester_id'])
            ->groupBy(['rto_students.id'])
            ->select([
                'rto_students.generated_stud_id',
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_students.nickname',
                'rto_students.email',
                'rto_students.current_home_phone',
                'rto_students.current_mobile_phone',
                'rto_students.current_building_name',
                'rto_students.current_unit_detail',
                'rto_students.current_street_no',
                'rto_students.current_street_name',
                'rto_students.current_city',
                'rto_students.current_state',
                'rto_students.current_postcode',
                'rto_students.current_country',
                'rto_student_courses.coe_name',
            ]);
        if ($data['course_id'] != 'All') {
            $sql->where('rto_student_subject_enrolment.course_id', '=', $data['course_id']);
        }
        if ($data['status'] != 'All') {
            $sql->where('rto_student_courses.status', '=', $data['status']);
        }

        return $sql->get();
    }

    public function getStudentListByCourseClass($data, $classStart, $classFinish)
    {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_timetable', 'rto_timetable.semester_id', '=', 'rto_student_subject_enrolment.semester_id')
            ->where('rto_student_subject_enrolment.college_id', '=', $data['college_id'])
            ->where('rto_student_subject_enrolment.course_id', '=', $data['course_id'])
            ->where('rto_student_subject_enrolment.semester_id', '=', $data['semester_id'])
            ->where('rto_student_subject_enrolment.term', '=', $data['term'])
            ->where('rto_student_subject_enrolment.subject_id', '=', $data['subject_id'])
            ->where('rto_student_subject_enrolment.batch', '=', $data['batch'])
            ->where('rto_timetable.start_week', '=', $classStart)
            ->where('rto_timetable.end_week', '=', $classFinish)
            ->groupBy(['rto_students.id'])
            ->select([
                'rto_students.generated_stud_id',
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_students.nickname',
                'rto_students.email',
                'rto_students.current_mobile_phone',
                'rto_students.current_building_name',
                'rto_students.current_unit_detail',
                'rto_students.current_street_no',
                'rto_students.current_street_name',
                'rto_students.current_city',
                'rto_students.current_state',
                'rto_students.current_postcode',
                'rto_students.current_country',
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_subject_enrolment.term',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.status',
                'rto_campus.name as campus_name',
                'rto_subject.subject_code',
            ]);

        return $sql->get();
    }

    public function getStudentListMissingUSINumbers($data)
    {
        $sql = Student::leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_visa_status', 'rto_visa_status.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_country as bc', 'bc.id', '=', 'rto_students.birth_country')
            ->leftjoin('rto_country as nc', 'nc.id', '=', 'rto_students.nationality')
            ->where('rto_students.college_id', '=', $data['college_id'])
            ->where('rto_students.USI', '=', '')
            ->select('bc.name as birthcountry', 'nc.name as nationality_name', 'rto_visa_status.name as visa_type', 'rto_students.*')
            ->where('rto_student_courses.course_type_id', '=', $data['course_type'])
            ->where('rto_student_courses.start_date', '>=', $data['from_date'])
            ->where('rto_student_courses.start_date', '<=', $data['to_date']);

        if ($data['course_id'] != 'All') {
            $sql->where('rto_student_courses.course_id', '=', $data['course_id']);
        }
        if ($data['status'] != 'All') {
            $sql->where('rto_student_courses.status', '=', $data['status']);
        }
        $sql->groupBy('rto_students.id');

        return $sql->get();
    }

    public function uploadLatterSetting(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $letterSetting = $request->file();
        if (isset($letterSetting['watermark'])) {
            $file = $letterSetting['watermark'];
            $filePath = Config::get('constants.uploadFilePath.LetterSetting');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);
            $filename = time().$uploadData['fileName'];
            $res = $file->move($destinationPath['default'], $filename);
            if ($res && ! empty($collegeId)) {
                $this->letterSetting->update(['watermark' => $filename], $collegeId);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Email Template Document uploaded successfully']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function getCoursesUpfrontFeeListData($request, $countOnly = false)
    {
        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $query = CoursesUpfrontFee::from('rto_course_upfront_fees as rcuf')
            ->leftjoin('rto_courses as rc', 'rcuf.course_id', '=', 'rc.id')
            ->leftjoin('rto_agents as ra', 'rcuf.agent_id', '=', 'ra.id')
            ->orderBy('rcuf.id', 'asc')
            ->where('rcuf.college_id', $college_id)
            ->select('rcuf.*',
                'rc.course_code',
                'rc.course_name',
                DB::raw("CONCAT(rc.course_code,' : ',rc.course_name) as course"),
                'ra.agency_name',
                'rc.couse_duration_type',
                'rc.course_type_id');

        $columns = [
            'course' => 'course',
            'agency_name' => 'agency_name',
            'student_type' => 'student_type',
            'upfront_fee' => 'upfront_fee',
            'no_of_installments' => 'no_of_installments',
            'frequency' => 'frequency',
            'couse_duration_type' => 'couse_duration_type',
            'material_fee' => 'material_fee',
        ];
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);

        return $this->gridDataPagination($query, $post, $countOnly);
    }

    public function getElicosDiscountWeekListData($request, $countOnly = false)
    {
        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $query = ElicosDiscount::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_elicos_discount.course_id')
            ->where('rto_courses.college_id', $college_id)
            ->select('rto_courses.course_code', 'rto_elicos_discount.*');

        $columns = [
            'course_code' => 'course_code',
            'applied_week' => 'applied_week',
            'discount_week' => 'discount_week',
            'from_date' => 'from_date',
            'to_date' => 'to_date',
        ];
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);

        return $this->gridDataPagination($query, $post, $countOnly);
    }

    public function getCoursePromotionPriceListData($request, $countOnly = false)
    {
        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $query = PromotionPrice::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_promotion_price.course_id')
            ->where('rto_courses.college_id', $college_id)
            ->select('rto_courses.course_code', 'rto_promotion_price.*');

        $columns = [
            'course_code' => 'course_code',
            'country_level' => 'country_level',
            'price' => 'price',
            'from_date' => 'from_date',
            'to_date' => 'to_date',
        ];
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);

        return $this->gridDataPagination($query, $post, $countOnly);
    }

    public function getStudentListForUnitAssessmentGrade($data)
    {
        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->join('rto_student_courses as rsc', function ($join) {
                $join->on('rsc.course_id', '=', 'rsse.course_id');
                $join->on('rsc.student_id', '=', 'rsse.student_id');
            })
            // ->join('rto_student_courses as rsc', 'rsc.course_id', '=', 'rsse.course_id')
            ->join('rto_courses', 'rto_courses.id', '=', 'rsc.course_id')
            ->join('rto_students', 'rto_students.id', '=', 'rsc.student_id')
            ->join('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsue.unit_id')

            ->leftjoin('rto_timetable', function ($join) {
                $join->on('rto_timetable.subject_id', '=', 'rsse.subject_id');
                $join->on('rto_timetable.batch', '=', 'rsse.batch');
            })
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rto_timetable.teacher_id')
            ->where('rsse.college_id', $data['college_id'])
            ->where('rsc.finish_date', '>=', $data['from_date'])    // rsse.activity_start_date
            ->where('rsc.finish_date', '<=', $data['to_date'])      // rsse.activity_finish_dat
            ->select([
                'rsse.id',
                'rto_students.generated_stud_id',
                'rto_courses.course_code',
                'rto_courses.course_name',
                DB::raw("CONCAT(DATE_FORMAT(rsc.finish_date, '%d %b %Y')) as finish_date"),
                // DB::raw("CONCAT(DATE_FORMAT(rsse.activity_finish_date, '%d %b %Y')) as activity_finish_date"),
                DB::raw("CONCAT(rst.first_name,' ', rst.last_name) as teacher_name"),
                DB::raw("CONCAT(rto_students.first_name,' ', rto_students.family_name) as student_name"),
                DB::raw("GROUP_CONCAT(DISTINCT rsu.unit_code SEPARATOR ', ') as units"),
            ]);

        if ($data['course_id'] != 'All') {
            $sql->where('rsse.course_id', '=', $data['course_id']);
        }

        if ($data['grade'] != 'All') {
            $sql->where('rsue.compentency', '=', $data['grade']);
        }

        $sql->groupBy(['rsse.student_id', 'rsse.course_id']);

        return $sql->get();
        // return $sql->paginate(10);
    }
}
