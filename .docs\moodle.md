# Moodle Integration — Galaxy360 Setup Guide

This guide walks you through integrating **Galaxy360** with your local **Moodle** installation.

---

## 1. Install Moodle

Follow Mood<PERSON>’s official installation instructions:

🔗 [Moodle Installation Quick Guide](https://docs.moodle.org/405/en/Installation_quick_guide)

---

## 2. Required Plugins

Before installing the Galaxy360 plugin, ensure the following plugins are installed:

### Mandatory Plugins

- **Moodle Webhooks**  
  🔗 [https://moodle.org/plugins/local_webhooks](https://moodle.org/plugins/local_webhooks)

- **OpenID Connect**  
  🔗 [https://moodle.org/plugins/auth_oidc](https://moodle.org/plugins/auth_oidc)

<!-- - Optionally, allow silent email updates by disabling email confirmation at:
  **Site administration → Users → Permissions → User policies** -->

---

### Install Galaxy360 Moodle Plugin

1. **Download the plugin:**
   🔗 [Download Galaxy360 Plugin](/uploads/competency.zip)

2. Upload it via: **Site administration → Plugins → Install plugins**
   🔗 [`{{ $moodle_url }}/admin/tool/installaddon/index.php`]({{ $moodle_url }}/admin/tool/installaddon/index.php)

---

## 3. Moodle Integration Setup

1. **Visit external services page:**

    - Visit: **Site Administration → Server → Web services → External Web Services**  
       🔗 [`{{ $moodle_url }}/admin/settings.php?section=externalservices`]({{ $moodle_url }}/admin/settings.php?section=externalservices) -![External Services](moodle-external-service.png)
    - Locate **_Galaxy360 External Web Service_**
    - Click **Authorized users** and add your admin user -![External Services Authorized Users](moodle-external-service-add-user.png)

2. **Generate a Web Service Token:**

    - Visit: **Site Administration → Server → Web services → Manage tokens**
      🔗 [`{{ $moodle_url }}/admin/webservice/tokens.php`]({{ $moodle_url }}/admin/webservice/tokens.php)

    - Click **Create Token**
    - Select the user which you added as authorized user for external service above.
    - Choose **_Galaxy360 External Web Service_** in the service dropdown
    - Uncheck the **"Valid until"** checkbox
    - Click Save Settings
    - ![Manage Token](moodle-manage-token.png)
    - Copy the generated token
    - ![Manage Token Copy](moodle-manage-token-copy.png)
    - Paste the copied token on **Galaxy360 → Moodle Setup → Moodle Api Token** and save.

3. **Inject Login Modifier Script:**

    If you are going to use Galaxy SSO login with moodle than we need to modify login url for the open id connect using a script. This will help maintain SSO session even though student visit moodle directly without logging in to galaxy.

    - Visit: **Site Administration → Appearance → Additional HTML**  
       🔗 [`{{ $moodle_url }}/admin/settings.php?section=additionalhtml`]({{ $moodle_url }}/admin/settings.php?section=additionalhtml)

    - Paste the following code in the **Before BODY is closed** field and Save:

        ```html
        <script src="/mod/lti/service/competency/amd/src/login_modifier.js"></script>
        ```

4. **Configure OpenID Connect:**

    - Enable **OpenID Connect** under:  
       **Site administration → Plugins → Authentication → Manage authentication**
      🔗 [`{{ $moodle_url }}/admin/settings.php?section=manageauths`]({{ $moodle_url }}/admin/settings.php?section=manageauths)
    - ![Enable Moodle Openid Authentication](moodle-openid-enable.png)

    - **Update field mappings for OpenID Connect:**

        - Visit **Administration → Plugins → Authentication → OpenID Connect**
        - Scroll to the bottom and Click **Field mappings**  
           🔗 [`{{ $moodle_url }}/admin/settings.php?section=auth_oidc_field_mapping`]({{ $moodle_url }}/admin/settings.php?section=auth_oidc_field_mapping)
        - Map `firstname` and `lastname` fields accordingly and save.
        - ![Openid Field Mapping](moodle-openid-fieldmap.png)

    - **Open ID config setting**

        - Visit **Site Administration → Authentication → OpenID Connect**
        - Click **IdP and authentication**  
           🔗 [`{{ $moodle_url }}/auth/oidc/manageapplication.php`]({{ $moodle_url }}/auth/oidc/manageapplication.php)
        - Fill in the following input fields:
            - **Identity provider IDP type:** `Other`
            - **Application ID:** `{{ $application_id }}`
            - **Client Authentication Method:** `Secret`
            - **Client Secret:** `{{ $client_secret }}`
            - **Authorization Endpoint:** `{{ $authorization_endpoint }}`
            - **Token Endpoint:** `{{ $token_endpoint }}`
            - **Scope:** `{{ $scopes }}`

---

## 4. Galaxy360 Integration Setup

1. **Navigate to the Moodle integration page in Galaxy360:**
   🔗 [`{{ $galaxy_url }}/integrations/moodle/setup`]({{ $galaxy_url }}/integrations/moodle/setup)

2. **Provide the following:**

    - Moodle installation URL
    - Token from the **MOODLE Manage Tokens step**.
    - (Optional) A name for this connection for easier identification

3. Save the configuration and click **Moodle Connect**

---

## 5. Post-Connection Steps

After connecting successfully:

- Sync grades using the **Moodle Grades Mapping** box on the right
- For VET courses:
    - Select the appropriate **scale** to map grades on the Galaxy side

---

✅ You’ve now successfully integrated Galaxy360 with Moodle!
