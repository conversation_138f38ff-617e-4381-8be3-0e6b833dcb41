<?php

namespace App\Http\Controllers\Spa;

use App\Classes\SiteConstants;
use App\Http\Controllers\Controller;
use App\Http\Requests\CourseFilterRequest;
use App\Http\Requests\CourseTemplateRequest;
use App\Http\Resources\CourseTemplatesResource;
use App\Model\v2\Courses;
use App\Model\v2\CourseTemplate;
use App\Model\v2\CourseTemplateStructure;
use App\Model\v2\GradingType;
use App\Model\v2\SubjectUnits;
use App\Services\CourseSetupService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class CourseTemplateController extends Controller
{
    private $service;

    public function __construct(CourseSetupService $service)
    {
        $this->service = $service;
        // $this->rememberToken
    }

    public function index(CourseFilterRequest $request)
    {
        $college_id = Auth::user()->college_id;
        /* prepare the array of search params */
        $data = CourseTemplate::SerchTemplatesList($request->DTO());
        $templates = $data['templates'] ?? collect();
        $course = $data['course'] ?? null;
        // get form inits
        $arrResultsCalculationMethod = config('constants.arrResultsCalculationMethod');
        Arr::forget($arrResultsCalculationMethod, '');
        $arrDepartments = Courses::getDepartments($college_id);
        $arrFaculties = Courses::getFaculties($college_id, false);

        $gradingTypes = GradingType::with(['resultgrade' => function ($query) {
            $query->select('id', 'grading_type', 'use_marks');
        }])
            ->where('college_id', $college_id)
            ->select('id', 'grading_name')
            ->get()
            ->map(function ($gradingType) {
                return [
                    'id' => $gradingType->id,
                    'text' => $gradingType->grading_name,
                    'marks' => optional($gradingType->resultgrade)->use_marks,
                ];
            });

        $formSeeders = [
            'course_types' => $this->service->getCourseTypes($college_id),
            'result_calculation_methods' => kendify($arrResultsCalculationMethod, 'value', 'label'),
            'course_recognitions' => kendify(Courses::getCourseRecognition()),
            'education_level' => Courses::getLevelEducation(),
            'education_field' => Courses::getFieldOfEducation(true),
            'anzsco_codes' => kendify(Courses::getANZSCOCodesList()),
            'faculties' => $arrFaculties,
            'departments' => $arrDepartments,
            'courses' => Courses::getCoursesList($college_id, true),
            'grading_types' => $gradingTypes,
        ];
        // dd($templates);
        // dd($formSeeders["courses"]);
        $pageData = [
            'tenant_id' => tenancy('id'),
            /* format the course collection */
            'templates' => CourseTemplatesResource::collection($templates),
            'currentcourse' => $course,
            /* Send the filter parameters to the renderer to render the course filter inputs */
            'filterScopes' => [
                'status' => config('constants.arrStatus'), // course status filters
                'courses' => Courses::getCoursesList($college_id, true),
            ],
            'formSeeds' => $formSeeders,
        ];

        /* render the results */
        // return Inertia::render('courses/List', $pageData);
        return Inertia::render('coursetemplates/List', $pageData);
    }

    public function getTemplateDetails($id)
    {
        $college_id = Auth::user()->college_id;
        $template = CourseTemplate::with(['course', 'templateunits.unitdata'])->find($id);
        $formattedData = $this->service->prepareTemplateUnitsList($template);

        return ajaxSuccess(['data' => $formattedData], null);
    }

    public function saveCourseTemplate(CourseTemplateRequest $request)
    {
        $collegeId = Auth::user()->college_id;
        $templateData = $request->validated();
        // to know where did the data came from
        // we will return saperate set of response according to the requested place
        $consumer = $templateData['consumer'] ?? 'settings';
        Arr::forget($templateData, 'consumer');
        $autosaving = $templateData['autosaving'] ?? false;
        Arr::forget($templateData, 'autosaving');
        $profilemode = $templateData['profilemode'] ?? false;
        Arr::forget($templateData, 'profilemode');
        DB::beginTransaction();
        try {
            $courseID = $templateData['course_id'] ?? null;
            $templateID = $templateData['id'] ?? null;
            // if ($templateData['is_master_template']) {
            //     Courses::with(['addedUnits'])->find($courseID)->updatePackagingRules($templateData);
            //     // $courseTemplate = null;
            //     // $msgtype = "messages.courses.templateupdated";
            // }
            if ($templateID) {
                $courseTemplate = CourseTemplate::with(['templateunits'])->findOrFail($templateID);
                $courseTemplate->updated_by = Auth::user()->id;
                $templateUnits = $courseTemplate->templateunits ?? collect([]);
                // $coreUnitsIds = $templateUnits->where("unit_type", SiteConstants::CORETEXT)->pluck("unit_id", "id");
                // $coreElectiveUnitIds = $templateUnits->where("unit_type", SiteConstants::ELECTIVETEXT)->pluck("unit_id", "id");
                // $availableUnits = SubjectUnits::where("course_id", $courseTemplate->course_id)->whereIn("id", $coreUnitsIds)->pluck("id");
                $coreUnits = $courseTemplate->templateunits->where('unit_type', SiteConstants::CORETEXT)->count();
                $electiveUnits = $courseTemplate->templateunits->where('unit_type', SiteConstants::ELECTIVETEXT)->count();
                $newCoreUnits = $templateData['no_of_core_subject'] ?? 0;
                $newElectiveUnits = $templateData['no_of_elective_subject'] ?? 0;
                if ($coreUnits > $newCoreUnits || $electiveUnits > $newElectiveUnits) {
                    if ($coreUnits > $newCoreUnits && $electiveUnits > $newElectiveUnits) {
                        $replace = ['core' => $coreUnits, 'elective' => $electiveUnits];
                        $message = ['messages.courses.packageexceeded', $replace];
                    } elseif ($coreUnits > $newCoreUnits) {
                        $replace = ['count' => $coreUnits];
                        $message = ['messages.courses.coreexceeded', $replace];
                    } else {
                        $replace = ['count' => $electiveUnits];
                        $message = ['messages.courses.electiveexceeded', $replace];
                    }
                    throw new \Exception(config_locale($message, 'Units more than the selected numbers have already been added.'));
                }
                if ($templateData['set_default']) {
                    // remove other defaults
                    CourseTemplate::where(['course_id' => $templateData['course_id'], 'set_default' => 1])->update(['set_default' => 0]);
                }
                $courseTemplate->update($templateData);
                /*
                if found course template check if template
                already have units and packaging units doesnot satisfy the numbers
                */
                $msgtype = 'messages.courses.templateupdated';
            } else {
                $courseTemplate = new CourseTemplate($templateData);
                $courseTemplate->created_by = Auth::user()->id;
                $courseTemplate->save();
                $msgtype = 'messages.courses.templateadded';
            }
            if ($courseTemplate && ! $profilemode) {
                // $courseTemplate->carryAllMasterTemplateUnits();
            }
            DB::commit();
            $templateID = $courseTemplate->id ?? null;
            $templatesList = [];
            if ($consumer == 'templates') {
                $currentTemplate = CourseTemplate::with(['course', 'templateunits.unitdata'])->find($templateID);
            } else {
                $templatesList = CourseTemplate::getTemplatesList($courseID, 'id');
                $currentTemplate = array_filter($templatesList, function ($template) use ($templateID) {
                    return $template->id == $templateID;
                });
                $currentTemplate = reset($currentTemplate);
            }
            $returnData = [
                'saved' => new CourseTemplatesResource($currentTemplate),
                'templates' => ['data' => CourseTemplatesResource::collection($templatesList)],
                'units' => $this->service->getCourseUnitDetails($currentTemplate),
                'course' => $this->service->getCourseEditDetails($courseID, 'id'),
            ];
            $msg = config_locale($msgtype, 'Template saved');
            if ($autosaving) {
                $msg = null;
            }

            return ajaxSuccess($returnData, $msg);
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteCourseTemplate(Request $request, $courseTemplateId)
    {
        $templateId = (int) $courseTemplateId;
        $college_id = Auth::user()->college_id;
        DB::beginTransaction();
        try {
            // get the template
            $template = CourseTemplate::with(['templateunits'])->find($templateId);
            $respond = $request->input('respond');
            if (! $template) {
                throw new \Exception(config_locale('messages.courses.templatenotfound', 'Template doesnot exists.'));
            }
            $templateUnits = $template->templateunits ?? [];
            if ($templateUnits) {
                $ids = $templateUnits->pluck('id')->toArray();
                CourseTemplateStructure::destroy($ids);
            }
            $template->delete();
            DB::commit();
            $data = [];
            if ($respond) {

            }

            return ajaxSuccess($data, config_locale('messages.courses.templatedeleted', 'Template Deleted'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function loadUnitsToCopy(Request $request)
    {
        $courseId = (int) ($request->input('course') ?? null);
        $template = (int) ($request->input('template') ?? null);
        try {
            $template = CourseTemplate::with(['templatesubjects.subjectdata', 'templateunits.unitdata'])->where([
                'course_id' => $courseId,
                'id' => $template,
            ])->first();
            if (! $template) {
                throw new \Exception('Template doesnot exists.');
            }
            $data = $this->service->loadUnitsToCopy($template);

            return ajaxSuccess(['templates' => $data], '');

        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }
    }

    public function copyUnitsToTemplate(Request $request)
    {
        $courseId = (int) ($request->input('course') ?? null);
        $template = (int) ($request->input('template') ?? null);
        DB::beginTransaction();
        try {
            $template = CourseTemplate::with(['templateunits', 'templatesubjects'])
                ->withCount(['templateunits', 'templatecoreunits', 'templateelectiveunits', 'templatesubjects', 'templatecoresubjects', 'templateelectivesubjects'])
                ->where([
                    'course_id' => $courseId,
                    'id' => $template,
                ])->first();
            if (! $template) {
                throw new \Exception('Template doesnot exists.');
            }
            $units = $request->input('units') ?? null;
            if (count($units) < 1) {
                throw new \Exception('No units selected.');
            }
            $copied = $this->service->CopyUnitsToTemplate($template, $units);
            if ($copied === 0) {
                throw new \Exception('No units copied.');
            }
            DB::commit();

            return ajaxSuccess([
                'units' => $this->service->getCourseUnitDetails($template),
                'course' => $this->service->getCourseEditDetails($courseId, 'id'),
            ], "{$copied} Units Copied to {$template->template_name}");

        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
        }
    }

    public function copySubjectsToTemplate(Request $request)
    {
        $courseId = (int) ($request->input('course') ?? null);
        $template = (int) ($request->input('template') ?? null);
        DB::beginTransaction();
        try {
            $template = CourseTemplate::with(['templateunits', 'templatesubjects'])
                ->withCount(['templateunits', 'templatecoreunits', 'templateelectiveunits', 'templatesubjects', 'templatecoresubjects', 'templateelectivesubjects'])
                ->where([
                    'course_id' => $courseId,
                    'id' => $template,
                ])->first();
            if (! $template) {
                throw new \Exception('Template doesnot exists.');
            }
            $subjects = $request->input('subjects') ?? null;
            if (count($subjects) < 1) {
                throw new \Exception('No subjects selected.');
            }
            $copied = $this->service->copySubjectsToTemplate($template, $subjects);
            DB::commit();

            if ($copied == 0) {
                throw new \Exception('No subjects copied. Please check the packaging rules and try again.');
            }

            return ajaxSuccess([
                'units' => $this->service->getCourseUnitDetails($template),
                'course' => $this->service->getCourseEditDetails($courseId, 'id'),
            ], "{$copied} Subjects Copied to {$template->template_name}");
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
        }
    }
}
