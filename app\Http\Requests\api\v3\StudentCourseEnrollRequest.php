<?php

namespace App\Http\Requests\api\v3;

use App\Classes\SiteConstants;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\Student;
use App\Model\v2\Tenant;
use App\Traits\CommonRequestMethodsTrait;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class StudentCourseEnrollRequest extends FormRequest
{
    use CommonRequestMethodsTrait;

    private $methodName;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        try {
            $user = auth()->user();
            if ($user->role_id == SiteConstants::STUDENT_ROLE_ID) {
                $studentId = Student::GetLoggedInStudentId($user);
                $student = Student::find($studentId);
            } else {
                $student = @$user->associatedStudent;
                $studentId = $student ? $student->id : null;
                if (! $student) {
                    $studentId = $this->route()->parameter('studentId') ?? $this->student ?? $this->student_id ?? null;
                    $studentId = ($studentId) ? decryptIt($studentId) : null;
                    $student = Student::find($studentId);
                }
            }
            /*
            old code
            if($user->role_id && $user->role_id == SiteConstants::STUDENT_ROLE_ID) {
                $studentId = Student::GetLoggedInStudentId($user);
            }else{
                $studentId = $this->route()->parameter('studentId') ?? $this->student ?? $this->student_id ?? null;
                $studentId = ($studentId) ? decryptIt($studentId) : null;
            }
            $student = Student::find($studentId);
            */
            if (empty($student) || ($student && ! $student->isApiAccessible(auth()->user()))) {
                throw new \Exception;
            }
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'student_id' => 'The student ID is invalid.',
            ]);
        }
        /*
        code to get the latest intake and campus if not provided
        */
        $intake = $this->intake ?? null;
        $campus = $this->campus ?? null;
        /*
        get the intake
        */
        $courseIntakesQuery = CoursesIntakeDate::with('campusIntakes.campus')
            ->where([
                'course_id' => $this->course ?? null,
                'active' => 1,
            ])
            ->where('intake_end', '>', Carbon::now())
            ->orderBy('intake_start');
        if ($intake) {
            $courseIntakesQuery->where('id', $intake);
        }
        // dd(getParsedSQL($courseIntakesQuery));

        $courseIntakes = $courseIntakesQuery->first();
        $intake = $courseIntakes->id ?? null;

        if (! $intake) {
            throw ValidationException::withMessages([
                'campus' => 'Intake detail not found. Provide a valid Intake ID that is available for the selected course.',
            ]);
        }
        // get the intake campuses
        $campuses = $courseIntakes->campusIntakes ?? null;
        if ($campus) {
            /* if campus id is provided pick the provided campus_id */
            $intakeCampus = ($campuses) ? $campuses->where('campus_id', $campus)->first() : null;
        } else {
            /* if campus not given, select the first campus available in the intake campuses list */
            $intakeCampus = ($campuses) ? $campuses->first() : null;
        }

        if (! $intakeCampus) {
            throw ValidationException::withMessages([
                'campus' => 'Campus not found for the selected intake.',
            ]);
        }

        $campus = $intakeCampus->campus_id ?? null;

        try {
            $rawHost = $hostUrl = $this->redirect_uri ?? '';

            if (! preg_match('/^https?:\/\//', $rawHost)) {
                $hostUrl = 'http://'.$rawHost;
            }

            $parsedUrl = parse_url($hostUrl);

            $host = $parsedUrl['host'] ?? null;

            $tenant = ($host) ? Tenant::GetTenantInfoByAllowedDomain($host) : null;
            $url = ($tenant) ? $tenant->GetApiBaseurl() : null;

            if (! $url) {
                throw ValidationException::withMessages([
                    'redirect_uri' => 'This Domain is not a registered as allowed domains.',
                ]);
            }

        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'redirect_uri' => $e->getMessage(),
            ]);
        }

        $this->merge([
            'student' => $studentId,
            'intake' => $intake,
            'campus' => $campus,
            'redirect_uri' => $hostUrl,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {

        return [

            /**
             * Hashed key of student Id (If the current user is the student, no need to pass the student Id)
             */
            'student' => ['required'],
            /**
             * Course Id to enroll student to
             */
            'course' => [
                'required',
                'integer',
                Rule::exists('App\Model\v2\Courses', 'id')->where(function ($query) {
                    $query->where('activated_now', 1);
                }),
            ],
            /**
             * Intake selected to enroll
             */
            'intake' => [
                'required',
                'integer',
            ],
            /**
             * Campus to enroll
             */
            'campus' => [
                'required',
                'integer',
            ],
            /**
             * Provide the url where user should come back to after the payment process
             *
             * @example scheme://domain/endpoint
             *
             * Example host: https://example.com/shourtcourse/coursecode
             */
            'redirect_uri' => [
                'required',
                'url',
            ],

        ];
    }

    public function messages()
    {
        return [
            'intake.required' => 'Provide any available intake for the course.',
            'campus.required' => 'Provide any available campus for the selected intake.',
        ];
    }
}
