<script id="courseProgressTemplate" type="text/html">
    <div class="w-full space-y-6">
        <div class="flex space-x-12 items-center justify-start w-full">
            <div class="flex space-x-2 items-center justify-start">
                <div class="w-14 h-10 row-span-2">
                    <div class="w-14 h-10 m-1 bg-white rounded-full">
                        <svg viewBox="0 0 36 36" class="circular-chart blue">
                            <path class="circle-bg"
                                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                            </path>
                            <path class="circle bg-primary-blue-500"
                                stroke-dasharray="#= Math.round(arr['days'] * 100 / arr['diff_days']) #, 100"
                                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                            </path>
                            <text x="18" y="20.35" class="percentage">#= Math.round(arr["days"] * 100 /
                                arr["diff_days"]) #%</text>
                        </svg>
                    </div>
                </div>
                <div class="col-span-2">
                    <p class="font-medium inset-x-0 leading-none mx-auto reletive text-gray-900 text-sm top-0 mt-1">#=
                        arr["days"] # of #= arr["diff_days"] # days</p>
                    <p class="leading-none right-0 text-gray-500 text-xs mt-1">#= arr["start_date"] # To #=
                        arr["finish_date"] #</p>
                </div>
            </div>
            # let unitPer = ((arr2["use_unit"] > 0) ? (arr2["use_unit"] * 100 / arr2["total_unit"]) : 0); #
            <div class="flex space-x-2 items-center justify-start">
                <div class="w-14 h-10 row-span-2">
                    <div class="w-14 h-10 m-1 bg-white rounded-full">
                        <svg viewBox="0 0 36 36" class="circular-chart green">
                            <path class="circle-bg"
                                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                            </path>
                            <path class="circle bg-primary-blue-500"
                                stroke-dasharray="#= (Number.isInteger(unitPer)) ? unitPer : unitPer.toFixed(1) #, 100"
                                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                            </path>
                            <text x="18" y="20.35" class="percentage">#= (Number.isInteger(unitPer)) ? unitPer :
                                unitPer.toFixed(1) #%</text>
                        </svg>
                    </div>
                </div>
                <div class="col-span-2">
                    <p class="font-medium inset-x-0 leading-none mx-auto reletive text-gray-900 text-sm top-0 mt-1">#=
                        arr2["use_unit"] # of #= arr2["total_unit"] # units</p>
                    <p class="leading-none right-0 text-gray-500 text-xs mt-1">#= arr2["title"] #</p>
                </div>
            </div>
        </div>
    </div>
</script>