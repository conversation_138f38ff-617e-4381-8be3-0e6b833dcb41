.tw-gte-steps {
    ol.k-step-list {
        li.k-step {
            &:first-child {
                display: none;
            }
            &:hover {
                .k-step-indicator {
                    .tw-stepper-icon {
                        color: white;
                    }
                }
            }
            &.k-step-current.k-focus {
                .k-step-link {
                    background: #e6f7ff;
                    border: 1px solid var(--color-primary-blue-500);
                    border-radius: 0.25rem;
                    padding: 0.125rem;
                }

                .k-step-indicator {
                    background-color: var(--color-primary-blue-500) !important;
                    border: 2px solid #fff !important;
                    outline: var(--color-primary-blue-500) solid 2px !important;
                    .tw-stepper-icon {
                        color: white;
                    }

                    &::after {
                        display: none;
                    }
                }

                .k-step-label .k-step-text {
                    color: var(--color-primary-blue-500);
                }
            }

            &.k-step-success.k-step-done .k-step-indicator {
                background-color: var(--color-primary-blue-500) !important;
                border: 2px solid transparent !important;
                outline: transparent solid 2px !important;

                .k-step-indicator-icon {
                    color: #fff;
                }
            }

            .k-step-indicator {
                width: 32px;
                height: 32px;
                border: 1px solid #d1d5db;

                .k-step-indicator-icon {
                    color: #9ca3af;
                }

                &.k-step-current.k-step-focus .k-step-indicator-icon {
                    color: #fff;
                }
            }
        }
    }
}
