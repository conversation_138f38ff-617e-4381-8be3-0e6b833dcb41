<script id="viewTimetableTemplate" type="text/x-kendo-template">
    {{-- <div class="flex justify-between items-center bg-white px-4 py-2 shadow border-b">
        <div class="flex space-x-2 items-center justify-start backToDash cursor-pointer">
            <span class="k-icon k-i-arrow-left k-color-gray-500"></span>
            <p class="text-2xl font-bold leading-7 text-gray-800">View TimeTable</p>
            <img class="w-5 h-5 mt-1 rounded-md" src="{{ asset('v2/img/info.svg') }}" class="" alt="searchIcon" />
        </div>
    </div> --}}
    <div class="flex flex-row w-full view-timetable-splitter">
        <div class="tw-toggle-filter pt-2" id="toggleFilter">
            <div class="relative toggelfilter w-full transition-all duration-450 filter-left-sidebar inline-flex flex-col  space-y-4 items-start justify-start bg-gray-50 border-r widthzero attendancepanelbar1 h-screen-header max-h-screen overflow-y-auto">
                <ul id="viewTimetablepanelbar" class="w-full flex items-start justify-start ">
                    <li>
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-1">
                                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Batch Start-End date</span>
                            </div>
                        </span>
                        <div>
                            <div class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <input type="text" data-value="2" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" data-field="" placeholder="Select Date" id="timetable_date_range">
                                <img src="{{ asset('v2/img/calendar.svg') }}" class="h-4 w-4" alt="searchIcon" />
                            </div>
                            <input type="hidden" id="alltimetable_start_date" name="alltimetable_start_date"/>
                            <input type="hidden" id="alltimetable_end_date" name="alltimetable_end_date"/>
                            <div class="all-timetable-filter" id="timetableDaterangepicker"></div>
                        </div>
                    </li>
                    <li>
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-2">
                                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Location</span>
                            </div>
                        </span>
                        <div>

                            <div class="all-timetable-filter" id="alltimetable_location">
                            </div>
                            <ul id="alltimetable_location" class="all-timetable-filter"></ul>
                        </div>
                    </li>
                    <li>
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-3">
                                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Trainer</span>
                            </div>
                        </span>
                        <div>
                            <div class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" data-field="alltimetable_trainer" placeholder="Search Trainer">
                            </div>
                            <div class="mb-2 mt-2">
                                <div class="flex flex-col items-start justify-start w-full ml-1">
                                    <div class="inline-flex space-x-1 items-center justify-start">
                                        <div class="form-check flex items-center space-x-1">
                                            <input hidden  class=" selectAllCheckbox  k-checkbox mr-2 cursor-pointer" type="checkbox" value="all" >
                                            <label  class="all-timetable-filter selectAllCheckboxLable cursor-pointer text-sm leading-5 text-gray-700 h-full" data-val="All Trainers">Select All</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <ul id="alltimetable_trainer" class="all-timetable-filter overflow-y-auto overflow-x-hidden"></ul>
                        </div>
                    </li>

                    {{-- <li>
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-4">
                                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Unit</span>
                            </div>
                        </span>
                        <div>
                            <div class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" data-field="alltimetable_unit" placeholder="Search Unit">
                            </div>
                        <ul class="all-timetable-filter" id="alltimetable_unit"></ul>
                        </div>
                    </li>
                    <li>
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-5">
                                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Batch</span>
                            </div>
                        </span>
                        <div>
                            <input class="all-timetable-filter" id="alltimetable_batch">
                        </div>
                    </li> --}}

                </ul>
                <span class="k-link k-state-hover ml-5">
                    <span class="k-panelbar-item-text">
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-3">
                                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Courses</span>
                            </div>
                        </span>
                    </span>
                    <div class="inline-flex space-x-2 items-center mt-2 mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput" style="margin-right:-1rem">
                        <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                        <input type="text" data-value="2" class="sidebarSearchCourse h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" data-field="alltimetable_unit" placeholder="Search Courses">
                    </div>
                </span>

                <ul id="alltimetable_unit"  class="flex items-start justify-start w-full" style="margin-top:1px;">

                </ul>
                <div class="filterFooterBoxTimetable flex flex-col items-center justify-start w-full py-3.5 bg-white border inset-x-0" style="display: none;">
                    <div class="flex space-x-4 items-center justify-end">
                        <div class="flex items-center justify-center w-20 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300">
                            <button type="button" id="" class="clearFilterTimetable text-xs font-medium leading-none text-gray-700">Clear</button>
                        </div>
                        <div class="flex items-center justify-center w-24 h-full px-3 py-2 bg-primary-blue-500 shadow rounded-lg">
                            <button id="viewAllTimetableFilter" class="text-xs font-medium leading-tight text-white">Apply <span></span></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="filter-result flex flex-col z-10 w-full bg-white overflow-x-hidden">
            <div class="flex flex-col space-y-2 items-start justify-center pt-6 pb-4 pl-8 pr-6 w-full">
                <div class="searchdata flex space-x-2 items-center justify-between w-full">
                    <div class="flex space-x-2 items-center justify-start h-9">
                        <button type="button" id="filterBtn" class="active btn-secondary btn-icon">
                            <img src="{{ asset('v2/img/f_icon.svg') }}" class="w-[14px] h-[14px]" alt="">
                            <span class="text-sm leading-5 font-normal text-gray-700">Filters</span>
                            <div class="flex items-center justify-center px-3 py-0.5 bg-primary-blue-100 rounded-full filterCountDiv hidden">
                                <span class="text-sm leading-5 text-primary-blue-800 filterCount">1</span>
                            </div>
                        </button>
                        <p class="text-base font-medium leading-tight text-gray-700 filter_title_timetable"></p>
                    </div>
                    <div class="flex space-x-2 items-center justify-end h-9">
                        <div class="flex space-x-2 items-center justify-start w-40 h-full px-3 py-2 bg-white border rounded-lg border-gray-300 hover:shadow-lg cusInput">
                            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                            <input type="text" id="" data-grid-id="viewAllTimetableList" class="searchInputField text-sm leading-5 font-normal text-gray-400" placeholder="Search">
                        </div>
                        <button type="button" id="exportAllTimetable" class="btn-secondary h-full px-3 py-2">
                            <span class="text-sm font-normal leading-5 text-gray-900 k-grid-excel">Export</span>
                        </button>
                        <div>
                            <button type="button" id="manageColumnsViewTimetable" class="btn-secondary w-9 h-full px-2.5 py-2.5 glob-tooltip" title="Manage Columns">
                                <x-v2.icons name="icon-columns" width="16" height="16" viewBox="0 0 16 16" class="text-gray-400" />
                            </button>
                            <div class="manageColumnBoxViewTimetable">
                                <div class="relative w-full">
                                    <div class="absolute top-2 right-0 w-auto pt-4 bg-white shadow border rounded-lg border-gray-200 space-y-4 min-w-[320px]">
                                        <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                                            <div class="inline-flex space-x-4 items-center justify-between w-full">
                                                <p class="text-sm font-medium leading-tight text-gray-700 pb-2">Columns</p>
                                            </div>
                                            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                                                @foreach($view_timetable_column as $column)
                                                    <div class="inline-flex items-center justify-start w-full pr-2 rounded">
                                                        <div class="flex space-x-2 items-center justify-start w-full cursor-pointer">
                                                            <input type="checkbox" class="k-checkbox rounded cursor-pointer" id="fc_{{ $column['id'] }}" value="{{ $column['id'] }}" {{ $column['default'] }}/>
                                                            <label for="fc_{{ $column['id'] }}" class="text-sm leading-none text-gray-700 cursor-pointer">{{ $column['title'] }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                            <button class="text-sm font-medium leading-tight pb-2 pt-1 text-primary-blue-500 reset column_filter_view_timetable">Reset Columns</button>
                                        </div>
                                        <div
                                            class="inline-flex space-x-4 items-center justify-end w-full py-4 pl-2 pr-2 border-t border-gray-200">
                                            <div class="flex space-x-4 items-center justify-end">
                                                <button type="button"
                                                    class="btn-secondary w-24 h-full px-3 py-2 clear column_filter text-xs">Cancel</button>
                                                <button type="button"
                                                    class="btn-primary w-28 h-full px-3 py-2 save column_filter text-xs">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="createTimetable btn-primary h-full px-3 py-2">
                            <img src="{{ asset('v2/img/icon-plus.svg')}}" class="h-4 w-4" alt="plus-icon" />
                            <span class="text-sm font-normal leading-5">New Timetable</span>
                        </button>
                    </div>
                </div>
                <div id="appliedFilterViewTimetableList" style="display: none"></div>
            </div>
            <div class="h-full bg-gray-100">
                <div id="viewAllTimetableList"></div>
            </div>
        </div>
    </div>
</script>

<script id="timetablefilterPanelbarTemplate" type="text/kendo-ui-template">
    <div class="flex flex-col items-start justify-start w-full #: item.value #">

        # if (item.id != 0 && typeof item.text != 'undefined') { #
        <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-#: item.id # ">
            <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.text #</span>
        </div>
        # } #

        # if (item.type == 'input') { #
        <div class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
            # if(item.field == 'course'){ #
                <input type="text" data-value="#: item.value #" class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
            # } else { #
                <input type="text" data-value="#: item.value #" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
            # } #
        </div>
        # } #

        # if (item.type == 'checkbox') { #
        <div class="inline-flex space-x-2 items-center justify-start">
            <div class="form-check flex items-center">
                <input class="form-check-input external-filter-timetable k-checkbox mr-2 cursor-pointer" type="checkbox" value="#: item.value #" data-category="#: item.field #" data-val="#: item.original #" id="#: item.category_id #_checkbox_#: item.id #">
                <label class="text-sm leading-5 text-gray-700 h-full" for="#: item.category_id #_checkbox_#: item.id #" data-val="#: item.original #" >
                    #: ((item.subtext.length > 26) ? (item.subtext.substring(0,26) + '...') : item.subtext)  #
                </label>
            </div>
        </div>
        # } #

        # if (item.type == 'dropdown') { #
        <select class="external-filter-timetable inline-flex space-x-2 items-center w-full justify-start inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 text-gray-500 custom-option leading-6 text-sm cursor-pointer #: item.field #" id="#: item.value #">
            # if(item.field == 'timetable_start_week') { #
                <option value="">Select Start Date</option>
            # } else if(item.field == 'timetable_end_week') { #
                <option value="">Select End Date</option>
            # } #
            # for(var i=0; i < item.arr.length; i++){ #
                <option value="#: item.arr[i].id #" title="#: item.arr[i].text #">#: ((item.arr[i].text.length > 30) ? (item.arr[i].text.substring(0,30) + '...') : item.arr[i].text) #</option>
            # } #
        </select>
        # } #

        # if (item.type == 'dropdownforbatch') { #
            <select class="external-filter-timetable inline-flex space-x-2 items-center w-full justify-start inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 text-gray-500 custom-option leading-6 text-sm cursor-pointer #: item.field #" id="#: item.value #">
                <option value="">Select Batch</option>
                # for(var i=0; i < item.arr.length; i++){ #
                    <option value="#: item.arr[i].fieldval #" title="#: item.arr[i].text #">#: ((item.arr[i].text.length > 30) ? (item.arr[i].text.substring(0,30) + '...') : item.arr[i].text) #</option>
                # } #
            </select>
        # } #

    </div>
</script>

<script id="classBreakTimeTemplate" type="text/x-kendo-template">
    <div class="flex flex-col w-full p-4 space-y-4 h-full bg-gray-50  h-full h-11/12 overflow-y-auto">
        <div class="inline-flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-md border-gray-200 w-full">
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <div class="inline-flex items-start justify-start w-full pl-4 border-l-8 border-green-500">
                    <p class="text-lg font-medium leading-6 text-gray-900 truncate">#= subject_name #</p>
                </div>
                <div class="flex flex-col space-y-1 items-start justify-start w-full pl-6">
                    <div class="flex w-full space-x-4">
                        <div class="flex space-x-1 items-start justify-start">
                            <p class="text-xs leading-none text-gray-500">Batch:</p>
                            <p class="text-xs leading-none text-gray-900">#= batch #</p>
                        </div>
                        <div class="flex space-x-1 items-start justify-start">
                            <p class="text-xs leading-none text-gray-500">Term:</p>
                            <p class="text-xs leading-none text-gray-900">#= term #</p>
                        </div>
                    </div>
                    <div class="flex space-x-1 items-start justify-start w-full">
                        <p class="text-xs leading-none text-gray-500">Semester:</p>
                        <p class="text-xs leading-none text-gray-900">#= semester_name #</p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-200 w-full h-px"></div>
            <div class="flex flex-col space-y-3 items-start justify-start w-full">
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/person-16-regular.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Teacher</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#= trainer_name #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/location-16-regular.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Room</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#= room_name #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/calendar.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Week Period</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#= start_time #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/app-folder-32-regular.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Mode</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#= class_type #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/column-triple-edit-24-regular.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Attendance Type</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#= attendance_type #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/clock.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Class Time</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#= class_time #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/barcode-scanner-24-regular.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Capacity</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#= class_capacity #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/clock-dismiss-24-regular.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Break From</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#=  (typeof(break_from_view) != "undefined" && break_from_view !== "null") ? break_from_view : 'N/A'  #</p>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <div class="flex space-x-1 items-center justify-start w-40">
                        <img src="{{ asset('v2/img/clock-dismiss-24-regular.svg') }}" class="h-4 w-4" />
                        <p class="text-sm leading-tight text-gray-500">Break To</p>
                    </div>
                    <div class="flex items-center justify-start">
                        <p class="text-sm leading-5 text-gray-900">#=   (typeof(break_to_view) != "undefined" && break_to_view !== "null") ? break_to_view : 'N/A'  #</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex bg-white border rounded-md border-gray-200 p-4 w-full">
            <form id="breaktimeForm" class="w-full">
                <input type="hidden" id="timetable_id" name="id">
            </form>
        </div>
    </div>
    <div class="w-full bottom-0 absolute inline-flex space-x-4 items-center justify-end px-6 py-4 border bg-white">
        <div class="float-right flex space-x-4 items-center justify-end">
            <button id="resetSMS" class="flex justify-center w-24  px-3 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancel_btn" data-type-id="classBreakTimeModal">
                <p class="text-sm font-medium leading-none text-gray-700">Cancel</p>
            </button>
            <button class="saveClassBreakTime flex justify-center h-full  px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600" type="button">
                <p class="text-sm font-medium leading-tight text-white">Set Class Break Time</p>
            </button>
        </div>
    </div>
</script>

<script id="actionTemplateViewTimetable" type="text/x-kendo-template">
    <div class="action-menu">
        <div class="px-1 space-y-1 py-2">
            <button class="classBreakTimeBTn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left"
                    data-id="#: timetable_id #"
                    data-subject="#: subject_name #"
                    data-batch="#: batch #"
                    data-semester="#: semester_name #"
                    data-term="#: term #"
                    data-trainer="#: trainer_name #"
                    data-room="#: room_name #"
                    data-start="#: start_time #"
                    data-class="#: class_type #"
                    data-attendance="#: attendance_type #"
                    data-capacity="#: class_capacity #"
                    data-time="#: class_time #"
                    data-break-from="#: break_from #"
                    data-break-to="#: break_to #"
                    >
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900">Class Break</span>
            </button>
            <button class="deleteTimetable single-row flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left" data-id="#: timetable_id #">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900">Delete TimeTable</span>
            </button>
            <button class="single-row flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left" data-id="#: timetable_id #">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900">Create Assessment</span>
            </button>
            {{-- <a href="/list-staff-materials/#: folder #/#: id #/#: flag # " class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900">Create Assessment</span>
            </a> --}}
        </div>
    </div>
</script>