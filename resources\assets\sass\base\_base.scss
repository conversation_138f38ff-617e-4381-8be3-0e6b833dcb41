// -----------------------------------------------------------------------------
// This file contains very basic styles.
// -----------------------------------------------------------------------------
// ::-webkit-scrollbar {
//   -webkit-appearance: none;
//   width: 10px;
// }

// ::-webkit-scrollbar-thumb {
//   border-radius: 5px;
//   background-color: rgba(0, 9, 14, 0.5);
//   -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
// }
/**
 * Set up a decent box model on the root element
 */
html {
    box-sizing: border-box;
    scroll-behavior: smooth;
    font-size: 97% !important;
}

@media (max-width: 1500px) {
    html {
        font-size: 88% !important;
    }
}

:root {
    @each $name, $value in $colors {
        --color-#{$name}: #{$value};
    }

    /* fonts */

    /* font sizes */
    --fs-h1: 6rem;
    --fs-h2: 3.75rem;
    --fs-h3: 3rem;
    --fs-h4: 2.13rem;
    --fs-h5: 1.5rem;
    --fs-subtitle: 1.13rem;
    --fs-xl: 1.25rem;
    --fs-sm: 0.94rem;
    --fs-xs: 0.81rem;
    --fs-base: 1rem;
    --fs-base-sm: 0.88rem;
    --fs-caption: 0.75rem;
    --fs-overline: 0.63rem;

    /* Gaps */
    --gap-base: 1rem;
    --gap-xs: 0.75rem;
    --gap-3xs: 0.63rem;
    --gap-5xs: 0.5rem;
    --gap-7xs: 0.38rem;
    --gap-9xs: 0.25rem;
    --gap-11xs: 0.13rem;
    --gap-xl: 1.25rem;
    --gap-5xl: 1.5rem;
    --gap-13xl: 2rem;
    --gap-21xl: 2.5rem;

    /* Paddings */
    --padding-base: 1rem;
    --padding-xs: 0.75rem;
    --padding-2xs: 0.69rem;
    --padding-3xs: 0.63rem;
    --padding-4xs: 0.56rem;
    --padding-5xs: 0.5rem;
    --padding-6xs: 0.44rem;
    --padding-7xs: 0.38rem;
    --padding-9xs: 0.25rem;
    --padding-10xs: 0.19rem;
    --padding-11xs: 0.13rem;
    --padding-smi: 0.81rem;
    --padding-mini: 0.94rem;
    --padding-mid: 1.06rem;
    --padding-xl: 1.25rem;
    --padding-5xl: 1.5rem;
    --padding-13xl: 2rem;
    --padding-45xl: 4rem;
    --padding-69xl: 5.5rem;
    --padding-101xl: 7.5rem;
    --padding-21xl: 2.5rem;
    --padding-418xl: 27.31rem;

    /* border radiuses */
    --br-xl: 20px;
    --br-4xl: 23px;
    --br-9xl: 28px;
    --br-85xl: 104px;
    --br-base: 16px;
    --br-xs: 12px;
    --br-3xs: 10px;
    --br-5xs: 8px;
    --br-7xs: 6px;
    --br-9xs: 4px;

    /* Effects */
    --shadows-shadow-xl:
        0px 20px 25px -5px rgba(0, 0, 0, 0.1), 0px 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadows-shadow-lg:
        0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadows-shadow-md: 0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadows-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px rgba(0, 0, 0, 0.06);
    --divider-top-divider: 0px 1px 0px #e5e7eb inset;
    --shadow-sm: 0px 1px 2px rgba(0, 0, 0, 0.05);
    --divider-bottom-divider: 0px -1px 0px #e5e7eb inset;
    --divider-right-divider: -1px 0px 0px #e5e7eb inset;

    //theming variables
    --tw--header-background: var(--color-gray-900);
    --sidebar-background: var(--color-gray-900);
    --sidebar-menuitem-background: var(--color-gray-700);
    --sidebar-menuitem-background-hover: var(--color-gray-600);
    --search-input-background: var(--color-gray-800);
    --search-input-border: var(--color-gray-800);
    --search-input-border-focus: #b8dae0;
    --search-input-color: #b8dae0;
    --release-notes-background: var(--color-gray-800);
    --release-notes-color: #b8dae0;
}

/**
 * Make all elements from the DOM inherit from the parent box-sizing
 * Since `*` has a specificity of 0, it does not override the `html` value
 * making all elements inheriting from the root box-sizing value
 * See: https://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/
 */
*,
*::before,
*::after {
    box-sizing: inherit;
}

/**
 * Basic styles for links
 */
// a {
// 	color: $brand-color;
// 	text-decoration: none;

// 	@include on-event {
// 		color: $text-color;
// 		text-decoration: underline;
// 	}
// }
