<div class="tw-timetable-grid grid grid-cols-12 {{ isset($isAjax) && $isAjax ? '' : 'hidden'}}">
    <div class="col-span-12 bg-gray-100">
        <div class="grid grid-cols-1 md:flex w-full items-center px-8 py-4 justify-between">
            <div class="course-list w-auto justify-start">
                <select class="header_course_list" style="display: none;"></select>
            </div>
        </div>
        <div class="flex flex-col px-8 pb-6 bg-gray-100 timetableView">
            <div id="studTimetableTab">
                <x-v2.skeleton.templates.timetable :partial="true" />
            </div>
            <div class="flex flex-col space-y-4 py-4" id="customDatePickerCalendar">
                <div class="flex justify-end items-center">
                    <div class="flex gap-4">
                        <div class="flex rounded-lg">
                            <button
                                class="tw-scheduler-view-btn inline-flex items-center justify-center w-16 h-9 bg-white border border-gray-200 rounded-l-lg"
                                data-view="day">
                                <p class="text-xs leading-none text-gray-500">Day</p>
                            </button>
                            <button
                                class="tw-scheduler-view-btn inline-flex items-center justify-center w-16 h-9 bg-white border-t border-b border-gray-200"
                                data-view="week">
                                <p class="text-xs leading-none text-gray-500">Week</p>
                            </button>
                            <button
                                class="tw-scheduler-view-btn inline-flex items-center justify-center w-16 h-9 bg-white border border-gray-200 rounded-r-lg active"
                                data-view="month">
                                <p class="text-xs leading-none text-gray-500">Month</p>
                            </button>
                        </div>
                        <div class="flex bg-white rounded-lg" id="studentTimetableView">
                            <button id="prevTimetable"
                                class="btn-secondary h-9 cursor-pointer rounded-e-none border-r p-2 focus:ring-0 focus:ring-offset-0">
                                <span class="k-icon k-i-arrow-60-left"></span>
                            </button>
                            <div class="tw-scheduler__datepicker w-32 hover:shadow-sm flex justify-center">
                                <input id="timeTableDatePicker" class="cursor-pointer" />
                            </div>
                            <button id="nextTimetable"
                                class="btn-secondary h-9 cursor-pointer rounded-s-none border-l p-2 focus:ring-0 focus:ring-offset-0">
                                <span class="k-icon k-i-arrow-60-right"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-6 w-full">
                <div id="timetable-month"></div>
            </div>
        </div>
    </div>
</div>


<script id="timetable-template" type="text/html">
    <div data-color="#: color1 #"
        class="items-center justify-center bgcolorset w-full h-full cursor-pointer #: color1 # mx-1"
        data-id="#: TaskId #" title="#: subject_name # \n #: timetable_time # | #: trainer_name #">
        <div class="flex gap-1 items-center w-full h-full overflow-hidden">
            <div class="w-[2px] max-w-[2px] flex-10 h-full #: color2 #"></div>
            <div class="text-[0.625rem] truncate">
                <p class=" font-normal leading-4 text-gray-900 truncate cal_title" data-cal-title="#: title #">
                    #: subject_name #
                </p>
                <div class="text-gray-700 space-x-1">
                    <span>#: timetable_time # |</span>
                    <span>#: trainer_name #</span>
                </div>
            </div>
        </div>
    </div>
</script>

<script id="viewDetail" type="text/html">
    #var uid = target.parent().attr("data-uid");#
    #var scheduler = target.closest("[data-role=scheduler]").data("kendoScheduler");#
    #var model = scheduler.occurrenceByUid(uid);#
    <x-v2.templates.course-popover>
        <x-slot name="header">
            <div class="space-y-2">
                <div class="flex justify-between">
                    <div class="flex gap-4">
                        <div class="h-6 w-2 #: model.color2 #">
                        </div>
                        <h3 class="text-lg font-medium leading-6 text-gray-900">
                            #: model.subject_name #
                        </h3>
                    </div>
                    <div class="flex justify-end gap-4 flex-10">
                        {{--
                        <x-v2.icons name="more-vertical"
                            class="h-6 w-6 text-gray-400 hover:text-gray-600 cursor-pointer" width="24" height="24"
                            viewBox="0 0 24 24" /> --}}
                        <x-v2.icons name="dismiss" class="h-6 w-6 text-gray-400 hover:text-gray-600 cursor-pointer"
                            id="hideTooltip" width="24" height="24" viewBox="0 0 24 24" />
                    </div>
                </div>
                <div class="flex items-center gap-x-4 gap-y-1 text-xs flex-wrap">
                    <div class="flex gap-2">
                        <span class="text-gray-500">Batch</span>
                        <span class="text-gray-900">#: displayOrDefault(model.batch) #</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="text-gray-500">Term</span>
                        <span class="text-gray-900">#: displayOrDefault(model.term) #</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="text-gray-500">Semester</span>
                        <span class="text-gray-900">>#: displayOrDefault(model.semester_name) #</span>
                    </div>
                </div>
            </div>
        </x-slot>
        <x-slot name="content">
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center gap-1">
                    <img src="{{ asset('v2/img/person-16-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Teacher</p>
                </div>
                <div class="text-gray-900">#: displayOrDefault(model.trainer_name) #</div>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center gap-1">
                    <img src="{{ asset('v2/img/location-16-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Room</p>
                </div>
                <div class="text-gray-900">#: displayOrDefault(model.room_name) #</div>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center gap-1">
                    <img src="{{ asset('v2/img/clock-alarm-32-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Time</p>
                </div>
                <div class="text-gray-900">#: displayOrDefault(model.timetable_time) #</div>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center gap-1">
                    <img src="{{ asset('v2/img/app-folder-32-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Mode</p>
                </div>
                <div class="text-gray-900">#: displayOrDefault(model.trainer_name) #</div>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center gap-1">
                    <img src="{{ asset('v2/img/app-folder-32-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Attendance Type</p>
                </div>
                <div class="text-gray-900">#: displayOrDefault(model.attendance_type) #</div>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center gap-1">
                    <img src="{{ asset('v2/img/clock-dismiss-24-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Break</p>
                </div>
                <div class="text-gray-900">#: displayOrDefault(model.break) #</div>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center gap-1">
                    <img src="{{ asset('v2/img/barcode-scanner-24-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Capacity</p>
                </div>
                <div class="text-gray-900">#: displayOrDefault(model.class_capacity) #</div>
            </div>
        </x-slot>
    </x-v2.templates.course-popover>
</script>

<script id="StudentInfoTemplateForTimetable" type="text/html">
    <x-v2.animation.slide class="flex tems-start justify-between p-4 bg-white border rounded-lg border-gray-200 w-full">
        <div class="w-full flex space-y-3 flex-col items-start justify-center">
            <div class="inline-flex space-x-1 items-center w-full">
                <div class="flex space-x-2 items-center justify-start">
                    # if (studentDetails.profile_pic == '') { let name =
                    studentDetails.student_name.toUpperCase().split(/\s+/); let shortName = name[0].charAt(0) +
                    name[1].charAt(0); #
                    <div class="rounded-full">
                        <div class='flex user-profile-pic w-12 h-12 !rounded-md bg-primary-blue-500 items-center'>
                            <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                                #</span>
                        </div>
                    </div>
                    # } else { #
                    <div class="w-12 h-12 rounded-full">
                        <img class="w-12 h-12 flex-1 !rounded-md object-cover" src="#= studentDetails.profile_pic #" />
                    </div>
                    # } #
                    <div class="inline-flex flex-col items-start justify-end">
                        <p class="text-gray-700 text-2xl font-medium">#= studentDetails.student_name #</p>
                    </div>
                </div>
            </div>
            <div
                class="grid grid-cols-2 lg:flex space-x-0 lg:space-x-4 items-start justify-start w-full gap-2 lg:gap-0">
                <div class="inline-flex flex-col space-y-1 items-start justify-center w-fit lg:w-1/5">
                    <p class="w-full text-gray-900 text-xs font-medium">Course</p>
                    <div class="flex space-x-1 w-full">
                        <p class="text-gray-700 leading-5 text-xs font-normal">#= studentDetails.course_name #</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-center w-fit lg:w-1/6">
                    <p class="w-full text-gray-900 text-xs font-medium">Course Duration</p>
                    <div class="flex space-x-1 w-full">
                        <p class="text-gray-700 leading-5 text-xs font-normal">#= studentDetails.course_duration #</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-center w-fit lg:w-1/6">
                    <p class="w-full text-gray-900 text-xs font-medium">Time Duration</p>
                    <div class="flex space-x-1 w-full">
                        <p class="text-gray-700 leading-5 text-xs font-normal">07:00 AM To 11:00 AM</p>
                    </div>
                </div>
            </div>
        </div>
    </x-v2.animation.slide>
    <x-v2.skeleton.templates.timetable :partial="true" class="tw-skeleton" style="display:none;" />
</script>