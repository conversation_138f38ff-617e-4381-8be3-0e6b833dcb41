{{-- Generate Payment Schedule Modal --}}
<div id="generatePaymentScheduleModal" style="display: none">
    <div class="flex flex-row w-full h-full space-x-6 bg-gray-100 p-6">
        <div class="flex flex-col space-y-2 items-start justify-start w-96 h-60 shadow rounded-md p-4 bg-white border">
            <div class="flex flex-col items-start justify-start rounded w-full">
                <div class="inline-flex items-start justify-start w-full cursor-pointer">
                    <div id="generatePaymentScheduleSteps" class="w-full generatePaymentScheduleSteps">

                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-col w-full">
            <div id="generatePaymentScheduleStepsWizard">

            </div>
        </div>
    </div>
</div>

<script id="commonDivForGeneratePaymentScheduleDiv" type="text/html">
    <div class="flex flex-col space-y-4 items-center justify-start bg-white w-full">
        <div class="inline-flex space-x-1 items-center justify-between w-full">
            <div class="flex space-x-2 items-center justify-start">
                # if (profile_pic == '') { let name = full_name.toUpperCase().split(/\s+/); let shortName =
                name[0].charAt(0) + name[1].charAt(0); #
                <div class="rounded-full">
                    <div class='flex user-profile-pic w-12 h-12 rounded-full bg-primary-blue-500 items-center'><span
                            class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                            #</span>
                    </div>
                </div>
                # } else { #
                <div class="w-12 h-12 rounded-full">
                    <img class="w-12 h-12 flex-1 rounded-full" src="#= profile_pic #" />
                </div>
                # } #
                <div class="inline-flex flex-col items-start justify-end">
                    <p class="text-sm font-bold leading-5 text-gray-900">#: full_name #</p>
                    <p class="text-xs leading-5 text-gray-400">#: generated_stud_id #</p>
                </div>
            </div>
            <div class="flex space-x-2 items-center justify-start">
                <p class="text-xs leading-5 text-gray-700">#:course_name #</p>
                <div class="flex items-center justify-center px-2.5 py-0.5 bg-primary-blue-100 rounded">
                    <p class="text-xs leading-5 text-center text-primary-blue-800">#: status #</p>
                </div>
            </div>
        </div>
    </div>
    <div
        class="inline-flex space-x-6 items-start justify-start p-4 bg-gray-50 border rounded-lg border-gray-200 w-full mt-4">
        <div class="flex space-x-2 items-start justify-start w-full">
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center w-1/5">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Length</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: course_duration #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center w-1/5">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Duration</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#:total_weeks# Weeks</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center w-1/5">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Agent</p>
                <input id="agent_id_for_generate_payment" hidden value="#: agent_id #" />
                <input id="hidden_course_start" hidden value="#: start_date #" />
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#:agency_name#</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center w-1/5">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Total Course Fee</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: kendo.toString(course_fee, "c") #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center w-1/5">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Subject Enrolled</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: noOfStudentSubjectEnrolment #</p>
            </div>
        </div>
    </div>
</script>
<script id="step1Template" type="text/html">
    <div class="inline-flex flex-col space-y-6 items-start justify-start w-full pb-20">
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="commonDivForGeneratePaymentSchedule w-full"></div>
        </div>
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Agent and student payment structure</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div class="w-full max-w-3xl" id="feeSetupCommissionForm"></div>
        </div>
    </div>
    <div
        class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-4 bottom-0 right-0 fixed border-t bg-white px-6">
        <div class="float-right flex space-x-4 items-center justify-end">
            <button type="button"
                class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">
                <span class="text-sm font-medium leading-4 text-gray-700">CANCEL</span>
            </button>
            <button type="button" data-id="1" data-form="feeSetupCommissionForm"
                class="nextUpFrontFee flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                <span class="text-sm font-medium leading-4 text-white uppercase">Next - Upfront Fee</span>
            </button>
        </div>
    </div>
</script>
<script id="step2Template" type="text/html">
    <div class="inline-flex flex-col space-y-6 items-start justify-start w-full pb-20">
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="commonDivForGeneratePaymentSchedule w-full"></div>
        </div>
        <div
            class="viewDetailsOfUpfrontFee flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Upfront Fee</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div class="h-5 justify-start items-center gap-2 inline-flex">
                <div class="p-0.5 rounded-2xl border border-green-500 justify-start items-start gap-1 flex">
                    <div class="w-4 h-4 relative"><img src="{{ asset('v2/img/Checkmark.svg') }}" class="" alt=""></div>
                </div>
                <div class="text-gray-700 text-sm font-normal leading-tight tracking-wide">
                    Upfront Payment for this student is already recorded. Here are upfront payment details
                </div>
            </div>
            <div
                class="inline-flex flex-col items-start justify-start px-6 bg-white border rounded-lg border-gray-200 w-full">
                <div class="flex flex-col items-center justify-start w-full">
                    <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-64 text-sm font-medium leading-5 text-gray-500">Upfront Fee Paid</p>
                            <p class="text-sm leading-5 text-gray-900 w-full upfront_fee_paid">1250</p>
                        </div>
                    </div>
                    <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-64 text-sm font-medium leading-5 text-gray-500">Remaining Fee Amount</p>
                            <p class="text-sm leading-5 text-gray-900 w-full remaining_fee_amount">15750</p>
                        </div>
                    </div>
                    <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-64 text-sm font-medium leading-5 text-gray-500">Paid Duration</p>
                            <p class="text-sm leading-5 text-gray-900 w-full paid_duration"> 4 Week </p>
                        </div>
                    </div>
                    <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-64 text-sm font-medium leading-5 text-gray-500">Upfront Receipt No</p>
                            <p class="text-sm leading-5 text-gray-900 w-full upfront_receipt_no">4646749 </p>
                        </div>
                    </div>
                    <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-64 text-sm font-medium leading-5 text-gray-500">Upfront Payment Date</p>
                            <p class="text-sm leading-5 text-gray-900 w-full upfront_payment_date">12 Feb 2023</p>
                        </div>
                    </div>
                    <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-64 text-sm font-medium leading-5 text-gray-500">Payment Mode</p>
                            <p class="text-sm leading-5 text-gray-900 w-full payment_mode"> Bank </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="space-x-4 text-primary-blue-500 items-center justify-center">
                <span class="text-base font-normal leading-5 text-gray-700">Need to correct Upfront Fee Details?</span>
                <button
                    class="editGenerateUpfrontFeeDetailBtn p-2 bg-primary-blue-50 rounded-lg justify-center items-center gap-2 inline-flex">
                    <span class="k-icon k-i-edit k-icon-edit"></span>
                    <div class="text-primary-blue-500 text-sm font-normal uppercase">Edit</div>
                </button>
            </div>
        </div>
        <div
            class="editDetailsOfUpfrontFee flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full hidden">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Upfront Fee</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div id="upFrontFeeFormHideDiv" class="scheduleGenerateHideDiv" style="display: none;">
                <p class="text-sm leading-5 text-gray-700 ">No needs to fill this step</p>
            </div>
            <div id="upFrontFeeFormForGeneratePaymentSchedule" class="scheduleGenerateShowDiv w-full max-w-3xl"></div>
        </div>
        <div
            class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-4 bottom-0 right-0 fixed border-t bg-white px-6">
            <div class="float-right flex space-x-4 items-center justify-end">
                <button type="button"
                    class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">
                    <span class="text-sm font-medium leading-4 text-gray-700">CANCEL</span>
                </button>
                <button type="submit" data-id="2" data-form="upFrontFeeFormForGeneratePaymentSchedule"
                    class="nextUpFrontFee flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                    <span class="text-sm font-medium leading-4 text-white uppercase">Next - Additional Payment</span>
                </button>
            </div>
        </div>
    </div>
</script>
<script id="step3Template" type="text/html">
    <div class="inline-flex flex-col space-y-6 items-start justify-start w-full pb-20">
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="commonDivForGeneratePaymentSchedule w-full"></div>
        </div>
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Additional Payment</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div class="w-full" id="setAdditionalPaymentData"></div>
            <div class="w-full" id="additionalPaymentForm"></div>
        </div>
        <div
            class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-4 bottom-0 right-0 fixed border-t bg-white px-6">
            <div class="float-right flex space-x-4 items-center justify-end">
                <button type="button"
                    class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">
                    <span class="text-sm font-medium leading-4 text-gray-700">CANCEL</span>
                </button>
                <button type="submit" data-id="3" data-form="additionalPaymentForm"
                    class="nextUpFrontFee flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                    <span class="text-sm font-medium leading-4 text-white uppercase">Next - Payment Schedule</span>
                </button>
            </div>
        </div>
    </div>
</script>
<script id="step4Template" type="text/html">
    <div class="inline-flex flex-col space-y-6 items-start justify-start w-full pb-20">
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="commonDivForGeneratePaymentSchedule w-full"></div>
        </div>
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Create Payment Schedule</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div id="paymentScheduleHideDiv" class="scheduleGenerateHideDiv" style="display: none;">
                <p class="text-sm leading-5 text-gray-700 ">No needs to fill this section</p>
            </div>
            <div class="scheduleGenerateShowDiv flex flex-col space-y-4 items-start justify-start w-full">
                <input type="hidden" name="check_start_date" id="check_start_date" />
                <input type="hidden" name="check_finish_date" id="check_finish_date" />
                <div class="w-full" id="createPaymentScheduleForm"></div>
                <input id="schedule_course_fee" hidden value="" />
                <div
                    class="w-full px-4 py-6 bg-gray-50 rounded-lg border border border border border-gray-200 flex-col justify-start items-start gap-6 inline-flex">
                    <div class="flex-col justify-center items-start gap-1 flex">
                        <div class="w-full text-gray-500 text-sm font-normal uppercase leading-3">Total Amount</div>
                        <div class="justify-start items-center gap-2 inline-flex">
                            <div class="text-gray-700 text-sm font-bold leading-9 total_amount_generate_upfront_fee">
                                $850</div>
                            <div class="text-gray-400 text-sm font-normal leading-normal tracking-wide">x
                                <span class="number_of_installment_generate_upfront_fee">20</span>
                                <span class="frequency_of_installment">months</span>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" hidden
                    class="generatePaymentScheduleBtn flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                    <span class="text-sm font-medium leading-4 text-white uppercase">Get Schedule List</span>
                </button>
                <button type="button"
                    class="generatePaymentScheduleBtnShow flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                    <span class="text-sm font-medium leading-4 text-white uppercase">Get Schedule List</span>
                </button>
                <div id="dataAppend" class="hidden w-full"></div>
            </div>
        </div>
        <div
            class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-4 bottom-0 right-0 fixed border-t bg-white px-6">
            <div class="float-right flex space-x-4 items-center justify-end">
                <button type="button"
                    class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">
                    <span class="text-sm font-medium leading-4 text-gray-700">CANCEL</span>
                </button>
                <button type="button"
                    class="savePaymentScheduleBtn flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                    <span class="text-sm font-medium leading-4 text-white uppercase">Generate Payment Schedule</span>
                </button>
            </div>
        </div>
    </div>
</script>

{{-- TODO::GNG-2805 --}}
<script id="setAdditionalPaymentTemplate" type="text/html">
    <div class="space-y-2 self-stretch">
        <div class="self-stretch h-9 p-2 bg-gray-100 rounded-lg flex-col justify-start items-start gap-1 flex">
            <div class="self-stretch justify-start items-center gap-1 inline-flex">
                # let defaultMaterialFee = 0; #
                # if (typeof material_data !== 'undefined' && typeof material_data['amount'] !== 'undefined') {#
                # let materialCreatedAt = convertJsDateTimeFormat(material_data['created_at']) #
                # let sourceType = (material_data['source_type'] == 'Import') ? 'imported' : 'added'; #
                <img src="{{ asset('v2/img/tikmark.png') }}" class=" " alt="" />
                <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                    <span>The Material fee of </span>
                    <span class="text-sky-500 text-sm font-normal leading-tight">#:
                        kendo.toString(material_data['amount'], "c") #</span>
                    <span>schedule was previously #: sourceType # on #: materialCreatedAt #. </span>
                    <span>The payment status is currently marked as #:
                        capitalizeFirstCharacter(material_data['payment_status']) #.</span>
                </div>
                #}else if(material_fee > 0) { defaultMaterialFee = material_fee; #
                <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                    The Material fee schedule of <span class="text-sky-500 text-sm font-normal leading-tight">#:
                        kendo.toString(material_fee, "c") #</span> is available for addition.
                </div>
                #}else{#
                <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                    There is no Material fee associated with this course.
                </div>
                #}#
                <input type="hidden" name="material_fee_value" value="#= defaultMaterialFee #" />
            </div>
        </div>
        <div class="self-stretch h-9 p-2 bg-gray-100 rounded-lg flex-col justify-start items-start gap-1 flex">
            <div class="self-stretch justify-start items-center gap-1 inline-flex">
                # let defaultEnrollFee = 0; #
                # if (typeof enroll_data !== 'undefined' && typeof enroll_data['amount'] !== 'undefined') {#
                # let enrollCreatedAt = convertJsDateTimeFormat(enroll_data['created_at']) #
                # let sourceType = (enroll_data['source_type'] == 'Import') ? 'imported' : 'added'; #
                <img src="{{ asset('v2/img/tikmark.png') }}" class=" " alt="" />
                <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                    <span>The Enroll fee of </span>
                    <span class="text-sky-500 text-sm font-normal leading-tight">#:
                        kendo.toString(enroll_data['amount'], "c") #</span>
                    <span>schedule was previously #: sourceType # on #: enrollCreatedAt #. </span>
                    <span>The payment status is currently marked as #:
                        capitalizeFirstCharacter(enroll_data['payment_status']) #.</span>
                </div>
                #}else if (enroll_fee > 0) { defaultEnrollFee = enroll_fee; #
                <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                    The Enroll fee schedule of <span class="text-sky-500 text-sm font-normal leading-tight">#:
                        kendo.toString(enroll_fee, "c") #</span> is available for addition.
                </div>
                #}else{#
                <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                    There is no Enroll fee associated with this course.
                </div>
                #}#
                <input type="hidden" name="enrollment_fee_value" value="#= defaultEnrollFee #" />
            </div>
        </div>

        # let defaultOshcFee = 0; #
        # if (typeof oshc_data !== 'undefined' && oshc_data.length > 0) {#

        # for (let i=0; i < oshc_data.length; i++) { # <div
            class="self-stretch h-9 p-2 bg-gray-100 rounded-lg flex-col justify-start items-start gap-1 flex">
            <div class="self-stretch justify-start items-center gap-1 inline-flex">
                # let oshcCreatedAt = convertJsDateTimeFormat(oshc_data[i]['created_at']) #
                # let sourceType = (oshc_data[i]['source_type'] == 'Import') ? 'imported' : 'added'; #
                <img src="{{ asset('v2/img/tikmark.png') }}" class=" " alt="" />
                <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                    <span>The OSHC fee of </span>
                    <span class="text-sky-500 text-sm font-normal leading-tight">#:
                        kendo.toString(oshc_data[i]['amount'], "c") #</span>
                    <span>schedule was previously #: sourceType # on #: oshcCreatedAt #. </span>
                    <span>The payment status is currently marked as #:
                        capitalizeFirstCharacter(oshc_data[i]['payment_status']) #.</span>
                </div>
            </div>
    </div>
    # } #

    #}else if (oshc_fee > 0) { defaultOshcFee = oshc_fee; #
    <div class="self-stretch h-9 p-2 bg-gray-100 rounded-lg flex-col justify-start items-start gap-1 flex">
        <div class="self-stretch justify-start items-center gap-1 inline-flex">
            <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                The OSHC fee schedule of <span class="text-sky-500 text-sm font-normal leading-tight">#:
                    kendo.toString(oshc_fee, "c") #</span> is available for addition.
            </div>
        </div>
    </div>
    #}else{#
    <div class="self-stretch h-9 p-2 bg-gray-100 rounded-lg flex-col justify-start items-start gap-1 flex">
        <div class="self-stretch justify-start items-center gap-1 inline-flex">
            <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">
                There is no OSHC fee associated with this course.
            </div>
        </div>
    </div>
    #}#
    <input type="hidden" name="oshc_fee_value" value="#= defaultOshcFee #" />
    </div>
</script>


{{-- TODO::GNG-1994 --}}
{{-- Re-Create Payment Schedule Modal --}}
<div id="reCreatePaymentScheduleModal" style="display: none;">
    <div class="flex w-full items-start justify-start space-y-6 bg-gray-100 p-6">
        <div class="inline-flex flex-col space-y-6 items-start justify-start w-full pb-20">
            <div class="bg-yellow-50 p-4 border border-bg-yellow-500 rounded-md flex gap-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8.25803 3.09858C9.02264 1.73928 10.9797 1.73928 11.7443 3.09858L17.3246 13.0191C18.0746 14.3523 17.1111 15.9996 15.5815 15.9996H4.42088C2.89123 15.9996 1.9278 14.3523 2.67773 13.0191L8.25803 3.09858ZM11.0011 12.9998C11.0011 13.552 10.5534 13.9998 10.0011 13.9998C9.44881 13.9998 9.0011 13.552 9.0011 12.9998C9.0011 12.4475 9.44881 11.9998 10.0011 11.9998C10.5534 11.9998 11.0011 12.4475 11.0011 12.9998ZM10.0011 4.99976C9.44881 4.99976 9.0011 5.44747 9.0011 5.99976V8.99976C9.0011 9.55204 9.44881 9.99976 10.0011 9.99976C10.5534 9.99976 11.0011 9.55204 11.0011 8.99976V5.99976C11.0011 5.44747 10.5534 4.99976 10.0011 4.99976Z"
                        fill="#FBBF24" />
                </svg>
                <div class="space-y-2">
                    <h4 class="text-sm font-medium text-yellow-800">Existing invoices will be deleted.</h4>
                    <div class="text-yellow-700 text-sm"></div>
                </div>
            </div>
            <div
                class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
                <div class="commonHeadForReCreatePaymentSchedule w-full"></div>
            </div>
            <div
                class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
                <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                    <div class="inline-flex items-center justify-between">
                        <div class="flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Course Fee</p>
                            <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                        </div>
                    </div>
                </div>
                <div class="w-full" id="reCreatePaymentScheduleForm">

                </div>
                <input id="check_start_date" hidden value="" />
                <input id="check_finish_date" hidden value="" />
                <input id="total_remaining_amount" hidden value="" />
                <input id="existing_applied_commission" hidden value="" />
                <input id="existing_gst" hidden value="" />
                <input id="schedule_course_fee" hidden value="" />
                <div
                    class="w-full px-4 py-6 bg-gray-50 rounded-lg border border-gray-200 flex-col justify-start items-start gap-6 inline-flex">
                    <div class="flex-col justify-center items-start gap-1 flex">
                        <div class="w-full text-gray-500 text-sm font-normal uppercase leading-3">Total Amount</div>
                        <div class="justify-start items-center gap-2 inline-flex">
                            <div class="text-gray-700 text-sm font-bold leading-9 remaining_installment_amount">$850
                            </div>
                            <div class="text-gray-400 text-sm font-normal leading-normal tracking-wide">x
                                <span class="number_of_remaining_installment">1</span>
                                <span class="remaining_frequency_for_installment">Day</span>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button"
                    class="generateNewPaymentScheduleBtn flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                    <span class="text-sm font-medium leading-4 text-white">Generate Payment Schedule</span>
                </button>
                <div id="newDataAppend" class="hidden w-full">

                </div>
            </div>
            <div
                class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-4 bottom-0 right-0 fixed border-t bg-white px-6">
                <div class="float-right flex space-x-4 items-center justify-end">
                    <button type="button"
                        class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">
                        <span class="text-sm font-medium leading-4 text-gray-700">Cancel</span>
                    </button>
                    <button type="button"
                        class="saveReCreateScheduleBtn flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <span class="text-sm font-medium leading-4 text-white">Recreate Schedule</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script id="commonHeadForReCreatePaymentScheduleDiv" type="text/html">
    <div class="flex flex-col space-y-4 items-center justify-start bg-white w-full">
        <div class="inline-flex space-x-1 items-center justify-between w-full">
            <div class="flex space-x-2 items-center justify-start">
                # if (profile_pic == '') { let name = full_name.toUpperCase().split(/\s+/); let shortName =
                name[0].charAt(0) + name[1].charAt(0); #
                <div class="rounded-full">
                    <div class='flex user-profile-pic w-12 h-12 rounded-full bg-primary-blue-500 items-center'>
                        <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                            #</span>
                    </div>
                </div>
                # } else { #
                <div class="w-12 h-12 rounded-full">
                    <img class="w-12 h-12 flex-1 rounded-full" src="#= profile_pic #" />
                </div>
                # } #
                <div class="inline-flex flex-col items-start justify-end">
                    <p class="text-sm font-bold leading-5 text-gray-900">#: full_name #</p>
                    <p class="text-xs leading-5 text-gray-400">#: generated_stud_id #</p>
                </div>
            </div>
            <div class="flex space-x-2 items-center justify-start">
                <p class="text-xs leading-5 text-gray-700">#:course_name #</p>
                <div class="flex items-center justify-center px-2.5 py-0.5 bg-primary-blue-100 rounded">
                    <p class="text-xs leading-5 text-center text-primary-blue-800">#: status #</p>
                </div>
            </div>
        </div>
    </div>
    <div
        class="inline-flex items-start justify-start w-full p-4 bg-gray-50 rounded-lg border border-gray-200 flex-col gap-4 mt-4">
        <div class="self-stretch justify-start items-start gap-6 inline-flex">
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Length</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: course_duration #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Duration</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: total_weeks # Weeks</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Subject Enrolled</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: total_subject_enrolled #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Agent</p>
                <input id="agent_id_for_generate_payment" hidden value="#: agent_id #" />
                <input id="hidden_course_start" hidden value="#: start_date #" />
                <p class="text-sm leading-5 text-gray-700 w-full">#:agency_name#</p>
            </div>
        </div>
        <div class="self-stretch justify-start items-start gap-6 inline-flex">
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Total Course Fee</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: kendo.toString(course_fee, "c") #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Upfront Fee</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: kendo.toString(course_upfront_fee, "c") #
                </p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Schedule Fee</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: kendo.toString(course_fee -
                    course_upfront_fee, "c") #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="w-full text-xs text-gray-500 font-medium uppercase truncate">Remaining Fee for schedule</p>
                <p class="text-sm leading-5 text-gray-700 truncate w-full">#: kendo.toString(remaining_amount, "c") #
                </p>
            </div>
        </div>
    </div>
</script>