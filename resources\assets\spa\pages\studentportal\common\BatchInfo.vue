<template>
    <div :class="rootClass">
        <h2 :class="titleClass">
            {{ batch || '-' }}
        </h2>
        <div class="flex items-center gap-8">
            <div class="min-w-24 space-y-1" v-for="(detail, index) in details" :key="index">
                <div :class="labelClass">{{ detail.label }}</div>
                <div v-if="isBatchDuration">
                    <FormatDate
                        :date="computeDate('start')"
                        :endDate="computeDate('end')"
                        :infix="'To'"
                        :formatType="dateFormat"
                    />
                </div>
                <div :class="valueClass" v-else>{{ detail.value }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watchEffect, computed } from 'vue';
import { twMerge } from 'tailwind-merge';
import FormatDate from '@spa/components/FormatDate.vue';
import { DEFAULT_DATE_FORMAT } from '@spa/helpers/constants.js';

const props = defineProps({
    batch: {
        type: String,
        required: true,
    },
    course: {
        type: String,
        required: true,
    },
    batchDuration: {
        type: String,
        required: true,
    },
    timeDuration: {
        type: String,
        required: true,
    },
    batchDays: {
        type: String,
        required: true,
    },
    roomNo: {
        type: String,
        required: true,
    },
    numOfStudents: {
        type: [Number, String],
        default: 0,
        required: true,
    },
    loadTable: Boolean,
    pt: {
        type: Object,
        default: {},
    },
});

const rootClass = computed(() => {
    return twMerge('space-y-4 bg-white border border-gray-200 p-4 rounded-md', props.pt.root);
});

const titleClass = computed(() => {
    return twMerge('text-2xl font-medium leading-8 text-gray-700', props.pt.title);
});

const labelClass = computed(() => {
    return twMerge('text-gray-900 text-xs font-medium leading-none', props.pt.label);
});

const valueClass = computed(() => {
    return twMerge('text-gray-700 text-xs font-normal leading-tight tracking-wide', props.pt.value);
});

const details = ref([]);
const dateFormat = DEFAULT_DATE_FORMAT;

watchEffect(() => {
    details.value = [
        { label: 'Course', value: props.course },
        { label: 'Batch Duration', value: props.batchDuration },
        { label: 'Time Duration', value: props.timeDuration },
        { label: 'Days', value: props.batchDays },
        { label: 'Room No', value: props.roomNo },
        { label: 'No. Of Students', value: props.numOfStudents },
    ];
});
</script>

<style></style>
