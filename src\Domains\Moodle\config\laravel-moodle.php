<?php

return [
    'url' => env('LM_URL', 'http://url-to-moodle-service.com'),
    'token' => env('LM_TOKEN', 'Y0uR!tOken'),

    'format' => 'json',
    //    'format' => 'RESPONSE_FORMAT_XML',

    'assessment-prefix' => 'GALAXY360: ',
    'sso_login_url' => '/mod/lti/service/competency/web/sso_redirect.php',
    'keycloak' => [
        'client_id' => env('LM_KEYCLOAK_CLIENT_ID', 'moodle'),
        'client_secret' => env('LM_KEYCLOAK_CLIENT_SECRET'),
        'authorization_path' => env('LM_KEYCLOAK_AUTHORIZATION_PATH', 'realms/galaxy/protocol/openid-connect/auth'),
        'token_path' => env('LM_KEYCLOAK_TOKEN_PATH', 'realms/galaxy/protocol/openid-connect/token'),
        'scopes' => env('L<PERSON>_KEYCLOAK_SCOPES', 'openid profile email'),
    ],
];
