<form id="basicInfoForm" name="basicInfoForm">
    <div class="flex flex-col items-start justify-start bg-white shadow rounded-lg w-full hidden editBasicInfo">
        <div class="w-full px-6 py-4 border-b">
            <div class="inline-flex space-x-1 items-center justify-start w-full">
                <p class="text-lg font-medium leading-7 text-gray-900">Basic Information</p>
            </div>
        </div>
        <div class="flex flex-col space-y-4 items-start justify-start p-6 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full">
                <div class="flex space-x-3 items-center justify-start w-full">
                    <div class="flex w-20 h-20 display_profile_pic" id="display_logo">
                        # if (arr.profile_pic == '') { let name = arr.full_name.toUpperCase().split(/\s+/); let
                        shortName = name[0].charAt(0) + name[1].charAt(0); #
                        <div class="rounded-full">
                            <div class='flex user-profile-pic w-20 h-20 rounded-full bg-primary-blue-500 items-center'>
                                <span class='text-4xl flex justify-center items-center leading-6 px-1 w-full'>#=
                                    shortName
                                    #</span></div>
                        </div>
                        # } else { #
                        <div class="w-20 h-20 rounded-full">
                            <img class="w-20 h-20 rounded-full" src="#= arr.profile_pic #" />
                        </div>
                        # } #
                    </div>
                    <div class="flex items-center justify-start upload-wrapper">
                        <input type="file" class="hidden" name="files" id="stud_profile_pic" />
                        <label for="stud_profile_pic"
                            class="flex justify-center px-3 py-2 border border-gray-300 shadow rounded-lg text-sm font-normal leading-5 text-gray-700 cursor-pointer"><img
                                src="{{ asset('v2/img/upload.svg') }}" class="mr-2" alt="" />Upload Photo</label>
                    </div>
                </div>
                <input class="hidden" name="full_name" id="full_name" value="#: arr.full_name #" />
                <input class="hidden" name="generated_stud_id" id="generated_stud_id"
                    value="#: arr.generated_stud_id #" />
                <input class="hidden" name="profile_pic" id="profile_pic" value="#= arr.profile_pic #" />
                <div class="space-y-2 w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700">Student Origin</p>
                    <ul class="tw-radio-group w-2/3 text-sm font-medium text-gray-700 bg-white rounded-lg">
                        # let offshoreActiveClass = (typeof(arr.student_type) != "undefined" && arr.student_type ==
                        'Offshore') ? 'border-primary-blue-200 bg-primary-blue-50 z-10' : ''; #
                        <li
                            class="form-check student_type_check w-full border border-gray-300 rounded-t-lg pl-4  #= offshoreActiveClass#">
                            <div class="flex items-center">
                                # let offshorevalue = (typeof(arr.student_type) != "undefined" && arr.student_type ==
                                'Offshore') ? 'checked' : ''; #
                                <input id="offshore" type="radio" value="Offshore" name="student_type"
                                    class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-primary-blue-500 checked:border-primary-blue-500 focus:outline-none transition duration-200 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                    #=offshorevalue#>
                                <label for="offshore"
                                    class="text-sm font-normal leading-5 text-gray-700 cursor-pointer inline-block w-full py-4 pr-4">Overseas
                                    Student (Offshore)</label>
                            </div>
                        </li>
                        # let onshoreActiveClass = (typeof(arr.student_type) != "undefined" && arr.student_type ==
                        'Onshore') ? 'border-primary-blue-200 bg-primary-blue-50 z-10' : ''; #
                        <li
                            class="form-check student_type_check w-full border border-gray-300 pl-4 #= onshoreActiveClass#">
                            <div class="flex items-center">
                                # let onshorevalue = (typeof(arr.student_type) != "undefined" && arr.student_type ==
                                'Onshore') ? 'checked' : ''; #
                                <input id="onshore" type="radio" value="Onshore" name="student_type"
                                    class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-primary-blue-500 checked:border-primary-blue-500 focus:outline-none transition duration-200 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                    #=onshorevalue#>
                                <label for="onshore"
                                    class="text-sm font-normal leading-5 text-gray-700 cursor-pointer inline-block w-full py-4 pr-4">Overseas
                                    Student in Australia (Onshore)</label>
                            </div>
                        </li>
                        # let domesticActiveClass = (typeof(arr.student_type) != "undefined" && arr.student_type ==
                        'Domestic') ? 'border-primary-blue-200 bg-primary-blue-50 z-10' : ''; #
                        <li
                            class="form-check student_type_check w-full border border-gray-300 rounded-b-lg pl-4 #= domesticActiveClass#">
                            <div class="flex items-center">
                                # let domesticvalue = (typeof(arr.student_type) != "undefined" && arr.student_type ==
                                'Domestic') ? 'checked' : ''; #
                                <input id="domestic" type="radio" value="Domestic" name="student_type"
                                    class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-primary-blue-500 checked:border-primary-blue-500 focus:outline-none transition duration-200 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                    #=domesticvalue#>
                                <label for="domestic"
                                    class="text-sm font-normal leading-5 text-gray-700 cursor-pointer inline-block w-full py-4 pr-4">Resident
                                    Student (Domestic)</label>
                            </div>
                        </li>
                    </ul>
                </div>

            </div>
            <div class="inline-flex space-x-4 items-start justify-start">
                <div class="flex flex-col space-y-4 items-start justify-start">
                    <p class="text-sm font-medium leading-5 text-gray-700">Enter Your Full Name</p>
                    <p class="text-sm leading-5 text-gray-500">Please write the name that you used when you applied for
                        your Unique Student Identifier (USI), including any middle names. If you do not yet
                        have a USI please write your name exactly as written in the identity document you choose to use
                    </p>
                </div>
            </div>
            <div class="flex space-x-4 items-start justify-start w-full">
                <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">First Name</p>
                    # let fNameText = (typeof(arr.first_name) != "undefined" && arr.first_name !== null) ?
                    arr.first_name : ''; #
                    <input required type="text" onkeydown="return /[a-z]/i.test(event.key)" name="first_name"
                        value="#= fNameText #"
                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                        id="firstname">
                </div>
                <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Middle Name</p>
                    # let mNameText = (typeof(arr.middel_name) != "undefined" && arr.middel_name !== null) ?
                    arr.middel_name : ''; #
                    <input type="text" onkeydown="return /[a-z]/i.test(event.key)" name="middel_name"
                        value="#= mNameText #"
                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                        id="middlename">
                </div>
                <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Last Name</p>
                    # let lNameText = (typeof(arr.family_name) != "undefined" && arr.family_name !== null) ?
                    arr.family_name : ''; #
                    <input required type="text" name="family_name" value="#= lNameText #"
                        class="onlyLettersAllowed text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                        id="lastname">
                </div>
            </div>
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <p class="text-sm font-medium leading-5 text-gray-700">Gender</p>
                <div class="inline-flex space-x-4 items-start justify-start w-full">
                    <div class="flex space-x-2 items-center justify-start">
                        # let malevalue = (typeof(arr.gender) != "undefined" && arr.gender == 'Male') ? 'checked' : '';
                        #
                        <div class="form-check">
                            <input
                                class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                type="radio" name="gender" value="Male" id="male" #=malevalue #>
                            <label for="male"
                                class="text-sm font-normal leading-5 text-gray-700 cursor-pointer">Male</label>
                        </div>
                    </div>
                    # let femalevalue = (typeof(arr.gender) != "undefined" && arr.gender == 'Female') ? 'checked' : '';
                    #
                    <div class="flex space-x-2 items-center justify-start">
                        <div class="form-check">
                            <input
                                class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                type="radio" name="gender" value="Female" id="female" #=femalevalue #>
                            <label for="female"
                                class="text-sm font-normal leading-5 text-gray-700 cursor-pointer">Female</label>
                        </div>
                    </div>
                    # let gendervalue = (typeof(arr.gender) != "undefined" && arr.gender == 'Not Specified') ? 'checked'
                    : ''; #
                    <div class="flex space-x-2 items-center justify-start">
                        <div class="form-check">
                            <input
                                class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                type="radio" name="gender" value="Other" id="other" #=gendervalue #>
                            <label for="other"
                                class="text-sm font-normal leading-5 text-gray-700 cursor-pointer">Other</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
            <div class="flex flex-col items-end justify-center w-full">
                <div class="inline-flex space-x-4 items-center justify-end">
                    <button class="backToBasicInfo btn-secondary px-4 py-2">
                        <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                    </button>
                    <button class="saveBasicInfo btn-primary px-4 py-2">
                        <p class="text-sm font-medium leading-5 text-white">Save</p>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>