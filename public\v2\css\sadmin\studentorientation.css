.k-panelbar > .k-item > .k-link,
.k-panelbar > .k-panelbar-header > .k-link {
    color: #bbbffc !important;
    background-color: transparent !important;
    padding: 12px 0px !important;
    cursor: pointer;
}

.k-panelbar {
    border-color: transparent;
    color: inherit;
    background-color: transparent;
}

/* CSS */

.k-panelbar-group > .k-panelbar-item > .k-link label {
    /* color: #374151 !important;
    height: 20px !important;
    font-size: 13px !important; */
    cursor: pointer;
}

/* .k-panelbar>.k-item {
    border-bottom: 1px solid #E5E7EB;
} */

.k-panelbar > .k-item > .k-link .k-icon,
.k-panelbar > .k-item > .k-link .k-panelbar-item-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-panelbar-item-icon {
    color: var(--color-primary-blue-500);
}

.k-grid td {
    border-width: 0px 0 1px 0px !important;
    background-color: white;
}

.k-grid tr.k-state-selected > td {
    background-color: #e6f7ff;
}

.k-checkbox:checked {
    border-color: var(--color-primary-blue-500);
    color: #fff;
    background-color: var(--color-primary-blue-500);
}

.k-grid td.k-state-focused {
    box-shadow: none;
}

.k-panelbar > .k-item > .k-link.k-state-focus,
.k-panelbar > .k-item > .k-link.k-state-focused,
.k-panelbar > .k-item > .k-link:focus,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focus,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focused,
.k-panelbar > .k-panelbar-header > .k-link:focus {
    box-shadow: none;
}

.k-panelbar-group {
    max-height: 125px !important;
    overflow-y: auto !important;
}

.k-panelbar-group.custom-panel-size {
    max-height: 230px !important;
}

.k-grid {
    border: none;
}

.k-grid .k-grid-header .k-header {
    /* border-bottom: none; */
    background-color: white;
    height: 30px;
}

.k-grid .k-grid-header .k-header a {
    color: #6b7280;
    font-size: 12px !important;
    font-weight: 500;
    margin-bottom: 0px;
    padding-right: 8px;
}

form.k-filter-menu .k-textbox,
form.k-filter-menu .k-widget {
    display: block;
}

/* filter */

.k-filter-menu .k-dropdown {
    border: 1px solid #b6b6b6;
    background-color: #e5e7eb;
}

.k-filter-menu .k-textbox,
.k-filter-menu .k-picker-wrap {
    border: 1px solid #b6b6b6;
}

.k-filter-menu .k-action-buttons .k-button {
    border: 1px solid #b6b6b6;
    background-color: #e5e7eb;
}

.k-filter-menu .k-action-buttons .k-button.k-primary {
    background-color: var(--color-primary-blue-500);
    color: white;
}

.searchdata input[type='text'],
.searchdata input[type='text']:focus {
    border: none;
    width: 100%;
    padding: 0px;
    border: none;
    box-shadow: none;
}
#statusForSendEmailModal.k-window-content {
    padding-bottom: 16px !important;
}
.bottomaction {
    position: sticky;
    bottom: 0px;
    z-index: 1000;
    overflow-y: auto;
    width: 100%;
    left: 0;
    box-shadow:
        0px -4px 6px -1px rgba(0, 0, 0, 0.1),
        0px -2px 4px -1px rgba(0, 0, 0, 0.06);
}

.widthzero {
    width: 0px;
}

.heightzero {
    overflow: hidden;
    height: 0px;
}

/* //model  */

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    outline: 0;
}

.modal.right .modal-dialog {
    position: fixed !important;
    margin: auto !important;
    width: 640px !important;
    height: 100% !important;
    -webkit-transform: translate3d(0%, 0, 0) !important;
    -ms-transform: translate3d(0%, 0, 0) !important;
    -o-transform: translate3d(0%, 0, 0) !important;
    transform: translate3d(0%, 0, 0) !important;
}

.modal.right .modal-body {
    padding: 0px;
}

.modal.right .modal-content {
    height: 100% !important;
    overflow-y: auto !important;
    padding: 0px;
}

.modal .modal-dialog {
    right: -640px !important;
    -webkit-transition:
        opacity 0.3s linear,
        right 0.3s ease-out !important;
    -moz-transition:
        opacity 0.3s linear,
        right 0.3s ease-out !important;
    -o-transition:
        opacity 0.3s linear,
        right 0.3s ease-out !important;
    transition:
        opacity 0.3s linear,
        right 0.3s ease-out !important;
}

.modal.right.fade.in .modal-dialog {
    right: 0 !important;
}

.modal-content {
    position: relative;
    background-color: #fff;
}

/**** Jignesh End ****/

.manageColumnBox {
    z-index: 1;
    position: absolute;
    display: none;
}

.active .manageColumnBox {
    z-index: 9999;
    position: absolute;
    display: block;
}

.manageColumnBox.active .w-full .absolute {
    margin-right: -35px;
}

.k-notification {
    border-radius: 8px;
    padding: 0px 0px;
    border-width: 0px;
}

/* #grid .k-grid-content {
    min-height: 600px;
    overflow: hidden;
} */

.k-dialog-titlebar {
    background-color: transparent !important;
}

.ck-editor__editable {
    min-height: 300px;
}

/* Add for attended filter width issue*/

/* .k-filter-menu.k-popup .k-filter-menu-container,
.k-grid-filter-popup.k-popup .k-filter-menu-container {
    width: auto;
} */

.hideprofiledropdown {
    z-index: 99;
}

.k-grid-header .k-grid-filter.k-state-active,
.k-grid-header .k-header-column-menu.k-state-active,
.k-grid-header .k-hierarchy-cell .k-icon.k-state-active {
    background-color: transparent !important;
}

.k-checkbox {
    border-radius: 4px;
}

.k-grid td > .k-checkbox {
    vertical-align: inherit;
}

[type='checkbox']:checked:hover,
[type='checkbox']:checked:focus,
[type='radio']:checked:hover,
[type='radio']:checked:focus {
    border-color: var(--color-primary-blue-500);
    background-color: var(--color-primary-blue-500);
}

.k-grid .k-detail-row > td:focus,
.k-grid .k-grid-pager.k-state-focused,
.k-grid .k-group-footer > td:focus,
.k-grid .k-grouping-row > td:focus,
.k-grid .k-master-row > td:focus,
.k-grid td.k-state-focused,
.k-grid th.k-state-focused,
.k-grid th:focus {
    box-shadow: none;
}

#dropdowncampus-list .k-list-scroller li {
    font-size: 13px;
    line-height: 30px;
    cursor: pointer;
}

/* .campus-filter .k-dropdown-wrap .k-input {
    line-height: 2.2em;
    font-size: 13px;
} */

.k-dropdown {
    width: 100%;
}
.campus-filter.k-widget.k-dropdown .k-dropdown-wrap {
    padding: 3px 0px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 10px;
    width: 100%;
}

.text-13 {
    font-size: 13px;
}

.k-filter-menu-container li span {
    font-size: 13px;
}

#panelbar .f-checkbox,
#Sendmailstudent .student_comm_log,
#student_comm_log {
    border-radius: 4px;
}

.k-filter-menu.k-popup .k-multicheck-wrap .k-check-all-wrap,
.k-grid-filter-popup.k-popup .k-multicheck-wrap .k-check-all-wrap {
    margin-bottom: 5px;
}

#panelbar .mt-1 {
    margin-top: 0.15rem;
}

#panelbar [type='text']:focus {
    --tw-ring-color: #fff;
}

#dropdowncampus-list {
    border-radius: 6px;
    margin-top: 8px;
}

.k-loading-mask .k-loading-image {
    background-image: none !important;
}

div.k-loading-image {
    display: none;
}

span.k-loading-text {
    text-indent: 0;
    top: 50%;
    left: 50%;
    /* background-color: #0F0; */
    z-index: 9999;
}

.k-loading-mask {
    z-index: 99999;
}

.k-widget.k-window.k-display-inline-flex.blur-modal {
    z-index: 9999;
}

.k-loading-color {
    z-index: 99;
}

.k-panelbar .k-panelbar-group > .k-item > .k-link:hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link:hover {
    cursor: pointer;
}

/* Start Tailwind Modal */

/* .titlebar-email-modal .k-window-title,
.titlebar-sms-modal .k-window-title {
    line-height: 3.25;
} */

#sendMailStudentModal,
#sendSmsStudentModal {
    padding: 0px;
}

#sendSmsStudentModal text {
    box-sizing: border-box !important;
}

#sendsmsto {
    min-height: 35px;
}

/* .titlebar-template-modal .k-window-title {
    line-height: 2.25;
} */

.titlebar-sms-modal .k-window-action {
    opacity: 1;
}

.titlebar-sms-modal .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}

.k-widget * {
    box-sizing: border-box !important;
}

/* End Tailwind Modal */

/* Start left-sidebar panel */

.sidebar-menu li > a {
    position: relative;
}

.sidebar-menu li > a > .pull-right-container {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translate(0, -50%);
}

.sidebar-menu .treeview-menu {
    display: none;
    list-style: none;
    padding: 0;
    margin: 0;
    padding-left: 10px;
}

/* End left-sidebar panel */

/* ======  filter box sticky  =====    */

.filterFooterBox {
    position: sticky;
}
[type='checkbox']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
    background: var(--color-primary-blue-500);
}
[type='checkbox']:checked:hover,
[type='checkbox']:checked:focus,
[type='radio']:checked:hover,
[type='radio']:checked:focus {
    background-color: var(--color-primary-blue-500) !important;
    box-shadow: none;
    outline: 2px solid var(--color-primary-blue-500);
    outline-offset: 2px;
}

/* multiselect filter */
.k-multiselect {
    border-width: 1px !important;
}
.k-multiselect-wrap {
    flex-direction: column !important;
}

.k-multiselect-wrap .k-input {
    width: 100% !important;
}
.k-multiselect-wrap .k-input:focus-within {
    box-shadow: none !important;
}

.k-popup .k-list .k-state-hover {
    background-color: #e5e7eb;
    cursor: pointer;
}

/* radio  select filter */
.k-filter-menu-container .k-checkbox-label {
    padding: 5px 0px !important;
    width: 100%;
}

.k-filter-menu-container .k-checkbox-label span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.k-filter-menu-container .k-checkbox-label .k-checkbox {
    margin-left: 4px !important;
    margin-right: 0px !important;
}

.k-filter-menu-container .k-checkbox-label:hover {
    background-color: #e5e7eb !important;
    border-radius: 4px;
}
.k-filter-menu.k-popup .k-multicheck-wrap {
    padding: 0px !important;
}

/* // ck editor border hide */
.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
    border-color: var(--ck-color-base-border);
    border-bottom: none;
    border-right: none;
    border-left: none;
}

.ck.ck-editor__editable:not(.ck-editor__nested-editable).ck-focused {
    border-bottom: none !important;
    border-right: none !important;
    border-left: none !important;
    box-shadow: none !important;
}

.ck .ck-editor__main {
    overflow-y: auto;
    max-height: 500px;
}

/* // text area border */

textarea {
    border: none !important;
}

textarea:focus {
    border: none !important;
}

/* dialog button css */

/* .k-dialog-buttongroup button:first-child,
.k-dialog-buttongroup button:last-child {
    padding: 8px 0px;
    border-radius: 10px;
} */

/* .k-window-content.k-dialog-content {
    padding: 24px 24px;
} */

.k-dialog-buttongroup button:first-child:focus {
    --tw-ring-offset-color: #ffffff;
    --tw-ring-offset-width: 2px;
    --tw-ring-opacity: 1;
    --tw-ring-color: rgba(156, 163, 175, var(--tw-ring-opacity));
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
        var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
        var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.k-dialog-buttongroup button:last-child:focus {
    --tw-ring-offset-color: #ffffff;
    --tw-ring-offset-width: 2px;
    --tw-ring-opacity: 1;
    --tw-ring-color: rgba(9, 109, 217, var(--tw-ring-opacity));
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
        var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
        var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.k-i-arrow-60-down::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
    /* margin-top: 3px; */
}

.k-panelbar-group.k-group {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
}
.k-panelbar-group.k-group::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
}

.cusInput:hover,
.cusInput:focus {
    border: 1px solid var(--color-primary-blue-500);
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

/* Tagify css */
.tagify__tag {
    border: 1px solid #ddd !important;
    border-radius: 30px !important;
}
.tagify__tag__removeBtn {
    font: 20px Arial !important;
    color: #9ca3af !important;
}
.tag-div {
    border-radius: 30px !important;
}
.tagify {
    --tag-bg: none;
    --tag-hover: #bae7ff !important;
    --tags-disabled-bg: #fff !important;
    --tags-border-color: #ddd !important;
    --tags-hover-border-color: #ddd !important;
    --tag-remove-btn-bg--hover: none !important;
    --tag-remove-btn-color: black;
    --tag-remove-bg: none !important;
}
.tagify {
    border: none !important;
}
