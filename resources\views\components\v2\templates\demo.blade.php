<div class="flex flex-col space-y-2 items-start justify-start w-full">
    <div class="inline-flex items-center justify-start w-full">
        <div class="flex space-x-2 items-center justify-between w-full group">
            <div class="flex gap-2 items-center justify-start">
                @if (empty($studentDetails[0]->profile_pic))
                <div class="rounded-full">
                    <div class='flex user-profile-pic w-16 h-16 !rounded-md bg-primary-blue-500 items-center'>
                        <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'
                            id="getStudentProfile">
                            {{ strtoupper($studentDetails[0]->first_name[0]) }}
                            {{ $studentDetails[0]->family_name != '' ? strtoupper($studentDetails[0]->family_name[0]) :
                            '' }}
                        </span>
                    </div>
                </div>
                @else
                <div class="w-16 h-16 rounded-full">
                    <img id="studProfilePic" class="w-16 h-16 flex-1 rounded-md object-cover object-top"
                        src="{{ $studentDetails[0]->profile_pic }}" alt="{{ $studentDetails[0]->first_name }}" />
                </div>
                @endif
                <div class="inline-flex flex-col items-start justify-center h-full">
                    <p class="text-base font-medium tracking-wide leading-normal text-gray-900" id="getStudentName">
                        {{ $studentDetails[0]->first_name . ' ' . $studentDetails[0]->family_name }}
                    </p>
                    <p class="text-xs leading-4 text-gray-400" id="getstudentID">
                        ID:{{ $studentDetails[0]->generated_stud_id }}</p>
                    <!--                            <div class="justify-end items-center gap-2 flex">
                                <div class="text-gray-400 text-xs font-normal leading-tight tracking-wide">ID:{{ $studentDetails[0]->generated_stud_id }}</div>
                                <div class="w-4 h-4 relative">
                                    <button id="syncDataShow">
                                        <img src="{{ asset('v2/img/arrow-sync.svg') }}" class="h-5 w-5" alt="Arrow Sync" />
                                    </button>
                                </div>
                            </div>-->
                </div>
            </div>
            <div class="hidden group-hover:block transition-all">
                <a href="#" class="btn-secondary text-gray-700 h-7 text-xs">Edit
                    Profile</a>
            </div>
        </div>
    </div>
</div>