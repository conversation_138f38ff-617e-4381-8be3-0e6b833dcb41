/* Start Mail */
var myEditor11;
var myEditor22;
var emailSubject = '';
var emailContent = '';
var letterSubject = '';
var letterContent = '';
var existEmailAttachmentId = [];
var existLetterAttachmentId = [];
var confirmEmail = false;
var confirmLatter = false;
let attachedFiles = [];
var alreadyDownloadedDivId = '#alreadyDownloaded';
var unitConfirmationDivId = '#unitConfirmationDiv';
var downloadRouteUrl = window.downloadRouteUrl || '/api/download-file-and-delete';

function generateDownloadUrl(filePath) {
    return downloadRouteUrl + '?filePath=' + encodeURIComponent(filePath);
}

function isFileDownloaded(filePath) {
    var downloadedFiles = JSON.parse(localStorage.getItem('downloadedFiles') || '[]');
    return downloadedFiles.includes(filePath);
}

function markFileAsDownloaded(filePath) {
    var downloadedFiles = JSON.parse(localStorage.getItem('downloadedFiles') || '[]');
    if (!downloadedFiles.includes(filePath)) {
        downloadedFiles.push(filePath);
        localStorage.setItem('downloadedFiles', JSON.stringify(downloadedFiles));
    }
}

function setDownloadLinkDownloaded($button) {
    $button
        .html(
            '<span class="text-sm font-medium leading-tight text-white">Downloaded & Deleted</span>'
        )
        .removeClass('downloading bg-primary-blue-500 hover:shadow-lg')
        .addClass('downloaded bg-gray-500 cursor-not-allowed')
        .attr('disabled', 'disabled')
        .prop('disabled', true);
}

$(document).on('click', '#downloadZipLink', function (e) {
    e.preventDefault();

    var filePath = $(this).data('file-path') || $(this).data('filepath');
    if (!filePath) {
        notificationDisplay('File path not found', '', 'error');
        return;
    }

    // Check if already downloaded (from localStorage or current state)
    if (
        $(this).hasClass('downloaded') ||
        $(this).attr('disabled') === 'disabled' ||
        isFileDownloaded(filePath)
    ) {
        notificationDisplay('File already downloaded & deleted', '', 'warning');
        return;
    }

    var $button = $(this);

    $button
        .html(
            '<i class="fa fa-spinner fa-spin text-white mr-2"></i><span class="text-sm font-medium leading-tight text-white">Downloading...</span>'
        )
        .prop('disabled', true)
        .addClass('downloading');

    var downloadUrl = generateDownloadUrl(filePath);
    window.location.href = downloadUrl;

    setTimeout(function () {
        markFileAsDownloaded(filePath);
        setDownloadLinkDownloaded($button);
        notificationDisplay('File downloaded successfully and deleted from server', '', 'success');
    }, 3000);
});

function loadHeaderDetails() {
    window.headerDetail = headerData;

    $(alreadyDownloadedDivId).kendoDialog({
        width: '400px',
        title: 'Certificate',
        content:
            "Certificate is already downloaded. Are you sure you want to re-download this Certificate ? <input type='hidden' name='id' id='id' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Download',
                primary: true,
                action: function () {
                    downloadStudentCertificate($(alreadyDownloadedDivId).find('#id').val());
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(alreadyDownloadedDivId),
        visible: false,
    });
    let isDownloadForUnit = '';
    $(unitConfirmationDivId).kendoDialog({
        width: '600px',
        title: 'Please confirm',
        content: `
        The certificate cannot be generated as the student has not completed the required
        number of core (<span id="coreUnitsCount"></span>) and elective units
        (<span id="electiveUnitsCount"></span>) for this qualification.
        Do you still want to generate the certificate? <input hidden name="isDownloadUnit" id="isDownloadUnit">`,
        actions: [
            { text: 'Close' },
            {
                text: 'Download',
                primary: true,
                action: function () {
                    downloadCertificate($('#isDownloadUnit').val());
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(unitConfirmationDivId),
        visible: false,
    });

    $('#sms_template').kendoDropDownList({
        dataTextField: 'name',
        dataValueField: 'id',
        dataSource: window.headerDetail.smsTemplateData,
    });

    var insertEmailTemplateSidebarMenu = new kendo.data.HierarchicalDataSource({
        data: window.headerDetail.mailTemplateData,
        // transport: window.cdetail.maildata,
        schema: {
            data: 'data',
            model: {
                id: 'id',
                hasChildren: 'hasChildren',
                children: 'sub_list',
            },
        },
    });

    var insertLetterTemplateSidebarMenu = new kendo.data.HierarchicalDataSource({
        data: window.headerDetail.letterTemplateData,
        schema: {
            data: 'data',
            model: {
                id: 'id',
                hasChildren: 'hasChildren',
                children: 'sub_list',
            },
        },
    });

    $('#insertEmailTemplatePanelBar').kendoPanelBar({
        template: kendo.template($('#email-panelbar-template').html()),
        dataSource: insertEmailTemplateSidebarMenu,
    });

    $('#insertLetterTemplatePanelBar').kendoPanelBar({
        template: kendo.template($('#letter-panelbar-template').html()),
        dataSource: insertLetterTemplateSidebarMenu,
    });

    // TODO: STUDENT PROFILE OPT -> move to payment
    const $templateElement = $('#student-payment-email-panelbar-template');
    const $panelBarElement = $('#insertTemplatePanelBarStudentPayment');
    if ($templateElement.length && $panelBarElement.length) {
        $panelBarElement.kendoPanelBar({
            template: kendo.template($templateElement.html()),
            dataSource: insertEmailTemplateSidebarMenu,
        });
    } else {
        // Log specific errors for missing elements
        // if (!$templateElement.length) {
        //     console.error(
        //         "Template element with id 'student-payment-email-panelbar-template' not found.",
        //     );
        // }
        // if (!$panelBarElement.length) {
        //     console.error(
        //         "PanelBar element with id 'insertTemplatePanelBarStudentPayment' not found.",
        //     );
        // }
    }

    setDropdownOnTabs(window.headerDetail.headerDropDownCourseData);
    showDivsWithDelay();
    if (selectedTabText === 'documents') {
        manageTabData();
    } else if (selectedTabText === 'activitylog') {
        manageActivityLogTab();
    }
}

CKEDITOR.ClassicEditor.create(document.querySelector('#comments'), {
    ckfinder: {
        uploadUrl: site_url + 'api/upload-file-email-text-editor',
    },
    mention: {
        feeds: [
            {
                marker: '{',
                feed: window.tagJson,
                minimumCharacters: 0,
            },
        ],
    },
    removePlugins: [
        'RealTimeCollaborativeComments',
        'RealTimeCollaborativeTrackChanges',
        'RealTimeCollaborativeRevisionHistory',
        'PresenceList',
        'Comments',
        'TrackChanges',
        'TrackChangesData',
        'RevisionHistory',
        'Pagination',
        'WProofreader',
        'MathType',
    ],
})
    .then((editor) => {
        editor.ui.view.editable.element.style.height = '300px';
        myEditor11 = editor;
    })
    .catch((error) => {
        console.error(error);
    });

CKEDITOR.ClassicEditor.create(document.querySelector('#letters'), {
    ckfinder: {
        uploadUrl: site_url + 'api/upload-file-email-text-editor',
    },
    mention: {
        feeds: [
            {
                marker: '{',
                feed: window.tagJson,
                minimumCharacters: 0,
            },
        ],
    },
    removePlugins: [
        'RealTimeCollaborativeComments',
        'RealTimeCollaborativeTrackChanges',
        'RealTimeCollaborativeRevisionHistory',
        'PresenceList',
        'Comments',
        'TrackChanges',
        'TrackChangesData',
        'RevisionHistory',
        'Pagination',
        'WProofreader',
        'MathType',
    ],
})
    .then((editor) => {
        editor.model.document.on('change:data', () => {
            const content = editor.getData();
            if (content.length > 0) {
                $(document).find('.preview-placeholder').hide();
                generateContentToPdf(content);
                $(document).find('.embed-content').show();
            } else {
                $(document).find('.preview-placeholder').show();
                $(document).find('.embed-content').hide();
            }
        });
        editor.ui.view.editable.element.style.height = 'auto';
        myEditor22 = editor;
    })
    .catch((error) => {
        ``;
        console.error(error);
    });

$('#sendMailStudentModal').kendoWindow(defaultWindowSlideFormat('Send Email', 60));
$('#loaderForEmail').kendoWindow(openCenterWindowWithHeight('Sending Email'));
$('#statusForSendEmailModal').kendoWindow(openCenterWindow('Email Send Status', 33, 15, 30));
$('#sendSmsStudentModal').kendoWindow(defaultWindowSlideFormat('Send SMS', 60));
$('#emailTemplatesModal').kendoWindow(openCenterWindow('Email Templates', 66, 12, 17));

$('#letterParameterModal').kendoWindow({
    title: 'Letter Parameter Replace',
    width: '66%',
    height: '76%',
    actions: ['close'],
    draggable: false,
    resizable: false,
    modal: true,
    position: {
        top: '12%',
        left: '17%',
    },
    animation: false,
    // animation: {
    //     close: {
    //         effects: "fade:out",
    //     },
    // },
});
$('#statusForSendEmailModal')
    .data('kendoWindow')
    .bind('close', function (e) {
        confirmEmail = true;
        $('.resetEmail').trigger('click');
        $('#sendMailStudentModal').data('kendoWindow').close();
        $('#selectedStudents').trigger('click');
    });

$('#sendMailStudentModal')
    .data('kendoWindow')
    .bind('close', function (e) {
        if (!confirmEmail) {
            kendoWindowOpen('#closeModelConformation');
            confirmEmail = false;
            e.preventDefault();
        }
    });

$('#closeModelConformation').kendoWindow(openCenterWindow('Discard Email'));

$('body').on('click', '.discard-yes', function (e) {
    confirmEmail = true;
    $('#closeModelConformation').data('kendoWindow').close();
    $('#sendMailStudentModal').data('kendoWindow').close();
    $('#selectedStudents').trigger('click');
    $('.resetEmail').trigger('click');
});

$('body').on('click', '.discard-no', function (e) {
    // $("#closeModelConformation").data("kendoWindow").close();
    $('#' + $(this).parents('.k-window-content').attr('id'))
        .data('kendoWindow')
        .close();
});

// TODO:OPTIMIZE -> Move to request
// var whitelistForReplyToEmail = {};
// fetch(site_url + "api/get-college-email-list")
//     .then((RES) => RES.json())
//     .then(function (newWhitelist) {
//         whitelistForReplyToEmail = newWhitelist.data;
//     });

// $("#sendMailStudentModal")
//     .data("kendoWindow")
//     .bind("open", function (e) {
//         attachedFiles = [];
//         confirmEmail = false;
//         setReplyToEmailSetup("#reply_to_email", whitelistForReplyToEmail);
//         $("#sendMailStudentModal")
//             .parent()
//             .addClass("student-custom-modal-wrapper");
//     });
let whitelistForReplyToEmail = {};

$('#sendMailStudentModal')
    .data('kendoWindow')
    .bind('open', async function (e) {
        attachedFiles = [];
        confirmEmail = false;

        if (Object.keys(whitelistForReplyToEmail).length === 0) {
            try {
                const response = await fetch(site_url + 'api/get-college-email-list');
                const newWhitelist = await response.json();
                whitelistForReplyToEmail = newWhitelist.data;
            } catch (error) {
                console.error('Failed to fetch email list:', error);
            }
        }

        setReplyToEmailSetup('#reply_to_email', whitelistForReplyToEmail);

        $('#sendMailStudentModal').parent().addClass('student-custom-modal-wrapper');
    });

addModalClassToWindows(['#loaderForEmail', '#statusForSendEmailModal', '#emailTemplatesModal']);

$(document).ready(function (e) {
    loadHeaderDetails();
    // ajaxActionV2(
    //     "api/get-header-data",
    //     "POST",
    //     { enroll_option: 1, student_id: studentId },
    //     function (response) {
    //         window.headerDetail = response.data;

    //         $("#sms_template").kendoDropDownList({
    //             dataTextField: "name",
    //             dataValueField: "id",
    //             dataSource: window.headerDetail.smsTemplateData,
    //         });

    //         var insertEmailTemplateSidebarMenu =
    //             new kendo.data.HierarchicalDataSource({
    //                 data: window.headerDetail.mailTemplateData,
    //                 // transport: window.cdetail.maildata,
    //                 schema: {
    //                     data: "data",
    //                     model: {
    //                         id: "id",
    //                         hasChildren: "hasChildren",
    //                         children: "sub_list",
    //                     },
    //                 },
    //             });

    //         var insertLetterTemplateSidebarMenu =
    //             new kendo.data.HierarchicalDataSource({
    //                 data: window.headerDetail.letterTemplateData,
    //                 schema: {
    //                     data: "data",
    //                     model: {
    //                         id: "id",
    //                         hasChildren: "hasChildren",
    //                         children: "sub_list",
    //                     },
    //                 },
    //             });

    //         $("#insertEmailTemplatePanelBar").kendoPanelBar({
    //             template: kendo.template($("#email-panelbar-template").html()),
    //             dataSource: insertEmailTemplateSidebarMenu,
    //         });

    //         $("#insertLetterTemplatePanelBar").kendoPanelBar({
    //             template: kendo.template($("#letter-panelbar-template").html()),
    //             dataSource: insertLetterTemplateSidebarMenu,
    //         });

    //         // TODO: STUDENT PROFILE OPT -> move to payment
    //         const $templateElement = $(
    //             "#student-payment-email-panelbar-template",
    //         );
    //         const $panelBarElement = $("#insertTemplatePanelBarStudentPayment");
    //         if ($templateElement.length && $panelBarElement.length) {
    //             $panelBarElement.kendoPanelBar({
    //                 template: kendo.template($templateElement.html()),
    //                 dataSource: insertEmailTemplateSidebarMenu,
    //             });
    //         } else {
    //             // Log specific errors for missing elements
    //             // if (!$templateElement.length) {
    //             //     console.error(
    //             //         "Template element with id 'student-payment-email-panelbar-template' not found.",
    //             //     );
    //             // }
    //             // if (!$panelBarElement.length) {
    //             //     console.error(
    //             //         "PanelBar element with id 'insertTemplatePanelBarStudentPayment' not found.",
    //             //     );
    //             // }
    //         }
    //         setDropdownOnTabs(window.headerDetail.headerDropDownCourseData);

    //         // const activetab = getQueryParam("activetab")
    //         //     ? getQueryParam("activetab")
    //         //     : "summary";
    //         //     console.log("startingpoint", activetab);
    //         // loadAjaxTabContent(
    //         //     activetab,
    //         //     window.headerDetail.headerDropDownCourseData,
    //         // );
    //     },
    // );
});

$('body').on('click', '.sendEmailBtn', function () {
    myEditor11.setData('');
    setFromEmail('#sendMailStudentModal');
    $(document).find('.studCourseIds').val(selectedStudCourseID);
    $(document).find('.email_subject').val('');
    $(document).find('.email_attachment').val('');
    $(document).find('.selected_file').text('');
    var input = document.querySelector('input[name=email_bcc]');
    let tagifyEmailBCC = new Tagify(input, {
        pattern:
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        callbacks: {
            invalid: onInvalidTag,
        },
        templates: {
            tag: tagTemplate,
        },
    });
    tagifyEmailBCC.removeAllTags();
    var input = document.querySelector('input[name=email_cc]');
    let tagifyEmailCC = new Tagify(input, {
        pattern:
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        callbacks: {
            invalid: onInvalidTag,
        },
        templates: {
            tag: tagTemplate,
        },
    });
    tagifyEmailCC.removeAllTags();
    kendoWindowOpen('#sendMailStudentModal');
});

function onInvalidTag(e) {
    notificationDisplay('Enter email only ', '', 'error');
}

function tagTemplate(tagData) {
    return `<tag title="${tagData.value}" contenteditable='false' spellcheck='false' tabIndex="-1" class="tagify__tag bg-gray-300">
                    <x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
                    <div class='flex items-center tag-div'>&nbsp;
                    <div class='text-xs leading-normal text-gray-900 pl-1'>${tagData.value}</div>
                    </div>
                </tag>`;
}

$('body').on('click', '#insertEmailTemplate', function () {
    $('#inputString').val('').trigger('keyup');
    $(document).find('.email_subject').html('');
    $(document).find('.email_content').html('');
    $(document).find('.exist_attachment').html('');
    kendoWindowOpen('#emailTemplatesModal');
});

$('body').on('click', '#useEmailTemplateBtn', function () {
    $(document).find('#email_subject').val(emailSubject);
    $(document).find('.isTemplateSelect').text(emailSubject);
    $(document).find('.email_subject_txt').val(emailSubject);
    myEditor11.setData(emailContent);
    $(document).find('.existing_attachment_id').val(existEmailAttachmentId);
    $('#emailTemplatesModal').data('kendoWindow').close();
    //$(document).find('#emailTemplatesModal').getKendoWindow().close();
});

$('body').on('click', '.email_template', function () {
    let templateID = $(this).attr('data-id');
    myEditor11.setData('');

    ajaxActionV2('api/get-mail-content', 'POST', { template_id: templateID }, function (response) {
        emailSubject = response.data[0].email_subject;
        emailContent = response.data[0].content;

        let emailAttachment = kendo.template($('#attachmentList').html())({
            files: response.data[0].files,
        });
        $(document).find('.email_template_id').val(templateID);
        $(document).find('.exist_attachment_div').show();
        $(document).find('.exist_attachment').html('').html(emailAttachment);
        manageExistEmailAttachmentId();

        $(document).find('.email_subject').html('').html(emailSubject);
        $(document).find('.email_content').html('').html(emailContent);
    });
});

$('body').on('click', '.existing_attachment', function () {
    $(this).closest('div').remove();
    manageExistEmailAttachmentId();
});

$('body').on('click', '.ccMail', function () {
    $(document).find('#emailccbox').toggle();
});

$('body').on('click', '.bccMail', function () {
    $(document).find('#emailbccbox').toggle();
});

$('body').on('click', '.studentCommLogDefault', function () {
    let ckBox = $(this).prev();
    let ckStatus = !ckBox.is(':checked');
    ckBox.prop('checked', ckStatus);
});

$('#email_attachment').change(function (event) {
    const files = event.target.files;
    const attachedFilesContainer = $('#attachedFilesContainer');

    $.each(files, function (index, file) {
        const fileName = file;
        // Create a new element to display the file name
        const fileElement = $('<div>').addClass(
            'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
        );
        const fileNameElement = $('<span>')
            .addClass('truncate')
            .attr('title', fileName.name)
            .text(fileName.name);
        const removeButton = $('<button>').addClass('ml-2 text-gray-500 hover:text-red-500');

        const removeIcon = $(
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
        ).addClass('cursor-pointer');

        // Add a button to remove the file
        removeIcon.click(function () {
            // Remove the file from the list, array, and update UI
            fileElement.remove();
            attachedFiles.splice(attachedFiles.indexOf(fileName), 1);
            $('#email_attachment').val(''); // Reset the file input
        });
        // Append file name and remove button to the container
        removeButton.append(removeIcon);
        fileElement.append(fileNameElement, removeButton);
        attachedFilesContainer.addClass('mt-2').append(fileElement);

        // Add the file name to the array
        attachedFiles.push(fileName);
    });
});

$('body').on('click', '.resetEmail', function () {
    $(document).find('#comments').text('');
    $('.isTemplateSelect').text('');
    $('#studentCommLogDefault').prop('checked', false);
    $('#email_subject').val('');
    $('.existing_attachment_id').val('');
    $('.email_template_id').val('');
    $('.email_template_id').val('');
    $('.selected_file').text('');
    $('#email_cc').html('');
    $('#email_bcc').html('');
    $(document).find('#emailccbox').hide();
    $(document).find('#emailbccbox').hide();
    myEditor11.setData('');
});

$('body').on('click', '.sendMail', function () {
    let validator = $('#emailTemplateAddForm').kendoValidator().data('kendoValidator');
    if (!validator.validate()) {
        return false;
    }

    let modalDiv = $(this).parent('.k-window');
    let formTag = $('#emailTemplateAddForm');
    let totalImageCount = emailTotalAttachmentAllowed;
    let formData = new FormData(formTag[0]);

    if (formTag.find('#email_subject').val().length == 0) {
        notificationDisplay('Email subject is required', '', 'error');
        return false;
    }
    if (myEditor11.getData().length == 0) {
        notificationDisplay('Email content is required', '', 'error');
        return false;
    }

    if (attachedFiles.length <= totalImageCount) {
        $.each(attachedFiles, function (index, fileName) {
            formData.append('attachment_file[]', fileName);
        });
    } else {
        notificationDisplay(
            'Please select Only ' + totalImageCount + ' attachments files',
            '',
            'error'
        );
        return false;
    }
    formData.append('email_content', myEditor11.getData());
    formData.append('log_type', 'offer');
    if (formData.get('email_cc')) {
        let tags_cc = JSON.parse(formData.get('email_cc'));
        formData.set('email_cc', tags_cc.map((item) => item.value).toString());
    }
    if (formData.get('email_bcc')) {
        let tags_bccc = JSON.parse(formData.get('email_bcc'));
        formData.set('email_bcc', tags_bccc.map((item) => item.value).toString());
    }
    if (formData.get('reply_to_email')) {
        try {
            let parsedRes = JSON.parse(formData.get('reply_to_email'));
            if (Array.isArray(parsedRes) && parsedRes.length > 0 && parsedRes[0].value) {
                formData.set('reply_to_email', parsedRes[0].value);
            }
        } catch (e) {
            formData.set('reply_to_email', '');
        }
    }
    modalDiv.addClass('blur-modal');
    ajaxCallWithMethodFileKendoV2(
        'Email',
        'api/student-send-email',
        formData,
        'POST',
        function (output) {
            modalDiv.removeClass('blur-modal');
        }
    );
});
/* End Email */

/* Start SMS */
function sendSmsUsingApi(phoneNumber, smsContent) {
    //console.log(phoneNumber + ' | ' + smsContent)
    if (
        window.location.hostname == 'www.rtomanager.dev' ||
        window.location.origin == 'http://www.rtomanager.dev'
    ) {
        //console.log('You Send message from local system message not send.');
        notificationDisplay('SMS send successfully.', 'SMS Send', 'success');
        return false;
    } else {
        var xhr = new XMLHttpRequest();
        xhr.open(
            'GET',
            sendSmsUrl +
                '?apiKey=' +
                SMSApiKey +
                '&to=' +
                phoneNumber +
                '&content=' +
                smsContent +
                '',
            true
        );
        xhr.onreadystatechange = function () {
            if (xhr.readyState == 4 && xhr.status == 200) {
                //console.log('success');
            }
            var data = JSON.parse(xhr.response);
            if (data.messages[0].accepted == false) {
                var errorMsg =
                    data.messages[0].error == 'null' || data.messages[0].error == ''
                        ? 'Invalid destination address'
                        : data.messages[0].error;
                notificationDisplay(errorMsg, 'SMS Error', 'error');
            } else {
                notificationDisplay('SMS send successfully.', 'SMS Send', 'success');
            }
        };
        xhr.send();
        return true;
    }
}

$('body').on('click', '.sendSmsBtn', function () {
    $(document).find('.studCourseIds').val(selectedStudCourseID);
    kendoWindowOpen('#sendSmsStudentModal');
    $('#sendSmsStudentModal').parent().addClass('student-custom-modal-wrapper');
    setTimeout(function () {
        $('#sms_template').trigger('change');
    }, 500);
});

$('body').on('change', '#sms_template', function () {
    let template_id = $(this).val();
    if (template_id != '' && template_id != 'undefined') {
        let dataArr = { template_id: template_id };
        let url = site_url + 'api/get-sms-template-contain';
        ajaxcallwithMethod(url, dataArr, 'POST', function (output) {
            $(document).find('#sms_text').val(output.data);
        });
    }
});

$('body').on('click', '#resetSMS', function () {
    $(document).find('#sms_text').val('');
});

$('body').on('click', '#sendMsg', function () {
    let sms_text = $('.sms_text').val();
    let dataArr = {
        is_send: true,
        id: studentId,
        student_course_id: selectedStudCourseID,
        message: sms_text,
    };
    let modalDiv = $(this).parent('.k-window');
    let url = site_url + 'api/student-add-sms-details';
    modalDiv.addClass('blur-modal');
    ajaxcallwithMethod(url, { dataArr }, 'POST', function (output) {
        notificationDisplay(output.message, 'Send SMS', output.status);
        modalDiv.removeClass('blur-modal');
    });
});
/* SMS End */

/* Issue letter Start */
$('#issueLetterStudentModal').kendoWindow(defaultWindowSlideFormat('Generate Warning Letter', 86));
$('#loaderForLetter').kendoWindow(openCenterWindow('Generate Warning Letter', 30, 30, 30, 35));
$('#statusForSendLetterModal').kendoWindow(openCenterWindow('Letter Status', 50, 25, 30, 35));
$('#letterTemplatesModal').kendoWindow(openCenterWindow('Letter Templates', 66, 12, 17));

// kendowindowOpen("#loaderForLetter");

addModalClassToWindows([
    '#letterTemplatesModal',
    '#loaderForLetter',
    '#statusForSendLetterModal',
    '#closeModelConformation',
]);

$(document).on('click', '#insertLetterTemplate', function () {
    $('#searchHeaderLetterTemplate').val('').trigger('keyup');
    kendoWindowOpen('#letterTemplatesModal');
});

$(document).on('click', '#useLetterTemplateBtn', function (e) {
    e.preventDefault();
    startAjaxLoader();
    ajaxActionV2(
        'api/get-letter-content-with-tag-value',
        'POST',
        {
            letterContent: letterContent,
            student_id: studentId,
            student_course_id: $('#letter_course_list').data('kendoDropDownList').value(),
        },
        function (response) {
            $(document).find('#letter_subject').val(letterSubject);
            $(document).find('.letter_subject_txt').val(letterSubject);
            myEditor22.setData(response.data);
            $(document).find('.letter_existing_attachment_id').val(existLetterAttachmentId);
            $(document).find('.preview-placeholder').hide();
            // generateContentToPdf(response.data);
            $(document).find('.embed-content').show();
            $('#letterTemplatesModal').data('kendoWindow').close();
            let dataItems = {
                letter_template_id: $('.letter_template_id').val(),
                student_id: studentId,
                course_id: selectedStudCourseID,
            };
            setLetterParameterInModel(dataItems);
            stopAjaxLoader();
        }
    );

    //$(document).find('#letterTemplatesModal').getKendoWindow().close();
});
function setLetterParameterInModel(dataItems) {
    $('#letterParameterModal').find('#student_id').val(dataItems.student_id);
    $('#letterParameterModal').find('#course_id').val(dataItems.course_id);
    startAjaxLoader();
    ajaxActionV2('api/get-letter-parameter-list', 'POST', dataItems, function (response) {
        if (response.data.parameter.length > 0) {
            displayParameterList(response.data.parameter);
            kendowindowOpen('#letterParameterModal');
        }
        startAjaxLoader();
    });
}
function displayParameterList(parameter) {
    if (parameter.length > 0) {
        // $("#addDeptModal").modal("show");
        var html = '';
        $.each(parameter, function (i, item) {
            html +=
                '<div class="inline-flex flex-col space-y-1 items-start justify-start w-full">' +
                '<label class="k-label k-form-label">' +
                item +
                ' : <span class="required-field">*</span></label>' +
                '<div class="inline-flex flex-col space-y-1 items-start justify-start w-full">' +
                '<input name="' +
                item +
                '" data-name="' +
                item +
                '" type="text" class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full newParameter" placeholder="Enter ' +
                item +
                ' Value">' +
                '</div>' +
                '</div>';
        });
        $('#parameterList').html(html);
    }
}
$('body').on('click', '.replaceParameter', function () {
    assignParameterValue();
});
function assignParameterValue() {
    var arr = [];
    var flag = true;
    $('.newParameter').each(function () {
        if ($(this).val()) {
            $(this).removeClass('error');
            arr.push({
                key: $(this).attr('data-name'),
                value: $(this).val(),
            });
        } else {
            flag = false;
            $(this).addClass('error');
        }
    });
    if (flag) {
        replaceInputParameterValue(arr);
    }
}

function replaceInputParameterValue(arr) {
    let student_id = $('#letterParameterModal').find('#student_id').val();
    let course_id = $('#letterParameterModal').find('#course_id').val();
    let letterId = $('.letter_template_id').val();
    var dataItems = {
        letter_template_id: letterId,
        student_id: student_id,
        course_id: course_id,
        arr: arr,
    };
    if (letterId > 0) {
        ajaxActionV2('api/get-replace-parameter-content', 'POST', dataItems, function (response) {
            myEditor22.setData(response.data.content);
            $('#letterParameterModal').data('kendoWindow').close();
        });
    }
}
// function generateContentToPdf(content) {
//     let dataArr = [{'contentText': content} ];
//     let encodedDataArr = encodeURIComponent(JSON.stringify(dataArr));
//     let url = site_url + "api/generate-content-to-pdf?data="+encodedDataArr;
//     $(".embed-content").show();
//     // $(".studentContentPreview").attr("src", url);

//     getPdf(url);
// }
function generateContentToPdf(content) {
    let dataArr = [{ contentText: content }];

    // Convert to JSON and send as data in the request body
    let url = site_url + 'api/generate-content-to-pdf';

    $('.embed-content').show();

    $.ajax({
        url: url,
        type: 'POST',
        data: {
            data: JSON.stringify(dataArr),
        },
        xhrFields: {
            responseType: 'blob', // Important for binary data
        },
        success: function (response) {
            getPdf(response);
        },
        error: function (error) {
            console.log('Error: ', error);
        },
    });
}
$('body').on('click', '.letter_existing_attachment', function () {
    $(this).closest('div').remove();
    manageExistLetterAttachmentId();
});
$('body').on('click', '.closeModelLetterParameterModal', function (e) {
    $('#letterParameterModal').data('kendoWindow').close();
});
$(document).on('click', '.letter_template', function () {
    let templateID = $(this).attr('data-id');
    myEditor22.setData('');

    ajaxActionV2(
        'api/get-letter-content',
        'POST',
        { template_id: templateID },
        function (response) {
            letterSubject = response.data[0].letter_name;
            letterContent = response.data[0].content;

            let letterAttachment = kendo.template($('#letterAttachmentList').html())({
                files: response.data[0].files,
            });
            $(document).find('.letter_exist_attachment_div').show();
            $(document).find('.letter_exist_attachment').html('').html(letterAttachment);
            manageExistLetterAttachmentId();

            $(document).find('.letter_template_id').val(templateID);
            $(document).find('.letter_subject').html('').html(letterSubject);
            $(document).find('.letter_content').html('').html(letterContent);
            $(document).find('.preview-placeholder').hide();
            $(document).find('.embed-content').show();
        }
    );
});

$(document).on('click', '.issueLetter', function () {
    kendoWindowOpen('#issueLetterStudentModal');
    $(document).find('.studCourseIds').val(selectedStudCourseID);
    $(document).find('.commLogForLetterDiv').show();
    $(document).find("input[name='is_attendance_warning']").remove();
    if (window.innerWidth > 1800) {
        getContentWidth('#issueLetterStudentModal');
    }
    $(document).find('#letter_course_list').data('kendoDropDownList').value(selectedStudCourseID);
    console.log(selectedStudCourseID);
    //$(document).find("#issueLetterStudentModal").find('form').find("input[name='is_attendance_warning']").remove();
});

$(document).on('click', '.studentCommLogForLetter', function () {
    let ckBox = $(this).prev();
    let ckStatus = !ckBox.is(':checked');
    ckBox.prop('checked', ckStatus);
});

$(document).on('click', '.sendLetterEmail', function () {
    let formTag = $('#issueEmailForm');
    let modalDiv = $(this).parent('.k-window');
    let formData = new FormData(formTag[0]);

    if (myEditor22.getData().length == 0) {
        notificationDisplay('Letter content is required', '', 'error');
        return false;
    }

    formData.append('letter_content', myEditor22.getData());
    formData.append('log_type', 'offer');
    formData.append('student_id', studentId);

    modalDiv.addClass('blur-modal');
    ajaxCallWithMethodFileKendoV2(
        'sendLetter',
        'api/student-issue-letter-email',
        formData,
        'POST',
        function (output) {
            modalDiv.removeClass('blur-modal');
            $(document).find('.letterZipDownload').hide();
        }
    );
});

$(document).on('click', '.generateLetter', function () {
    let formTag = $('#issueEmailForm');
    let modalDiv = $(this).parent('.k-window');
    let formData = new FormData(formTag[0]);

    if (myEditor22.getData().length == 0) {
        notificationDisplay('Letter content is required', '', 'error');
        return false;
    }

    formData.append('letter_content', myEditor22.getData());
    formData.append('log_type', 'offer');
    formData.append('student_id', studentId);

    modalDiv.addClass('blur-modal');
    ajaxCallWithMethodFileKendoV2(
        'generateLetter',
        'api/student-issue-letter-generate',
        formData,
        'POST',
        function (output) {
            modalDiv.removeClass('blur-modal');
        }
    );
});

$(document).on('click', '.generateLetterWithWatermark', function () {
    if (myEditor22.getData().length == 0) {
        notificationDisplay('Letter content is required', '', 'error');
        return false;
    }

    ajaxActionV2(
        'api/verify-letter-watermark',
        'POST',
        { college_id: collegeId },
        function (response) {
            if (response.status == 'success') {
                generateLetterWithWatermark();
            } else {
                notificationDisplay(response.message, '', 'error');
                return false;
            }
        }
    );
});

$(document).on('click', '.closeAndRefresh', function () {
    let type = $(this).attr('data-type');
    $(`#statusForSend${type}Modal`).data('kendoWindow').close();
});

$(document).on('click', function (e) {
    if (!$(e.target).closest('.courseActionBtn, .courseActionMenu').length) {
        // Clicked outside: Hide the menu
        $('.courseActionMenu').addClass('hidden');
    }
});

$(document).on('click', '.courseActionBtn', function (e) {
    e.stopPropagation(); // Prevent bubbling to document
    // Toggle visibility of the menu
    if ($('.courseActionMenu').hasClass('block')) {
        $('.courseActionMenu').removeClass('block');
    } else {
        $('.courseActionMenu').addClass('block');
    }
});

$(document).on('click', function (e) {
    if (!$(e.target).closest('.moreActionBtn, .moreActionMenu').length) {
        // Clicked outside: Hide the menu
        $('.moreActionMenu').addClass('hidden');
    }
});

$(document).on('click', '.moreActionBtn', function (e) {
    e.stopPropagation(); // Prevent bubbling to document
    // Toggle visibility of the menu
    if ($('.moreActionMenu').hasClass('block')) {
        $('.moreActionMenu').removeClass('block');
    } else {
        $('.moreActionMenu').addClass('block');
    }
});

function generateLetterWithWatermark() {
    let formTag = $('#issueEmailForm');
    let modalDiv = $(this).parent('.k-window');
    let formData = new FormData(formTag[0]);

    formData.append('letter_content', myEditor22.getData());
    formData.append('log_type', 'offer');
    formData.append('student_id', studentId);
    formData.append(
        'student_course_id',
        $('#letter_course_list').data('kendoDropDownList').value()
    ); // Selected Course from dropdown
    modalDiv.addClass('blur-modal');
    console.log('hereader action');
    ajaxCallWithMethodFileKendoV2(
        'generateLetter',
        'api/student-issue-letter-generate-with-watermark',
        formData,
        'POST',
        function (output) {
            modalDiv.removeClass('blur-modal');
        }
    );
}

function getSelectedStudentTags() {
    let selectedStudentTags = [];
    $.each(selectedStudent, function (index, row) {
        selectedStudentTags.push({
            id: row.id,
            value: row.id,
            name: row.name,
            profile_pic: row.profile_pic,
            contact: row.contact,
        });
    });
    return selectedStudentTags;
}

function zipDownload(formData) {
    ajaxActionV2('api/student-issue-letter-zip', 'POST', formData, function (response) {
        let zipDownloadDiv = $(document).find('.letterZipDownload');
        zipDownloadDiv.html('');
        if (response.status == 'success') {
            let downloadURL = site_url + response.data.download_path;
            let ApiUrl = site_url + response.data.url + '?filePath=' + response.data.download_path;
            let mainUrl = response.data.url == '' ? downloadURL : ApiUrl;
            let downloadIcon = site_url + '/v2/img/download_arrow.svg';
            let zipDownloadHtml = `<button class="ml-3 flex justify-center w-auto h-6 px-3 py-1 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500">
                                        <a download href="${mainUrl}" class="flex space-x-2 items-center justify-start" title="Download Zip File">
                                            <p class="text-sm font-medium leading-tight text-white">Download</p>
                                        </a>
                                    </button>`;
            zipDownloadDiv.html(zipDownloadHtml);
        }
    });
}

function manageExistEmailAttachmentId() {
    let existDocDiv = $(document).find('.existing_attachment');
    existEmailAttachmentId = [];
    if (existDocDiv.length == 0) {
        let noEmailAttachmentHtml =
            '<p class="text-xs leading-tight text-center text-gray-800"> No attachments available </p>';
        $(document).find('.exist_attachment').html('').html(noEmailAttachmentHtml);
    } else {
        existDocDiv.each(function () {
            existEmailAttachmentId.push($(this).attr('data-file-id'));
        });
    }

    $(document).find('.existing_attachment_id').val(existEmailAttachmentId);

    const attachedFilesContainer = $('#templateFilesContainer');
    attachedFilesContainer.html('');
    $.each(existDocDiv, function () {
        const that = $(this);
        const fileName = $(this).attr('data-file-name');

        // Create a new element to display the file name
        const fileElement = $('<div>').addClass(
            'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
        );
        const fileNameElement = $('<span>')
            .addClass('truncate')
            .attr('title', fileName)
            .text(fileName);
        const removeButton = $('<button>').addClass('ml-2 text-gray-500 hover:text-red-500');

        const removeIcon = $(
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
        ).addClass('cursor-pointer');

        // Add a button to remove the file
        removeIcon.click(function () {
            fileElement.remove();
            that.closest('div').remove();
            manageExistEmailAttachmentId();
            return false;
        });
        // Append file name and remove button to the container
        removeButton.append(removeIcon);
        fileElement.append(fileNameElement, removeButton);
        attachedFilesContainer.addClass('mt-2').append(fileElement);
    });
}

function manageExistLetterAttachmentId() {
    let existDocDiv = $(document).find('.letter_existing_attachment');
    existLetterAttachmentId = [];
    if (existDocDiv.length == 0) {
        let noLetterAttachmentHtml =
            '<p class="text-xs leading-tight text-center text-gray-800"> No attachments available </p>';
        $(document).find('.letter_exist_attachment').html('').html(noLetterAttachmentHtml);
    } else {
        existDocDiv.each(function () {
            existLetterAttachmentId.push($(this).attr('data-file-id'));
        });
    }
}

function ajaxCallWithMethodFileKendoV2(typeName, url, data, method, callback) {
    kendo.ui.progress($(document), false);
    let type = typeName == 'generateLetter' || typeName == 'sendLetter' ? 'Letter' : typeName;
    var rtrn = $.ajax({
        type: method,
        url: site_url + url,
        data: data,
        processData: false,
        contentType: false,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        beforeSend: function () {
            const loaderElement = $(`#loaderFor${type}`);
            kendoWindowOpen(loaderElement);
            // kendo.ui.progress(loaderElement, true);
            kendo.ui.progress($(document), false);
        },
        success: function (result) {
            let studRes = result.data;
            // let studRes = JSON.parse(result);
            // kendo.ui.progress($(`#loaderFor${type}`), false);
            $('#loaderFor' + type)
                .data('kendoWindow')
                .close();
            kendoWindowOpen(`#statusForSend${type}Modal`);

            $(`#title${type}SuccessMsg`).text(studRes.success_msg);
            $(`#title${type}FailMsg`).text(studRes.fail_msg);
            $(`#studentFail${type}List`).html('');
            if (studRes.fail_msg) {
                $(`.title${type}FailMsg`).show();
                $(`#studentFail${type}List`).show();
                manageFailStatusGrid(`#studentFail${type}List`, studRes.failData);
            } else {
                $(`.title${type}FailMsg`).hide();
                $(`#studentFail${type}List`).hide();
            }
            if (typeName == 'generateLetter') {
                // zipDownload();
                zipDownloadSetup(studRes, typeName);
            }
        },
        error: function (result) {
            // kendo.ui.progress($(document.body), false);
            callback(result);
        },
    });
    return rtrn;
}
function zipDownloadSetup(res, typeName) {
    let zipDownloadDiv = $(document).find('.letterZipDownload');
    zipDownloadDiv.html('');
    if (res && res.file_name && typeName == 'generateLetter') {
        let fileName = res.file_name.split('/').pop();
        let fileType = fileName.includes('.zip') ? 'ZIP' : 'PDF';
        let icon = fileType === 'ZIP' ? 'fa-file-archive' : 'fa-file-pdf';

        let isAlreadyDownloaded = isFileDownloaded(res.file_name);
        let buttonClass = isAlreadyDownloaded
            ? 'cursor-not-allowed'
            : 'bg-primary-blue-500 hover:shadow-lg';
        let linkClass = isAlreadyDownloaded ? 'downloaded' : '';
        let disabled = isAlreadyDownloaded ? 'disabled="disabled"' : '';
        let buttonText = isAlreadyDownloaded ? 'Downloaded & Deleted' : `Download ${fileType}`;
        let buttonIcon = isAlreadyDownloaded ? 'fa-check' : icon;

        zipDownloadDiv.html(
            `<div class="flex download-container">
                <button id="downloadZipLink"
                        class="flex justify-center w-auto h-8 px-4 py-2 ${buttonClass} rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500 download-letter-file ${linkClass}"
                        data-file-path="${res.file_name}"
                        data-filename="${fileName}"
                        title="Download ${fileType} File"
                        ${disabled}>
                    <i class="fas ${buttonIcon} text-white mr-2"></i>
                    <span class="text-sm font-medium leading-tight text-white">${buttonText}</span>
                </button>
                <small class="text-muted p-2">
                    <i class="fas fa-info-circle"></i>
                    ${isAlreadyDownloaded ? 'File already downloaded & deleted' : 'File will be deleted after download'}
                </small>
            </div>`
        );
    }
    $(document)
        .find('.queueOptionDiv')
        .toggle(typeName === 'sendLetter');
}

function manageFailStatusGrid(failDataGrid, failData) {
    $(failDataGrid)
        .html('')
        .kendoGrid({
            dataSource: failData,
            groupable: false,
            sortable: true,
            columns: [
                {
                    field: 'name',
                    title: 'NAME',
                    template: function (dataItem) {
                        return manageProfilePic(dataItem.id, dataItem.profile_pic, dataItem.name);
                    },
                },
                {
                    field: 'reason',
                    title: 'REASON',
                },
            ],
        });
}

function kendowindowOpen(windowID) {
    const isLetterParamsId = windowID === '#letterParameterModal';
    let kendoWindow = $(document).find(windowID);
    kendoWindow.getKendoWindow().open();
    kendoWindow
        .parent('div')
        .addClass('!rounded-md custom-discard-modal')
        .find('.k-window-titlebar')
        .addClass('titlebar-sms-modal gradientbackground !rounded-t-md')
        .find('.k-window-title')
        .addClass('text-lg font-medium leading-normal text-white');

    if (isLetterParamsId) {
        kendoWindow.parent('div').addClass('tw-dialog k-dialog');
    }
}

$('#closeLetterModelConformation').kendoWindow(openCenterWindow('Discard Letter'));

$('#issueLetterStudentModal')
    .data('kendoWindow')
    .bind('open', function (e) {
        confirmLatter = false;
        $('#issueLetterStudentModal').parent().addClass('student-custom-modal-wrapper');
    });

$('#issueLetterStudentModal')
    .data('kendoWindow')
    .bind('close', function (e) {
        if (!confirmLatter) {
            kendowindowOpen('#closeLetterModelConformation');
            confirmLatter = false;
            e.preventDefault();
        }
    });

$('body').on('click', '.letterDiscard-yes', function (e) {
    confirmLatter = true;
    $('#closeLetterModelConformation').data('kendoWindow').close();
    $('#issueLetterStudentModal').data('kendoWindow').close();
    myEditor22.setData('');
    $('#studentCommLogForLetter').prop('checked', false);
});

$('#viewSentMailMessageModal').kendoWindow(defaultWindowSlideFormat('View Mail', 52));

$('#letter_course_list').kendoDropDownList({
    dataTextField: 'course_title',
    dataValueField: 'id',
    dataSource: getDropdownDataSource('get-student-courses', { student_id: studentId }),
    change: function (e) {
        myEditor22.setData('');
    },
});
