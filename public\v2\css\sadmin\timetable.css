.k-widget * {
    box-sizing: border-box !important;
}

#timeTableSteps,
.hideradio {
    display: none;
}

.k-wizard-steps {
    margin: 0 !important;
}

.k-i-arrow-left {
    font-size: 24px !important;
}

.k-widget.k-tooltip.k-popup.k-group.k-reset {
    padding: 0px;
    background-color: transparent;
}

.k-tooltip .k-callout {
    display: none !important;
}

.k-i-close,
.k-i-more-vertical,
.k-i-user,
.k-i-marker-pin,
.k-i-chevron-down,
.k-i-calendar {
    color: #9ca3af;
}

.k-wizard {
    padding: 0px;
}

.k-wizard .k-wizard-step {
    padding: 0px;
}

.k-wizard-step:focus {
    outline: none !important;
}

/* Start manage column bar CSS */
.manageColumnBoxAttendance,
.manageColumnBoxViewTimetable {
    z-index: 1;
    position: absolute;
    display: none;
}

.active .manageColumnBoxAttendance,
.active .manageColumnBoxViewTimetable {
    z-index: 9999;
    position: absolute;
    display: block;
}

.manageColumnBoxAttendance.active .w-full .absolute,
.manageColumnBoxViewTimetable.active .w-full .absolute {
    margin-right: -35px;
}

/* End manage column bar CSS */

.widthzero {
    width: 0px;
}

.create-timetable-header {
    background-color: #fff;
}

#createTimeTableModal,
#bookRoomModal,
#singleDateRoomModal,
#recurringDateRoomModal,
#bookTrainerModal,
#classBreakTimeModal,
#defaultTimetableModal,
#studentListModal {
    padding: 0px;
}

#createTimeTableModal .k-i-check {
    color: #10b981;
}

#createTimeTableModal .k-i-close {
    color: #ef4444;
}

#timeTableStepsWizard {
    height: 100%;
    /* min-height: 100vh; */
}

#timetableStep .k-step-indicator,
#timetableStep .k-progressbar {
    display: none !important;
}

/* .k-form-field {
    padding: 0px 16px !important;
}

.k-form .k-form-field {
    margin-top: 0px !important;
} */

/* Dropdown */

#createTimeTableModal .k-widget.k-dropdown .k-dropdown-wrap,
#classBreakTimeModal .k-widget.k-dropdown .k-dropdown-wrap,
#defaultTimetableTemp .k-widget.k-dropdown .k-dropdown-wrap,
#defaultTimetableModal .k-widget.k-dropdown .k-dropdown-wrap,
#replacementTeacherForm .k-widget.k-dropdown .k-dropdown-wrap,
#replacementTeacherCourseForm .k-widget.k-dropdown .k-dropdown-wrap,
#advancedTimetableTemplate .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 5px 0px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

.attendancepanelbar1 .k-widget.k-dropdown .k-dropdown-wrap,
#dashboardTimetableTemplate .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 3px 0px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#attendancepanelbar .k-widget.k-dropdown .k-dropdown-wrap:hover,
#attendancepanelbar .k-widget.k-dropdown .k-dropdown-wrap.k-state-focused,
#createTimeTableModal .k-widget.k-dropdown .k-dropdown-wrap:hover,
#createTimeTableModal .k-widget.k-dropdown .k-dropdown-wrap.k-state-focused,
.k-form-field .k-form-field-wrap .k-widget.k-dropdown .k-dropdown-wrap.k-state-hover,
.k-widget .k-timepicker:hover {
    /* border: 1px solid var(--color-primary-blue-500) !important; */
    /* box-shadow: 0px -2px 2px 2px rgba(24, 144, 255, 0.1), 0px 2px 2px 2px rgba(24, 144, 255, 0.1); */
}

/* #createTimeTableModal .k-dropdown-wrap .k-select,
#classBreakTimeModal .k-dropdown-wrap .k-select {
    line-height: 35px !important;
    width: 35px !important;
} */

.sortable.open .dropdown-menu.dm-toggle {
    top: inherit !important;
    left: inherit !important;
    display: block;
}

.k-list-optionlabel.k-state-selected,
.k-list-optionlabel {
    display: none;
}

/* .k-widget  .k-invalid-msg {
    display: none;
} */

/* DatePicker */

.k-picker-wrap .k-input[type='text'] {
    border-radius: 0.5rem;
    height: 36px !important;
}

.newBatchDiv .k-textbox {
    border-width: 1px;
    border-radius: 0.5rem;
    height: 38px !important;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.k-datepicker {
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

.k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

/* .k-label.k-form-label {
    color: #374151;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 5px;
} */
.text-xxs {
    font-size: 10px;
}

/* End Sidebar filter hide-show */

/* Start Sidebar filter */
.k-panelbar > .k-item > .k-link,
.k-panelbar > .k-panelbar-header > .k-link:hover {
    background-color: transparent !important;
    cursor: pointer;
}

.searchTrainer input[type='text']:focus {
    box-shadow: none;
}

.k-panelbar .k-group > .k-item > .k-link.k-state-hover,
.k-panelbar .k-group > .k-item > .k-link:hover,
.k-panelbar .k-group > .k-panelbar-item > .k-link.k-state-hover,
.k-panelbar .k-group > .k-panelbar-item > .k-link:hover,
.k-panelbar .k-panelbar-group > .k-item > .k-link.k-state-hover,
.k-panelbar .k-panelbar-group > .k-item > .k-link:hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link.k-state-hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link:hover {
    color: #fff;
    background-color: transparent !important;
}

.k-panelbar .k-group > .k-item > .k-link.k-state-selected,
.k-panelbar .k-group > .k-panelbar-item > .k-link.k-state-selected,
.k-panelbar .k-panelbar-group > .k-item > .k-link.k-state-selected,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link.k-state-selected {
    color: #fff;
    background-color: transparent !important;
    box-shadow: none;
}

.k-panelbar {
    border-color: transparent;
    color: inherit;
    background-color: transparent;
}

.k-panelbar-group > .k-panelbar-item > .k-link label {
    cursor: pointer;
}

.k-panelbar-group {
    max-height: 500px !important;
    overflow-y: auto !important;
    padding-left: 10px;
}

.custom-panel-size {
    max-height: 300px !important;
}

.k-panelbar > .k-item > .k-link .k-icon,
.k-panelbar > .k-item > .k-link .k-panelbar-item-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-panelbar-item-icon {
    color: var(--color-primary-blue-500);
}

.k-panelbar > .k-item > .k-link.k-state-selected,
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected {
    color: var(--color-primary-blue-500);
}

.k-panelbar > .k-item > .k-link.k-state-focused,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focused {
    box-shadow: none;
}

/* End Sidebar filter */

/* tab strip */
.day-view-li .k-tabstrip-content.k-content.k-state-active:focus,
.week-view-li .k-tabstrip-content.k-content.k-state-active:focus {
    border: 0px !important;
    outline: 0px !important;
}

.k-tabstrip-content,
.k-tabstrip > .k-content {
    /* margin-top: 24px; */
    background-color: transparent !important;
}

.k-tabstrip-items .k-link {
    padding: 0px !important;
}

.k-tabstrip-items-wrapper .k-item.k-state-active p {
    color: white;
}

.k-tabstrip-items-wrapper .k-item.k-state-active button {
    background-color: var(--color-primary-blue-500);
    border-color: var(--color-primary-blue-500);
}

#weekViewtabstrip.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    margin-bottom: -1px;
    border-bottom: none !important;
}

#weekViewtabstrip .k-tabstrip-items-wrapper ul {
    justify-content: end;
    padding: 12px 0px;
}

/* end tab strip */

/* //filter menu */
.k-filter-menu .k-dropdown,
.k-action-buttons .k-button {
    border: 1px solid #b6b6b6;
    background-color: #e5e7eb;
}

.k-filter-menu .k-textbox,
.k-filter-menu .k-picker-wrap {
    border: 1px solid #b6b6b6;
}

/* .k-fieldselector .k-list .k-item,
.k-list-optionlabel.k-state-focused,
.k-list-optionlabel.k-state-selected,
.k-listbox .k-item,
.k-popup .k-list .k-state-focused,
.k-popup .k-list .k-state-selected,
.k-action-buttons .k-primary {
    background-color: var(--color-primary-blue-500) !important;
    cursor: pointer !important;
    color: white !important;
} */

.k-popup .k-list .k-state-hover {
    background-color: #e5e7eb;
    cursor: pointer;
    /* color: var(--color-primary-blue-500) ; */
}

.k-popup .k-list .k-state-selected .k-state-focused .k-state-hover {
    background-color: var(--color-primary-blue-500) !important;
    cursor: pointer !important;
    color: white !important;
}

.k-popup .k-list .k-item {
    /* padding: 1px 5px 1px 5px; */
    line-height: 30px !important;
    min-height: 1.8em;
}

input[type='text']:focus {
    box-shadow: none;
}

/* //filter menu */

.formSpace {
    background-color: rgb(241, 245, 249);
    margin: 0.75rem -25px !important;
}

.formSpace legend,
.k-form-fieldset #space,
.k-form-fieldset #space1 {
    display: none;
}

.k-form-fieldset legend {
    color: #111827;
    font-weight: 700;
    font-size: 1.5rem !important;
    text-transform: none !important;
    border: none !important;
}

/* radio button */

.k-radio-item .k-radio {
    margin-right: 5px !important;
}

.k-radio.k-checked.k-state-focus,
.k-radio:checked:focus {
    box-shadow: none;
}

/* radio button End*/

/* notification start */

.k-notification {
    border-radius: 0.5rem;
    padding: 0px 0px;
    border-width: 0px;
}

/* notification end */

/* schedular start */
.kendo-row-custom2,
.kendo-row-custom4,
.kendo-row-custom6,
.kendo-row-custom8,
.kendo-row-custom10,
.kendo-row-custom12,
.kendo-row-custom14,
.kendo-row-custom16,
.kendo-row-custom18,
.kendo-row-custom20,
.kendo-row-custom22,
.kendo-row-custom24,
.kendo-row-custom26 {
    display: none;
}

.k-middle-row {
    /* display: none; */
}

.k-scheduler-times-all-day {
    /*display: none;*/
}

.k-scheduler-footer {
    display: none !important;
}

/* schedular end */

/* checkbox :start */
.k-checkbox-item .k-checkbox {
    margin-right: 5px !important;
}

.k-checkbox.k-checked.k-focus,
.k-checkbox:checked:focus {
    box-shadow: none;
}

/* checkbox end */

.k-tabstrip-content.k-content.k-state-active:focus {
    border: 0px !important;
    outline: 0px !important;
}

.existingBatchDiv .k-dropdown,
#confirmationTemplate .k-dropdown,
#defaultTimetableModal .k-dropdown,
#confirmationTemplate .k-datepicker,
#dashboardTimetableTemplate .k-dropdown,
.attendancepanelbar1 .k-dropdown {
    width: 100%;
}

#advancedTimetableTemplate .k-dropdown {
    width: 100%;
}

.newBatchDiv .k-textbox {
    width: 100%;
}

.newBatchDiv .k-input:focus {
    box-shadow: none;
}

#createTimeTableWizard {
    overflow: visible !important;
}

#createTimeTableWizard .k-wizard-content {
    overflow: visible !important;
}

#createTimeTableWizard .k-wizard-steps {
    overflow: visible !important;
}

/* #courseAndUnitDateForm .k-list-horizontal .k-checkbox-item {
    margin: 0px 0px 10px 0px !important;
    width: 100px !important;
} */

.advanceTimetable.active {
    background: var(--color-primary-blue-500);
    border-color: var(--color-primary-blue-500);
}

.advanceTimetable.active p {
    color: #fff;
}

/* delete dialog */
.k-dialog-titlebar {
    background: linear-gradient(270deg, #06b6d4 20.35%, #1e93ff 75.64%) !important;
}

/* schedule start */
.k-scheduler-times table tr th {
    text-align: center;
    vertical-align: middle;
}

/* schedule End */

.k-scheduler-header,
.k-scheduler-view-header {
    padding-inline-end: unset !important;
}

.k-scheduler-times th.k-scheduler-group-cell {
    /* border-width: 0 0px 1px 0; */
}

.kendo-row-custom {
    display: none;
}

#advancetimetable-calendar .k-scheduler-monthview .k-event,
#advancetimetable-teacher .k-scheduler-monthview .k-event,
#advancetimetable-room .k-scheduler-monthview .k-event {
    height: 70px !important;
    background: transparent !important;
}

#advancetimetable-calendar .k-scheduler-dayview .k-event,
#advancetimetable-teacher .k-scheduler-dayview .k-event,
#advancetimetable-room .k-scheduler-dayview .k-event {
    background: unset !important;
}

/* #advancetimetable-calendar .k-event , #advancetimetable-calendar-week .k-event{
   height: 70px !important;
   background: transparent !important;
} */
#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-header th.k-scheduler-group-cell {
    padding: 8px 4px;
}

#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-header th,
#advancetimetable-room .k-scheduler-dayview .k-scheduler-header th {
    background-color: #fff;
}

#advancetimetable-teacher .k-event,
#advancetimetable-room .k-event {
    /* height: 70px !important */
    background-color: unset !important;
    /* margin-left: 35px !important; */
}

/* #room-availability .k-event,
#teacher-availability .k-event {
    height: 70px !important
    background-color: unset !important;
    margin-left: 30px !important;
} */

#room-availability .k-scheduler-timelineWeekview .k-event,
#teacher-availability .k-scheduler-timelineWeekview .k-event {
    /* margin-left: -33px !important; */
}

/* .k-scheduler-timelineWeekview {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
} */

/* #advancetimetable-calendar .k-scheduler-monthview .k-scheduler-content,
#advancetimetable-teacher .k-scheduler-monthview .k-scheduler-content,
#advancetimetable-room .k-scheduler-monthview .k-scheduler-content {
    overflow-y: hidden;
} */

.customTitle .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}

.titlebar-sms-modal .k-window-action {
    opacity: 1;
}

.k-i-filter {
    color: white !important;
}

.k-i-ungroup {
    color: #9ca3af !important;
}

/* action tooltip */

#viewAllTimetableList_tb_active .k-tooltip-content,
#studentAttendanceList_tb_active .k-tooltip-content,
#defaultTimetableList_tb_active .k-tooltip-content {
    /* box-shadow: 0px 20px 25px 5px rgb(0 0 0 / 10%), 0px 10px 10px -5px rgb(0 0 0 / 4%); */
}

/* action tooltip end*/

.k-calendar .k-calendar-td.k-state-selected .k-link {
    background-color: var(--color-primary-blue-500) !important;
}

.k-calendar .k-calendar-td.k-state-focused .k-link {
    background-color: var(--color-primary-blue-500) !important;
}

.k-calendar .k-nav-today,
.k-calendar .k-calendar-view .k-today {
    color: #0b79e0 !important;
}

.create-timetable-bg {
    /* background-image: url("../../img/create_timetable_bg-50.png"); */
    background-image: url('../../img/create-timetable-bg-units.png');
    background-position: center;
    background-repeat: no-repeat;
    /*padding: 15px;*/
    background-size: 28%;
}

.create-timetable-bg.trainersForUnit {
    background-image: url('../../img/create-timetable-bg-trainers.png');
    background-position: center;
    background-repeat: no-repeat;
    /*padding: 15px;*/
    background-size: 28%;
}

.create-timetable-bg.batchStudents {
    background-image: url('../../img/create-timetable-bg-batch.png');
    background-position: center;
    background-repeat: no-repeat;
    /*padding: 15px;*/
    background-size: 28%;
}

.maincontainer span.k-loading-text {
    text-indent: 0;
    top: 0;
    left: 0;
    /* background-color: #0F0; */
    width: 100%;
    height: 100%;
    z-index: 9999;
}

.mainCalendar div.k-loading-mask {
    text-indent: 0;
    top: 0px;
    left: 0px;
    /* background-color: #0F0; */
    width: 100%;
    height: 100%;
}

.mainCalendar span.k-loading-text {
    text-indent: 0;
    top: 0px;
    left: 0px;
    /* background-color: #0F0; */
    width: 100%;
    height: 100%;
    z-index: 9999;
}

input:checked + .slider {
    background-color: #2196f3;
}

input:checked ~ .dot {
    transform: translateX(100%);
}

.k-list-filter > .k-icon {
    right: 15px !important;
}

.k-list-filter .k-textbox {
    width: 100% !important;
}

.k-loading-mask {
    /* background-color: #f5f8fa; */
}

.maincontainer {
    position: relative;
    /* z-index: 8; */
}

#createtimeTableSteps.k-stepper {
    color: #374151 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

#createtimeTableStatus.k-stepper {
    color: #6b7280 !important;
    font-size: 14px !important;
    font-weight: 400 !important;
}

#createtimeTableSteps.k-stepper .k-step:hover .k-step-label {
    color: #374151 !important;
    /* font-weight: 500 !important; */
}

#createtimeTableStatus.k-stepper .k-step:hover .k-step-label,
#createtimeTableStatus.k-stepper .k-step-current .k-step-label,
#createtimeTableStatus.k-stepper .k-step-first .k-step-label {
    color: #6b7280 !important;
    font-weight: 400 !important;
}

.action-div,
#alltimetable_unit .k-checkbox-label {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.k-i-arrow-60-down::before {
    content: close-quote !important;
    background-image: url('../../img/drop-down-arrow.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-i-arrow-60-left::before {
    content: close-quote !important;
    background-image: url('../../img/left-side-calendar-arrow-1.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-i-arrow-60-right::before {
    content: close-quote !important;
    background-image: url('../../img/right-side-calendar-arrow-1.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-icon.k-i-clock {
    width: 1.3em;
    height: 1.3em;
}

.k-i-clock::before {
    content: close-quote !important;
    background-image: url('../../img/clock.svg');
    background-position: center;
    background-repeat: no-repeat;
    width: 1.3em;
    height: 1.3em;
    margin-top: 1px;
}

span.k-state-active .k-i-arrow-60-down::before {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
    background-image: url('../../img/drop-down-arrow.svg');
    background-position: center;
    background-repeat: no-repeat;
}

#timeTableStepsWizard .k-calendar-view {
    margin: 0;
}

#calendardiv.k-calendar .k-nav-today {
    display: none;
}

#dashboardTimetableTemplate .k-select {
    line-height: 32px;
    width: 32px;
}

#room-availability .k-scheduler-layout.k-scheduler-weekview tbody tr td:nth-child(1),
#teacher-availability .k-scheduler-layout.k-scheduler-weekview tbody tr td:nth-child(1) {
    border-bottom: 1px solid #e6e3e3;
    border-right: 1px solid #e6e3e3;
}

#timeTableStepsWizard-0 {
    overflow-y: hidden;
}

#room-availability table,
#teacher-availability table,
#advancetimetable-room .k-scheduler-timelineWeekview table,
#advancetimetable-teacher .k-scheduler-timelineWeekview table,
#advancetimetable-calendar-week table,
#advancetimetable-room-week table,
#advancetimetable-teacher-week table {
    border-collapse: separate;
    border-spacing: 0 7px;
}

#advancetimetable-room .k-scheduler-timelineWeekview .k-scheduler-times tr th,
#advancetimetable-teacher .k-scheduler-timelineWeekview .k-scheduler-times tr th,
#room-availability .k-scheduler-times tr th,
#teacher-availability .k-scheduler-times tr th {
    border-width: 1px 1px 1px 1px;
    background-color: white;
    border-color: #e6e3e3;
    color: #374151;
    /* text-align: center; */
    vertical-align: middle;
    border-radius: 0.25rem 0 0 0.25rem;
}

#advancetimetable-teacher .k-scheduler-header tr th,
#advancetimetable-room .k-scheduler-header tr th,
#advancetimetable-teacher .k-scheduler-header tr th,
#room-availability .k-scheduler-header tr th,
#teacher-availability .k-scheduler-header tr th,
#advancetimetable-calendar-week .k-scheduler-header tr th,
#advancetimetable-room-week .k-scheduler-header tr th,
#advancetimetable-teacher-week .k-scheduler-header tr th {
    border-width: 0px 0px 0px 0px;
}

#advancetimetable-teacher .k-scheduler-content tr td,
#advancetimetable-room .k-scheduler-content tr td,
#advancetimetable-teacher .k-scheduler-content tr td,
#room-availability .k-scheduler-content tr td,
#teacher-availability .k-scheduler-content tr td,
#advancetimetable-calendar-week .k-scheduler-content tr td,
#advancetimetable-room-week .k-scheduler-content tr td,
#advancetimetable-teacher-week .k-scheduler-content tr td {
    border-width: 1px 1px 1px 0px;
    border-color: #e6e3e3;
    background-color: white;
}

#advancetimetable-room .k-scheduler-monthview .k-scheduler-content tr td,
#advancetimetable-teacher .k-scheduler-monthview .k-scheduler-content tr td {
    border-width: 0px 1px 1px 0px !important;
}

#advancetimetable-room .k-scheduler-dayview .k-scheduler-content tr td,
#advancetimetable-room .k-scheduler-dayview .k-scheduler-times tr td {
    border-width: 0px 1px 1px 0px;
    border-color: #e6e3e3;
    background-color: white;
}

#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-content tr td,
#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-times tr td {
    border-width: 0px 1px 1px 0px;
    border-color: #e6e3e3;
    background-color: white;
}

#advancetimetable-calendar-week .k-scheduler-header-all-day tr td,
#advancetimetable-room-week .k-scheduler-header-all-day tr td,
#advancetimetable-teacher-week .k-scheduler-header-all-day tr td {
    border-width: 1px 0px 1px 1px !important;
    border-color: #e6e3e3 !important;
    background-color: white !important;
}

#advancetimetable-room .k-scheduler-content tr td:last-child,
#advancetimetable-teacher .k-scheduler-content tr td:last-child,
#room-availability .k-scheduler-content tr td:last-child,
#teacher-availability .k-scheduler-content tr td:last-child {
    border-radius: 0px 5px 5px 0px;
}

#advancetimetable-teacher.k-scheduler,
#advancetimetable-teacher .k-scheduler-header-wrap,
#advancetimetable-room.k-scheduler,
#advancetimetable-room .k-scheduler-header-wrap,
#room-availability.k-scheduler,
#room-availability .k-scheduler-header-wrap,
#teacher-availability.k-scheduler,
#teacher-availability .k-scheduler-header-wrap,
#advancetimetable-calendar-week,
#advancetimetable-calendar-week .k-scheduler-header-wrap {
    /* background-color:transparent; */
    --tw-bg-opacity: 1;
    background-color: rgba(249, 250, 251, var(--tw-bg-opacity));
    border: none;
}

#advancetimetable-room .k-scheduler-dayview .k-scheduler-header-wrap,
#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-header-wrap {
    /* Hide because day view header have double border*/
    /* border: 1px solid #E6E3E3; */
}

/* #advancetimetable-teacher .k-scheduler-dayview,
#advancetimetable-room .k-scheduler-dayview {
    width: 200%;
} */

.k-scheduler-layout.k-scheduler-weekview tbody tr.k-scheduler-header-all-day td:nth-child(1) {
    /* border-bottom: 1px solid #e6e3e3; */
    border-right: 0px solid #e6e3e3;
}

.k-scheduler-refresh {
    display: none;
}

/* dashBoard Teacher Panelbar :start */
.dashboardTrainer > .k-item > .k-link,
.dashboardTrainer .k-group > .k-item.k-level-1 .k-link {
    padding: 0px 0px 16px 4px !important;
}

.cusInput:hover,
.cusInput:focus {
    border: 1px solid var(--color-primary-blue-500);
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

/* dashBoard Teacher Panelbar :end */
/*
New PanelBar */
.k-panelbar > .k-item,
.k-panelbar > .k-item + .k-item,
.k-panelbar > .k-panelbar-header,
.k-panelbar > .k-panelbar-header + .k-panelbar-header {
    border-bottom: 1px solid #e5e7eb;
    /* padding-left: 16px;
    padding-right: 16px; */
}

.k-panelbar .k-item .k-content {
    background-color: transparent;
    padding: 0.5rem 0;
}

/* .attendancepanelbar1 .k-radio-list .k-radio-item {
    margin: 8px 0px 0px 4px;
}
.k-checkbox-list .k-checkbox-item {
    margin: 8px 0px 0px 4px;
} */
#attendancepanelbar,
#calendarViewpanelbar,
#viewTimetablepanelbar,
#alltimetable_unit {
    padding: 0 1rem;
}

.k-dropdown-wrap .k-select {
    line-height: 2.25rem !important;
}

/* advance changes */

#advancetimetable-calendar .k-scheduler-toolbar,
#advancetimetable-teacher .k-scheduler-toolbar,
#advancetimetable-room .k-scheduler-toolbar,
#advancetimetable-teacher .k-scheduler-toolbar,
#room-availability .k-scheduler-toolbar,
#teacher-availability .k-scheduler-toolbar {
    display: none !important;
}

#customDatePickerCalendar .k-datepicker,
#customDatePickerRoom .k-datepicker,
#customDatePickerTeacher .k-datepicker {
    border-radius: 0px !important;
    width: 100%;
    box-shadow: none !important;
    caret-color: transparent;
}

#scheduleDatePickerCalenderView .k-picker-wrap,
#scheduleDatePickerRoomView .k-picker-wrap,
#scheduleDatePickerTeacherView .k-picker-wrap {
    padding-right: 0px;
    padding-bottom: 0px !important;
    border-width: 0;
}

#advancetimetable-calendar .k-scheduler-header-wrap,
#advancetimetable-teacher .k-scheduler-header-wrap,
#advancetimetable-room .k-scheduler-header-wrap {
    background-color: rgb(249 250 251) !important;
}

#advancetimetable-calendar .k-scheduler-header .k-scheduler-header-wrap th,
#advancetimetable-teacher .k-scheduler-header .k-scheduler-header-wrap th,
#advancetimetable-room .k-scheduler-header .k-scheduler-header-wrap th {
    border: none;
    color: #6b7280;
}

#advancetimetable-calendar .k-more-events.k-button {
    /* display: none; */
    /* pointer-events: none !important; */
}

#advancetimetable-room .k-scheduler-header .k-scheduler-header-wrap th,
#advancetimetable-teacher .k-scheduler-header .k-scheduler-header-wrap th,
#room-availability .k-scheduler-header .k-scheduler-header-wrap th,
#teacher-availability .k-scheduler-header .k-scheduler-header-wrap th,
#advancetimetable-calendar-week .k-scheduler-header .k-scheduler-header-wrap th {
    color: #6b7280;
}

#advancetimetable-room .k-scheduler-dayview .k-scheduler-header .k-scheduler-header-wrap th,
#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-header .k-scheduler-header-wrap th {
    border-right: 1px solid #e6e3e3;
}

#advancetimetable-calendar,
#advancetimetable-teacher,
#advancetimetable-room {
    border-top: none;
    border-right: none;
    border-left: none;
}

#advancetimetable-calendar .k-scheduler-monthview .k-scheduler-content,
#advancetimetable-teacher .k-scheduler-monthview .k-scheduler-content,
#advancetimetable-room .k-scheduler-monthview .k-scheduler-content {
    /* overflow-y: hidden; */
    border: 1px solid #e5e7eb;
    border-left-width: 0px;
}

.k-scheduler .k-other-month,
.k-scheduler-other-month {
    background-color: rgb(249 250 251);
}

#scheduleDatePicker,
#availableRoomDatePicker,
#availableTeacherDatePicker,
#advanceRoomDatePicker,
#advanceTeacherDatePicker {
    text-align: center;
    margin-left: 20px;
}

#availableRoomPrevDate .k-i-arrow-60-left::before,
#availableTeacherPrevDate .k-i-arrow-60-left::before,
#btnPrevDate .k-i-arrow-60-left::before,
#roomBtnPrevDate .k-i-arrow-60-left::before,
#teacherBtnPrevDate .k-i-arrow-60-left::before {
    content: close-quote !important;
    background-image: url('../../img/left-side-calendar-arrow.svg');
    background-position: center;
    background-repeat: no-repeat;
}

#availableRoomNextDate .k-i-arrow-60-right::before,
#availableTeacherNextDate .k-i-arrow-60-right::before,
#btnNextDate .k-i-arrow-60-right::before,
#roomBtnNextDate .k-i-arrow-60-right::before,
#teacherBtnNextDate .k-i-arrow-60-right::before {
    content: close-quote !important;
    background-image: url('../../img/right-side-calendar-arrow.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-grid tr:hover td {
    background-color: #f9fafb !important;
}

#classBreakTimeModal .k-form .k-form-field {
    margin-bottom: 0px !important;
}

#calendardiv .k-calendar-view .selectedWeekDates td,
#calendardiv .k-calendar-view .selectedWeekDates td:hover .k-link {
    background-color: var(--color-primary-blue-500) !important;
    color: #ffffff !important;
}

#calendardiv .k-calendar-view .selectedWeekDates {
    pointer-events: none;
}

#calendardiv .k-calendar-view {
    width: 19em;
}

#calendardiv .k-calendar-view .selectedWeekDates td {
    color: #ffffff !important;
}

#calendardiv .k-calendar-view tr.selectedWeekDates td {
    border-radius: 0px;
}

#calendardiv .k-calendar-view tr.selectedWeekDates td:first-child {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

#calendardiv .k-calendar-view tr.selectedWeekDates td:last-child {
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
}

#createtimeTableSteps .k-step-list-horizontal .k-step-label {
    margin-top: 0px !important;
    top: 3px !important;
    position: relative !important;
}

#createtimeTableStatus .k-step-list-horizontal .k-step-label {
    margin-top: 0px !important;
    position: relative !important;
}

.create-timetable-header .k-i-arrow-left::before,
#timeTableStepsWizard-0 .k-i-arrow-left::before,
#timeTableStepsWizard-1 .k-i-arrow-left::before,
#timeTableStepsWizard-2 .k-i-arrow-left::before,
#timeTableStepsWizard-3 .k-i-arrow-left::before,
#timeTableStepsWizard-4 .k-i-arrow-left::before,
#timeTableStepsWizard-5 .k-i-arrow-left::before {
    content: close-quote !important;
    background-image: url('../../img/arrow_icon.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-daterangepicker .k-textbox-container {
    display: none;
}

#timetableDaterangepicker_dateview .k-calendar-td.k-range-mid {
    background-color: #bae7ff;
}

.selectAllCheckboxLable {
    color: var(--color-primary-blue-500);
}

.create-timetable-header .k-window-actions {
    display: none;
}

#advancetimetable-calendar .k-scheduler-dayview tr:first-child td:first-child .k-scheduler-times,
#advancetimetable-teacher .k-scheduler-dayview tr:first-child td:first-child .k-scheduler-times,
#advancetimetable-room .k-scheduler-dayview tr:first-child td:first-child .k-scheduler-times {
    display: none;
}

#advancetimetable-calendar table.k-scheduler-dayview tbody,
#advancetimetable-teacher table.k-scheduler-dayview tbody,
#advancetimetable-room table.k-scheduler-dayview tbody {
    background-color: #ffffff;
}

#advancetimetable-calendar .k-scheduler-dayview tr:nth-child(2) th,
#advancetimetable-teacher .k-scheduler-dayview tr:nth-child(2) th,
#advancetimetable-room .k-scheduler-dayview tr:nth-child(2) th {
    background-color: #ffffff;
}

#advancetimetable-calendar
    .k-scheduler-dayview
    tr:nth-child(2)
    td:first-child
    .k-scheduler-times
    .k-scheduler-table,
#advancetimetable-teacher
    .k-scheduler-dayview
    tr:nth-child(2)
    td:first-child
    .k-scheduler-times
    .k-scheduler-table,
#advancetimetable-room
    .k-scheduler-dayview
    tr:nth-child(2)
    td:first-child
    .k-scheduler-times
    .k-scheduler-table {
    border-right: #d1d5db;
}

/* #advancetimetable-calendar table.k-scheduler-dayview table {
	border-collapse: separate;
	border-spacing: 0 7px;
}
#advancetimetable-calendar table.k-scheduler-dayview .k-slot-cell {
	display: none;
}
#advancetimetable-calendar table.k-scheduler-dayview .k-scheduler-content tr:nth-of-type(even) {
	display: none;
} */
#advancetimetable-calendar .k-scheduler-dayview .k-scheduler-table .k-middle-row td,
#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-table .k-middle-row td,
#advancetimetable-room .k-scheduler-dayview .k-scheduler-table .k-middle-row td {
    border-bottom-style: dotted;
    border-color: #fff;
}

#advancetimetable-teacher .k-scheduler-dayview .k-scheduler-table .k-middle-row td,
#advancetimetable-room .k-scheduler-dayview .k-scheduler-table .k-middle-row td {
    border-right: 1px solid #e6e3e3;
}

#advancetimetable-calendar
    table.k-scheduler-layout.k-scheduler-dayview
    tr:first-child
    td:not([role='gridcell']),
#advancetimetable-teacher
    table.k-scheduler-layout.k-scheduler-dayview
    tr:first-child
    td:not([role='gridcell']),
#advancetimetable-room
    table.k-scheduler-layout.k-scheduler-dayview
    tr:first-child
    td:not([role='gridcell']) {
    background-color: #f9fafb;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

#advancetimetable-calendar table.k-scheduler-layout.k-scheduler-dayview .k-scheduler-header tbody,
#advancetimetable-teacher table.k-scheduler-layout.k-scheduler-dayview .k-scheduler-header tbody,
#advancetimetable-room table.k-scheduler-layout.k-scheduler-dayview .k-scheduler-header tbody {
    background-color: #f9fafb;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

#advancetimetable-calendar
    table.k-scheduler-layout.k-scheduler-dayview
    .k-scheduler-times
    table.k-scheduler-table
    span,
#advancetimetable-teacher
    table.k-scheduler-layout.k-scheduler-dayview
    .k-scheduler-times
    table.k-scheduler-table
    span,
#advancetimetable-room
    table.k-scheduler-layout.k-scheduler-dayview
    .k-scheduler-times
    table.k-scheduler-table
    span {
    color: #6b7280;
}

#advancetimetable-calendar table.k-scheduler-layout.k-scheduler-dayview .k-scheduler-times,
#advancetimetable-teacher table.k-scheduler-layout.k-scheduler-dayview .k-scheduler-times,
#advancetimetable-room table.k-scheduler-layout.k-scheduler-dayview .k-scheduler-times {
    width: 200px;
}

#advancetimetable-calendar .k-scheduler-weekview .k-scheduler-table .k-middle-row td,
#advancetimetable-teacher .k-scheduler-weekview .k-scheduler-table .k-middle-row td,
#advancetimetable-room .k-scheduler-weekview .k-scheduler-table .k-middle-row td {
    border-bottom-style: dotted;
    border-color: #fff;
}

#advancetimetable-calendar
    table.k-scheduler-layout.k-scheduler-weekview
    tr:first-child
    td:not([role='gridcell']),
#advancetimetable-teacher
    table.k-scheduler-layout.k-scheduler-weekview
    tr:first-child
    td:not([role='gridcell']),
#advancetimetable-room
    table.k-scheduler-layout.k-scheduler-weekview
    tr:first-child
    td:not([role='gridcell']) {
    background-color: #f9fafb;
    /* border-bottom: 1px solid rgba(0, 0, 0, 0.08); */
}

#advancetimetable-calendar table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-header tbody,
#advancetimetable-teacher table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-header tbody,
#advancetimetable-room table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-header tbody {
    background-color: #f9fafb;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

#advancetimetable-calendar
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-times
    table.k-scheduler-table
    span,
#advancetimetable-teacher
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-times
    table.k-scheduler-table
    span,
#advancetimetable-room
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-times
    table.k-scheduler-table
    span {
    color: #6b7280;
}

#advancetimetable-calendar table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-times,
#advancetimetable-teacher table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-times,
#advancetimetable-room table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-times {
    width: 200px;
}

#advancetimetable-calendar table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-table td,
#advancetimetable-calendar table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-table th,
#advancetimetable-teacher table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-table td,
#advancetimetable-teacher table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-table th,
#advancetimetable-room table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-table td,
#advancetimetable-room table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-table th {
    /* padding: 8px 8px;
	height: 1.42857143em;
	overflow: hidden;
	white-space: nowrap;
	border-style: solid; */
    border-width: 0 0 0px 0px;
    /* vertical-align: top;
	box-sizing: content-box; */
}

#advancetimetable-calendar
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-header-wrap
    .k-scheduler-table
    tr
    th,
#advancetimetable-teacher
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-header-wrap
    .k-scheduler-table
    tr
    th,
#advancetimetable-room
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-header-wrap
    .k-scheduler-table
    tr
    th {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

#advancetimetable-calendar
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-content
    .k-scheduler-table
    td,
#advancetimetable-teacher
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-content
    .k-scheduler-table
    td,
#advancetimetable-room
    table.k-scheduler-layout.k-scheduler-weekview
    .k-scheduler-content
    .k-scheduler-table
    td {
    padding: 8px 8px;
    height: 1.42857143em;
    overflow: hidden;
    white-space: nowrap;
    border-style: solid;
    border-width: 0 1px 1px 0px;
    vertical-align: top;
    box-sizing: content-box;
    border-color: rgba(0, 0, 0, 0.08);
}

#advancetimetable-teacher
    table.k-scheduler-layout.k-scheduler-dayview
    .k-scheduler-times
    .k-scheduler-table
    tr,
#advancetimetable-room
    table.k-scheduler-layout.k-scheduler-dayview
    .k-scheduler-times
    .k-scheduler-table
    tr {
    border-bottom-color: #e6e3e3 !important;
}

.hoverStepper1.current {
    background-color: rgb(229 231 235);
}

.hoverStepper2.current {
    background-color: rgb(229 231 235);
}

.hoverStepper3.current {
    background-color: rgb(229 231 235);
}

.hoverStepper4.current {
    background-color: rgb(229 231 235);
}

.hoverStepper5.current {
    background-color: rgb(229 231 235);
}

/* #createtimeTableStatus .k-step:hover{
    background-color: rgb(229 231 235);
} */

/* .k-steperhover:hover{
    background-color: rgb(229 231 235);
} */

.set-svgicon {
    /* display: block;
    align-content: end; */
}

.k-step-list-horizontal .k-step {
    width: 60px !important;
}

#createtimeTableStatus .k-step-list-horizontal .k-step {
    padding-bottom: 8px !important;
    padding-top: 2px !important;
}

#createtimeTableSteps .k-step-list-horizontal .k-step {
    padding-top: 8px !important;
    padding-bottom: 2px !important;
}

#createtimeTableSteps.k-stepper .k-step-current .k-step-label {
    color: var(--color-primary-blue-500) !important;
}

/* radio search filter */

.k-filter-menu-container .k-checkbox-label {
    padding: 5px 4px !important;
    width: 100%;
}

.k-filter-menu-container .k-checkbox-label:hover {
    background-color: #e5e7eb !important;
    border-radius: 4px;
}

.k-filter-menu.k-popup .k-multicheck-wrap {
    padding: 0px !important;
}

/* multiselect filter */
.k-multiselect {
    border-width: 1px !important;
}

.k-multiselect-wrap {
    flex-direction: column !important;
}

.k-multiselect-wrap .k-input {
    width: 100% !important;
}

.k-multiselect-wrap .k-input:focus-within {
    box-shadow: none !important;
}

#createtimeTableStatus.k-stepper .k-step-current .k-step-label {
    color: var(--color-primary-blue-500) !important;
}

.timetable-btn-common {
    height: 34px;
    padding-left: 13px;
    padding-right: 13px;
    padding-top: 9px;
    padding-bottom: 9px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.timetable-btn-common p {
    font-size: 13px !important;
    font-weight: 500;
    line-height: 20px;
}

.timetable-btn {
    height: 34px;
    padding-left: 13px;
    padding-right: 13px;
    padding-top: 9px;
    padding-bottom: 9px;
    background-color: rgba(24, 144, 255, 1);
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.timetable-btn p {
    font-size: 13px;
    font-weight: 500;
    line-height: 20px;
    color: white;
}

#calendardiv .k-calendar-title {
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
}

#calendardiv .k-calendar-td .k-link {
    font-size: 12px;
    font-weight: 400;
    /* line-height: 16px; */
}

#dayViewTab,
#weekViewTab {
    /* height: 100vh; */
    overflow-y: auto;
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

#dayViewTab::-webkit-scrollbar,
#weekViewTab::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

#teacherPanelbar .k-panelbar-group {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

#teacherPanelbar .k-panelbar-group::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

#alltimetable_trainer {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

#alltimetable_trainer::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

#advance_room {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

#advance_room::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

#advance_trainer {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

#advance_trainer::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

#attendance_trainer {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

#attendance_trainer::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

#scheduleDatePickerCalenderView .k-picker-wrap .k-select,
#scheduleDatePickerTeacherView .k-picker-wrap .k-select,
#scheduleDatePickerRoomView .k-picker-wrap .k-select {
    line-height: 2.5em !important;
    border-width: 0 !important;
    border-color: unset !important;
    right: unset !important;
    width: 4rem !important;
}

#scheduleDatePickerCalenderView .k-picker-wrap .k-select .k-i-calendar,
#scheduleDatePickerRoomView .k-picker-wrap .k-select .k-i-calendar,
#scheduleDatePickerTeacherView .k-picker-wrap .k-select .k-i-calendar {
    background-image: url('../../img/calendar_v2.svg') !important;
    font-size: 18px !important;
    background-size: cover;
}

#scheduleDatePickerCalenderView .k-picker-wrap .k-select .k-i-calendar::before,
#scheduleDatePickerRoomView .k-picker-wrap .k-select .k-i-calendar::before,
#scheduleDatePickerTeacherView .k-picker-wrap .k-select .k-i-calendar::before {
    content: unset;
}

#scheduleDatePicker_dateview .k-calendar .k-header {
    border-bottom-width: unset;
    border-bottom-style: unset;
    box-shadow: unset;
}

#scheduleDatePicker_dateview .k-footer {
    display: none !important;
}

.moreCount {
    color: #ffffff;
    text-align: left;
}

.moreCountDiv {
    background-color: rgba(24, 144, 255, 1);
    padding: 8px !important;
}

.attendancepanelbar1 {
    background-color: #f9fafb !important;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

#scheduleDatePickerCalenderView .monthViewDate .k-picker-wrap .k-select,
#scheduleDatePickerTeacherView .monthViewDate .k-picker-wrap .k-select,
#scheduleDatePickerRoomView .monthViewDate .k-picker-wrap .k-select {
    width: 4rem !important;
}

#scheduleDatePickerCalenderView .dayViewDate .k-picker-wrap .k-select,
#scheduleDatePickerTeacherView .dayViewDate .k-picker-wrap .k-select,
#scheduleDatePickerRoomView .dayViewDate .k-picker-wrap .k-select {
    width: 3rem !important;
}

.active-step .text-xs {
    color: var(--color-primary-blue-500) !important;
}

.closeFilter {
    background-image: url('../../img/close_filter.svg');
    background-position: center;
    background-repeat: no-repeat;
}

#roomDropDownList .k-dropdown-wrap {
    padding: 3px 0px !important;
}

#teacherDropDownList .k-dropdown-wrap {
    padding: 3px 0px !important;
}

.closeIcon {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

/* #advancetimetable-teacher:has(> table.k-scheduler-dayview) {
    width: 200%;
} */
.k-animation-container .k-filter-menu-container .k-checkbox-label span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.k-popup .k-multicheck-wrap {
    scrollbar-width: none;
}

.k-window {
    z-index: 999999 !important;
}

.k-grid.k-grid-display-block {
    display: flex;
}

.k-form-field {
    margin: 0;
}

#alltimetable_unit .k-panelbar-item-text {
    width: 91%;
}
