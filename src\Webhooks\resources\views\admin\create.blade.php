@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="flex items-center mb-6">
            <a href="{{ route('settings.webhooks.index') }}" class="text-gray-500 hover:text-gray-700 mr-4">
                ← Back to Webhooks
            </a>
            <h1 class="text-3xl font-bold text-gray-900">Create New Webhook</h1>
        </div>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form id="webhookForm" class="space-y-6">
                @csrf

                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                    <input type="text" name="name" id="name" required
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div>
                    <label for="url" class="block text-sm font-medium text-gray-700">URL</label>
                    <input type="url" name="url" id="url" required
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="https://example.com/webhook">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Events</label>
                    <div class="space-y-2 max-h-60 overflow-y-auto border border-gray-300 rounded-md p-4">
                        @foreach($availableEvents as $eventKey => $eventName)
                        <label class="flex items-center">
                            <input type="checkbox" name="events[]" value="{{ $eventKey }}"
                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">{{ $eventName }}</span>
                        </label>
                        @endforeach
                    </div>
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" checked
                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-3">
                    <a href="{{ route('admin.webhooks.index') }}"
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit"
                        class="bg-primary-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Create Webhook
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.getElementById('webhookForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        name: formData.get('name'),
        url: formData.get('url'),
        events: formData.getAll('events'),
        is_active: formData.has('is_active') ? 1 : 0
    };

    fetch('{{ route("admin.webhooks.store") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Webhook created successfully!');
            window.location.href = '{{ route("admin.webhooks.index") }}';
        } else {
            alert('Error creating webhook: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        alert('Error creating webhook: ' + error.message);
    });
});
</script>
@endsection