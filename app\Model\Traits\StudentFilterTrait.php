<?php

namespace App\Model\Traits;

use App\Repositories\StudentRepository;

trait StudentFilterTrait
{
    public function scopeFilterQuery($query, $value)
    {
        if (! $value) {
            return $query;
        }
        $studentRepository = new StudentRepository($this);
        $studentIds = $studentRepository->getFilteredStudentIds(
            $value,
        );

        return $query->whereIn('id', $studentIds);
    }

    public function scopeFilterHasUsi($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value == 'all') {
            return $query;
        }
        if ($value == 1) {
            return $query->whereNotNull('USI');
        } else {
            return $query->whereNull('USI');
        }
    }

    public function scopeFilterHasID($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value) {
            return $query->whereNotNull('generated_stud_id');
        }

        return $query->whereNull('generated_stud_id');
    }

    public function scopeFilterUsiStatus($query, $value)
    {
        if ($value == 'all') {
            return $query;
        }
        if ($value == 1) {
            return $query->where('is_usi_verified', 1);
        } elseif ($value == 0) {
            return $query->where('is_usi_verified', 0);
        }

        return $query;
    }

    public function scopeFilterCourseTypeIds($query, $ids)
    {
        if (! $ids) {
            return $query;
        }
        if ($ids == 'all') {
            return $query;
        }

        return $query->whereHas('studentCourses', function ($q) use ($ids) {
            $q->whereIn('course_type_id', $ids);
        });
    }

    public function scopeCollegeId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    //    public function scopeFilterAgentId($query, $value)
    //    {
    //        if (empty($value)) {
    //            return $query;
    //        }
    //
    //        return $query->whereHas('studentCourses', function ($q) use ($value) {
    //            $q->where('agent_id', $value);
    //        });
    //    }

}
