<template>
    <FormElement class="space-y-4 pt-4">
        <fieldset class="k-form-fieldset">
            <div class="space-y-4 px-6">
                <Field
                    :id="'name'"
                    :name="'name'"
                    :label="'Template Name:'"
                    :component="'roleTemplate'"
                    :validator="requiredtrue"
                    :pt="{
                        root: 'flex items-center gap-5',
                        input: 'w-full',
                    }"
                >
                    <template #roleTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </Field>
                <div>
                    <div class="font-medium text-gray-700">Select Template Type</div>
                    <RadioGroup
                        v-model="templateType"
                        :default-value="templateType"
                        :data-items="booleanOptions"
                        :layout="'horizontal'"
                    />
                </div>
                {{ templateType }}
                <div class="grid grid-cols-2 gap-3">
                    <Field
                        :id="'width'"
                        :name="'width'"
                        :label="'Width:'"
                        :component="FormNumericInput"
                        :default-value="defaultDimensions.width"
                        :pt="{
                            root: 'flex items-center gap-5',
                            input: 'w-full',
                        }"
                        :disabled="templateType === 'default'"
                    />
                    <Field
                        :id="'height'"
                        :name="'height'"
                        :label="'Height:'"
                        :component="FormNumericInput"
                        :default-value="defaultDimensions.height"
                        :pt="{
                            root: 'flex items-center gap-5',
                            input: 'w-full',
                        }"
                        :disabled="templateType === 'default'"
                    />
                </div>
            </div>
        </fieldset>
        <template v-if="isCreate">
            <div class="flex items-center justify-end gap-3 px-6 pt-1">
                <Button
                    type="submit"
                    size="sm"
                    variant="primary"
                    class="min-w-[100px]"
                    :loading="loaderStore.contextLoaders['button']"
                    loadingText="Creating..."
                    >Create
                </Button>
            </div>
        </template>
        <template v-else>
            <div
                class="sticky bottom-0 flex w-full items-center justify-end gap-4 bg-white px-6 py-4 shadow-inner-top"
            >
                <Button :variant="'secondary'" size="sm" @click="handleClose"
                    ><span>Cancel</span></Button
                >
                <Button
                    type="submit"
                    size="sm"
                    variant="primary"
                    class="min-w-[100px]"
                    :loading="loaderStore.contextLoaders['button']"
                    loadingText="Saving..."
                    >Save
                </Button>
                <Button
                    @click="handleSaveAndPreview"
                    type="button"
                    size="sm"
                    variant="primary"
                    class="min-w-[100px]"
                    :loading="loaderStore.contextLoaders['button1']"
                    loadingText="Saving..."
                    >Save and Preview
                </Button>
            </div>
        </template>
    </FormElement>
</template>
<script setup>
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import Button from '@spa/components/Buttons/Button.vue';
import { Form, FormElement, Field } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
// import FormCheckbox from "@spa/components/KendoInputs/FormCheckbox.vue";
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import { useCertificateStore } from '@spa/stores/modules/certificate.store';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import { getCurrentInstance } from 'vue';
import { RadioGroup } from '@progress/kendo-vue-inputs';

const booleanOptions = ref([
    {
        label: 'CR80',
        value: 'default',
    },
    {
        label: 'Custom',
        value: 'custom',
    },
]);

const templateType = ref('default');

const { proxy } = getCurrentInstance(); // access $refs

const loaderStore = useLoaderStore();

const popupSettings = {
    animate: false,
    offset: { left: 150, top: 50 },
};

const props = defineProps({
    dataItem: {
        type: Object,
        default: () => {},
    },
    isCreate: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['close', 'submit']);

const defaultDimensions = ref({
    width: 324,
    height: 204,
});

const isDisabled = ref(true);

const handleSubmit = (formData) => {
    console.log('formData', formData);
    emit('submit', formData);
};

const handleSaveAndPreview = async (formData) => {
    const formRef = proxy.$refs.formRef;
    console.log('formRef', formRef.form);
    if (formRef) {
        if (formRef.form.valid) {
            const formData = formRef.form.values; // get values
            emit('submitPreview', formData);
        } else {
            formRef.form.validate();
            console.warn('Form is invalid. Fix errors first!');
            // You can also show a toast, error message, etc.
        }
    }
};

const handleClose = () => {
    emit('close');
};

const formInitialValues = ref({
    name: '',
    certificate_type: 0,
    certificate_number_formate_id: 0,
});

onMounted(() => {
    if (props.dataItem) {
        const { name = '', type = 0, certificate_number_formate_id = 0 } = props.dataItem;
        formInitialValues.value = {
            name,
            certificate_type: parseInt(type),
            certificate_number_formate_id: parseInt(certificate_number_formate_id),
        };
    }
});
</script>
<style lang=""></style>
