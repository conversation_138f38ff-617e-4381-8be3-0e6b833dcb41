<script id="studentCourseStatusHistoryTemplate" type="text/html">
    <div class="inline-flex flex-col items-start justify-start bg-white rounded-lg overflow-y-auto w-full">
        <div class="space-y-0">
            # if(arr.length > 0){ #
            #for(var i=0; i< arr.length; i++){# # bgcolor='gray' ; textcolor='gray' ; if
                (arr[i]['status_from']=='Current Student' ){ bgcolor='primary-blue' ; textcolor='primary-blue' ; } else
                if (arr[i]['status_from']=='Cancelled' ){ bgcolor='red' ; textcolor='red' ; } else if
                (arr[i]['status_from']=='Transitioned' ){ bgcolor='yellow' ; textcolor='yellow' ; } else if
                (arr[i]['status_from']=='Completed' ){ bgcolor='green' ; textcolor='green' ; } else if
                (arr[i]['status_from']=='Finished' ){ bgcolor='green' ; textcolor='green' ; } else if
                (arr[i]['status_from']=='Withdrawn' ){ bgcolor='pink' ; textcolor='pink' ; } else if
                (arr[i]['status_from']=='Suspended' ){ bgcolor='red' ; textcolor='red' ; } # # bgcolor_to='gray' ;
                textcolor_to='gray' ; if (arr[i]['status_to']=='Current Student' ){ bgcolor_to='primary-blue' ;
                textcolor_to='primary-blue' ; } else if (arr[i]['status_to']=='Cancelled' ){ bgcolor_to='red' ;
                textcolor_to='red' ; } else if (arr[i]['status_to']=='Transitioned' ){ bgcolor_to='yellow' ;
                textcolor_to='yellow' ; } else if (arr[i]['status_to']=='Completed' ){ bgcolor_to='green' ;
                textcolor_to='green' ; } else if (arr[i]['status_to']=='Finished' ){ bgcolor_to='green' ;
                textcolor_to='green' ; } else if (arr[i]['status_to']=='Withdrawn' ){ bgcolor_to='pink' ;
                textcolor_to='pink' ; } else if (arr[i]['status_to']=='Suspended' ){ bgcolor_to='red' ;
                textcolor_to='red' ; } # <div class="inline-flex space-x-2 items-center justify-start w-full py-2">
                <div class="flex items-center justify-start bg-light-blue-500 rounded-full">
                    <img class="rounded-lg" src="{{ asset('v2/img/history.svg') }}" />
                </div>
                <div class="flex flex-col items-start justify-center">
                    <p class="text-xs leading-5 text-gray-500">Status changed for #= arr[i]['student_name'] # from</p>
                    <p class="text-xs leading-5 text-gray-400">by #= arr[i]['created_by_name'] #, #= arr[i]['createAt']
                        #</p>
                </div>
                <div class="flex space-x-1.5 items-center justify-end ml-2">
                    <div class="flex items-center justify-center px-2.5 py-0.5 bg-#=bgcolor #-200 rounded">
                        <p class="text-xs leading-5 text-center text-#= textcolor #-900">#= arr[i]['status_from'] #</p>
                    </div>
                    <img class="w-4 h-2/3 rounded-full" src="{{ asset('v2/img/left_arrow_history.svg') }}" />
                    <div class="flex items-center justify-center px-2.5 py-0.5 bg-#=bgcolor_to #-100 rounded">
                        <p class="text-xs leading-5 text-center text-#= textcolor_to #-800">#= arr[i]['status_to'] #</p>
                    </div>
                </div>
        </div>

        #if(arr[i]['is_cancel'] == 1){#
        <div class="inline-flex space-x-2 items-center justify-start w-full py-2">
            <div class="bg-gray-100 rounded gap-1 ml-8 p-1 ">
                <div class="grow shrink basis-0">
                    <span class="text-xs leading-5 text-center text-gray-900">Reason for Change:</span>
                    <span class="text-gray-400 text-xs font-normal leading-tight tracking-wide"> </span>
                    <span class="text-gray-500 text-xs font-normal leading-tight tracking-wide">#=
                        arr[i]['cancel_reason'] #</span>
                </div>
            </div>
        </div>
        <!-- <div class="text-xs leading-5 text-red-500">
                    <b>Reason for Cancelled : </b>
                    <i class="text-red-500"> #= arr[i]['cancel_reason'] #</i>
                </div> -->
        # } #

        # } #
        # } else { #
        <p class="flex text-sm leading-5 font-medium text-gray-900">No History Found</p>
        # } #
    </div>
    </div>
</script>