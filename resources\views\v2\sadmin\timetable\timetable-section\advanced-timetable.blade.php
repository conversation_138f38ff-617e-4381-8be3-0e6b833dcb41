<script id="advancedTimetableTemplate" type="text/html">
    <div id="advancedTimetableTemplate">
        {{-- <div class="flex justify-between items-center bg-white px-4 py-2 shadow border-b border-gray-200">
            <div class="flex space-x-2 items-center justify-start backToDash w-full cursor-pointer">
                <span class="k-icon k-i-arrow-left k-color-gray-500"></span>
                <p class="text-2xl font-bold leading-7 text-gray-800">View Advance Timetable</p>
                <img class="w-5 h-5 mt-1 rounded-md" src="{{ asset('v2/img/info.svg') }}" class="" alt="searchIcon" />
            </div>
            <div class="flex items-center justify-end w-full space-x-4">
                <div class="flex bg-white shadow rounded-md">
                    <button
                        class="timetableCalendar advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300 rounded-tl-md rounded-bl-md active">
                        <p class="text-xs leading-none text-gray-500">Calender View</p>
                    </button>
                    <button
                        class="timetableRoomData advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300">
                        <p class="text-xs leading-none text-gray-500">Room View</p>
                    </button>
                    <button
                        class="timetableTeacherData advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300  rounded-tr-md rounded-br-md">
                        <p class="text-xs leading-none text-gray-500">Trainer View</p>
                    </button>
                </div>

            </div>
        </div> --}}

        <div class="flex flex-row w-full">
            <div
                class="relative toggelfilter transition-all duration-450 filter-left-sidebar w-80 inline-flex flex-col  space-y-4 items-start justify-start pt-6 pb-4 bg-white attendancepanelbar1 border-r h-screen-header max-h-screen overflow-y-auto">
                <div class="px-4 w-full">
                    <span class="flex flex-col items-start justify-start w-full mb-4">
                        <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-1">
                            <p class="text-base font-medium leading-normal cursor-pointer">View by</p>
                        </div>
                    </span>

                    <div class="flex bg-white shadow rounded-md">
                        <button
                            class="w-full timetableCalendar advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300 rounded-tl-md rounded-bl-md active">
                            <p class="text-xs leading-none text-gray-500">Default</p>
                        </button>
                        <button
                            class="w-full timetableRoomData advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300">
                            <p class="text-xs leading-none text-gray-500">Room</p>
                        </button>
                        <button
                            class="w-full timetableTeacherData advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300  rounded-tr-md rounded-br-md">
                            <p class="text-xs leading-none text-gray-500">Trainer</p>
                        </button>
                    </div>
                </div>
                <ul id="calendarViewpanelbar" class="w-full flex items-start justify-start">
                    <li>
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-1">
                                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Campus
                                    Location</span>
                            </div>
                        </span>
                        <div class='mb-3'>
                            <input class="advance-calendar-filter" id="advance_timetable_campus" />
                        </div>
                    </li>
                    <li id="roomsCheckboxList">
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-1">
                                <span
                                    class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Rooms</span>
                            </div>
                        </span>
                        <div>
                            <div
                                class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2"
                                    class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full"
                                    data-field="advance_room" placeholder="Search Room">
                            </div>
                            <div class="mb-2 mt-2">
                                <div class="flex flex-col items-start justify-start w-full ml-1">
                                    <div class="inline-flex space-x-1 items-center justify-start">
                                        <div class="form-check flex items-center space-x-1">
                                            <input id="selectAllAdvancedRoom"
                                                class="selectAllAdvancedRoom advance-calendar-filter   k-checkbox mr-2 cursor-pointer"
                                                type="checkbox" value="all">
                                            <label for="selectAllAdvancedRoom"
                                                class="all-timetable-filter selectAllAdvancedRoomLabel cursor-pointer text-sm leading-5 text-gray-700 h-full"
                                                data-val="All room">Select all</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <ul class="advance-calendar-filter calnedar_filter_room overflow-y-auto" id="advance_room">
                            </ul>
                        </div>
                    </li>
                    <li>
                        <span class="flex flex-col items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-2">
                                <span
                                    class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">Trainer</span>
                            </div>
                        </span>
                        <div>
                            <div
                                class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2"
                                    class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full"
                                    data-field="advance_trainer" placeholder="Search Trainer">
                            </div>
                            <div class="mb-2 mt-2">
                                <div class="flex flex-col items-start justify-start w-full ml-1">
                                    <div class="inline-flex space-x-1 items-center justify-start">
                                        <div class="form-check flex items-center space-x-1">
                                            <input id="selectAllAdvancedTrainer"
                                                class="advance-calendar-filter selectAllAdvancedTrainer  k-checkbox mr-2 cursor-pointer"
                                                type="checkbox" value="all">
                                            <label for="selectAllAdvancedTrainer"
                                                class="all-timetable-filter  cursor-pointer text-sm leading-5 text-gray-700 h-full"
                                                data-val="All Trainers">Select all trainers</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <ul class="advance-calendar-filter calnedar_filter_trainer overflow-y-auto"
                                id="advance_trainer"></ul>
                        </div>
                    </li>
                </ul>
            </div>


            <div id="adCalendarDiv" class="flex flex-row w-full mainCalendar">

                <div class="flex calView flex-col z-10 w-full  bg-gray-50">
                    <div class="flex flex-col space-y-4 pt-6 pb-4 pl-8 pr-6" id="customDatePickerCalendar">
                        <div class="flex space-x-1">
                            <p class="text-base font-medium leading-normal">Showing Timetable For</p>
                            <p class="text-base font-medium leading-normal advanceSelectedCampus"></p>
                            <p class="text-base font-medium leading-normal">,</p>
                            <p class="text-base font-medium leading-normal advanceSelectedRoom"></p>
                            <p class="text-base font-medium leading-normal">And</p>
                            <p class="text-base font-medium leading-normal advanceSelectedTeacher"></p>
                        </div>
                        <div class="flex justify-between items-center">

                            <div class="flex justify-between space-x-2">
                                <button type="button" id="filterBtn" class="btn-secondary p-2">
                                    <span class="k-icon closeFilter"></span>
                                </button>
                                <div class="flex">
                                    <div
                                        class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                        <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                        <input type="text"
                                            class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full cursor-pointer"
                                            placeholder="Search ">
                                    </div>
                                </div>
                            </div>
                            {{-- <div>
                                <p class="text-base leading-6 font-medium text-gray-700" id="showFullDateFromCalender">
                                    october</p>
                            </div> --}}
                            <div class="flex justify-between space-x-4">
                                <div class="flex bg-white shadow rounded-md">
                                    <div
                                        class="advanceCalendarDay advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300 cursor-pointer rounded-tl-md rounded-bl-md">
                                        <p class="text-xs leading-none text-gray-500">Day</p>
                                    </div>
                                    {{-- <div
                                        class="advanceCalendarWeek advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300 cursor-pointer">
                                        <p class="text-xs leading-none text-gray-500">Week</p>
                                    </div> --}}
                                    <button
                                        class="advanceCalendarMonth advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300 rounded-r-md active">
                                        <p class="text-xs leading-none text-gray-500">Month</p>
                                    </button>
                                </div>
                                <div class="flex bg-white rounded-md shadow" id="scheduleDatePickerCalenderView">
                                    <div id="btnPrevDate"
                                        class="flex items-center justify-center p-2 border-y border-l bg-gray-50 border-gray-300 rounded-tl-md rounded-bl-md">
                                        <span class="k-icon k-i-arrow-60-left"></span>
                                    </div>
                                    <div class="w-32 setDateDiv">
                                        <input id="scheduleDatePicker" class="cursor-pointer"
                                            onkeydown="return false" />
                                    </div>
                                    <div id="btnNextDate"
                                        class="flex items-center justify-center p-2 border-y border-r bg-gray-50 border-gray-300 rounded-tr-md rounded-br-md">
                                        <span class="k-icon k-i-arrow-60-right"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="activeCalenderView" id="activeCalenderView"
                        value="advanceCalendarMonth" />
                    <div id="adCalendarMonthDiv">
                        <div id="advancetimetable-calendar"></div>
                    </div>
                    <div id="adCalendarWeekDiv" style="display:none;">
                        <div id="advancetimetable-calendar-week">
                        </div>
                    </div>
                    <div id="adCalendarDayDiv" style="display:none;">
                        <div id="advancetimetable-calendar-day">
                            day
                        </div>
                    </div>
                </div>
            </div>

            <div id="adRoomDiv" class="flex flex-row w-full mainCalendar" style="display:none;">
                <div class="flex calView flex-col z-10 w-full  bg-gray-50">
                    <div class="flex flex-col space-y-4 px-6 pt-6 pb-4 " id="customDatePickerRoom">
                        <div class="flex space-x-1">
                            <p class="text-base font-medium leading-normal">Showing Timetable For</p>
                            <p class="text-base font-medium leading-normal advanceSelectedCampus"></p>
                            <p class="text-base font-medium leading-normal">,</p>
                            <p class="text-base font-medium leading-normal advanceSelectedRoom">Room No</p>
                            <p class="text-base font-medium leading-normal">And</p>
                            <p class="text-base font-medium leading-normal advanceSelectedTeacher">Teacher</p>
                        </div>
                        <div class="flex justify-between items-center">

                            <div class="flex items-center justify-between space-x-2">
                                <button type="button" id="filterBtn"
                                    class="flex items-center justify-center p-2 border bg-white border-gray-300 rounded-md shadow">
                                    <span class="k-icon closeFilter"></span>
                                </button>
                                <div class="flex space-x-2 items-center px-2 justify-start w-44" id="roomDropDownList">
                                    <input id="advancedCalendarRoomDropdown">
                                </div>
                                <div class="">
                                    <div
                                        class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                        <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                        <input type="text"
                                            class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full cursor-pointer"
                                            placeholder="Search ">
                                    </div>
                                </div>
                            </div>
                            {{-- <div>
                                <p class="text-base leading-6 font-medium text-gray-700" id="showFullDateFromRoom">
                                    october</p>
                            </div> --}}
                            <div class="flex justify-between space-x-4">
                                <div class="flex bg-white shadow rounded-md">
                                    <button
                                        class="advanceCalendarRoomDay advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300 rounded-tl-md rounded-bl-md">
                                        <p class="text-xs leading-none text-gray-500">Day</p>
                                    </button>
                                    <button
                                        class="advanceCalendarRoomWeek advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300 active">
                                        <p class="text-xs leading-none text-gray-500">Week</p>
                                    </button>
                                    <button
                                        class="advanceCalendarRoomMonth advanceTimetable flex items-center justify-center px-4 py-3 bg-white border border-gray-300  rounded-tr-md rounded-br-md">
                                        <p class="text-xs leading-none text-gray-500">Month</p>
                                    </button>
                                </div>
                                <div class="flex bg-white rounded-md" id="scheduleDatePickerRoomView">
                                    <div id="roomBtnPrevDate"
                                        class="flex items-center justify-center p-2 border bg-gray-50 border-gray-300 rounded-tl-md rounded-bl-md">
                                        <span class="k-icon k-i-arrow-60-left"></span>
                                    </div>
                                    <div class="w-36 hover:shadow-md">
                                        <input id="advanceRoomDatePicker" class="cursor-pointer"
                                            onkeydown="return false" />
                                    </div>
                                    <div id="roomBtnNextDate"
                                        class="flex items-center justify-center p-2 border bg-gray-50 border-gray-300 rounded-tr-md rounded-br-md">
                                        <span class="k-icon k-i-arrow-60-right"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="activeCalenderRoomView" id="activeCalenderRoomView"
                        value="advanceCalendarRoomWeek" />

                    <div id="advancetimetable-room"></div>

                </div>

            </div>

            <div id="adTrainerDiv" class="flex flex-row w-full mainCalendar" style="display:none;">
                <div class="flex calView flex-col z-10 w-full  bg-gray-50">
                    <div class="flex flex-col space-y-4 px-6 pt-6 pb-4" id="customDatePickerTeacher">
                        <div class="flex space-x-1">
                            <p class="text-base font-medium leading-normal">Showing Timetable For</p>
                            <p class="text-base font-medium leading-normal advanceSelectedCampus"></p>
                            <p class="text-base font-medium leading-normal">,</p>
                            <p class="text-base font-medium leading-normal advanceSelectedRoom">Room No</p>
                            <p class="text-base font-medium leading-normal">And</p>
                            <p class="text-base font-medium leading-normal advanceSelectedTeacher">Teacher</p>
                        </div>
                        <div class="flex justify-between items-center">

                            <div class="flex items-center justify-between space-x-2">
                                <button type="button" id="filterBtn"
                                    class="flex items-center justify-center p-2 border bg-white border-gray-300 rounded-md">
                                    <span class="k-icon closeFilter"></span>
                                </button>
                                <div class="flex space-x-2 items-center px-2 justify-start w-44"
                                    id="teacherDropDownList">
                                    <input id="advancedTeacherRoomDropdown">
                                </div>
                                <div class="">
                                    <div
                                        class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                        <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                        <input type="text"
                                            class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full cursor-pointer"
                                            placeholder="Search ">
                                    </div>
                                </div>
                            </div>
                            {{-- <div>
                                <p class="text-base leading-6 font-medium text-gray-700" id="showFullDateFromTeacher">
                                    october</p>
                            </div> --}}
                            <div class="flex justify-between space-x-4">
                                <div class="flex bg-white shadow rounded-md">
                                    <button
                                        class="advanceTimetable advanceCalendarTeacherDay flex items-center justify-center px-4 py-3 bg-white border border-gray-300 rounded-tl-md rounded-bl-md">
                                        <p class="text-xs leading-none text-gray-500">Day</p>
                                    </button>
                                    <button
                                        class="advanceTimetable advanceCalendarTeacherWeek flex items-center justify-center px-4 py-3 bg-white border border-gray-300 active">
                                        <p class="text-xs leading-none text-gray-500">Week</p>
                                    </button>
                                    <button
                                        class="advanceTimetable advanceCalendarTeacherMonth flex items-center justify-center px-4 py-3 bg-white border border-gray-300  rounded-tr-md rounded-br-md">
                                        <p class="text-xs leading-none text-gray-500">Month</p>
                                    </button>
                                </div>
                                <div class="flex bg-white rounded-md" id="scheduleDatePickerTeacherView">
                                    <div id="teacherBtnPrevDate"
                                        class="flex items-center justify-center p-2 border bg-gray-50 border-gray-300 rounded-tl-md rounded-bl-md">
                                        <span class="k-icon k-i-arrow-60-left"></span>
                                    </div>
                                    <div class="w-36 hover:shadow-md">
                                        <input id="advanceTeacherDatePicker" class="cursor-pointer"
                                            onkeydown="return false" />
                                    </div>
                                    <div id="teacherBtnNextDate"
                                        class="flex items-center justify-center p-2 border bg-gray-50 border-gray-300 rounded-tr-md rounded-br-md">
                                        <span class="k-icon k-i-arrow-60-right"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="activeCalenderTeacherView" id="activeCalenderTeacherView"
                        value="advanceCalendarTeacherWeek" />
                    <div id="advancetimetable-teacher"></div>
                </div>

            </div>
        </div>
    </div>
</script>

<script id="calendarViewPanelbarTemplate" type="text/kendo-ui-template">
    <div class="flex flex-col items-start justify-start w-full #: item.value #">
        # if (item.id != 0 && typeof item.text != 'undefined') { #
        <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-#: item.id # ">
            <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.text #</span>
        </div>
        # } #

        # if (item.type == 'input') { #
        <div class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
            # if(item.field == 'course'){ #
                <input type="text" data-value="#: item.value #" class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full cursor-pointer" placeholder="Search #: item.subtext #">
            # } else { #
                <input type="text" data-value="#: item.value #" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full cursor-pointer" placeholder="Search #: item.subtext #">
            # } #
        </div>
        # } #

        # if (item.type == 'radio') { #
        <div class="inline-flex space-x-2 items-center justify-start">
            <div class="form-check">
                <input class="form-check-input advance-calendar-filter f-radio appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer" type="radio" name="radio_#: item.field #" value="#: item.value #" data-category="#: item.field #" id="#: item.category_id #_radio_#: item.id #" data-val="#: item.original #" #: ((item.id == 1 || item.id == 51) ? 'checked' : '') # >
                <label class="text-xs leading-tight text-gray-700" for="#: item.category_id #_radio_#: item.id #" data-val="#: item.subtext #">
                    #: item.subtext #
                </label>
            </div>
        </div>
        # } #

        # if (item.type == 'dropdown') { #
        <select class="advance-calendar-filter inline-flex space-x-2 items-center w-full justify-start inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 text-gray-500 custom-option leading-6 text-sm #: item.field #" id="#: item.value #">
            # for(var i=0; i < item.arr.length; i++){ #
                <option value="#: item.arr[i].id #" title="#: item.arr[i].text #">#: ((item.arr[i].text.length > 30) ? (item.arr[i].text.substring(0,30) + '...') : item.arr[i].text) #</option>
            # } #
        </select>
        # } #
    </div>
</script>
<script id="slotTemplate" type="text/x-kendo-template">

    #var uid = target.attr("data-uid");#
    #var scheduler = target.closest("[data-role=scheduler]").data("kendoScheduler");#

    #var slot = scheduler.slotByElement(target);#
    #var model = scheduler.occurrenceByUid(uid);#
    #console.log(model);#

    #if(model) {#
        <div class="inline-flex space-x-1 items-center justify-start w-80 bg-blue-50">
            <div class="w-0.5 h-full bg-primary-blue-500 rounded-lg"/>
            <div class="inline-flex flex-col items-start justify-start">
                <div class="inline-flex space-x-0.5 items-center justify-start pr-1">
                    <p class="opacity-90 text-xs leading-3 text-gray-900">Diploma of Accounting: FNSACC511</p>
                </div>
                <div class="inline-flex space-x-0.5 items-center justify-start pr-1">
                    <p class="opacity-90 text-xs leading-3 text-gray-700">2 PM - 7:00 PM</p>
                    <div class="w-0.5 h-2/3 bg-gray-400 rounded-full"/>
                    <p class="opacity-90 text-xs leading-3 text-gray-700">Anjali Jojhnson</p>
                </div>
            </div>
        </div>
    #} else {#
        <strong>No event data is available</strong>
    #}#
</script>

<script id="MonthViewDetail" type="text/x-kendo-template">
    #var uid = target.parent().attr("data-uid");#
    #var scheduler = target.closest("[data-role=scheduler]").data("kendoScheduler");#
    #var model = scheduler.occurrenceByUid(uid);#

    # console.log(model); #
    <div class="flex flex-col space-y-4 items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full">
        <div class="flex space-x-4 items-center justify-between w-full">
            <div class="w-2 h-8 #: model.color2 #"></div>
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <div class="inline-flex items-start justify-start">
                    <p class="text-base font-medium leading-6 text-gray-900">#: model.subject_name #</p>
                </div>
                <div class="flex space-x-4 items-start justify-start">
                    <div class="flex space-x-1 items-start justify-start">
                        <p class="text-xs leading-none text-gray-500">Batch:</p>
                        <p class="text-xs leading-none text-gray-900">#: model.batch #</p>
                    </div>
                    <div class="flex space-x-1 items-start justify-start">
                        <p class="text-xs leading-none text-gray-500">Term:</p>
                        <p class="text-xs leading-none text-gray-900">#: model.term #</p>
                    </div>
                    <div class="flex space-x-1 items-start justify-start">
                        <p class="text-xs leading-none text-gray-500">Semester:</p>
                        <p class="text-xs leading-none text-gray-900">#: model.semester_name #</p>
                    </div>
                </div>
            </div>
            <div class="flex flex-col justify-start w-14">
                <button id="hideTooltip" class=""><span class="k-icon k-i-close"></span></button>

            </div>
        </div>
        <div class="bg-gray-200 w-full h-px"></div>
        <div class="flex flex-col space-y-3 items-start justify-start w-full">
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <div class="flex space-x-1 items-center justify-start w-40">
                    <img src="{{ asset('v2/img/person-16-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Teacher</p>
                </div>
                <div class="flex items-center justify-start">
                    <p class="text-sm leading-5 text-gray-900">#: model.trainer_name #</p>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <div class="flex space-x-1 items-center justify-start w-40">
                    <img src="{{ asset('v2/img/location-16-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Room</p>
                </div>
                <div class="flex items-center justify-start">
                    <p class="text-sm leading-5 text-gray-900">#: model.room_name #</p>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <div class="flex space-x-1 items-center justify-start w-40">
                    <img src="{{ asset('v2/img/clock-alarm-32-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Time</p>
                </div>
                <div class="flex items-center justify-start">
                    <p class="text-sm leading-5 text-gray-900">#: model.timetable_time #</p>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <div class="flex space-x-1 items-center justify-start w-40">
                    <img src="{{ asset('v2/img/app-folder-32-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Mode</p>
                </div>
                <div class="flex items-center justify-start">
                    <p class="text-sm leading-5 text-gray-900">#: model.trainer_name #</p>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <div class="flex space-x-1 items-center justify-start w-40">
                    <img src="{{ asset('v2/img/column-triple-edit-24-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Attendance Type</p>
                </div>
                <div class="flex items-center justify-start">
                    <p class="text-sm leading-5 text-gray-900">#: model.attendance_type #</p>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <div class="flex space-x-1 items-center justify-start w-40">
                    <img src="{{ asset('v2/img/clock-dismiss-24-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Break</p>
                </div>
                <div class="flex items-center justify-start">
                    <p class="text-sm leading-5 text-gray-900">#: model.break #</p>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <div class="flex space-x-1 items-center justify-start w-40">
                    <img src="{{ asset('v2/img/barcode-scanner-24-regular.svg') }}" class="h-4 w-4" />
                    <p class="text-sm leading-5 text-gray-500">Capacity</p>
                </div>
                <div class="flex items-center justify-start">
                    <p class="text-sm leading-5 text-gray-900">#: model.class_capacity #</p>
                </div>
            </div>
        </div>
    </div>
</script>

<script id="slotTemplate" type="text/x-kendo-template">

    #var scheduler = target.closest("[data-role=scheduler]").data("kendoScheduler");#
    #var slot = scheduler.slotByElement(target);#
        #console.log(slot);#
            #if(slot) {#
                <div class="flex flex-col space-y-4 items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full">
                    <div class="flex space-x-4 items-center justify-between w-full">
                        <div class="w-2 h-8"></div>
                        <div class="flex flex-col space-y-2 items-start justify-start w-full">
                            <div class="inline-flex items-start justify-start">
                                <p class="text-base font-medium leading-6 text-gray-900"></p>
                            </div>

                        </div>
                        <div class="flex flex-col justify-start w-14">
                            <button id="hideTooltip" class=""><span class="k-icon k-i-close"></span></button>

                        </div>
                    </div>
                    <div class="bg-gray-200 w-full h-px"></div>
                </div>
    #} else {#
        <strong>No slot data is available</strong>
    #}#
</script>
<script id="moreViewDetail" type="text/x-kendo-template">

    <div class="flex flex-col space-y-4 items-start justify-center p-4 bg-white shadow border rounded-lg border-gray-200 w-full">
            <div style="width: 100%; display: inline-flex; flex-direction: row; align-items: center; justify-content: space-between;">
                <p class="moreViewDetailsDataDate px-1" style="font-size: 13px; font-weight: 500; line-height: 20px; color: rgba(55, 65, 81, 1);"></p>
                <div style="border-radius: 6px;">
                    <button id="hideTooltip"><span class="k-icon k-i-close"></span></button>
                </div>
            </div>
            <div class="w-full moreViewDetailsData"></div>
        </div>
</script>