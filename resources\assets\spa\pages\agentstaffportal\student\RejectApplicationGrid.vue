<template lang="">
    <Layout :noSpacing="true" :loading="false" :actionSticky="true">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Student Applications - Requested'" :back="false" />
        </template>
        <template #tabs>
            <OfferHeaderTabs :currentTab="'reject'" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <HeaderTab
                :filters="getFilters"
                :hastextsearch="true"
                :actions="getActionBtns"
                @export="handleExport"
                @filter="updateFilter"
                :action="'reject'"
                :width="'w-[300px]'"
                :dateRangeWidth="'w-[300px]'"
                :variant="'primary'"
            />
            <div
                v-if="data.currentActiveTab == 'reject'"
                class="inline-flex h-11 w-full flex-col items-end justify-start gap-2.5"
            >
                <div
                    class="inline-flex items-center justify-start gap-6 rounded-md border border-amber-400 bg-amber-50 p-3"
                >
                    <div class="flex items-center justify-start gap-3">
                        <div class="flex h-5 w-5 items-center justify-center px-0.5 pb-1 pt-0.5">
                            <svg
                                width="16"
                                height="14"
                                viewBox="0 0 16 14"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M6.25706 1.09858C7.02167 -0.260724 8.97875 -0.260725 9.74336 1.09858L15.3237 11.0191C16.0736 12.3523 15.1102 13.9996 13.5805 13.9996H2.4199C0.890251 13.9996 -0.0731769 12.3523 0.676753 11.0191L6.25706 1.09858ZM9.00012 10.9998C9.00012 11.552 8.55241 11.9998 8.00012 11.9998C7.44784 11.9998 7.00012 11.552 7.00012 10.9998C7.00012 10.4475 7.44784 9.99976 8.00012 9.99976C8.55241 9.99976 9.00012 10.4475 9.00012 10.9998ZM8.00012 2.99976C7.44784 2.99976 7.00012 3.44747 7.00012 3.99976V6.99976C7.00012 7.55204 7.44784 7.99976 8.00012 7.99976C8.55241 7.99976 9.00012 7.55204 9.00012 6.99976V3.99976C9.00012 3.44747 8.55241 2.99976 8.00012 2.99976Z"
                                    fill="#FBBF24"
                                />
                            </svg>
                        </div>
                        <div class="inline-flex flex-col items-start justify-start gap-0.5">
                            <div
                                class="text-sm font-normal leading-tight tracking-tight text-amber-800"
                            >
                                Please note: Applications will be automatically deleted after 90
                                days.
                            </div>
                        </div>
                    </div>
                    <div class="flex h-4 w-4 items-center justify-center p-0.5"></div>
                </div>
            </div>
            <StudentOfferGrid
                :data="gridData"
                :type="'application'"
                :currentTab="data.currentActiveTab"
                :pagination="this.resource.state.pageable"
                @sort="sortDataHandler"
                @changepage="changePageHandler"
                :actions="getActionBtns"
                :isNew="true"
            />
        </div>
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import { TabStrip } from '@progress/kendo-vue-layout';
import OfferHeaderTabs from '@agentportal/student/partials/OfferHeaderTabs.vue';
import HeaderTab from '@agentportal/payments/partials/HeaderTab.vue';
import StudentOfferGrid from '@agentportal/student/partials/StudentOfferGrid.vue';
import { IconArrowDownload24Regular } from '@iconify-prerendered/vue-fluent';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import {
    agentsResource,
    setPagination,
    prepareStudentOfferGridData,
} from '@spa/services/agent/agentsResource.js';
import { routeHistory } from '@spa/helpers/routeHistory';
import StudentsCommonGrid from '@agentportal/common/CommonGrid.vue';

export default {
    setup(props) {
        const resource = agentsResource({
            filters: {
                search: props.query?.search || '',
                commission: props.filters?.trace?.commission || null,
                date: props.filters?.trace?.date || null,
                fromdate: props.filters?.trace?.fromdate || null,
                todate: props.filters?.trace?.todate || null,
                course: props.filters?.trace?.course || null,
                take: props.grid?.commission?.per_page || 10,
                page: props.grid?.commission?.current_page || 1,
            },
            only: ['grid', 'data'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        data: { type: Object, default: {} },
        grid: { type: Object, default: {} },
        filters: { type: Object, default: {} },
    },
    data() {
        return {
            gridData: [],
            meta: {
                total: 7, // Total number of records
                current_page: 1,
                per_page: 5, // Default page size
            },
            headerActions: [
                {
                    icon: 'downloadIcon',
                    action: 'export',
                    value: 'excel',
                    title: 'Export (xls)',
                },
            ],
        };
    },
    created() {
        routeHistory.previousRoute = window.location.pathname;
    },
    mounted() {
        this.gridData = this.prepareStudentOfferGridData(this.grid.students.data);
        this.setPagination(this.resource, this.grid.students.meta);
    },
    components: {
        Layout,
        PageTitleContent,
        OfferHeaderTabs,
        StudentOfferGrid,
        'search-input': IconInput,
        Button,
        'icon-download': IconArrowDownload24Regular,
        HeaderTab,
    },
    computed: {
        getFilters() {
            return this.filters || {};
        },
        getActionBtns() {
            return this.headerActions;
        },
    },
    methods: {
        prepareStudentOfferGridData,
        setPagination,
        sortDataHandler(sort) {
            this.resource.state.filters.sort = sort[0]?.field || null;
            this.resource.state.filters.dir = sort[0]?.dir || null;
        },
        changePageHandler(page, take) {
            this.resource.state.filters.page = page;
            this.resource.state.filters.take = take;
        },
        updateFilter(filters) {
            Object.keys(filters).reduce((acc, key) => {
                const value = filters[key];
                // If the value is an array, convert it to a comma-separated string
                this.resource.state.filters[key] = Array.isArray(value) ? value.join(',') : value;
                return acc;
            }, {});
            this.resource.state.filters.page = 1;
        },
        handleExport($e) {
            this.resource.exportdata({ export: $e });
        },
    },
    watch: {
        grid: {
            handler(newval, oldval) {
                console.log('newval', newval);
                this.gridData = this.prepareStudentOfferGridData(newval.students.data);
                this.setPagination(this.resource, newval.students.meta);
            },
            deep: true,
        },
    },
};
</script>
<style lang=""></style>
