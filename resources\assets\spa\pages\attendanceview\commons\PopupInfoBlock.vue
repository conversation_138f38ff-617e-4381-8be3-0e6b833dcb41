<template>
    <div class="inline-flex w-[472px] items-center justify-start gap-8 pr-[120px]">
        <div class="flex h-5 items-center justify-start gap-1">
            <div class="flex h-4 w-4 items-center justify-center pb-1 pl-1 pr-1 pt-1">
                <img :src="icon" class="h-4 w-4" />
            </div>
            <div
                class="shrink grow basis-0 text-sm font-normal leading-tight tracking-tight text-gray-400"
            >
                {{ label }}
            </div>
        </div>
        <div class="flex h-5 items-center justify-start gap-1">
            <div class="flex h-5 w-5 items-center justify-center pb-1 pl-1 pr-1 pt-1"></div>
            <div class="text-sm font-normal leading-tight tracking-tight text-gray-900">
                {{ labelValue }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
    labelValue: String,
    label: String,
    icon: String,
});
</script>
