<x-v2.layouts.default>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/tagify.css') }}" type="text/css" />
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/student.css') }}">
    </x-slot>
    {{-- Filter panelbar template --}}
    <script id="filterPanelBarTemplate" type="text/kendo-ui-template">

        <div class="flex flex-col items-start justify-start w-full #: item.value #">

            # if (item.id != 0 && typeof item.text != 'undefined') { #
            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-#: item.id # ">
                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.text #</span>
            </div>
            # } #

            # if (item.type == 'switch') { #
            <div class="flex items-center justify-center">
                <label for="switch_#: item.value.toLowerCase() #_course" class="flex items-center cursor-pointer">
                    <div class="relative">
                        <input type="checkbox" class="sr-only external-filter" role="switch" id="switch_#: item.value.toLowerCase() #_course" data-category="#: item.field #" value="#: item.value #" data-val="#: item.original #" checked/>
                        <div class="w-10 h-5 bg-gray-200 rounded-full shadow-inner outer-dot"></div>
                        <div class="dot absolute w-5 h-5 bg-white rounded-full shadow left-0 top-0 transition"></div>
                    </div>
                    <div class="ml-3 text-gray-700 font-medium">
                        #: item.value #
                    </div>
                </label>
            </div>
            # } #

            # if (item.type == 'input') { #
            <div class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
                <img src="{{ asset('v2/img/search.png') }}" class="h-4 w-4" alt="searchIcon" />
                # if(item.field == 'course'){ #
                    <input type="text" data-value="#: item.value #" class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } else { #
                    <input type="text" data-value="#: item.value #" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } #
            </div>
            # } #

            # if (item.type == 'checkbox') { #
            <div class="inline-flex space-x-2 items-center justify-start">
                <div class="form-check flex items-center">
                    <input class="form-check-input external-filter mt-0 f-checkbox appearance-none h-4 w-4 border border-gray-300 rounded-sm bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200  align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer" type="checkbox" value="#: item.value #" data-category="#: item.field #" data-val="#: item.original #" id="#: item.category_id #_checkbox_#: item.id #">
                    <label class="text-sm leading-5 text-gray-700 h-full" for="#: item.category_id #_checkbox_#: item.id #" data-val="#: item.original #" >
                        #: item.subtext #
                    </label>
                </div>
            </div>
            # } #

            # if (item.type == 'dropdown') { #
            <select class="external-filter inline-flex space-x-2 items-center w-full justify-start inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 text-gray-500 custom-option leading-6 text-sm #: item.field #" id="#: item.value #">
                # if(item.field == 'Course') { #
                    <option value="">Select Course</option>
                # } #

                # for(var i=0; i < item.arr.length; i++){ #
                    <option value="#: item.arr[i].id #" title="#: item.arr[i].text #">#: ((item.arr[i].text.length > 30) ? (item.arr[i].text.substring(0,30) + '...') : item.arr[i].text) #</option>
                # } #
            </select>
            # } #

            # if (item.type == 'button') { #
            <button class="inline-flex space-x-2 items-center justify-center w-full h-10 py-1.5 bg-white border rounded-lg border-gray-300 hover:bg-primary-blue-500 hover:text-white" id="#: item.value #">
                <span class="text-sm font-medium leading-none text-blue-500 hover:text-white">
                    <span class="k-icon k-i-plus text-blue-500 hover:text-white"></span>
                    Add Course
                </span>
            </button>
            # } #

        </div>

    </script>

    {{-- Student details when expand row --}}
    <script id="rowDetailTemplate" type="text/html">
        <div class='student-details'>
            <div class="grid grid-cols-2 gap-4 bg-gray-100 w-full overflow-auto">
                <div class="inline-flex flex-col space-x-6 m-4">
                    <div class="flex space-x-4 m-5">
                        # if (profile_pic == '') { let name = student_name.toUpperCase().split(/\s+/); let shortName =
                        name[0].charAt(0) + name[1].charAt(0); #
                        <div class="w-16 h-16 rounded-full">
                            <div class='flex user-profile-pic w-16 h-16 rounded-full bg-primary-blue-500 items-center'>
                                <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#=
                                    shortName
                                    #</span></div>
                        </div>
                        # } else { #
                        <div class="w-16 h-16 rounded-full">
                            <img class="w-16 h-16 flex-1 rounded-full" src="#= profile_pic #" />
                        </div>
                        # } #
                        <div class="space-y-2">
                            <p class="view_profile text-base font-medium leading-6 text-gray-900 cursor-pointer hover:text-blue-500"
                                data-student-id="#: id #">#: student_name #</p>
                            <div class="inline-flex space-x-4 items-start justify-start">
                                {{-- <div class="flex items-center justify-center px-2.5 py-1 bg-gray-300 rounded-full">
                                    <p class="text-xs leading-none text-center text-gray-800">CURRENT XXXX</p>
                                </div> --}}
                                <div class="flex space-x-1 items-center justify-start">
                                    <p class="text-sm leading-5 text-gray-700">#: student_id #</p>
                                    <x-v2.copy width="16" height="16" data-text="#: student_id #" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <div class="inline-flex flex-col space-y-3 items-start justify-start">
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/globe.svg') }}" class="h-5 w-5" alt="overseasIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: stud_type_name #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/gift.svg') }}" class="h-5 w-5" alt="bdayIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: convertJsDateFormat(DOB)
                                    #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/mail.svg') }}" class="h-5 w-5" alt="emailIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: email #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/phone.svg') }}" class="h-5 w-5" alt="phoneIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: contact #</p>
                            </div>
                            <div class="inline-flex space-x-5 justify-start w-full">
                                <img src="{{ asset('v2/img/location.svg') }}" class="h-5 w-5" alt="locationIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: address #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/contacts.svg') }}" class="h-5 w-5" alt="dairyIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: ((emergency == null) ?
                                    'N/A': emergency) #</p>
                            </div>
                        </div>
                        <div class="inline-flex flex-col space-y-3 items-start justify-start">
                            <div class="inline-flex items-center justify-start">
                                <p class="w-24 text-sm leading-5 font-medium text-gray-900">Emergency</p>
                                <div class="inline-flex flex-col items-start justify-start w-56">
                                    <p class="text-sm font-normal leading-5 text-gray-700">#: ((emergency_phone == null)
                                        ? 'N/A': emergency_phone) #</p>
                                </div>
                            </div>
                            <div class="inline-flex items-center justify-start w-full">
                                <p class="w-24 text-sm leading-5 font-medium text-gray-900">USI</p>
                                <div class="flex-1">
                                    <p class="w-full text-sm font-normal leading-5 text-gray-700">#: ((USI == null) ?
                                        'N/A': USI) #</p>
                                </div>
                            </div>
                            <div class="inline-flex  justify-start w-full">
                                <p class="w-24 text-sm leading-5 font-medium text-gray-900">Agent</p>
                                <div class="flex-1">
                                    <p class="w-full text-sm font-normal leading-5 text-gray-700">#: agency_name #</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="inline-flex flex-col justify-center space-y-2 m-8">
                    #for(var i=0; i< course_detail.length; i++){# <div
                        class="bg-white p-4 relative rounded-lg items-center justify-start shadow w-full">
                        # let color = (course_detail[i]["status"] == 'Current Student') ? 'green' :
                        ((course_detail[i]["status"] == 'Cancelled') ? 'red' : 'blue'); #
                        <div class="w-1 h-full absolute left-0 top-0 rounded-l-lg bg-#= color #-500"></div>
                        {{-- <img class="w-1 h-20 absolute left-0 top-0 rounded-lg"
                            src="https://via.placeholder.com/4x101" />--}}
                        <div class="flex-col inline-flex space-y-4 w-full">
                            <div class="inline-flex items-center justify-between space-x-2">
                                <div class="flex items-center font-normal px-2.5 py-0.5 bg-gray-200 rounded">
                                    <p class="text-xs leading-4 font-normal text-gray-500">#=
                                        course_detail[i]["course_code"] #</p>
                                </div>
                                <p class="text-sm font-medium leading-5 text-gray-700 truncate w-auto">#=
                                    course_detail[i]["course_name"] #</p>
                                <div class="flex items-center justify-end px-2.5 py-0.5 bg-gray-200 rounded">
                                    <p class="text-xs leading-4 font-normal text-gray-700 truncate">#=
                                        course_detail[i]["status"].toUpperCase() #</p>
                                </div>
                            </div>
                            <div class="inline-flex items-center justify-start space-x-4">
                                <div class="flex space-x-4 items-start justify-start">
                                    <div class="flex space-x-2 items-center justify-start">
                                        <p class="text-xs font-medium leading-4 text-gray-700">Start Date</p>
                                        <p class="text-xs leading-4 text-gray-700">#=
                                            convertJsDateFormat(course_detail[i]["start_date"]) #</p>
                                    </div>
                                    <div class="flex space-x-2 items-center justify-start">
                                        <p class="text-xs font-medium leading-4 text-gray-700">End Date</p>
                                        <p class="text-xs leading-4 text-gray-700">#=
                                            convertJsDateFormat(course_detail[i]["finish_date"]) #</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2 items-center justify-start">
                                    <div class="inline-flex space-x-0.5 items-start justify-center">
                                        # for(let j=0; j < course_detail[i]['course_progress'].length; j++){ # <div
                                            class="flex items-center justify-center w-1.5 h-6 bg-#= course_detail[i]['course_progress'][j]['color'] # rounded">
                                            # if(course_detail[i]['course_progress'][j]['is_active']==1){ #
                                            <div class="arrow-down"></div>
                                            # } #
                                    </div>
                                    # } #
                                </div>
                            </div>
                        </div>
                </div>
            </div>
            #}#
        </div>
        </div>
        </div>
    </script>
    {{-- Tooltip with student details when hover on full name column --}}
    <script id="tooltipTemplateForFullName" type="text/html">
        <div
            class="firstTdTooltip inline-flex flex-col space-y-2 items-center justify-center pb-4 bg-white shadow-xl rounded-md border border-gray-100 w-full h-full">
            <div class="flex items-center gap-2 py-2 px-4 bg-gray-200 rounded-tl-md rounded-tr-md w-full">
                <div class="flex items-center gap-2">
                    <div class="w-12 h-12 rounded-md">
                        # if (arr.profile_pic == '') { let name = arr.student_name.toUpperCase().split(/\s+/); let
                        shortName = name[0].charAt(0) + name[1].charAt(0); #
                        <div class='flex user-profile-pic w-12 h-12 !rounded-md bg-primary-blue-500 items-center'><span
                                class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                                #</span></div>
                        # } else { #
                        <img class="w-12 h-12 flex-1 !rounded-md object-cover" src="#= arr.profile_pic #" />
                        # } #
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center gap-2">
                        <p class="view_profile text-lg font-medium leading-tight text-gray-700 cursor-pointer hover:text-primary-blue-500"
                            data-student-id="#: arr.secure_id #">#: arr.student_name #</p>
                        <div class="flex items-center gap-1">
                            <p class="text-xs leading-tight text-gray-700">#: arr.student_id #</p>
                            <x-v2.copy width="16" height="16" data-text="#: arr.student_id #" />
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="inline-flex gap-2 items-center">
                            <span class="text-xs text-gray-500">Login Status</span>
                            <div class="flex space-x-1 items-center justify-start">
                                # let color2=(arr.galaxy_status) ? 'green' : 'gray'; #
                                <div class="flex items-center justify-center px-2.5 py-1 bg-#= color2 #-200 rounded-md">
                                    <p
                                        class="text-xs leading-none font-normal text-center text-#= color2 #-900 truncate">
                                        #=
                                        arr.galaxy_status ? 'Active' : 'Inactive' # </p>
                                </div>
                            </div>
                            <div class="inline-flex gap-2 items-center">
                                <span class="text-xs text-gray-500">Last Login</span>
                                <p class="font-medium text-xs text-gray-700">#= arr.last_login ?
                                    convertJsDateFormat(arr.last_login) : 'N/A' # </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-4 w-full overflow-hidden flex-1">
                #for(var i=0; i<1; i++){# # let color2=(arr.student_courses[i]["status"]=='Current Student' ) ? 'green'
                    : ((arr.student_courses[i]["status"]=='Cancelled' ) ? 'red' : 'blue' ); # <div
                    class="bg-white relative rounded-lg shadow w-full">
                    <div class="w-1 h-full absolute left-0 top-50 rounded-l-lg bg-#= color2 #-500"></div>
                    <div class="p-2 space-y-1 w-full border-gray-200 border rounded-md overflow-hidden">
                        <div class="inline-flex items-center justify-between gap-2">
                            <div class="flex items-center justify-center px-2.5 py-1 bg-gray-200 rounded">
                                <p class="text-xs leading-none text-center text-gray-500">#=
                                    arr.student_courses[i].course.course_code #</p>
                            </div>
                            <p
                                class="text-sm font-medium leading-tight text-gray-700 truncate max-w-[17rem] glob-tooltip">
                                #= arr.student_courses[i].course.course_name #</p>
                            <div class="flex items-center justify-center px-2.5 py-1 bg-blue-200 rounded-full">
                                <p class="text-xs leading-none text-center text-blue-900 truncate">#=
                                    arr.student_courses[i]["status"].toUpperCase() # </p>
                            </div>
                        </div>
                        <div class="inline-flex items-center gap-4">
                            <div class="flex gap-4 items-start truncate">
                                <div class="flex gap-2 items-center">
                                    <p class="text-xs font-medium leading-none text-gray-700">Start date</p>
                                    <p class="text-xs leading-none text-gray-700"> #=
                                        convertJsDateFormat(arr.student_courses[i]["start_date"]) #
                                    </p>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <p class="text-xs font-medium leading-none text-gray-700">End date</p>
                                    <p class="text-xs leading-none text-gray-700">#=
                                        convertJsDateFormat(arr.student_courses[i]["finish_date"]) #
                                    </p>
                                </div>
                            </div>
                            <div class="flex gap-2 items-center w-auto truncate min-h-[1.5rem]">
                                <div class="inline-flex gap-0.5 items-start justify-center w-auto">

                                </div>
                            </div>
                        </div>
                    </div>
            </div>
            #}#
        </div>
        <div class="inline-flex gap-4 items-center justify-between px-4 w-full">
            <button id="viewProfileBtn" class="view_profile btn-primary px-2.5 py-2"
                data-student-id="#: arr.secure_id #">
                <p class="text-xs font-medium leading-none text-white">View Full Profile</p>
            </button>
            <div class="flex gap-6 items-center">
                <button id="single_email" class="single_email" data-student-id="#: arr.id #"
                    data-name="#: arr.student_name.toUpperCase() #" data-contact="#: arr.email #">
                    <img src="{{ asset('v2/img/mailshape.svg') }}" class="h-5 w-5" alt="mailIcon" />
                </button>
                <button id="single_sms" class="single_sms">
                    <img src="{{ asset('v2/img/sms_shape.svg') }}" class="h-5 w-5" alt="smsIcon" />
                </button>
            </div>
        </div>
        </div>
    </script>

    {{-- Tooltip for current course column --}}
    <script id="tooltipTemplateForCurrentCourse" type="text/html">
        <div
            class="inline-flex flex-col space-y-2.5 items-start justify-start bg-white shadow border rounded-md p-4 border-gray-100 w-72 max-h-72 overflow-y-auto">
            <div class="flex flex-col space-y-2 items-start justify-start">
                <p class="w-full text-sm font-medium leading-tight text-gray-700">#= arr.course.course_code # - #=
                    arr.course.course_name #</p>
                <div class="inline-flex items-center justify-center px-2.5 py-0.5 bg-green-100 rounded-full">
                    <p class="text-xs leading-none text-center text-green-800">#= arr.status #</p>
                </div>
                <div class="flex flex-col space-y-4 p-2 items-start justify-start">

                    <div class="grid grid-flow-col gap-2">
                        <div class="w-10 h-10 row-span-2">
                            <div class="w-10 h-10 m-1 bg-white rounded-full">

                                <svg viewBox="0 0 36 36" class="circular-chart blue">
                                    <path class="circle-bg" d="M18 2.0845
                                        a 15.9155 15.9155 0 0 1 0 31.831
                                        a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="circle bg-primary-blue-500"
                                        stroke-dasharray="#= Math.round(arr['days'] * 100 / arr['diff_days']) #, 100" d="M18 2.0845
                                        a 15.9155 15.9155 0 0 1 0 31.831
                                        a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <text x="18" y="20.35" class="percentage">#= Math.round(arr.days * 100 /
                                        arr.diff_days) #%</text>
                                </svg>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <p
                                class="relative font-medium inset-x-0 leading-none mx-auto text-gray-700 text-xs top-0 mt-1">
                                #= arr["days"] # of #= arr["diff_days"] # days</p>
                            <p class="relative leading-none bottom-0 right-0 text-gray-500 text-xs mt-1">Ending #=
                                convertJsDateFormat(arr["finish_date"]) #</p>
                        </div>
                        {{-- <div class="col-span-2">
                            <p class="text-xs font-medium leading-none text-gray-900">#= arr["days"] # of #=
                                arr["diff_days"] # days</p>
                        </div>
                        <div class="col-span-2">
                            <p class="text-xs leading-none text-gray-500">Ending #= arr["finish_date"] #</p>
                        </div> --}}

                    </div>
                    <div class="grid grid-flow-col gap-2">
                        <div class="w-10 h-10 row-span-2">
                            <div class="w-10 h-10 m-1 bg-white rounded-full">
                                # let unitPer = ((arr2[0].use_unit > 0) ? (arr2[0].use_unit * 100 / arr2[0].total_unit)
                                : 0); #
                                <svg viewBox="0 0 36 36" class="circular-chart green">
                                    <path class="circle-bg" d="M18 2.0845
                                        a 15.9155 15.9155 0 0 1 0 31.831
                                        a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="circle bg-primary-blue-500"
                                        stroke-dasharray="#= (Number.isInteger(unitPer)) ? unitPer : unitPer.toFixed(1) #, 100"
                                        d="M18 2.0845
                                        a 15.9155 15.9155 0 0 1 0 31.831
                                        a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <text x="18" y="20.35" class="percentage">#= (Number.isInteger(unitPer)) ? unitPer :
                                        unitPer.toFixed(1) #%</text>
                                </svg>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <p
                                class="relative font-medium inset-x-0 leading-none mx-auto text-gray-700 text-xs top-0 mt-1">
                                #= arr2[0].use_unit # of #= arr2[0].total_unit # units</p>
                            <p class="relative leading-none bottom-0 right-0 text-gray-500 text-xs mt-1">#=
                                arr2[0].title #</p>
                        </div>
                    </div>
                </div>
            </div>

            #for(var i=0; i
            < course_list.length; i++){# #if(i !=0){# <div class="w-full h-0.5 bg-gray-200" />
            <div class="w-full">
                <p class="w-full text-sm font-medium leading-tight text-gray-700"> #= course_list[i].course.course_code
                    # - #= course_list[i].course.course_name #</p>
                <div class="inline-flex items-center justify-center px-2.5 py-0.5 bg-green-100 rounded-full">
                    <p class="text-xs leading-none text-center text-green-800">#= course_list[i].status #</p>
                </div>
            </div>
            #}#
            #}#

        </div>
    </script>

    {{-- Tooltip for course progress column --}}
    <script id="tooltipTemplateForCourseProgress" type="text/html">
        <div class="p-4 bg-white shadow-xl border rounded-md border-gray-100 min-w-20">
            <p class="text-xs leading-none text-gray-700">#= displayOrDefault(unit_name) #</p>
        </div>
    </script>

    {{-- Tooltip for attendance column --}}
    <script id="tooltipTemplateForAttendance" type="text/html">
        <div class="space-y-2 p-4 bg-white shadow-xl border rounded-md border-gray-100">
            <div class="inline-flex space-x-12 items-center justify-between w-full">
                <div class="flex space-x-2 items-center">
                    <div class="w-2 h-2 bg-green-500"></div>
                    <p class="text-xs leading-none text-gray-700">Present</p>
                </div>
                <p class="text-xs leading-none text-right text-gray-500 truncate">#= (arr.present_day == null ) ?
                    0:arr.present_day # days</p>
            </div>
            <div class="inline-flex space-x-12 items-center justify-between w-full">
                <div class="flex space-x-2 items-center">
                    <div class="w-2 h-2 bg-yellow-500"></div>
                    <p class="text-xs leading-none text-gray-700">Absent</p>
                </div>
                <p class="text-xs leading-none text-right text-gray-500 truncate">#= (arr.absent_day == null ) ?
                    0:arr.absent_day # days</p>
            </div>
            <div class="inline-flex space-x-12 items-center justify-between w-full">
                <div class="flex space-x-2 items-center">
                    <div class="w-2 h-2 bg-gray-200"></div>
                    <p class="text-xs leading-none text-gray-700">Total</p>
                </div>
                <p class="text-xs leading-none text-right text-gray-500 truncate">#= arr.total_day # days</p>
            </div>
        </div>
    </script>

    {{-- Tooltip for payment column --}}
    <script id="tooltipTemplateForPayment" type="text/html">
        <div class="space-y-2 p-4 bg-white shadow-xl border rounded-md border-gray-100">
            <div class="inline-flex space-x-12 items-center justify-between w-full">
                <div class="flex space-x-2 items-center">
                    <div class="w-2 h-2 bg-green-teal-500"></div>
                    <p class="text-xs leading-none text-gray-700 truncate">Amount Paid</p>
                </div>
                <p class="text-xs leading-none text-right text-gray-500 truncate">$#= ((arr.paid) ?
                    arr.paid.toLocaleString() : 0) #</p>
            </div>
            <div class="inline-flex space-x-12 items-center justify-between w-full">
                <div class="flex space-x-2 items-center">
                    <div class="w-2 h-2 bg-yellow-500"></div>
                    <p class="text-xs leading-none text-gray-700 truncate">Payment Missed</p>
                </div>
                <p class="text-xs leading-none text-right text-gray-500 truncate">#= ((arr.missed_count) ?
                    arr.missed_count : 0) # | $#= ((arr.missed) ? arr.missed.toLocaleString() : 0) #</p>
            </div>
            <div class="inline-flex space-x-12 items-center justify-between w-full">
                <div class="flex space-x-2 items-center">
                    <div class="w-2 h-2 bg-primary-blue-500"></div>
                    <p class="text-xs leading-none text-gray-700 truncate">Amount Remaining</p>
                </div>
                <p class="text-xs leading-none text-right text-gray-500 truncate">$#= ((arr.unpaid) ?
                    arr.unpaid.toLocaleString() : 0) #</p>
            </div>
            <div class="inline-flex space-x-12 items-center justify-between w-full">
                <div class="flex space-x-2 items-center">
                    <div class="w-2 h-2 bg-gray-200"></div>
                    <p class="text-xs leading-none text-gray-700 truncate">Total Amount</p>
                </div>
                <p class="text-xs leading-none text-right text-gray-500 truncate">$#= ((arr.total) ?
                    parseInt(arr.total).toLocaleString() : 0) #</p>
            </div>
        </div>
    </script>

    {{-- Tooltip/template for action list --}}
    <script id="actionListTemplate" type="text/html">
        <div class="action-menu z-10">
            <div class="space-y-1 px-1 py-2">
                <!-- <button class="view_miniprofile inline-flex items-center justify-between w-full hover:bg-primary-blue-500 text-left" data-student-id="#: id #" data-name="#: student_name#" data-profile="#: profile #" data-contact="#: contact#">
                        <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">View Profile</span>
                    </button> -->
                #if(username == null){#
                <x-v2.dropdown-menuitem data-isBulk="0" class="inviteToGalaxyBtn" data-college_name="#: college_name #"
                    data-first_name="#: first_name #" data-family_name="#: family_name #" data-email="#: email #"
                    data-student-id="#: sId #" data-name="#: student_name#">
                    <span class="text-xs leading-5 text-gray-700">Invite to Galaxy</span>
                </x-v2.dropdown-menuitem>
                #}#
                <x-v2.dropdown-menuitem class="issue_single_letter" data-student-id="#: sId #"
                    data-name="#: student_name#" data-profile="#: profile #" data-contact="#: contact#">
                    <span class="text-xs leading-5 text-gray-700">Send Letter</span>
                </x-v2.dropdown-menuitem>
            </div>
            <div class="w-full h-px bg-gray-100"></div>
            <div class="space-y-1 px-1 py-2">
                <a href="/student-profile-view/#: id #?activetab=course" target="_blank"
                    class="flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                    <span class="text-xs leading-5 text-gray-700">View Course Progress</span>
                </a>
                <a href="/student-profile-view/#: id #?activetab=attendance" target="_blank"
                    class="flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                    <span class="text-xs leading-5 text-gray-700">View Attendance</span>
                </a>
            </div>
            <div class="w-full h-px bg-gray-100"></div>
        </div>
    </script>

    {{-- Email templates for send email modal --}}
    <script id="emailPanelBarTemplate" type="text/kendo-ui-template">
        # if (item.id != 0 && typeof item.title != 'undefined') { #
        <div class="inline-flex space-x-4 items-center justify-between w-full">
            <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.title #</span>
        </div>
        # } #

        # if (item.id != 0 && typeof item.template_name != 'undefined') { #
        <div class="email_template w-full tw-panelbar__sub-item hover:bg-primary-blue-50 ps-4 py-2 pe-2.5 rounded-md text-sm truncate cursor-pointer" data-id="#: item.id #">
            #: item.template_name #
        </div>
        # } #
    </script>

    {{-- Email Attachment List --}}
    <script id="attachmentList" type="text/html">
        # if (files.length > 0) { #
        #for (var i = 0; i < files.length; i++){ # <div
            class="flex space-x-0.5 items-center justify-center py-0.5 pl-2.5 pr-1 bg-gray-200 rounded-full">
            <p class="text-xs leading-tight text-center text-gray-800">#=files[i].file#</p>
            <span class="cursor-pointer k-icon k-i-close existing_attachment" data-file-id="#=files[i].id#"
                data-file-name="#=files[i].file#"></span>
            </div>
            # } #
            # } else { #
            <p class="text-xs leading-tight text-center text-gray-800">No attachments available</p>
            # } #
    </script>

    <div class="flex flex-row w-full splitter" id="studentListSplitter">
        <div id="toggleFilter">
            <div style="overflow: auto;"
                class="relative toggelfilter transition-all duration-450 filter-left-sidebar space-y-4 pt-4 border-r bg-gray-50 widthzero w-[320px]">
                {{-- <ul id="panelbar" class="w-full flex items-start justify-start">
                </ul> --}}
                <ul id="studentsFilterPanelbar" class="tw-panelbar--expand w-full">
                    <li>
                        <span class="w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-3">
                                <span class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Student By
                                    Intakes</span>
                            </div>
                        </span>
                        <!-- <div>
                            <input class="" id="filter_list">
                        </div> -->
                        <div>
                            <div class="flex flex-row w-full space-x-1">
                                <div class="flex w-1/2">
                                    <input class="" id="intake_year">
                                </div>
                                <div class="flex w-1/2">
                                    <input class="" id="intake_date">
                                </div>
                            </div>
                            <!-- <div class="flex flex-row w-full space-x-1 intake_between mt-2">
                                <div class="flex w-1/2">
                                    <input class="" id="intake_year_2">
                                </div>
                                <div class="flex w-1/2">
                                    <input class="" id="intake_date_2">
                                </div>
                            </div> -->
                        </div>
                        <div class="w-full add_course">
                            <button type="button" class="btn-secondary w-full h-9 py-1 px-2" id="intake_add_btn">
                                <span
                                    class="k-icon k-i-plus text-sm font-medium leading-5 text-primary-blue-500"></span>
                                <p class="text-xs font-medium leading-5 text-primary-blue-500 uppercase">Add</p>
                            </button>
                        </div>
                        <div>
                            <ul class="student-filter overflow-y-auto k-checkbox-list k-list-vertical"
                                id="student_intake"></ul>
                        </div>
                    </li>
                    <li>
                        <span class="w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-1">
                                <span class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Course
                                    Name</span>
                            </div>
                        </span>
                        <div>
                            <span class="w-full">
                                <div class="inline-flex  p-2 items-center w-full ">
                                    <input type="checkbox" class="k-checkbox" name="showInActiveCourse"
                                        id="showInActiveCourse">
                                    <label class="k-checkbox-label" for="d453204b-3237-4272-8e8d-afbdc78990e4">Show
                                        Inactive Courses</label>
                                </div>
                            </span>
                            <div
                                class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2"
                                    class="filterSearchInput h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full"
                                    data-field="course_id" placeholder="Search Course">
                            </div>
                            <ul class="student-filter overflow-y-auto" id="course_id"></ul>
                        </div>
                    </li>
                    <li>
                        <span class="flex flex-col items-start justify-start w-full undefined">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-5">
                                <span class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Status</span>
                            </div>
                        </span>
                        <div>
                            <div
                                class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2"
                                    class="filterSearchInput h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full"
                                    data-field="status" placeholder="Search Status">
                            </div>
                            <ul class="student-filter overflow-y-auto" id="status"></ul>
                        </div>
                    </li>
                    <li>
                        <span class="w-full">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-2">
                                <span class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Type</span>
                            </div>
                        </span>
                        <div>
                            <ul class="student-filter overflow-y-auto" id="student_type"></ul>
                        </div>
                    </li>
                    <li>
                        <span class="flex flex-col items-start justify-start w-full undefined">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-2">
                                <span class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Campus</span>
                            </div>
                        </span>
                        <div>
                            <ul class="student-filter overflow-y-auto" id="campus_id"></ul>
                        </div>
                    </li>

                    <li>
                        <span class="flex flex-col items-start justify-start w-full undefined">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-4">
                                <span
                                    class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Nationality</span>
                            </div>
                        </span>
                        <div>
                            <div
                                class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2"
                                    class="filterSearchInput h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full"
                                    data-field="nationality" placeholder="Search Nationality">
                            </div>
                            <ul class="student-filter overflow-y-auto" id="nationality"></ul>
                        </div>
                    </li>

                    <li>
                        <span class="flex flex-col items-start justify-start w-full undefined">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-6">
                                <span class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Batch</span>
                            </div>
                        </span>
                        <div>
                            <div
                                class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2"
                                    class="filterSearchInput h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full"
                                    data-field="batch" placeholder="Search Batch">
                            </div>
                            <ul class="student-filter overflow-y-auto" id="batch"></ul>
                        </div>
                    </li>
                    <li>
                        <span class="flex flex-col items-start justify-start w-full undefined">
                            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-7">
                                <span class="text-sm font-medium leading-5 text-gray-700 cursor-pointer">Teacher</span>
                            </div>
                        </span>
                        <div>
                            <div
                                class="inline-flex space-x-2 items-center mb-2 px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full cusInput">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                                <input type="text" data-value="2"
                                    class="filterSearchInput h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full"
                                    data-field="teacher" placeholder="Search Teacher">
                            </div>
                            <ul class="student-filter overflow-y-auto" id="teacher"></ul>
                        </div>
                    </li>
                </ul>
                <div class="filterFooterBox flex flex-col items-center justify-start w-full py-3.5 bg-white border absolute inset-x-0 bottom-0"
                    style="display: none;">
                    <div class="flex space-x-4 items-center justify-end">
                        <div
                            class="flex items-center justify-center w-20 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300">
                            <button type="button" id="clearFilter"
                                class="text-xs font-medium leading-none text-gray-700">Clear</button>
                        </div>
                        <div
                            class="flex items-center justify-center w-24 h-full px-3 py-2 bg-primary-blue-500 shadow rounded-lg">
                            <button id="applyFilter" class="text-xs font-medium leading-tight text-white">Apply
                                <span></span></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="filter-result z-10 !w-full bg-bluegray-100" id="">
            <div class="flex flex-col justify-center pt-6 pb-4 pl-8 pr-6 w-full">
                <div class="searchdata flex gap-2 items-center justify-between w-full">
                    <div class="flex gap-2 items-center">
                        <button type="button" id="filterBtn" class="active btn-secondary btn-icon">
                            <img src="{{ asset('v2/img/f_icon.svg') }}" class="w-[14px] h-[14px]" alt="">
                            <span class="text-sm leading-5 font-normal text-gray-700">Filters</span>
                            <div
                                class="flex items-center justify-center px-3 py-0.5 bg-primary-blue-100 rounded-full filterCountDiv hidden">
                                <span class="text-sm leading-5 text-primary-blue-800 filterCount">1</span>
                            </div>
                        </button>
                        <p class="text-base font-medium leading-tight text-gray-700 filter_title" style="display: none">
                            Showing 1 to <span class="w-3 bg-gray-100 animate-pulse h-3 inline-block"></span> of <span
                                class="w-4 bg-gray-200 animate-pulse h-3 inline-block"></span> students</p>
                    </div>
                    <div class="flex space-x-2 items-center justify-end">
                        <div class="btn-secondary px-4 totalStudentCount hidden hover:shadow-none">
                            <span class="text-sm font-normal text-gray-900 k-grid-excel">Total Students:
                                <span class="totalStudent">5555</span></span>
                        </div>
                        <div class="relative">
                            <div
                                class="flex space-x-2 items-center justify-start w-40 h-[2.125rem] px-3 py-2 bg-white border rounded-lg border-gray-300 hover:shadow cusInput activeExpandBtn duration-200 ">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M19 19L13 13M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z"
                                        stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                                <input type="text" id="offscreen" autocomplete="off" data-grid-id="studentList"
                                    class="textInputField text-sm leading-5 font-normal text-gray-400"
                                    placeholder="Search students">
                                <span class="text-primary-blue-500 search-loading" style="display: none;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-loader-circle-icon lucide-loader-circle animate-spin text-primary-blue-500">
                                        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                                    </svg>
                                </span>
                            </div>
                            <div id="search-message"
                                class="search-message border border-gray-200 bg-gray-50 text-gray-700 text-xs absolute px-2 h-8 leading-8 shadow rounded-md inset-x-0 top-[calc(100%+4px)] z-10"
                                style="display: none;">
                            </div>
                        </div>

                        <button type="button" id="exportData" class="btn-secondary px-4">
                            <span class="text-sm font-normal leading-tight text-gray-900 k-grid-excel">Export</span>
                        </button>
                        <div>
                            <button type="button" id="manageColumns"
                                class="btn-secondary w-9 h-full px-2.5 py-2.5 glob-tooltip" title="Columns">
                                <svg width="14" height="12" viewBox="0 0 14 12" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7.00033 9.3335V2.66683M7.00033 9.3335C7.00033 10.0699 6.20439 10.6668 5.22255 10.6668H3.44477C2.46293 10.6668 1.66699 10.0699 1.66699 9.3335V2.66683C1.66699 1.93045 2.46293 1.3335 3.44477 1.3335H5.22255M7.00033 9.3335C7.00033 10.0699 7.79626 10.6668 8.7781 10.6668H10.5559C11.5377 10.6668 12.3337 10.0699 12.3337 9.3335V2.66683C12.3337 1.93045 11.5377 1.3335 10.5559 1.3335H8.7781M5.22255 1.3335C6.20439 1.3335 7.00033 1.93045 7.00033 2.66683M5.22255 1.3335H8.7781M7.00033 2.66683C7.00033 1.93045 7.79626 1.3335 8.7781 1.3335"
                                        stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </button>
                            <div class="manageColumnBox">
                                <div class="relative w-full">
                                    <div
                                        class="manage-column-box__dropdown absolute top-2 right-0 w-auto pt-4 bg-white shadow border rounded-lg border-gray-200 space-y-2 min-w-[320px]">
                                        <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                                            <div class="inline-flex space-x-4 items-center justify-between w-full">
                                                <p class="text-sm font-medium leading-tight text-gray-700 pb-2">Columns
                                                </p>
                                            </div>
                                            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                                                @foreach ($column_arr as $column)
                                                <div class="inline-flex items-center justify-start w-full pr-2 rounded">
                                                    <div class="flex space-x-2 items-center justify-start">
                                                        <input type="checkbox" class="k-checkbox cursor-pointer rounded"
                                                            id="fc_{{ $column['id'] }}" value="{{ $column['value'] }}"
                                                            {{ $column['default'] }} />
                                                        <label for="fc_{{ $column['id'] }}"
                                                            class="text-sm leading-none text-gray-700">{{
                                                            $column['title'] }}</label>
                                                    </div>
                                                </div>
                                                @endforeach
                                            </div>
                                            <button
                                                class="text-sm font-medium leading-tight pb-2 pt-1 text-blue-500 reset column_filter">Reset
                                                Columns</button>
                                        </div>
                                        <div
                                            class="inline-flex space-x-4 items-center justify-end w-full py-4 pl-2 pr-2 border-t border-gray-200">
                                            <div class="flex space-x-4 items-center justify-end">
                                                <button type="button"
                                                    class="btn-secondary w-24 h-full px-3 py-2 clear column_filter text-xs">Cancel</button>
                                                <button type="button"
                                                    class="btn-primary w-28 h-full px-3 py-2 save column_filter text-xs">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="appliedFilterList" class="items-center justify-center space-y-2" style="display: none;">
                </div>
            </div>
            <div class="h-fit bg-gray-100">
                <div id="progressbar"></div>
                <div id="studentList" class="student-list-loading overflow-y-hidden bg-white"></div>
            </div>
        </div>
    </div>

    {{-- List of action in bottom using Modal --}}
    <div style="transition: height 0.2s;" id="action"
        class="relative overflow-visible bottomaction heightzero flex items-center justify-between px-8 bg-white shadow border-gray-300 h-16">
        <div class="flex gap-2 items-center justify-start">
            <input id="selectedStudents" name="Selected" type="checkbox"
                class="focus:ring-geekBlue-500 h-4 w-4 k-checkbox cursor-pointer rounded">
            <label id="selected_title" for="selectedStudents"
                class="text-sm leading-5 font-normal text-gray-800 cursor-pointer"></label>
            <button
                class="selectAllStudents text-sky-500 text-xs font-normal leading-tight tracking-wide cursor-pointer">
                <span class="filterTextFooter selectUnSelect">Select All</span>
                <span class="filterTextFooter totalStudent"></span>
                <span class="generalText">in all pages</span>
            </button>
        </div>
        <div class="flex gap-3 items-center justify-end">
            <button title="Send Email" id="bulk_email" class="btn-secondary py-2 px-4">
                <span class="text-sm leading-5 font-normal text-gray-700 group-hover:text-white changeBulkEmail">Send
                    Email</span>
            </button>
            <button title="Send SMS" id="bulk_sms" class="selectedStudentSendSms btn-secondary py-2 px-4">
                <span class="text-sm leading-5 font-normal text-gray-700 group-hover:text-white changeBulkSms">Send
                    SMS</span>
            </button>
            <button title="Generate Letters" id="issue_letter" class="issue_letter btn-secondary py-2 px-4">
                <span class="text-sm leading-5 font-normal text-gray-700 group-hover:text-white">Generate
                    Letters</span>
            </button>
            <button id="inviteToGalaxy" data-isBulk="1" class="inviteToGalaxyBtn btn-secondary py-2 px-4">
                <span class="text-sm leading-5 font-normal text-gray-700 group-hover:text-white">Invite to
                    Galaxy</span>
            </button>
        </div>
        <button type="button" title="Cancel" class="closeAction">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 13L13 1M1 1L13 13" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
            </svg>
        </button>
    </div>

    {{-- Student MiniProfile Modal --}}
    <div id="studentMiniProfileModal" style="display: none;">
        <div class="flex flex-col space-y-4 items-start justify-start w-full">
            <div class="inline-flex items-center justify-start p-2 bg-gray-50 rounded-md w-full">
                <div class="flex space-x-4 items-start justify-start">
                    <button id="single_sms"
                        class="flex single_sms space-x-2 items-center justify-center py-1.5 pl-2 pr-2.5 rounded-lg">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C6.72679 16 5.49591 15.7018 4.38669 15.1393L4.266 15.075L0.621091 15.9851C0.311269 16.0625 0.0262241 15.8369 0.00130987 15.5438L0.00114131 15.4624L0.0149329 15.3787L0.925 11.735L0.86169 11.6153C0.406602 10.7186 0.124331 9.74223 0.0327466 8.72826L0.00737596 8.34634L0 8C0 3.58172 3.58172 0 8 0ZM8 1C4.13401 1 1 4.13401 1 8C1 9.21704 1.31054 10.3878 1.89352 11.4249C1.94046 11.5084 1.9621 11.603 1.95692 11.6973L1.94274 11.7912L1.187 14.812L4.21104 14.0583C4.27294 14.0429 4.33662 14.0396 4.39873 14.0479L4.4903 14.0691L4.57701 14.1075C5.61362 14.6898 6.7837 15 8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1ZM8.5 9C8.77614 9 9 9.22386 9 9.5C9 9.74546 8.82312 9.94961 8.58988 9.99194L8.5 10H5.5C5.22386 10 5 9.77614 5 9.5C5 9.25454 5.17688 9.05039 5.41012 9.00806L5.5 9H8.5ZM10.5 6C10.7761 6 11 6.22386 11 6.5C11 6.74546 10.8231 6.94961 10.5899 6.99194L10.5 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.25454 5.17688 6.05039 5.41012 6.00806L5.5 6H10.5Z"
                                fill="#6B7280" />
                        </svg>
                        <p class="text-xs font-medium leading-5 text-gray-700">SMS</p>
                    </button>
                    <button id="single_email"
                        class="flex single_email space-x-2 items-center justify-center py-1.5 pl-2 pr-2.5 rounded-lg">
                        <svg width="16" height="13" viewBox="0 0 16 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M13.5 0C14.8807 0 16 1.11929 16 2.5V10.5C16 11.8807 14.8807 13 13.5 13H2.5C1.11929 13 0 11.8807 0 10.5V2.5C0 1.11929 1.11929 0 2.5 0H13.5ZM15 3.961L8.25351 7.93097C8.12311 8.00767 7.96661 8.02045 7.82751 7.96932L7.74649 7.93097L1 3.963V10.5C1 11.3284 1.67157 12 2.5 12H13.5C14.3284 12 15 11.3284 15 10.5V3.961ZM13.5 1H2.5C1.67157 1 1 1.67157 1 2.5V2.802L8 6.91991L15 2.801V2.5C15 1.67157 14.3284 1 13.5 1Z"
                                fill="#6B7280" />
                        </svg>
                        <p class="text-xs font-medium leading-5 text-gray-700">Email</p>
                    </button>
                    <button id="single_letter"
                        class="flex issue_single_letter space-x-2 items-center justify-center py-1.5 pl-2 pr-2.5 rounded-lg">
                        <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.08535 1C3.29127 0.417403 3.84689 0 4.5 0H7.5C8.15311 0 8.70873 0.417404 8.91465 1H10.5C11.3284 1 12 1.67157 12 2.5V8.33697L11.3698 6.89819C11.2826 6.69904 11.1554 6.52562 11 6.38568V2.5C11 2.22386 10.7761 2 10.5 2H8.91465C8.70873 2.5826 8.15311 3 7.5 3H4.5C3.84689 3 3.29127 2.5826 3.08535 2H1.5C1.22386 2 1 2.22386 1 2.5V14.5C1 14.7761 1.22386 15 1.5 15H5.08561C4.9672 15.3338 4.97447 15.6857 5.08567 16H1.5C0.671573 16 0 15.3284 0 14.5V2.5C0 1.67157 0.671573 1 1.5 1H3.08535ZM4.5 1C4.22386 1 4 1.22386 4 1.5C4 1.77614 4.22386 2 4.5 2H7.5C7.77614 2 8 1.77614 8 1.5C8 1.22386 7.77614 1 7.5 1H4.5ZM10.4538 7.2994C10.3741 7.11744 10.1942 6.99992 9.9956 7C9.79695 7.00008 9.61719 7.11776 9.53765 7.29979L6.04172 15.2998C5.93114 15.5528 6.04663 15.8476 6.29967 15.9582C6.55271 16.0687 6.84747 15.9533 6.95805 15.7002L8.13802 13H11.859L13.0419 15.7006C13.1527 15.9535 13.4475 16.0688 13.7005 15.958C13.9534 15.8472 14.0687 15.5523 13.9579 15.2994L10.4538 7.2994ZM11.421 12H8.57501L9.99635 8.74745L11.421 12Z"
                                fill="#6B7280" />
                        </svg>
                        <p class="text-xs font-medium leading-5 text-gray-700">Letter</p>
                    </button>
                </div>
            </div>
            <div id="studHistoryData" class="w-full space-y-4"></div>
        </div>
        <script id="studHistoryTemplate" type="text/html">
            <div class="inline-flex flex-col space-y-4 items-center justify-center w-full">
                <div
                    class="flex flex-col space-y-4 items-center justify-start px-2 py-4 bg-primary-blue-50 rounded-md w-full">
                    <div class="flex flex-col space-y-4 items-start justify-start w-full">
                        <div class="flex space-x-16 items-center justify-between w-full">
                            <div class="flex space-x-2 items-start justify-start">
                                <div class="w-12 h-12 rounded-full">
                                    # if (arr.profile_pic == '') { let name =
                                    arr.student_name.toUpperCase().split(/\s+/); let shortName = name[0].charAt(0) +
                                    name[1].charAt(0); #
                                    <div
                                        class='flex user-profile-pic w-12 h-12 rounded-full bg-primary-blue-500 items-center'>
                                        <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#=
                                            shortName #</span>
                                    </div>
                                    # } else { #
                                    <img class="w-12 h-12 flex-1 rounded-full" src="#= arr.profile_pic #" />
                                    # } #
                                </div>
                                {{-- <img class="w-14 h-full rounded-full" id="stud-profile-pic-circle"
                                    src="{{ asset($studentDetails[0]->profile_picture) }}"
                                    alt="Student Profile Picture"> --}}
                                <div class="inline-flex flex-col items-start justify-center h-full">
                                    <p class="text-sm font-medium leading-5 text-gray-900">#: arr.student_name #</p>
                                    <p class="text-xs leading-4 text-gray-400">#: arr.student_id #</p>
                                </div>
                            </div>
                            <div class="flex space-x-2 items-center justify-end">
                                <button id="viewProfileBtn" class="view_profile btn-primary h-full px-6 py-2" id=""
                                    type="button">
                                    <p class="text-sm font-medium leading-5 text-white">View Full Profile</p>
                                </button>
                                <button id="cancelProfileBtn"
                                    class="flex justify-center h-full px-6 py-2 bg-primary-blue-50 border border-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary-blue-50 focus:ring-primary-blue-500"
                                    id="" type="button">
                                    <p class="text-sm font-medium leading-5 text-primary-blue-500">Edit Details</p>
                                </button>
                            </div>
                        </div>
                        <div class="inline-flex space-x-16 items-center justify-between w-full">
                            <p class="w-32 text-sm font-medium leading-5 text-gray-700">Status</p>
                            <div class="flex space-x-1 items-center justify-end h-full py-0.5 pl-1 pr-0.5 rounded-full">
                                <label class="switch"><input type="checkbox" id="togBtn">
                                    <div class="slider round">
                                        <!--ADDED HTML --><span class="on">Active</span><span class="off">NA</span>
                                        <!--END-->
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-2 items-start justify-start w-full">
                        #for(var i=0; i< arr.student_courses.length; i++){# # let bgcolor='gray' # # let
                            textcolor='gray' # # if (arr.student_courses[i]["status"]=='Current Student' ){ # #
                            bgcolor='primary-blue' # # textcolor='primary-blue' # # } else if
                            (arr.student_courses[i]["status"]=='Cancelled' ){ # # bgcolor='red' # # textcolor='red' # #
                            } else if (arr.student_courses[i]["status"]=='Transitioned' ){ # # bgcolor='yellow' # #
                            textcolor='yellow' # # } else if (arr.student_courses[i]["status"]=='Completed' ){ # #
                            bgcolor='green' # # textcolor='green' # # } else if
                            (arr.student_courses[i]["status"]=='Finished' ){ # # bgcolor='green' # # textcolor='green' #
                            # } else if (arr.student_courses[i]["status"]=='Withdrawn' ){ # # bgcolor='pink' # #
                            textcolor='pink' # # }else if(arr.student_courses[i]["status"]=='Suspended' ){# #
                            bgcolor='red' # # textcolor='red' # # } # <div
                            class="inline-flex items-center space-x-4 justify-start h-12 bg-white border rounded-md border-gray-200 w-full w-full">
                            <div class="w-1 h-full rounded-lg bg-#: bgcolor #-100"></div>
                            <div class="flex items-center justify-between w-full">
                                <div class="flex space-x-1 items-center justify-start w-full">
                                    <p class="text-sm  leading-5 text-gray-400">#=
                                        arr.student_courses[i].course.course_code #</p>
                                    <p class="text-sm font-medium leading-5 text-gray-800"> #=
                                        arr.student_courses[i].course.course_name #</p>
                                </div>
                                <div
                                    class="flex items-center justify-end px-2.5 py-0.5 bg-#: bgcolor #-100 rounded-full mr-4">
                                    <p class="text-xs leading-4 text-center truncate text-#: textcolor #-800">#=
                                        arr.student_courses[i]["status"] #</p>
                                </div>
                            </div>
                    </div>
                    #}#
                </div>
            </div>
            <div class="flex space-x-1 items-start justify-start w-full">
                <div class="flex flex-col space-y-2 w-1/2">
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Date of Birth</p>
                        <p class="text-xs leading-4 text-gray-500">#: convertJsDateFormat(arr.DOB) #</p>
                    </div>
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">USI</p>
                        <p class="text-xs leading-4 text-gray-500">#: ((arr.USI == null) ? 'N/A': arr.USI) #</p>
                    </div>
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Email Address</p>
                        <p class="text-xs leading-4 text-gray-500">#: arr.email #</p>
                    </div>
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Phone</p>
                        <p class="text-xs leading-4 text-gray-500">#: arr.contact #</p>
                    </div>
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Emergency Phone</p>
                        <p class="text-xs leading-4 text-gray-500">#: ((arr.emergency_phone == null) ? 'N/A':
                            arr.emergency_phone) #</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-2 w-1/2">
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Student Type</p>
                        <p class="text-xs leading-4 text-gray-500">#: arr.stud_type_name #</p>
                    </div>
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Agent</p>
                        <p class="text-xs leading-4 text-gray-500">#: arr.agency_name #</p>
                    </div>
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Address</p>
                        <p class="text-xs leading-4 text-gray-500">#: arr.address #</p>
                    </div>
                    <div class="flex flex-col items-start justify-start w-full">
                        <p class="text-xs font-medium leading-4 text-gray-700">Emergency Contact Name</p>
                        <p class="text-xs leading-4 text-gray-500">#: ((arr.emergency == null) ? 'N/A': arr.emergency) #
                        </p>
                    </div>
                </div>
            </div>
            </div>
            <div
                class="inline-flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-md w-full border-gray-200">
                <div class="inline-flex space-x-4 items-center justify-between w-full">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-sm font-medium leading-5 text-gray-900">Progress</p>
                        <div class="flex items-center justify-center px-2.5 py-0.5 bg-green-100 rounded-full">
                            <p class="text-xs leading-5 text-center text-green-800">Normal</p>
                        </div>
                    </div>
                </div>
                <div class="inline-flex items-center space-x-4 justify-start w-full">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start">
                        <p class="text-xs font-medium leading-4 text-gray-900">Course Duration</p>
                        <p class="text-xs leading-4 text-gray-700">#= arr1["start_date"] # To #= arr1["finish_date"] #
                        </p>
                    </div>
                    <div class="flex space-x-2 items-center justify-start">
                        <div class="w-10 h-10 bg-primary-blue-500 rounded-full row-span-2">
                            <div class="w-8 h-8 m-1 bg-white rounded-full">
                                <p class="text-xs pt-2.5 leading-3 text-center text-gray-400">#= Math.round(arr1["days"]
                                    * 100 / arr1["diff_days"]) #%</p>
                            </div>
                        </div>
                        <div class="inline-flex flex-col items-start justify-center h-full">
                            <p class="text-xs font-medium leading-4 text-gray-900">#= arr1["days"] # of #=
                                arr1["diff_days"] # days</p>
                            <p class="text-xs leading-4 text-gray-500">#= arr1["start_date"] # To #= arr1["finish_date"]
                                #</p>
                        </div>
                    </div>
                    <div class="flex space-x-2 items-center justify-start">
                        <div class="w-10 h-10 bg-green-500 rounded-full row-span-2">
                            <div class="w-8 h-8 m-1 bg-white rounded-full">
                                # let unitPer = ((arr2["use_unit"] > 0) ? (arr2["use_unit"] * 100 / arr2["total_unit"])
                                : 0); #
                                <p class="text-xs pt-2.5 leading-3 text-center text-gray-400">#=
                                    (Number.isInteger(unitPer)) ? unitPer : unitPer.toFixed(1) #%</p>
                            </div>
                        </div>
                        <div class="inline-flex flex-col items-start justify-center h-full">
                            <p class="text-xs font-medium leading-4 text-gray-900">#= arr2["use_unit"] # of #=
                                arr2["total_unit"] # units</p>
                            <p class="text-xs leading-4 text-gray-500">#= arr2["title"] #</p>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="inline-flex flex-col space-y-6 items-start justify-start p-4 bg-white border rounded-md w-full border-gray-200">
                <div class="inline-flex items-center  space-x-4 justify-between">
                    <div class="flex space-x-2 justify-between">
                        <p class="text-sm font-medium leading-5 text-gray-900">Attendance</p>
                        <div class="flex items-center justify-center px-2.5 py-0.5 bg-green-100 rounded-full">
                            <p class="text-xs leading-5 text-center text-green-800">Normal</p>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col space-y-6 items-start justify-start w-64">
                    <div class="rounded-lg">
                        <div class="flex space-x-5 items-center justify-start w-full">
                            # let overallAttd = ((arr3.overall_attd) ? ((arr3.overall_attd == 100) ? arr3.overall_attd :
                            parseFloat(arr3.overall_attd).toFixed(1)) : 0); #
                            <div class="w-18 h-18 bg-green-500 rounded-full row-span-2">
                                <div class="w-16 h-16 m-2 bg-white rounded-full">
                                    <p class="text-xl pt-7 leading-3 text-center text-gray-400">#= overallAttd #%</p>
                                </div>
                            </div>
                            <div class="inline-flex flex-col items-start justify-center h-full w-56">
                                <p class="w-full text-sm font-medium leading-5 text-gray-400">Overall Attendance</p>
                                <p class="text-2xl font-medium leading-loose text-gray-900">#= overallAttd #%</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-full rounded-lg">
                        <div class="flex space-x-5 items-center justify-start w-full">
                            # let projectedAttd = ((arr3.projected_attd) ? ((arr3.projected_attd == 100) ?
                            arr3.projected_attd : parseFloat(arr3.projected_attd).toFixed(1)) : 0); #
                            <div class="w-18 h-18 bg-orange-500 rounded-full row-span-2">
                                <div class="w-16 h-16 m-2 bg-white rounded-full">
                                    <p class="text-xl pt-7 leading-3 text-center text-gray-400">#= projectedAttd #%</p>
                                </div>
                            </div>
                            <div class="inline-flex flex-col items-start justify-center h-full w-56">
                                <p class="w-full text-sm font-medium leading-5 text-gray-400">Projected Attendance</p>
                                <p class="text-2xl font-medium leading-loose text-gray-900">#= projectedAttd #%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{-- <div class="inline-flex flex-col space-y-11 items-center justify-start pt-7 w-full my-2">
                <div class="inline-flex space-x-6 items-center justify-between w-full emp-history">
                    <div id="stepper"></div>
                </div>
            </div> --}}
        </script>
    </div>

    {{-- Send Email Modal student list Email --}}
    <x-v2.templates.email id="sendMailStudentModal" formId="emailTemplateAddForm" style="display: none">
        <x-slot:header>
            <div id="emailLoadingState" class="h-full space-y-6 opacity-20">
                <div class="flex px-6 p-4 justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <div class="w-32 h-6 bg-gray-100 rounded animate-pulse mb-2"></div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="bg-white rounded-md shadow-inner-line py-4 px-6 w-full">
                        <div class="w-full space-y-4">
                            <div class="w-3/4 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                        </div>
                    </div>
                    <div class="bg-white rounded-md shadow-inner-line py-4 px-6 w-full">
                        <div class="w-full space-y-4">
                            <div class="w-3/4 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                        </div>
                    </div>
                    <div class="bg-white rounded-md shadow-inner-line py-4 px-6 w-full">
                        <div class="w-full space-y-4">
                            <div class="w-3/4 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                        </div>
                    </div>
                    <div class="bg-white rounded-md shadow-inner-line py-4 px-6 w-full h-32">
                        <div class="w-full space-y-4">
                            <div class="w-3/4 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                            <div class=" w-1/2 h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                        </div>
                    </div>
                </div>
            </div>
            <x-v2.templates.list-item class="filter_course items-start" label="Course:">
                <label class="filter_course_name text-sm leading-5 text-gray-700"></label>
                <input type="hidden" class="filter_course_id" name="course_id" />
            </x-v2.templates.list-item>
            <!--            <x-v2.templates.list-item class="mail__type items-start" label="Type:">
                <div class="space-y-2">
                    <div class="email_type flex items-center gap-7">
                        <div class="flex items-center gap-2">
                            <input type="radio" id="email_type_course" name="email_type" class="cursor-pointer k-radio"
                                value="course">
                            <label for="email_type_course" class="cursor-pointer">Course Type</label>
                        </div>
                        <div class="flex items-center gap-2">
                            <input type="radio" id="email_type_generic" name="email_type" class="cursor-pointer k-radio"
                                value="generic" checked>
                            <label for="email_type_generic" class="cursor-pointer">Generic Email</label>
                        </div>
                    </div>
                    <div class="mail__course" style="display: none;">
                        <div class="course_list">
                            <input type="text" id="course_list_for_email" name="course_id" class="cursor-pointer">
                        </div>
                    </div>
                </div>
            </x-v2.templates.list-item>-->
            <div class="flex space-x-1 items-start justify-between pl-4 pr-2 bg-blue-gray-50 py-1 w-full">
                <div class="flex items-start justify-start w-full studentListBoxBorder">
                    <span class="text-sm leading-5 text-gray-500 pt-2.5 pb-2.5 w-16">To:</span>
                    <div id="mailToUser"
                        class="overflow-y-auto studentNameList bg-transparent border-none text-gray-700 py-1 leading-tight text-sm">
                    </div>
                    <input name="selected_stud_id" value="" class="bg-transparent border-none w-full"
                        id="student_name_email_list" />
                    {{-- <div id="mailToUser"
                        class="studentNameList bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight text-sm">
                    </div> --}}
                    <input type="hidden" class="studentIds" name="student_id" id="studentIds" value="" />
                </div>
                <div class="justify-end flex space-x-2">
                    <button class="ccmail flex space-x-2 items-center justify-end cursor-pointer" type="button">
                        <span class="text-sm leading-5 font-normal text-primary-blue-500">Cc</span>
                    </button>
                    <button class="bccmail flex items-center justify-end cursor-pointer" type="button">
                        <span class="text-sm leading-5 font-normal text-primary-blue-500">Bcc</span>
                    </button>
                </div>
            </div>
            <!--            <x-v2.templates.list-item class="mail__course mail_notification" label="">
                <div class="w-full h-5 justify-start items-center gap-1 flex">
                    <div class="w-4 h-4 relative">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" class="glob-tooltip" title="Help" data-role="tooltip">
                            <path d="M6 0C9.31371 0 12 2.68629 12 6C12 9.31371 9.31371 12 6 12C2.68629 12 0 9.31371 0 6C0 2.68629 2.68629 0 6 0ZM6 1C3.23858 1 1 3.23858 1 6C1 8.76142 3.23858 11 6 11C8.76142 11 11 8.76142 11 6C11 3.23858 8.76142 1 6 1ZM6 8.5C6.41421 8.5 6.75 8.83579 6.75 9.25C6.75 9.66421 6.41421 10 6 10C5.58579 10 5.25 9.66421 5.25 9.25C5.25 8.83579 5.58579 8.5 6 8.5ZM6 2.5C7.10457 2.5 8 3.39543 8 4.5C8 5.23053 7.78822 5.63969 7.24605 6.20791L6.98196 6.47745C6.60451 6.87102 6.5 7.0831 6.5 7.5C6.5 7.77614 6.27614 8 6 8C5.72386 8 5.5 7.77614 5.5 7.5C5.5 6.76947 5.71178 6.36031 6.25395 5.79209L6.51804 5.52255C6.89549 5.12898 7 4.9169 7 4.5C7 3.94772 6.55228 3.5 6 3.5C5.44772 3.5 5 3.94772 5 4.5C5 4.77614 4.77614 5 4.5 5C4.22386 5 4 4.77614 4 4.5C4 3.39543 4.89543 2.5 6 2.5Z" fill="#9CA3AF"></path>
                        </svg>
                    </div>
                    <div class="text-gray-500 text-xs font-normal leading-tight tracking-wide">
                        Only students with selected course will receive this email.
                    </div>
                    <div class="justify-start items-center flex">
                        <div class="text-sky-500 text-xs font-normal leading-tight tracking-wide">
                            <a class="notReceivedEmailStudentListBtn text-sky-500 text-xs font-normal leading-tight tracking-wide cursor-pointer">
                                <span class="notReceivedEmailStudentCount">12</span> <span>students will not receive this email.</span>
                            </a>
                        </div>
                    </div>
                </div>
            </x-v2.templates.list-item>-->
            <x-v2.templates.list-item class="mail__cc" id="emailccbox" label="Cc:" style="display: none;">
                <input class="tw-input-borderless" id="email_cc" placeholder="Enter CC Email" name="email_cc"
                    type="text">
            </x-v2.templates.list-item>
            <x-v2.templates.list-item class="mail__bcc" id="emailbccbox" label="Bcc:" style="display: none;">
                <input class="tw-input-borderless" id="email_bcc" placeholder="Enter BCC Email" name="email_bcc"
                    type="text">
            </x-v2.templates.list-item>
            <x-v2.templates.list-item class="mail__subject" label="Subject:">
                {{ Form::text('email_subject', null, [
                'class' => 'w-full text-xs appearance-none
                bg-transparent border-none text-gray-700 mr-3 py-1 px-2
                leading-tight',
                'id' => 'email_subject',
                'placeholder' => 'Enter Subject',
                ]) }}
            </x-v2.templates.list-item>
            <x-v2.templates.list-item class="mail__form" label="From:">
                <p class="text-xs font-medium text-gray-700 px-2">Academic Email</p>
            </x-v2.templates.list-item>
            <x-v2.templates.list-item class="mail__template" label="Reply To:">
                <input type="text" id="reply_to_email" name="reply_to_email" />
            </x-v2.templates.list-item>
            <x-v2.templates.list-item class="mail__template" label="Template:">
                <p class="text-xs text-gray-700 px-2 isTemplateSelect">No Template Selected</p>
                <a href="javascript:void(0);" class="btn-secondary" id="insertEmailTemplate">
                    <span class="text-gray-400">
                        <x-v2.icons name="icon-template" width="14" height="14" viewBox="0 0 14 14" />
                    </span>
                    <span class="text-sm font-medium leading-5 text-gray-700">Insert Template</span>
                </a>
            </x-v2.templates.list-item>
        </x-slot:header>
        <x-slot:editor>
            <textarea class="inline-flex overflow-y-auto h-auto" id="comments" name="email_content"></textarea>
        </x-slot:editor>
        <x-slot:attachment>
            <input type="hidden" class="email_template_id" name="email_template_id" />
            <input type="hidden" class="existing_attachment_id" name="existing_attachment_id" />
            <input class="" id="email_attachment" name="email_attachment" type="file" multiple style="display: none;">
            <label for="email_attachment" class="btn-secondary w-fit">
                <span class="text-gray-400">
                    <x-v2.icons name="icon-attachment" width="14" height="14" viewBox="0 0 14 14" />
                </span>
                <span>Attach Files</span>
            </label>
            <span id="templateFilesContainer" class="flex flex-wrap gap-1 selected_file"></span>
            <span id="attachedFilesContainer" class="flex flex-wrap gap-1 selected_file"></span>
        </x-slot:attachment>
        <x-slot:checkbox>
            <div class="flex gap-2 items-center">
                {{ Form::checkbox('offer_comm_log', null, false, ['class' => 'k-checkbox', 'id' => 'student_comm_log'])
                }}
                <div for="student_comm_log" class="student_comm_log text-sm cursor-pointer">
                    Add to Student Communication Log</div>
            </div>
        </x-slot:checkbox>
        <x-slot:footer>
            <x-v2.button size="sm" variant="secondary" class="resetEmail">
                <span>Reset</span>
            </x-v2.button>
            <x-v2.button size="sm" variant="primary" id="sendMail" class="sendmail">
                <span>Send Email</span>
            </x-v2.button>
        </x-slot:footer>
    </x-v2.templates.email>

    <div id="loaderForEmail" style="display: none;">
        <div class="relative h-full pb-6">
            <x-v2.loader />
        </div>
    </div>
    <div id="statusForSendEmailModal" style="display: none;">
        <div class="py-3 w-full">
            <h3 class="text-green-600">
                <span class="k-icon k-i-check mr-2"></span>
                <span id="titleEmailSuccessMsg"></span>
            </h3>
        </div>
        <div class="py-3 w-full titleEmailFailMsg">
            <h3 class="text-red-500">
                <span class="k-icon k-i-cancel mr-2"></span>
                <span id="titleEmailFailMsg"></span>
            </h3>
        </div>
        <div class="w-full">
            <div id="studentFailEmailList"></div>
        </div>
        <div class="flex items-center gap-4 mt-4">
            <button data-type="Email" class="closeAndRefreshGrid btn-primary h-8 px-2 py-2" id="load1" type="button">
                <p class="text-sm font-medium leading-tight text-white">Done</p>
            </button>
            <a href="{{ route('global-queue') }}" target="_blank" class="btn-secondary h-8 px-2 py-2" type="button">
                <p class="text-sm font-medium leading-tight text-black">Go To Queue</p>
            </a>
        </div>
    </div>
    {{-- <div id="notReceivedEmailStudentListModal" style="display: none;">
        <div class="space-y-6">
            <x-v2.search-input id="studentSearchFromTags" class=" w-full" placeholder="Search name">
                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
            </x-v2.search-input>
            <div class="flex w-full overflow-scroll">
                <input type="hidden" class="student_name_remove_email_list w-full" name="student_name_remove_email_list"
                    id="student_name_remove_email_list" value="" />
            </div>
            <div class="flex">
                <button type="button" class="removeEmailNotSendStudents btn-secondary">
                    <span class="k-icon k-i-close"></span>
                    <p class="text-xs leading-4 uppercase">Remove above students who are not from selected course</p>
                </button>
            </div>
        </div>
    </div> --}}

    <div id="OtherEmailStudentListModal" style="display: none;">
        <div class="flex-col space-y-4">
            <x-v2.search-input id="otherStudentSearchFromTags" class=" w-full" placeholder="Search name">
                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
            </x-v2.search-input>
            <input type="hidden" class="other_student_name_list w-full" name="other_student_name_list"
                id="other_student_name_list" value="" />

            <button type="button" class="removeEmailNotSendStudents btn-secondary">
                <span class="k-icon k-i-close"></span>
                <p class="text-xs leading-4 uppercase">Remove above all students</p>
            </button>
        </div>
    </div>

    {{-- Insert Email Template Modal --}}
    <x-v2.templates.email-template id="emailTemplatesModal" style="display: none;">
        <x-slot:options searchInputId="seachTemplate3" panelId="insertTemplatePanelBar">
        </x-slot:options>
        <x-slot:preview></x-slot:preview>
        <x-slot:footer></x-slot:footer>
    </x-v2.templates.email-template>

    {{-- Send SMS Modal --}}
    <x-v2.templates.sms id="sendSmsStudentModal" formId="smsTemplateAddForm" style="display: none;">
        <x-slot:header>
            <x-v2.templates.list-item class="sms__to" label="To:">
                <input name="selected_stud_id" value="" class="bg-transparent border-none w-full"
                    id="student_name_sms_list" />
                {{-- <div id="sendsmsto"
                    class="overflow-y-auto studentNameList bg-transparent border-none text-gray-700 py-1 px-8 leading-tight text-sm">
                    <span class="h-8"></span>
                </div> --}}
                <input type="hidden" class="studCourseIds" value="" />
            </x-v2.templates.list-item>
            <x-v2.templates.list-item class="sms__from" label="From:" style="display: none">
                <div class="flex items-center justify-between w-full pr-2">
                    <input class="tw-input-borderless" type="text">
                    <span class="text-gray-400">
                        <x-v2.icons name="icon-chevron-down" width="12" height="8" viewBox="0 0 12 8" />
                    </span>
                </div>
            </x-v2.templates.list-item>
            <x-v2.templates.list-item class="sms__template" label="SMS Template">
                <input id="sms_template" />
            </x-v2.templates.list-item>
        </x-slot:header>
        <x-slot:editor>
            <textarea class="sms_text text-sm leading-5 text-gray-700 px-4 pt-4 border-t bg-white w-full h-full"
                id="sms_text" name="email_content" placeholder="Enter SMS Text"></textarea>
        </x-slot:editor>
        <x-slot:footer>
            <x-v2.button loading="Saving..." size="sm" variant="secondary" id="resetSMS">
                <span>Reset</span>
            </x-v2.button>
            <x-v2.button loading="Saving..." size="sm" variant="primary" id="sendMsg">
                <span>Send SMS</span>
            </x-v2.button>
        </x-slot:footer>
    </x-v2.templates.sms>

    {{-- Issue Letter --}}
    <x-v2.templates.generate-letter id="issueLetterStudentModal" style="display: none;">
        <x-slot:hiddenInputs></x-slot:hiddenInputs>
        <x-slot:selectBox></x-slot:selectBox>
        <x-slot:inputPanel></x-slot:inputPanel>
        <x-slot:editor></x-slot:editor>
        <x-slot:checkbox></x-slot:checkbox>
        <x-slot:buttonPanel></x-slot:buttonPanel>
        <x-slot:previewPanel></x-slot:previewPanel>
    </x-v2.templates.generate-letter>

    <div id="generateLetterModal" style="display: none;">
        <div class="relative h-32 w-32">
            {{-- <button
                class="absolute inset-x-0 bottom-0 justify-center py-1 shadow border hover:shadow-lg rounded-lg border-gray-300">Cancle
                Operation</button> --}}
        </div>
    </div>

    <div id="loaderForLetter" style="display: none;">
        <div class="relative h-full pb-6">
            <x-v2.loader />
        </div>
    </div>
    <div id="letterDiscardConfirmation"></div>
    <x-v2.templates.letter-close-confirm id="closeLetterModelConformation" style="display: none;">
    </x-v2.templates.letter-close-confirm>

    <x-v2.templates.email-close-confirm id="closeModelConformation" style="display: none;">
    </x-v2.templates.email-close-confirm>

    <div id="statusForSendLetterModal" style="display: none;">
        <div class="flex flex-row py-3 w-full">
            <h3 class="text-green-600 flex items-center space-x-1">
                <span class="k-icon k-i-check mr-2"></span>
                <span id="titleLetterSuccessMsg"></span>
                <span class="letterZipDownload" class="pr-4"></span>
            </h3>
        </div>
        <div class="flex flex-row py-3 w-full">
            <h3 id="titleLetterFailMsg" class="text-red-500"></h3>
        </div>

        <div class="flex items-center gap-4 mt-4 queueOptionDiv">
            <button data-type="Letter" class="closeAndRefreshGrid btn-primary h-8 px-2 py-2" id="load1" type="button">
                <p class="text-sm font-medium leading-tight text-white">Done</p>
            </button>
            <a href="{{ route('global-queue') }}" target="_blank" class="btn-secondary h-8 px-2 py-2" type="button">
                <p class="text-sm font-medium leading-tight text-black">Go To Queue</p>
            </a>
        </div>
    </div>

    {{-- Insert Letter Template Modal --}}
    <x-v2.templates.email-template id="letterTemplatesModal" style="display: none;">
        <x-slot:options searchInputId="seachTemplate3" panelId="insertLetterTemplatePanelBar">
        </x-slot:options>
        <x-slot:preview>
            <div class="grow overflow-y-auto rounded-lg border border-gray-200 p-6 space-y-4">
                <h3 class="letter_subject text-2xl font-medium leading-none text-gray-900">
                </h3>
                <div class="letter_content text-gray-700"></div>
            </div>
            <div class="letter_exist_attachment_div" style="display: none;">
                <div class="flex gap-2 items-center">
                    <span class="text-gray-400">
                        <x-v2.icons name="icon-attachment" height="18" viewBox="0 0 16 18" />
                    </span>
                    <p class="text-sm font-medium leading-tight text-gray-900">Attachments</p>
                </div>
                <div class="letter_exist_attachment flex gap-2 items-center"></div>
            </div>
        </x-slot:preview>
        <x-slot:footer buttonId="useLetterTemplateBtn"></x-slot:footer>
    </x-v2.templates.email-template>
    <div id="letterParameterModal" style="display: none;">
        <div class="bg-white border rounded-lg border-gray-200 w-full p-6">
            <div id="parameterList" class="grid  grid-cols-2 gap-4">

            </div>
        </div>

        <input id="student_id" name="student_id" hidden>
        <input id="course_id" name="course_id" hidden>
        <div
            class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 fixed border-t bg-white px-6">
            <button type="button" class="closeModelLetterParameterModal btn-secondary min-w-[6.25rem]">
                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
            </button>
            <button class="replaceParameter btn-primary min-w-[6.25rem]" type="button">
                <p class="text-sm font-medium leading-5 text-white">Replace</p>
            </button>
        </div>
    </div>
    {{-- Invite Student Modal --}}
    <div id="inviteStudentToGalaxyModal" style="display: none;">
        <div class="flex bg-gray-100 px-4 pt-4 pb-24 space-x-12">
            <div class="flex flex-col space-y-4 items-start justify-start w-full">
                <div id="forBulkInvite" class="flex flex-col h-full w-full">
                    <div class="inline-flex space-x-1 items-center justify-start">
                        <p class="text-sm font-medium leading-6 text-gray-900">Selected Student</p>
                    </div>
                    <div class="flex flex-col space-y-6 bg-white border rounded-lg border-gray-200 h-full w-full">
                        <div class="inline-flex items-center justify-start rounded-md w-full">
                            <div>
                                <input type="hidden" name="selected_stud_id_2" id="invite_student_name_email_list"
                                    value="" disabled />
                            </div>
                        </div>
                    </div>
                    <span class="text-xs italic text-red-500 mt-2">Those who are already invited are not included in
                        the selection bar.</span>
                </div>
                <div id="forOneInvite"
                    class="flex flex-col space-y-6 bg-white border rounded-lg border-gray-200 h-full w-full p-6">
                    <div class="inline-flex items-center justify-start rounded-md w-full">
                        <div class="inline-flex space-x-1 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Basic</p>
                            <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                        </div>
                    </div>
                    <div class="w-full h-px bg-gray-200">

                    </div>
                    <form id="inviteStudentToGalaxyBasicForm" name="inviteStudentToGalaxyBasicForm" class="w-full">

                    </form>
                </div>
                <div
                    class="w-full pl-4 pr-6 py-4 bg-sky-100 rounded-md border border-blue-500 flex-col justify-start items-start gap-4 inline-flex">
                    <div class="self-stretch h-48 flex-col justify-start items-start gap-2 flex">
                        <div class="text-gray-700 text-sm font-medium leading-tight">
                            Student will be assigned access to their student portal with following permissions.
                        </div>
                        <div class="p-1 justify-start items-start gap-1 inline-flex">
                            <div class="h-5 justify-start items-center gap-2 flex">
                                <div class="w-1 h-1 bg-gray-400 rounded-full">

                                </div>
                                <div
                                    class="grow shrink basis-0 text-gray-500 text-xs font-normal leading-tight tracking-wide">
                                    Student will receive email with invitation for galaxy portal.
                                </div>
                            </div>
                        </div>
                        <div class="p-1 justify-start items-start gap-1 inline-flex">
                            <div class="h-5 justify-start items-center gap-2 flex">
                                <div class="w-1 h-1 bg-gray-400 rounded-full">

                                </div>
                                <div
                                    class="grow shrink basis-0 text-gray-500 text-xs font-normal leading-tight tracking-wide">
                                    They will need to set a new password..
                                </div>
                            </div>
                        </div>
                        <div class="p-1 justify-start items-start gap-1 inline-flex">
                            <div class="h-5 justify-start items-center gap-2 flex">
                                <div class="w-1 h-1 bg-gray-400 rounded-full">

                                </div>
                                <div
                                    class="grow shrink basis-0 text-gray-500 text-xs font-normal leading-tight tracking-wide">
                                    They can access their courses, timetable, assessments and documents.
                                </div>
                            </div>
                        </div>
                        <div class="p-1 justify-start items-start gap-1 inline-flex">
                            <div class="h-5 justify-start items-center gap-2 flex">
                                <div class="w-1 h-1 bg-gray-400 rounded-full">

                                </div>
                                <div
                                    class="grow shrink basis-0 text-gray-500 text-xs font-normal leading-tight tracking-wide">
                                    They can manage their account password from account setting.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col space-y-6 bg-white border rounded-lg border-gray-200 h-full w-full p-6">
                    <div class="inline-flex items-center justify-start rounded-md w-full">
                        <div class="inline-flex space-x-1 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Invitation Email</p>
                            <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                        </div>
                    </div>
                    <div class="w-full h-px bg-gray-200"></div>
                    <form id="inviteStudentToGalaxyInviteForm" name="inviteStudentToGalaxyInviteForm" class="pb-6">
                        <input class="hidden" id="invite_student_id" name="student_id" />
                        <input class="hidden" id="template_id" name="template_id" />
                        <div class="flex flex-col space-y-6">
                            <div class="inline-flex flex-col space-y-6 items-start justify-start pb-6">
                                <div class="inline-flex space-x-4 items-center justify-start w-full">
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Subject</p>
                                        <input class="w-full border rounded-lg border-gray-200 p-2" id="subject"
                                            name="subject" required />
                                    </div>
                                </div>
                                <div class="inline-flex space-x-4 items-center justify-start w-full">
                                    <div class="w-full border rounded-lg border-gray-200">
                                        <input class="w-full border rounded-lg border-gray-200 p-2" id="email_content"
                                            name="email_content" required />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="k-form-buttons">
                            <div
                                class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 fixed border-t bg-white px-6">
                                <button type="button" class="cancelBtn  btn-secondary min-w-[6.25rem]">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                                </button>
                                <button id="sendInviteMail" class="sendInviteMail btn-primary min-w-[6.25rem]"
                                    id="load1" type="button">
                                    <p class="text-sm font-medium leading-5 text-white">Invite Student to Galaxy</p>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {{-- Email templates for send email modal --}}
    <script id="studentInvitePanelBarTemplate" type="text/kendo-ui-template">
        # if (item.id != 0 && typeof item.title != 'undefined') { #
            <div class="inline-flex space-x-4 items-center justify-between w-full">
                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.title #</span>
            </div>
            # } #

            # if (item.id != 0 && typeof item.template_name != 'undefined') { #
            <div class="email_template w-full tw-panelbar__sub-item hover:bg-primary-blue-50 ps-4 py-2 pe-2.5 rounded-md text-sm truncate cursor-pointer" data-id="#: item.id #">
                #: item.template_name #
            </div>
            # } #
    </script>

    {{-- Letter Templates For Send letter Modal --}}
    <script id="letterPanelBarTemplate" type="text/kendo-ui-template">
        # if (item.id != 0 && typeof item.title != 'undefined') { #
            <div class="inline-flex space-x-4 items-center justify-between w-full">
                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.title #</span>
            </div>
            # } #

            # if (item.id != 0 && typeof item.letter_name != 'undefined') { #
            <div class="letter_template w-full tw-panelbar__sub-item hover:bg-primary-blue-50 ps-4 py-2 pe-2.5 rounded-md text-sm truncate cursor-pointer" data-id="#: item.id #">
                #: item.letter_name #
            </div>
            # } #
    </script>

    {{-- Letter Attachment List --}}
    <script id="letterAttachmentList" type="text/html">
        # if (files.length > 0) { #
        #for (var i = 0; i < files.length; i++){ # <div
            class="flex space-x-0.5 items-center justify-center py-0.5 pl-2.5 pr-1 bg-gray-200 rounded-full">
            <p class="text-xs leading-tight text-center text-gray-800">#=files[i].letter_attachment#</p>
            <span class="cursor-pointer k-icon k-i-close letter_existing_attachment"
                data-file-id="#=files[i].id#"></span>
            </div>
            # } #
            # } else { #
            <p class="text-xs leading-tight text-center text-gray-800">No attachments available</p>
            # } #
    </script>

    {{-- Notification Templates --}}
    @include('v2.sadmin.notification')

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/jszip.min.js') }}"></script>
        <script src="https://cdn.ckeditor.com/ckeditor5/35.3.2/super-build/ckeditor.js"></script>
        <script src="{{ asset('v2/js/tagify.js') }}"></script>
        <script src="{{ asset('v2/js/sadmin/student_scout.js') }}"></script>
        <script>
            window.downloadRouteUrl = '{{ route("download-file-and-delete") }}';
        </script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ isset($api_token) ? "Bearer $api_token" : '' }}";
        var SMSApiKey = "{{ Config::get('constants.SMSApiKey') }}";
        var sendSmsUrl = "{{ Config::get('constants.sendSmsUrl') }}";
        var queue_email = "{{ Config::get('features.queue_email') == 1 ? true : false }}";
        let emailTotalAttachmentAllowed = "{{ Config::get('constants.emailTotalAttachmentAllowed') }}";
    </x-slot>
    <style>
        #OtherEmailStudentListModal .tagify__tag.border-bottom {
            border-bottom: 1px solid #ddd !important;
        }

        .other_student_name_list .tagify__input {
            display: none;
        }

        .ck.ck-balloon-panel.ck-balloon-panel_visible {
            z-index: 999999;
        }
    </style>

    <script id="tooltipTemplateForCollege" type="text/html">
        <ul class="space-y-2 text-gray-600 text-sm p-3">
            # for (var i = 0; i < campuses.length; i++) { # <li class="flex items-center gap-2"><span
                    class="min-w-fit">#: campuses[i] #</span>
                # if (i === 0) { #
                <span
                    class="text-green-700 bg-green-100 leading-4 rounded-lg text-xxs font-normal px-1.5">Current</span>
                # } #
                </li>
                # } #
        </ul>
    </script>
</x-v2.layouts.default>