<?php

namespace App\Http\Controllers\v3\api;

use App\Classes\SiteConstants;
use App\DTO\api\v3\StudentApplicationEmploymentDTO;
use App\DTO\api\v3\StudentApplicationQualificationDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\api\v3\DisabilityDetailRequest;
use App\Http\Requests\api\v3\LanguageDetailRequest;
use App\Http\Requests\api\v3\LanguageInfoRequest;
use App\Http\Requests\api\v3\StudentCourseCheckoutRequest;
use App\Http\Requests\api\v3\StudentCourseEnrollRequest;
use App\Http\Requests\api\v3\StudentCreateQualificationRequest;
use App\Http\Requests\api\v3\StudentDocumentDeleteRequest;
use App\Http\Requests\api\v3\StudentDocumentUploadRequest;
use App\Http\Requests\api\v3\StudentEmergencyContactRequest;
use App\Http\Requests\api\v3\StudentEmploymentCreateRequest;
use App\Http\Requests\api\v3\StudentEmploymentDeleteRequest;
use App\Http\Requests\api\v3\StudentEmploymentRequest;
use App\Http\Requests\api\v3\StudentOshcRequest;
use App\Http\Requests\api\v3\StudentPersonalDetailsRequest;
use App\Http\Requests\api\v3\StudentProfileUpdateRequest;
use App\Http\Requests\api\v3\StudentQualificationRequest;
use App\Http\Requests\api\v3\StudentRegisterRequest;
use App\Http\Requests\api\v3\StudentSchoolingRequest;
use App\Http\Requests\api\v3\StudentSubmitApplicationRequest;
use App\Http\Requests\api\v3\StudentUSIDetailsRequest;
use App\Http\Requests\api\v3\ValidateStudentQueryRequest;
use App\Http\Resources\api\v3\CourseFavouriteCoursesResource;
use App\Http\Resources\api\v3\CourseSummaryResource;
use App\Http\Resources\api\v3\NewStudentApplicationDataResource;
use App\Http\Resources\api\v3\StudentApplicationDataResource;
use App\Http\Resources\api\v3\StudentApplicationDataResourceAllFields;
use App\Http\Resources\api\v3\StudentApplicationStatusDataResource;
use App\Http\Resources\api\v3\StudentEnrolledCoursesResource;
use App\Http\Resources\api\v3\StudentRegisteredDataResource;
use App\Model\v2\Colleges;
use App\Model\v2\Courses;
use App\Model\v2\EmploymentStatus;
use App\Model\v2\Language;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudyReason;
use App\Roles;
use App\Services\api\v3\StudentApiService;
use App\Traits\CommonRequestMethodsTrait;
use App\Traits\ResponseTrait;
use App\Traits\SendEmailTrait;
use App\Users;
use Carbon\Carbon;
use Domains\Customers\Billing\Models\StripeConfig;
use GalaxyAPI\Resources\StudentResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

/*
*/
class StudentApiController extends Controller
{
    use CommonRequestMethodsTrait;
    use ResponseTrait;
    use SendEmailTrait;

    protected $service;

    public function __construct(StudentApiService $commonService)
    {
        $this->service = $commonService;
    }

    /**
     * Register new student.
     *
     * @unauthenticated
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentRegisteredDataResource[]}
     */
    public function registerStudent(StudentRegisterRequest $request)
    {
        /**
         * Register a new student.
         *
         * This endpoint gives all the details of any application.
         */
        DB::beginTransaction();
        try {
            $validatedData = $request->all();

            $user = $this->service->registerNewUser($validatedData);

            if (! $user) {
                throw new \Exception(__('messages.shortcourse.could_not_register_user'));
            }

            $studentId = $user['student']->id ?? null;
            if (! $studentId) {
                DB::commit();
                $message = $user['message'] ?? __('messages.shortcourse.process_stuck');

                return $this->successResponse($message, 'data', $user, 202);
            }
            DB::commit();
            if ($user['email_verified_at'] == null) {
                $message = __('messages.shortcourse.registered_email_not_verified');

                return $this->successResponse($message, null, null, 202);
            }
            $studentToken = $user['token'] ?? '';
            $applicationData = $this->service->getApplicantData($studentId, ['studentDetails']);
            $applicationData->setRelation('token', $studentToken);
            $resource = ($applicationData) ? new StudentRegisteredDataResource($applicationData) : null;
            $message = $user['action_required'] ?? __('messages.shortcourse.user_registered_successfully');

            return $this->successResponse($message, 'data', $resource);
        } catch (\Exception $e) {
            DB::rollBack();
            if ($e instanceof ValidationException) {
                throw $e;
            }

            return $this->errorResponse($e->getMessage());
        }

    }

    /**
     * Get Auth Token for Student.
     *
     * @unauthenticated
     *
     * @response array{token: "TOKEN_STRING", "email_verified": bool, student: StudentApplicationDataResourceAllFields[]}
     */
    public function authenticateStudent(Request $request)
    {
        try {
            $request->validate([
                'email' => ['required', 'string', 'email'],
                'password' => ['required', 'string'],
                'redirect_uri' => ['nullable', 'string'],
            ]);
            $credentials = $request->only('email', 'password');

            $redirectUri = $request->redirect_uri ?? null;

            if (! empty($redirectUri)) {
                $redirectUri = validateRedirectUri($redirectUri, false);
                if (! $redirectUri) {
                    throw ValidationException::withMessages(['redirect_uri' => __('messages.shortcourse.invalid_redirect_uri')]);
                }
            } else {
                $redirectUri = validateRedirectUri();
            }

            if (! Auth::guard('web')->validate($credentials)) {
                throw new \Exception(__('messages.shortcourse.invalid_credentialss'));
            }

            $user = Users::with('associatedStudent')->where('email', $credentials['email'])->first();

            // Check if the user's role_id is 13
            if ($user->role_id !== SiteConstants::STUDENT_ROLE_ID) {
                // but if there is record in students table with the same email or username the user should be authenticated
                if (! $user->associatedStudent) {
                    throw ValidationException::withMessages(['email' => __('messages.shortcourse.email_in_use')]);
                }
            }

            if ($user->email_verified_at == null) {
                $student = Student::where('email', $user['email'])->orWhere('generated_stud_id', $user['username'])->first();
                $this->service->sendEmailVerificationEmail($user, $student, $request->redirect_uri);
                throw new \Exception(__('messages.shortcourse.email_not_verified'));
            }

            $token = $user->generateUserToken(config('settings.studentapitokenname'), true, true);

            if (empty($token)) {
                throw new \Exception(__('messages.shortcourse.unable_to_generate_token'));
            }

            $studentId = $this->service->getLoggedInUserStudentRecord($user, 'id');
            $applicationData = $this->service->getApplicantData($studentId, null, true);
            $token['email_verified'] = $user->email_verified_at !== null;
            $token['student'] = ($applicationData) ? new StudentApplicationDataResource($applicationData) : null;
            $token['message'] = __('messages.shortcourse.user_authenticated_successfully');
            $token['code'] = 200;
            $token['success'] = 1;
            $token['status'] = 'success';

            return response()->json($token, 200);
        } catch (\Exception $e) {
            if ($e instanceof ValidationException) {
                throw $e;
            }

            return $this->errorResponse($e->getMessage(), null, null, 401);
        }

    }

    /**
     * Check if user email is verified.
     *
     *
     * @response array{email_verified: bool}
     */
    public function isUserEmailVerified(Request $request)
    {
        try {
            $emailVerified = authUser() && authUser()->email_verified_at !== null;
            if (! $emailVerified) {
                throw new \Exception(__('messages.shortcourse.email_not_verified_only'));
            }

            return $this->successResponse(__('messages.shortcourse.email_verified_only'), 'email_verified', $emailVerified);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'email_verified', false);
        }

    }

    public function searchForStudent(Request $request)
    {
        /**
         * Retrive student application details.
         *
         * This endpoint gives all the details of any application.
         */
        $validatedData = $request->validate([
            /**
             * Search string.
             *
             * @var string
             */
            'string' => ['required', 'string'],
        ]);

    }

    /**
     * Get Student Personal Information.
     *
     * @param  string  $studentId  Galaxy ID of the student.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentResource[]}
     */
    public function getStudentPersonalInformation(Request $request, $studentId)
    {
        $student = Student::where('generated_stud_id', $studentId)->first();
        if (! $student) {
            abort(404, __('messages.shortcourse.student_application_doesnot_exist'));
        }

        return $this->successResponse('Student found', 'data', new StudentResource($student));
    }

    /**
     * Get Student Application Details.
     *
     * @param  string  $studentId  The hashed key of the student or the student's application reference ID or the student's Generated ID. NOTE: To retrieve application data for the currently authenticated student, you can set the studentId value to 'self'.
     *
     * @example eyJpdiI6Ik1Ybis2R.......DEyIiwidGFnIjoiIn0=
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentApplicationDataResourceAllFields[]}
     */
    public function getStudentApplication(Request $request, $studentId)
    {
        try {
            $studentKey = $request->route()->parameter('studentId');
            $referenceId = $studentId = null;
            try {
                $studentId = (int) decryptIt($studentKey);
            } catch (\Exception $e) {
                $referenceId = trim($studentKey);
                if ($referenceId === 'self') {
                    // get the application reference id of current student
                    $referenceId = Student::GetLoggedInStudentId(auth()->user(), 'application_reference_id');
                }
            }

            $filter = $orFilter = null;
            if ($studentId) {
                $filter = ['id' => $studentId];
            } elseif ($referenceId) {
                $filter = ['application_reference_id' => $referenceId];
                $orFilter = ['generated_stud_id' => $referenceId];
            }

            $student = null;

            if ($filter) {
                $filterQuery = Student::query();
                $filterQuery->where($filter);
                if ($orFilter) {
                    $filterQuery->orWhere($orFilter);
                }
                $student = $filterQuery->first();
            }

            if (! $student) {
                throw new \Exception(__('messages.shortcourse.student_application_doesnot_exist'));
            } elseif (! $student->isApiAccessible(auth()->user())) {
                throw new \Exception(__('messages.shortcourse.particular_student_is_not_available_for_you'));
            }
            $studentId = $student->id;
            $data = $this->service->getApplicantData($studentId);

            return $this->successResponse(__('messages.shortcourse.student_found'), 'data', ($data) ? new StudentApplicationDataResource($data) : null);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Student Application Form Status.
     *
     * This endpoint will give the completed status of each step of student application form.
     *
     * @param  string  $studentId  The hashed key of the student or the student's application reference ID. NOTE: To retrieve application data for the currently authenticated student, you can set the studentId value to 'self'.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentApplicationStatusDataResource[]}
     */
    public function getStudentApplciationCompletionStatus(Request $request, $studentId)
    {
        try {
            $studentKey = $request->route()->parameter('studentId');
            $referenceId = $studentId = null;
            try {
                $studentId = (int) decryptIt($studentKey);
            } catch (\Exception $e) {
                $referenceId = trim($studentKey);
                if ($referenceId === 'self') {
                    // get the application reference id of current student
                    $referenceId = Student::GetLoggedInStudentId(auth()->user(), 'application_reference_id');
                }
            }
            $filter = null;
            if ($studentId) {
                $filter = ['id' => $studentId];
            } elseif ($referenceId) {
                $filter = ['application_reference_id' => $referenceId];
            }
            $student = ($filter) ? Student::where($filter)->first() : null;
            if (! $student) {
                throw new \Exception(__('messages.shortcourse.student_application_doesnot_exist'));
            } elseif (! $student->isApiAccessible(auth()->user())) {
                throw new \Exception(__('messages.shortcourse.particular_student_is_not_available_for_you'));
            }
            $studentId = $student->id;
            $data = $this->service->getApplicantData($studentId, ['application_status']);

            return $this->successResponse(__('messages.shortcourse.student_found'), 'data', ($data) ? new StudentApplicationStatusDataResource($data->appprocess ?? []) : null);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Form Data.
     *
     * <div>This method will provide all the required values for any field that may have options to choose from.</div>
     * <br />
     * <div class="sl-text-on-primary sl-p-2 sl-bg-primary sl-rounded-lg">
     * <i><u>To get specefic item, provide the type like countries, phonecodes with the search text you want to search for</u></i>
     * </div>
     * <br />
     * List of all resources available are
     *
     * <ul>
     *  <li><b>countries</b> Will give the list of countries.</li>
     *  <li><b>phonecodes</b> Will give the list of international phone codes for countries.</li>
     *  <li><b>nationalities</b> Will give the list of Nationalities</li>
     *  <li><b>visatypes</b> Will give the list of Vista types</li>
     *  <li><b>englishspeakingrating</b> Will give the levels to self rate English Speaking Skill</li>
     *  <li><b>languages</b> Will give the list of all Languages</li>
     *  <li><b>disabilities</b> Will give the list of accepted Disabilities</li>
     *  <li><b>schoollevels</b> Will give the list of Level of schooling</li>
     *  <li><b>schooltype</b> Will give the list of types of schooling</li>
     *  <li><b>qualificationlevels</b> Will give the list of different qualification levels</li>
     *  <li><b>employmentstatus</b> Will give the list of different employment status</li>
     *  <li><b>studyreason</b> Will give the list of reasons of willingness to study</li>
     *  <li><b>aboriginals</b> Will give the list of options for type of language capabilities</li>
     *  <li><b>languagetests</b> Will give the list of accepted language test types</li>
     *  </ul>
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: AnonymsResource[]}
     */
    public function getFormData(Request $request)
    {
        $request->validate([
            /*
            Type accepts any specefic type of form data if required. Default will be null and will return all the form values.
            If more than one required, provide the value as comma saperated values like countries,visatypes
            */
            'type' => ['nullable', 'string'],
            /*
            Except accepts any specefic type of form data you dont want not fetch. Default will be null and will return all the form values.
            If more than one to be ommitted from the resource, provide the value as comma saperated values like countries,visatypes
            */
            'except' => ['nullable', 'string'],
            /*
            If the resource needs to be filtered, provide the search text. Will apply for countries, nationalities, languages resources only
            */
            'search' => ['nullable', 'string'],
        ]);
        $inputData = $request->input();

        $returnData = $this->service->loadFormData($inputData);

        return $this->successResponse('', 'data', $returnData);
    }

    /**
     * Create new application.
     *
     * <div>The application process begins with this endpoint, which is responsible for creating a new application instance for a student. Upon successful creation, the endpoint returns a Student ID, which is a securely hashed unique identifier for the student.</div>
     * <div>This Student ID serves as a key reference and must be included in all subsequent API calls that pertain to the specific student application. Ensure that the provided Student ID is securely stored and consistently used across endpoints requiring it, to maintain data integrity and proper linkage throughout the application process.<div>
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", student: AnonymousResourceCollection<NewStudentApplicationDataResource>}
     *
     * <div><b>The Example of response student Object is</b></div>
     *
     * <div><b>The value of student_id is required for further applciation process.</b></div>
     */
    public function saveStudent(Request $request)
    {
        DB::beginTransaction();
        try {
            $collegeId = auth()->user()->college_id ?? null;
            $studentData = $request->validate([
                'name_title' => ['required', 'string', 'in:Mr.,Mrs.,Ms.,Other'],
                'first_name' => ['required', 'string', 'max:200'],
                'middel_name' => ['nullable', 'string', 'max:200'],
                'family_name' => ['required', 'string', 'max:200'],
                'gender' => ['required', 'string', 'in:Male,Female,Other'],
            ]);
            $studentData['college_id'] = $collegeId;
            $student = $this->service->createNewStudentApplication($studentData);
            $student = Student::with(['creator', 'updater'])->find($student->id);
            DB::commit();

            return $this->successResponse(__('messages.shortcourse.student_application_initiated'), 'student', ($student) ? new NewStudentApplicationDataResource($student) : null);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateStudent(StudentPersonalDetailsRequest $request)
    {
        $this->savePersonalDetails($request);
    }

    /**
     * Save Personal Details.
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Save Personal Details endpoint allows clients to submit and update a student's personal information as part of the application process. This endpoint accepts a JSON payload containing the student's unique identifier (student_id) and comprehensive personal details, including demographic information, contact details, and address information.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to link the details to the specific application.
     *      </li>
     * <li>
     *   Personal Information:<br />
     *   Collects key details such as name, date of birth, gender, and nationality.
     *   </li>
     * <li>
     *   Contact and Address Information:<br />
     *   Captures current, postal, and permanent address details, along with phone and email contact information.
     * </li>
     * <li>
     *   Travel and Visa Information:<br />
     *   Includes fields for passport details, visa status, and visa expiration date.
     *  </li>
     * <li>
     *   Optional Fields:<br />
     *   Allows submission of additional details like optional email, middle name, and building/unit information.
     * </li>
     *
     * @response StudentApplicationDataResourceAllFields
     */
    public function savePersonalDetails(StudentPersonalDetailsRequest $request)
    {
        DB::beginTransaction();
        try {
            $studentData = $request->DTO();
            $student = $this->service->savePersonalDetails($studentData);

            if (! $student) {
                throw new \Exception("Could not save student's personal details");
            }
            DB::commit();
            $applicationData = $this->service->getApplicantData($student->id);

            return $this->successResponse(
                'Personal details saved',
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save Student's USI Number.
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * @response StudentApplicationDataResourceAllFields
     */
    public function saveStudentUSI(StudentUSIDetailsRequest $request)
    {
        DB::beginTransaction();
        try {
            $studentData = $request->DTO();
            if (! empty($studentData->USI)) {
                $student = $this->service->updateStudentUSI($studentData);
                if (! $student) {
                    throw new \Exception("Could not save student's usi details");
                }
                DB::commit();
                $message = __('messages.shortcourse.usi_details_saved');
                $studentId = $student->id;
            } else {
                $message = __('messages.shortcourse.continued_without_usi');
                $studentId = $studentData->student_id;
            }
            $applicationData = $this->service->getApplicantData($studentId);

            return $this->successResponse(
                $message,
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Language Details.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentApplicationDataResourceAllFields[]}
     */
    public function getLanguageDetails(ValidateStudentQueryRequest $request)
    {
        DB::beginTransaction();
        try {
            $studentId = $request->getId();
            $student = ($studentId) ? Student::find($studentId) : null;
            if (! $student) {
                throw new \Exception(__('messages.shortcourse.student_not_found'));
            }
            // $student = $this->service->createNewStudentApplication($studentData);
            // dd($student);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save Language Information
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     *  <br /><br />
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentApplicationDataResourceAllFields[]}
     */
    public function saveLanguageInfo(LanguageInfoRequest $request)
    {
        DB::beginTransaction();
        try {

            $languageData = $request->DTO();
            if ($languageData->EPL_test_name) {
                $languageData->is_EPL_test = true;
            }
            $data = $this->service->updateLanguage($languageData, 'all');

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_update_student_language_details'));
            }
            DB::commit();
            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.language_details_saved'),
                'data',
                (($applicationData) ? new StudentApplicationDataResource($applicationData) : null),
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save Language Proficiency Test Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Record English Language Test Result endpoint enables clients to submit details of a student's English language proficiency test results as part of the application process. This endpoint supports various test types, including TOEFL, IELTS, TOEIC, or other specified tests, and requires a JSON payload with the student's unique identifier (student_id) and test details.
     *  </div>
     *  <br /><br />
     *
     * @response StudentApplicationDataResourceAllFields
     */
    public function saveLanguageDetails(LanguageDetailRequest $request)
    {
        DB::beginTransaction();
        try {

            $languageData = $request->DTO();

            $data = $this->service->updateLanguage($languageData, 'test');

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_update_student_language_details'));
            }
            DB::commit();
            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.language_details_saved'),
                null,
                (($applicationData) ? new StudentApplicationDataResource($applicationData) : null),
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete Language Details.
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * Delete previously recorded Language Profeciency scores.
     *  </div>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function deleteLanguage(ValidateStudentQueryRequest $request)
    {
        DB::beginTransaction();
        try {
            $studentId = $request->getId();

            $data = $this->service->deleteLanguage($studentId);

            if (! $data) {
                throw new \Exception("Could not update student's language details");
            }
            DB::commit();
            $applicationData = $this->service->getApplicantData($studentId);

            return $this->successResponse(
                __('messages.shortcourse.language_deleted'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save Disability Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Save Disability Details endpoint allows clients to submit information about a student's disabilities or special needs as part of the application process. This endpoint requires a JSON payload containing the student's unique identifier (student_id) and a set of boolean fields indicating the presence of specific disabilities or conditions.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to associate the disability information with the specific application.
     *      </li>
     * <li>
     *   Comprehensive Disability Reporting:<br />
     *   Supports detailed reporting of various disabilities, including acquired brain injuries, hearing impairments, intellectual disabilities, learning disabilities, medical conditions, mental illnesses, physical disabilities, vision impairments, and others.
     *   </li>
     * <li>
     *   Boolean Values:<br />
     *   Each disability field accepts a boolean value (`true` or `false`), indicating whether the condition applies to the student.
     * </li>
     * <li>
     *   Customization:<br />
     *   Includes an `other` field to accommodate any disabilities not explicitly listed.
     *  </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function saveDisabilityDetails(DisabilityDetailRequest $request)
    {
        DB::beginTransaction();
        try {

            $disabilityData = $request->DTO();

            $data = $this->service->updateDisability($disabilityData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_update_student_disability_details'));
            }
            DB::commit();
            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.disability_details_saved'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save School Education Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Save School Education Details endpoint allows clients to submit a student's school education information as part of the application process. This endpoint requires a JSON payload containing the student's unique identifier (student_id) and relevant details about their highest completed school level, current school attendance, and related educational information.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to associate the education details with the specific application.
     *      </li>
     * <li>
     *   Highest School Level:<br />
     *   Captures the highest level of school completed using standardized codes.
     *   </li>
     * <li>
     *   Current School Attendance:<br />
     *   Indicates whether the student is currently attending secondary school.
     * </li>
     * <li>
     *   School Type and Funding Source:<br />
     *   Collects information about the type of school attended and the funding source code (if applicable).
     *  </li>
     * <li>
     *   VET in Schools Participation:<br />
     *   Records whether the student is participating in a VET in Schools program.
     *  </li>
     * <li>
     *   School Location ID:<br />
     *   Accepts a boolean or identifier for the school location, facilitating data tracking.
     * </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function saveSchoolingDetails(StudentSchoolingRequest $request)
    {
        DB::beginTransaction();
        try {

            $schoolingData = $request->DTO();

            $data = $this->service->updateSchooling($schoolingData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_update_student_schooling_details'));
            }

            DB::commit();
            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.schooling_details_saved'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save Qualification Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Save Qualification Details endpoint allows clients to submit information about a student's prior qualifications as part of the application process. This endpoint requires a JSON payload containing the student's unique identifier (student_id) and details of the qualification, including its level, name, and completion information.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to associate the qualification details with the specific application.
     *      </li>
     * <li>
     *   Qualification Level:<br />
     *   Captures the level of the qualification using a standardized identifier (e.g., diploma, degree, etc.).
     *   </li>
     * <li>
     *   Qualification Name:<br />
     *   Records the name of the qualification (e.g., Bachelor of Science, Certificate IV in IT).
     * </li>
     * <li>
     *   School or Institution Details:<br />
     *   Includes the name of the school or institution where the qualification was obtained.
     *  </li>
     * <li>
     *   Location and Year of Completion:<br />
     *   Captures the state where the qualification was completed and the year it was awarded.
     * </li>
     *
     * @response StudentApplicationDataResourceAllFields
     */
    public function addQualification(StudentCreateQualificationRequest $request)
    {
        DB::beginTransaction();
        try {

            $qualificationData = $request->DTO();

            $data = $this->service->addQualification($qualificationData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.qualification_not_saved'));
            }

            DB::commit();
            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.qualification_details_saved'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update Qualification Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Update Qualification Details endpoint allows clients to update information about a previously saved qualification for a student. This endpoint requires a JSON payload containing the student's unique identifier (student_id), the qualification identifier (qualification_id), and the updated details for the qualification.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student and Qualification Identification:<br />
     *          Requires a hashed unique student_id to associate the update with the correct application, and a qualification_id to identify the specific qualification being updated.
     *      </li>
     * <li>
     *   Editable Qualification Details:<br />
     *   Allows updating of key fields such as qualification level, name, school name, state, and year completed.
     *   </li>
     * <li>
     *   Qualification Level:<br />
     *   Supports updating the qualification level using a standardized identifier (e.g., diploma, degree, etc.).
     * </li>
     * <li>
     *   Institutional Details:<br />
     *   Enables editing of the school or institution name and its state.
     *  </li>
     * <li>
     *   Timeline Updates:<br />
     *   Allows modification of the year of qualification completion.
     * </li>
     * <li>
     *   Qualification Identifier:<br />
     *   The `qualification_id` is the ID of the qualification that is being updated.
     *  </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function saveQualificationDetails(StudentQualificationRequest $request)
    {
        DB::beginTransaction();
        try {

            $qualificationData = $request->DTO();

            $data = $this->service->updateQualification($qualificationData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.qualification_not_updated'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.qualification_details_saved'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete Qualification Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Delete Qualification Details endpoint allows clients to remove a previously saved qualification from a student's application. This endpoint requires a JSON payload containing the student's unique identifier (student_id) and the qualification identifier (qualification_id) to specify the qualification to be deleted.
     *  </div>
     *  <br /><br />
     *
     * <b>Permanent Action:</b><br />
     *  Once deleted, the qualification data will be permanently removed from the system.
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function deleteQualification($studentId = '', $qualificationId = '')
    {
        DB::beginTransaction();
        try {
            $student = $this->checkStudent($studentId);

            $qualificationId = (int) $qualificationId;
            $qualificationData = StudentApplicationQualificationDTO::LazyFromArray(['student_id' => $student->id, 'id' => $qualificationId]);

            $data = $this->service->deleteQualification($qualificationData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.qualification_not_deleted'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($qualificationData->student_id);

            return $this->successResponse(
                __('messages.shortcourse.qualification_details_deleted'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save Qualification Status.
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * Use only the previous_qualifications parameter to specifically update the Qualification status
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function updateQualificationProfile(StudentProfileUpdateRequest $request)
    {
        DB::beginTransaction();
        try {
            $studentData = $request->DTO();

            $student = $this->service->updateStudentProfile($studentData->toArray(), ['previous_qualifications']);
            $this->service->updateApplicationProcess('previous_qualifications_achieved', $student);

            if (! $student) {
                throw new \Exception(__('messages.shortcourse.could_not_update_student_qualification_details'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($student->student_id);

            return $this->successResponse(
                __('messages.shortcourse.profile_updated'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update study reason.
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * Use only the study_reason parameter to specifically update the Qualification status
     *
     * @response StudentApplicationDataResourceAllFields
     */
    public function updateStudyReasonProfile(StudentProfileUpdateRequest $request)
    {
        DB::beginTransaction();
        try {
            $studentData = $request->DTO();
            $student = $this->service->updateStudentProfile($studentData->toArray(), ['study_reason']);

            if (! $student) {
                throw new \Exception(__('messages.shortcourse.could_not_save_student_personal_details'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($student->student_id);

            return $this->successResponse(
                __('messages.shortcourse.profile_updated'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    public function getEmploymentDetails(ValidateStudentQueryRequest $request)
    {
        // dd("saveEmploymentDetails");
    }

    /**
     * Add Employment Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Add Employment Details endpoint allows clients to submit information about a student's past or current employment as part of the application process. This endpoint requires a JSON payload containing the student's unique identifier (student_id) along with details about their occupation, employer, duration, and job duties.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to associate the employment details with the correct application.
     *      </li>
     * <li>
     *   Employment Information:<br />
     *   Collects details about the student's occupation, employer, employment duration, and job duties.
     *   </li>
     * <li>
     *   Employment Duration:<br />
     *   Captures the employment period with start (`duration_from`) and end dates (`duration_to`).
     * </li>
     * <li>
     *   Duties Description:<br />
     *   Allows submission of job responsibilities and duties the student undertook during the employment.
     * </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function addEmploymentDetails(StudentEmploymentCreateRequest $request)
    {
        DB::beginTransaction();
        try {

            $employmentData = $request->DTO();

            $data = $this->service->addEmployment($employmentData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_update_student_employment'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.employment_details_saved'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update Employment Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Update Employment Details endpoint allows clients to modify previously submitted employment information. This endpoint requires a JSON payload containing the student's unique identifier (student_id), the employment identifier (employee_id), and the updated details about the student's employment.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student and Employment Identification:<br />
     *          Requires a hashed unique student_id to associate the update with the correct application, and an employee_id to specify the employment record being updated.
     *      </li>
     * <li>
     *   Editable Employment Information:<br />
     *   Supports updating occupation, employer, duration, and duties fields for the specified employment record.
     *   </li>
     * <li>
     *   Duration Updates:<br />
     *   Allows modification of employment start (`duration_from`) and end dates (`duration_to`).
     * </li>
     * <li>
     *   Job Duties Updates:<br />
     *   Enables modification of the job responsibilities and duties.
     * </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function saveEmploymentDetails(StudentEmploymentRequest $request)
    {
        DB::beginTransaction();
        try {

            $employmentData = $request->DTO();

            $data = $this->service->updateEmployment($employmentData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_update_student_employment'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.employment_details_saved'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete Employment Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Delete Employment Details endpoint allows clients to remove a student's previously saved employment record. This endpoint requires a JSON payload containing the student's unique identifier (student_id) and the employment identifier (employee_id) to specify which employment record to delete.
     *  </div>
     *  <br /><br />
     *  <b>Permanent Removal:</b><br />
     *  Once deleted, the employment data is permanently removed from the system.
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function deleteEmployment($studentId = '', $employmentId = null)
    {
        DB::beginTransaction();
        try {

            $student = $this->checkStudent($studentId);

            $employmentId = (int) $employmentId;

            $qualificationData = StudentApplicationEmploymentDTO::LazyFromArray(['student_id' => $student->id, 'id' => $employmentId]);

            // StudentEmploymentDeleteRequest $request

            $data = $this->service->deleteEmployment($qualificationData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_delete_student_employment'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($qualificationData->student_id);

            return $this->successResponse(
                __('messages.shortcourse.employment_deleted'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save Employment Status.
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * Use only the current_employment_status parameter to specifically update the Employment status
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function updateEmploymentProfile(StudentProfileUpdateRequest $request)
    {
        DB::beginTransaction();
        try {
            $studentData = $request->DTO();
            $student = $this->service->updateStudentProfile($studentData->toArray(), ['current_employment_status']);
            $this->service->updateApplicationProcess('employment', $student);

            if (! $student) {
                throw new \Exception(__('messages.shortcourse.could_not_save_student_personal_details'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($student->student_id);

            return $this->successResponse(
                __('messages.shortcourse.profile_updated'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    public function saveStudyReasonDetails(Request $request)
    {
        // dd("saveStudyReasonDetails");
    }

    public function saveUsiDetails(Request $request)
    {
        // dd("saveUsiDetails");
    }

    public function saveReachDetails(Request $request)
    {
        // dd("saveReachDetails");
    }

    public function addAdditionalService(Request $request)
    {
        // dd("addAdditionalService");
    }

    public function deleteAdditionalService(Request $request)
    {
        // dd("deleteAdditionalService");
    }

    /**
     * Add/Update OSHC Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Add/Update OSHC Details endpoint allows clients to submit or modify a student's Overseas Student Health Cover (OSHC) information as part of the application process. This endpoint requires a JSON payload containing the student's unique identifier (student_id), along with key details about their OSHC, such as the coverage start and end dates, provider, type, fee, and arrangement status.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to link the OSHC details to the correct student application.
     *      </li>
     * <li>
     *   OSHC Coverage Information:<br />
     *   Captures details such as the coverage provider (`OSHC_provider`), type of coverage (`OSHC_type`), and duration.
     *   </li>
     * <li>
     *   OSHC Duration and Dates:<br />
     *   Captures the start and end dates of the OSHC coverage (`OSHC_start_date`, `OSHC_end_date`), along with the duration.
     * </li>
     * <li>
     *   Fee Information:<br />
     *   Allows submission of the total OSHC fee (`OSHC_fee`) for the coverage.
     * </li>
     * <li>
     *   Arrangement Status:<br />
     *   The `arrange_OSHC` field determines whether the student has arranged OSHC (true/false).
     * </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function saveOSHC(StudentOshcRequest $request)
    {
        DB::beginTransaction();
        try {

            $oshcData = $request->DTO();

            $data = $this->service->saveOshc($oshcData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_save_student_oshc'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.oshc_details_saved'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Add/Update Emergency Contact Details
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Add/Update Emergency Contact Details endpoint allows clients to submit or modify a student's emergency contact information as part of the application process. This endpoint requires a JSON payload containing the student's unique identifier (student_id) and detailed emergency contact information, including the contact person's name, phone number, relationship, email, and address.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to associate the emergency contact details with the correct student application.
     *      </li>
     * <li>
     *   Emergency Contact Information:<br />
     *   Collects essential emergency contact details such as the contact person's name, phone number, email, and relationship to the student.
     *   </li>
     * <li>
     *   Emergency Address:<br />
     *   Captures the address of the emergency contact for urgent situations.
     * </li>
     *
     * @response StudentApplicationDataResourceAllFields
     */
    public function saveEmergencyContact(StudentEmergencyContactRequest $request)
    {
        DB::beginTransaction();
        try {

            $contactData = $request->DTO();

            $data = $this->service->saveEmergencyContact($contactData);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_save_student_emergency_contact'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($data->student_id);

            return $this->successResponse(
                __('messages.shortcourse.emergency_contact_details_updated'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    public function saveStudentSurveyDetails(Request $request)
    {
        dd('saveStudentSurveyDetails');
    }

    /**
     * Upload Student Document
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Upload Student Document endpoint allows clients to upload important documents related to a student's application. This endpoint requires a JSON payload containing the student's unique identifier (student_id), a document identifier (document_id), document type (document_type), and the actual document content (document). The document can be any valid file type as specified by the system.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to link the document to the specific student application.
     *      </li>
     * <li>
     *   Document Identification:<br />
     *   Requires a `document_id` to uniquely identify the uploaded document in the system.
     *   </li>
     * <li>
     *   Document Type:<br />
     *   Specifies the type of the document being uploaded (e.g., passport, visa, transcript, etc.) via the `document_type` field.
     * </li>
     * <li>
     *   Document Content:<br />
     *   The actual content of the document is provided as a `document` field.
     * </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function saveStudentDocument(StudentDocumentUploadRequest $request)
    {
        DB::beginTransaction();
        try {

            $document = $request->DTO();

            $data = $this->service->uploadDocument($document);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_save_student_document'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($document->student_id);

            return $this->successResponse(
                __('messages.shortcourse.document_uploaded'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete Student Document
     *
     * @param  string  $studentId  The hashed key of the student. NOTE: If the request is made by the currently authenticated student, you can use 'self' as the value for studentId.
     *
     * <div>
     * The Delete Student Document endpoint allows clients to delete previously uploaded documents related to a student's application. This endpoint requires a JSON payload containing the student's unique identifier (student_id), the document identifier (document_id), and the document type (document_type) to specify which document to remove from the system.
     *  </div>
     *  <br /><br />
     *   <b>Key Features:</b>
     *  <ul>
     *      <li>
     *          Student Identification:<br />
     *          Requires a hashed unique student_id to identify which student's document is being deleted.
     *      </li>
     * <li>
     *   Document Identification:<br />
     *   The `document_id` specifies the document to be deleted.
     *   </li>
     *
     *  @response StudentApplicationDataResourceAllFields
     */
    public function deleteStudentDocument(StudentDocumentDeleteRequest $request)
    {
        DB::beginTransaction();
        try {

            $document = $request->DTO();

            $data = $this->service->deleteDocument($document);

            if (! $data) {
                throw new \Exception(__('messages.shortcourse.could_not_delete_student_document'));
            }

            DB::commit();

            $applicationData = $this->service->getApplicantData($document->student_id);

            return $this->successResponse(
                __('messages.shortcourse.document_deleted'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Enroll Student to a course.
     *
     * If the authenticated user is a student, the particular student will be enrolled to the course. For the course to be successfully enrolled,
     * the student must have the application form filled completely. On the successful enrollment and the payment is enabled for the API, the response will provide
     * the checkout url to complete the payment process.
     *
     * <br />
     * <div class="sl-text-on-primary sl-p-2 sl-bg-warning sl-rounded-lg">
     * <i><u>If the student application is not complete, response won't have checkout url. Instead it will have a array key named checkout_issues with the description of missing application status.</u></i>
     * </div>
     * <br />
     *
     * In the case of successful payment, the API will redirect user the url provided in the redirect_uri.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: array{checkout: "", amount: "", currency: "", enrollment_id: "", booking_reference: "",course_code: "",course_name: "",course_duration: "",intake_year: "",intake_name: "",intake_start: "",campus: "",email_verified: ""}}
     */
    public function enrollStudentToCourse(StudentCourseEnrollRequest $request)
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();
            /* check for application form completion status */
            $studentData = Student::with('appprocess')->find($validated['student']);
            $applicationStatus = $studentData->appprocess ?? [];
            $checkCompleted = $applicationStatus->checkComplettionStatus('minimal');
            // if(!$checkCompleted["result"]){
            //     throw(new \Exception("Could not enroll student to the course. First complete the student application process."));
            // }

            $data = $this->service->enrollStudent($validated, false);

            $url = $checkoutData = null;
            if ($data) {

                $data->setRelation('application_status', $checkCompleted);

                if ($data->offer_status == SiteConstants::ENROLLED_STATUS) {
                    throw (new \Exception(__('messages.shortcourse.student_already_enrolled_to_course')));
                }

                $isPaymentEnabled = Colleges::getCollegeInfoQuery()->value('enable_stripe_checkout_from_api');

                $isStripeConnected = StripeConfig::isConnected();

                $intakeData = $data->intake;
                $campusData = $data->campus;
                $courseData = new CourseSummaryResource($data->course);
                $courseData = $courseData->toArray();

                $checkoutOkay = false;
                if ($isPaymentEnabled == 1 && $isStripeConnected) {
                    $checkoutOkay = true;
                }

                $paymentData = $this->service->startPaymentProcess($data, $request, $checkoutOkay);

                if (! $paymentData) {
                    throw (new \Exception(__('messages.shortcourse.could_not_process_checkout')));
                }
                $responseOkText = $checkoutOkay ? __('messages.shortcourse.proceed_to_payment') : __('messages.shortcourse.successfully_enrolled_to_the_course');

                $responseMsg = ($paymentData['checkout']) ? $responseOkText : __('messages.shortcourse.booking_received_issue_with_application');

                $courseData = [
                    'enrollment_id' => encryptIt($data->id ?? null),
                    'booking_reference' => $paymentData['booking_reference'] ?? '',
                    'course_code' => $courseData['course_code'] ?? '',
                    'course_name' => $courseData['course_name'] ?? '',
                    'course_duration' => $courseData['duration_text'] ?? '',
                    'intake_year' => $intakeData->intake_year ?? '',
                    'intake_name' => $intakeData->intake_name ?? '',
                    'intake_start' => $intakeData->intake_start ?? '',
                    'campus' => $campusData->name ?? null,
                    'email_verified' => authUser()->email_verified_at !== null,
                ];
                $checkoutData = array_merge($paymentData, $courseData);
            } else {
                throw (new \Exception(__('messages.shortcourse.could_not_enroll')));
            }
            // $applicationData = $this->service->getApplicantData($validated["student"]);
            DB::commit();

            return $this->successResponse(
                $responseMsg,
                'data',
                $checkoutData,
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Checkout Link for Enrolled Course.
     *
     * Get the checkout url for the enrolled course. This endpoint will allow to continue the enrollment flow.
     *
     *
     * <br />
     * <div class="sl-text-on-primary sl-p-2 sl-bg-warning sl-rounded-lg">
     * <i><u>If the student application is not complete, response won't have checkout url. Instead it will have a array key named checkout_issues with the description of missing application status.</u></i>
     * </div>
     * <br />
     *
     * In the case of successful payment, the API will redirect user the url provided in the redirect_uri.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: array{checkout: "", amount: "", currency: "", enrollment_id: "", booking_reference: "",course_code: "",course_name: "",course_duration: "",intake_year: "",intake_name: "",intake_start: "",campus: "",email_verified: ""}}
     */
    public function getCheckoutLinkToEnrolledCourse(StudentCourseCheckoutRequest $request)
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            /* check for application form completion status */
            $studentData = Student::with('appprocess')->find($validated['student']);
            $applicationStatus = $studentData->appprocess ?? [];
            $checkCompleted = $applicationStatus->checkComplettionStatus('minimal');
            // if(!$checkCompleted["result"]){
            //     throw(new \Exception("Could not enroll student to the course. First complete the student application process."));
            // }

            $data = StudentCourses::with(['course', 'intake', 'campus'])->find($validated['enrollment_id']);
            $url = $checkoutData = null;
            if ($data) {

                $data->setRelation('application_status', $checkCompleted);

                /*
                update the redirect uri if came different
                This is required when the checkout process is done later (not while enrolling)
                and he checkout_url was different than previously saved.
                */
                if ($validated['redirect_uri'] && $validated['redirect_uri'] !== $data->enrolled_host_url) {
                    $data->enrolled_host_url = $validated['redirect_uri'];
                    $data->save();
                }

                $isPaymentEnabled = Colleges::getCollegeInfoQuery()->value('enable_stripe_checkout_from_api');

                $isStripeConnected = StripeConfig::isConnected();

                $intakeData = $data->intake;
                $campusData = $data->campus;
                $courseData = new CourseSummaryResource($data->course);
                $courseData = $courseData->toArray();

                $checkoutOkay = false;
                if ($isPaymentEnabled == 1 && $isStripeConnected) {
                    $checkoutOkay = true;
                }

                $paymentData = $this->service->startPaymentProcess($data, $request, $checkoutOkay);

                if (! $paymentData) {
                    throw (new \Exception(__('messages.shortcourse.could_not_process_checkout')));
                }
                $responseOkText = $checkoutOkay ? __('messages.shortcourse.proceed_to_payment') : __('messages.shortcourse.successfully_enrolled_to_the_course');

                $responseMsg = ($paymentData['checkout']) ? $responseOkText : __('messages.shortcourse.booking_received_issue_with_application');

                $courseData = [
                    'enrollment_id' => encryptIt($data->id ?? null),
                    'booking_reference' => $paymentData['booking_reference'] ?? '',
                    'course_code' => $courseData['course_code'] ?? '',
                    'course_name' => $courseData['course_name'] ?? '',
                    'course_duration' => $courseData['duration_text'] ?? '',
                    'intake_year' => $intakeData->intake_year ?? '',
                    'intake_name' => $intakeData->intake_name ?? '',
                    'intake_start' => $intakeData->intake_start ?? '',
                    'campus' => $campusData->name ?? null,
                    'email_verified' => authUser()->email_verified_at !== null,
                ];
                $checkoutData = array_merge($paymentData, $courseData);
            } else {
                throw (new \Exception(__('messages.shortcourse.could_not_enroll')));
            }
            // $applicationData = $this->service->getApplicantData($validated["student"]);
            DB::commit();

            return $this->successResponse(
                $responseMsg,
                'data',
                $checkoutData,
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Student Favourite and Enrolled Courses.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: array{favourites: CourseFavouriteCoursesResource, enrolled: StudentEnrolledCoursesResource}}
     */
    public function getStudentCourses(Request $request)
    {
        try {
            $request->validate([
                /* Provide student Id of the current logged in user is not a student */
                'student' => ['nullable', 'string'],
                /* Search favourite courses by keyword */
                'keyword' => ['nullable', 'string'],
            ]);
            $course = $request->input('keyword') ?? null;
            if (auth()->user()->role_id == Roles::TYPE_STUDENT) {
                $student = Student::getLoginStudentInfo();
                $studentId = $student->id ?? null;
            } else {
                $studentId = $request->input('student') ?? null;
                $studentId = ($studentId) ? decryptIt($studentId) : null;
            }
            $courses = ($student) ? Student::with([
                'favouriteCourses' => function ($q) {
                    $q->with('course')->where(['is_favourite' => 1]);
                },
                'studentCourses' => function ($q) {
                    $q->with([
                        'galaxyInvoices' => function ($iq) {
                            // $iq->orderByDesc("status")->orderByDesc("id");
                            $iq->orderByRaw('FIELD(status, 1, 3, 0, 2)')->orderByDesc('id');
                        },
                        'course.courseType',
                        'intake.campusIntakes',
                    ])->orderByDesc('id');
                },
            ])->find($student->id) : null;

            $total = ($courses) ? $courses->favouriteCourses->count() : 0;
            $totalEnrolled = ($courses) ? $courses->studentCourses->count() : 0;
            $favResource = ($courses) ? CourseFavouriteCoursesResource::collection($courses->favouriteCourses) : null;
            $enrResource = ($courses) ? StudentEnrolledCoursesResource::collection($courses->studentCourses) : null;

            return $this->successResponse(__('messages.shortcourse.total_courses_found', ['total' => $total]), 'data', ['favourites' => $favResource, 'enrolled' => $enrResource]);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Favourite Courses.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: CourseFavouriteCoursesResource[]}
     */
    public function getFavouriteCourses(Request $request)
    {
        try {
            $request->validate([
                /* Provide student Id of the current logged in user is not a student */
                'student' => ['nullable', 'string'],
                /* Search favourite courses by keyword */
                'keyword' => ['nullable', 'string'],
            ]);
            $course = $request->input('keyword') ?? null;
            if (auth()->user()->role_id == Roles::TYPE_STUDENT) {
                $student = Student::getLoginStudentInfo();
                $studentId = $student->id ?? null;
            } else {
                $studentId = $request->input('student') ?? null;
                $studentId = ($studentId) ? decryptIt($studentId) : null;
            }
            $courses = ($student) ? Student::with([
                'favouriteCourses' => function ($q) {
                    $q->with('course')->where(['is_favourite' => 1])->orderByDesc('id');
                },
            ])->find($student->id) : null;
            $total = ($courses) ? $courses->favouriteCourses->count() : 0;
            $resource = ($courses) ? CourseFavouriteCoursesResource::collection($courses->favouriteCourses) : null;

            return $this->successResponse(__('messages.shortcourse.total_courses_found', ['total' => $total]), 'data', $resource);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Enrolled Courses.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentEnrolledCoursesResource[]}
     */
    public function getEnrolledCourses(Request $request)
    {
        try {
            $request->validate([
                /* Provide student Id of the current logged in user is not a student */
                'student' => ['nullable', 'string'],
                /* Search favourite courses by keyword */
                'keyword' => ['nullable', 'string'],
            ]);
            $course = $request->input('keyword') ?? null;
            if (auth()->user()->role_id == Roles::TYPE_STUDENT) {
                $student = Student::getLoginStudentInfo();
                $studentId = $student->id ?? null;
            } else {
                $studentId = $request->input('student') ?? null;
                $studentId = ($studentId) ? decryptIt($studentId) : null;
            }
            $courses = ($student) ? Student::with([
                'studentCourses' => function ($q) {
                    $q->with([
                        'galaxyInvoices' => function ($iq) {
                            // $iq->orderByDesc("status")->orderByDesc("id");
                            $iq->orderByRaw('FIELD(status, 1, 3, 0, 2)')->orderByDesc('id');
                        },
                        'course.courseType',
                        'intake.campusIntakes',
                    ])->orderByDesc('id');
                },
            ])->find($student->id) : null;

            $total = ($courses) ? $courses->studentCourses->count() : 0;
            $resource = ($courses) ? StudentEnrolledCoursesResource::collection($courses->studentCourses) : null;

            return $this->successResponse(__('messages.shortcourse.total_courses_found', ['total' => $total]), 'data', $resource);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Enrolled Courses Detail.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: StudentEnrolledCoursesResource}
     */
    public function getEnrolledCourseDetail(Request $request, $id)
    {
        try {

            $id = ($id) ? decryptIt($id) : null;

            if (auth()->user()->role_id == Roles::TYPE_STUDENT) {
                $student = Student::getLoginStudentInfo();
                $studentId = $student->id ?? null;
            } else {
                $studentId = $request->input('student') ?? null;
                $studentId = ($studentId) ? decryptIt($studentId) : null;
            }
            $enrollmentData = StudentCourses::with([
                'galaxyInvoices' => function ($iq) {
                    // $iq->orderByDesc("status")->orderByDesc("id");
                    $iq->orderByRaw('FIELD(status, 1, 3, 0, 2)')->orderByDesc('id');
                },
                'course.courseType',
                'intake.campusIntakes',
            ])->find($id);
            if (! $enrollmentData) {
                throw (new \Exception(__('messages.shortcourse.enrollment_not_found')));
            }
            $resource = ($enrollmentData) ? new StudentEnrolledCoursesResource($enrollmentData) : null;

            return $this->successResponse(__('messages.shortcourse.enrollment_detail_found'), 'data', $resource);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Add Course as Favourite.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: bool}
     */
    public function addFavouriteCourses(Request $request)
    {
        DB::beginTransaction();
        try {
            $request->validate([
                /* Provide student Id of the current logged in user is not a student */
                'student' => ['nullable', 'string'],
                /* Course Id */
                'course' => ['nullable', 'numeric'],
            ]);
            $courseId = $request->input('course') ?? null;
            if (auth()->user()->role_id == Roles::TYPE_STUDENT) {
                $student = Student::getLoginStudentInfo();
            } else {
                $studentId = $request->input('student') ?? null;
                $student = Student::find($studentId);
            }
            if (! $student) {
                throw new \Exception(__('messages.shortcourse.student_not_found'));
            }

            $courese = Courses::find($courseId);
            if (! $courese) {
                throw new \Exception(__('messages.shortcourse.course_not_found'));
            }
            $favourite = $student->addCourseAsFavourite($courese, 'click', true);
            DB::commit();

            return $this->successResponse(__('messages.shortcourse.successfully_saved'), 'favourite', $favourite);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Submit the application.
     *
     * @response StudentApplicationDataResourceAllFields
     */
    public function submitStudentApplication(StudentSubmitApplicationRequest $request)
    {
        DB::beginTransaction();
        try {
            // dd("submitStudentApplication");
            $validated = $request->all();
            // get the student
            $student = Student::with(['appprocess'])->find($validated['student_id']);
            $appProcess = $student->appprocess;
            $applicationComplete = $appProcess->checkComplettionStatus('minimal', true);

            /* form is not filled */
            if (! $applicationComplete['result']) {
                $msg = __('messages.shortcourse.can_not_submit_application');

                return $this->errorResponse(
                    $msg,
                    'data',
                    [
                        'issue' => $applicationComplete['reason'],
                        'application_status' => new StudentApplicationStatusDataResource($appProcess),
                    ]);
            }

            $student->applicant = 1;
            $student->is_applicant = 1;
            $student->read_and_agree = 'YES';
            $student->save();

            $this->service->updateApplicationProcess('application_declared_agreed', $student);

            DB::commit();

            $applicationData = $this->service->getApplicantData($student->id);

            return $this->successResponse(
                __('messages.shortcourse.application_completed'),
                'data',
                ($applicationData ? new StudentApplicationDataResource($applicationData) : null),
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Send Email Verification Link.
     *
     * @unauthenticated
     *
     * Send email verification link to the student.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: bool}
     */
    public function sendEmailVerification(Request $request)
    {
        try {
            $validator = Validator::make($request->post(), [
                'email' => ['required', 'email'],
                'redirect_uri' => ['nullable', 'string'],
            ]);

            $validated = $validator->validate();

            $email = $validated['email'] ?? null;
            $redirectUri = trim($validated['redirect_uri'] ?? null);

            $user = Users::where(['email' => $email, 'role_id' => Roles::TYPE_STUDENT])->first();

            if (! $user) {
                throw new \Exception('User not found.');
            }

            if ($user->email_verified_at) {
                throw new \Exception(__('messages.shortcourse.email_already_verified'));
            }

            $student = Student::where('email', $email)->orWhere('generated_stud_id', $user->username)->first();

            if (! $student) {
                throw new \Exception(__('messages.shortcourse.student_not_found'));
            }

            $this->service->sendEmailVerificationEmail($user, $student, $redirectUri);

            return $this->successResponse(__('messages.shortcourse.email_verification_link_sent'), 'data', true);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function verifyStudentEmail(Request $request, $code)
    {
        DB::beginTransaction();
        try {

            $requestEmail = $request->input('email') ?? null;
            $requestRedirect = $request->input('redirect') ?? null;

            $code = decryptIt($code);
            $code = explode('|', $code);

            $userId = $code[0] ?? null;
            $redirectUri = $code[1] ?? null;
            $timestamp = (int) $code[2] ?? null;
            $currentTimestamp = Carbon::now()->timestamp;
            if ($currentTimestamp - $timestamp > 24 * 60 * 60) {
                throw new \Exception(__('messages.shortcourse.verification_link_expired'));
            }
            $user = Users::where(['username' => $userId, 'role_id' => Roles::TYPE_STUDENT])->first();

            if (! $user) {
                throw new \Exception(__('messages.shortcourse.user_not_found'));
            }

            if ($user->email_verified_at) {
                throw new \Exception(__('messages.shortcourse.email_already_verified'));
            }

            if ($requestEmail != $user->email || $requestRedirect != $redirectUri) {
                throw new \Exception('Invalid request.');
            }
            $user->email_verified_at = now();
            $user->save();
            DB::commit();

            // Ensure redirectUri has protocol
            $getParams = explode('?', string: $redirectUri);
            $redirectUri = $getParams[0] ?? '';
            $getParams = $getParams[1] ?? '';

            if (! preg_match('/^https?:\/\//', $redirectUri)) {
                $redirectUri = 'https://'.$redirectUri;
            }

            $redirectUri = $redirectUri.'?'.http_build_query([
                'success' => 1,
                'action' => 'email_verified',
            ]).($getParams ? '&'.$getParams : '');

            return redirect($redirectUri);
        } catch (\Exception $e) {
            DB::rollBack();

            return response($e->getMessage(), 400);
        }
    }

    /**
     * Send Password Reset Link.
     *
     * @unauthenticated
     *
     * Send password reset link to the student.
     *
     * @response array{success: "0/1", status: "Success/Error", message: "String", data: bool}
     */
    public function sendPasswordResetLink(Request $request)
    {
        try {

            $validator = Validator::make($request->post(), [
                'email' => ['required', 'email'],
                'redirect_uri' => ['nullable', 'string'],
            ]);

            $validated = $validator->validate();

            $email = $validated['email'] ?? null;
            $redirectUri = trim($validated['redirect_uri'] ?? null);

            if (! empty($redirectUri)) {
                $redirectUri = validateRedirectUri($redirectUri, false);
                if (! $redirectUri) {
                    throw new \Exception(__('messages.shortcourse.invalid_redirect_uri'));
                }
            } else {
                $redirectUri = validateRedirectUri();
            }

            $user = Users::where(['email' => $email, 'role_id' => Roles::TYPE_STUDENT])->first();

            if (! $user) {
                throw new \Exception(__('messages.shortcourse.user_not_found'));
            }

            $this->service->sendPasswordResetEmail($user, $redirectUri);

            return $this->successResponse(__('messages.shortcourse.password_reset_link_sent'), 'data', true);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function showPasswordResetForm(Request $request, $token)
    {
        try {
            $code = decryptIt($token);
            $code = explode('|', $code);

            $userId = (int) $code[0] ?? null;

            $redirectUri = $code[1] ?? null;

            $timestamp = (int) $code[2] ?? null;
            $currentTimestamp = Carbon::now()->timestamp;
            if ($currentTimestamp - $timestamp > 24 * 60 * 60) {
                throw new \Exception(__('messages.shortcourse.verification_link_expired'));
            }

            if (! empty($redirectUri)) {
                $redirectUri = validateRedirectUri($redirectUri, false);
                if (! $redirectUri) {
                    throw new \Exception(__('messages.shortcourse.invalid_redirect_uri'));
                }
            } else {
                $redirectUri = validateRedirectUri();
            }

            $user = Users::where(['id' => $userId, 'role_id' => Roles::TYPE_STUDENT])->first();

            if (! $user) {
                throw new \Exception(__('messages.shortcourse.user_not_found'));
            }

            $student = Student::where('email', $user->email)->orWhere('generated_stud_id', $user->username)->first();
            $lastPasswordResetRequestedAt = $student->password_reset_requested_at ?? null;
            $lastPasswordResetRequestedTimestamp = ($lastPasswordResetRequestedAt) ? Carbon::parse($lastPasswordResetRequestedAt)->timestamp : null;
            if ($lastPasswordResetRequestedTimestamp != $timestamp) {
                throw new \Exception(__('messages.shortcourse.password_reset_request_expired'));
            }

            $student = Student::where('email', $user->email)->orWhere('generated_stud_id', $user->username)->first();

            return view('jetstream.auth.reset-password', ['request' => $request, 'submitUrl' => route('shortcourse.update.password', $token)]);
        } catch (\Exception $e) {
            return response($e->getMessage());
        }
    }

    public function updateStudentPassword(Request $request, $token)
    {
        DB::beginTransaction();
        try {
            $request->validate([
                'password' => ['required', 'string', 'min:8', 'confirmed'],
            ]);
            $code = decryptIt($token);
            $code = explode('|', $code);

            $userId = (int) $code[0] ?? null;
            $redirectUri = $code[1] ?? null;

            $timestamp = (int) $code[2] ?? null;
            $currentTimestamp = Carbon::now()->timestamp;
            if ($currentTimestamp - $timestamp > 24 * 60 * 60) {
                throw new \Exception(__('messages.shortcourse.verification_link_expired'));
            }

            if (! empty($redirectUri)) {
                $redirectUri = validateRedirectUri($redirectUri, false);
                if (! $redirectUri) {
                    throw new \Exception(__('messages.shortcourse.invalid_redirect_uri'));
                }
            } else {
                $redirectUri = validateRedirectUri();
            }

            $user = Users::where(['id' => $userId, 'role_id' => Roles::TYPE_STUDENT])->first();

            if (! $user) {
                throw new \Exception(__('messages.shortcourse.user_not_found'));
            }

            $student = Student::where('email', $user->email)->orWhere('generated_stud_id', $user->username)->first();
            $lastPasswordResetRequestedAt = $student->password_reset_requested_at ?? null;
            $lastPasswordResetRequestedTimestamp = ($lastPasswordResetRequestedAt) ? Carbon::parse($lastPasswordResetRequestedAt)->timestamp : null;
            if ($lastPasswordResetRequestedTimestamp == $timestamp) {
                $user->password = Hash::make($request->input('password'));
                $user->api_token = null;
                $user->save();

                // GNG-4916 (Auto-verify email when password is set via email link)
                if (! $user->hasVerifiedEmail()) {
                    $user->markEmailAsVerified();
                }

                // delete all user tokens
                $user->tokens()->delete();
                $student->password_reset_requested_at = null;
                $student->save();
                DB::commit();
            } else {
                throw new \Exception(__('messages.shortcourse.password_reset_request_expired'));
            }

            // Ensure redirectUri has protocol
            $getParams = explode('?', string: $redirectUri);
            $redirectUri = $getParams[0] ?? '';
            $getParams = $getParams[1] ?? '';

            if (! preg_match('/^https?:\/\//', $redirectUri)) {
                $redirectUri = 'https://'.$redirectUri;
            }

            $redirectUri = $redirectUri.'?'.http_build_query([
                'success' => 1,
                'action' => 'password_updated',
            ]).($getParams ? '&'.$getParams : '');

            return redirect($redirectUri);
        } catch (\Exception $e) {
            DB::rollBack();

            return response($e->getMessage());
        }
    }
}
