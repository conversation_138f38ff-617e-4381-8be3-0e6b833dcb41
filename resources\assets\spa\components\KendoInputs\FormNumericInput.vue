<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :editor-id="id"
            :editor-valid="internalValid"
            :disabled="disabled"
            :optional="optional"
            :class="labelClass"
            v-if="label"
        >
            {{ label }}
            <InputLabelInfo :info="labelinfo" />
            <span v-if="required || indicaterequired" class="ml-1 text-red-500">*</span>
        </klabel>
        <div :class="wrapClass">
            <numerictextbox
                :valid="internalValid"
                :id="id"
                :value="currentValue"
                :disabled="disabled"
                :spinners="spinners"
                :step="step"
                :min="min"
                :max="max"
                :name="name"
                :class="fieldClass"
                :placeholder="placeholder"
                :format="format"
                :default-value="defaultValue"
                @change="handleChange"
                @blur="handleBlur"
                @focus="handleFocus"
            ></numerictextbox>

            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else>{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { NumericTextBox } from '@progress/kendo-vue-inputs';
import InputLabelInfo from '@spa/components/inputLabelInfo.vue';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: [Number, String],
        optional: Boolean,
        disabled: Boolean,
        spinners: Boolean,
        step: {
            type: [Number],
            default: 1,
        },
        min: [null, Number],
        max: [null, Number],
        placeholder: String,
        touched: Boolean,
        label: String,
        labelinfo: {
            type: Object,
            default: {},
        },
        validationMessage: String,
        hint: String,
        id: String,
        valid: {
            type: Boolean,
            default: true,
        },
        format: { type: String, default: 'n' },
        value: [Number, String],
        name: String,
        orientation: { type: String, default: 'vertical' },
        pt: {
            type: Object,
            default: {},
        },
        indicaterequired: { type: Boolean, default: false },
        defaultValue: [Number, String],
        required: { type: Boolean, default: false },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        numerictextbox: NumericTextBox,
        InputLabelInfo,
    },
    data() {
        return {
            internalValid: this.valid,
        };
    },
    computed: {
        currentValue() {
            return this.modelValue !== undefined ? this.modelValue : this.value;
        },
        vModel: {
            get() {
                return Number(this.modelValue);
            },
            set(value) {
                this.$emit('update:modelValue', Number(value));
            },
        },
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        rootClass() {
            const baseClass = 'tw-form__fieldwrapper';
            const orientationClass = this.orientation === 'horizontal' ? 'field-horizontal' : '';
            return twMerge(`${baseClass} ${orientationClass}`, this.pt.root);
        },

        wrapClass() {
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },

        labelClass() {
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
        fieldClass() {
            return twMerge('tw-form__input', this.pt.input);
        },
    },
    emits: {
        input: null,
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    watch: {
        modelValue(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.internalValid = true; // Reset valid on input change
            }
        },
        valid(newVal) {
            this.internalValid = newVal; // sync from parent
        },
    },
    methods: {
        handleInput(e) {
            this.$emit('input', e);
        },
        handleChange(e) {
            const numericValue = Number(e.target.value);
            this.$emit('update:modelValue', numericValue);

            this.$emit('input', e);
            this.$emit('change', e);
        },
        handleBlur(e) {
            this.$emit('change', e);
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
