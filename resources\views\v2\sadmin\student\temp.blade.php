{{--
1. user have not synced to xero yet (need sync to xero button)
2. user has synced to xero successfully ( show synced indicator and user can resync to xero so show sync to xero button)
3. user sync failed the first time ( show failed indicator and show sync to xero with failed reason and time and
indicator)
4.user has synced in the past but latest sync failed (need to show failed indicator and 2 buttons, 1 sync to xero again,
2
sync from xero and if possible show last successful synced datetime and failed datetime with message) --}}

<div id="sendMailStudentModal" style="display: none;">
    <form method="POST" accept-charset="UTF-8"
        class="form-horizontal vertical-add-form flex flex-col justify-between w-full h-full" id="emailTemplateAddForm"
        enctype="multipart/form-data" data-nordpass-autofill="identity" data-np-checked="1">
        <div class="flex flex-col justify-between overflow-y-auto h-screen">
            <div class="flex flex-col w-full">

                <x-v2.templates.list-item class="mail__type" label="Type:">
                    <div class="email_type">
                        <input type="radio" id="email_type_course" name="email_type" class="cursor-pointer"
                            value="course" checked>
                        <label for="email_type_course" class="cursor-pointer">Course</label>
                        <input type="radio" id="email_type_generic" name="email_type" class="cursor-pointer"
                            value="generic">
                        <label for="email_type_generic" class="cursor-pointer">Generic</label>
                    </div>
                </x-v2.templates.list-item>
                <div
                    class="flex items-center justify-start pl-4 pr-2 bg-blue-gray-50 border-b py-2 w-full mail__course">
                    <div class="flex items-center justify-start w-full">
                        <span class="text-sm leading-5 text-gray-500 w-16">Course:</span>
                        <div class="flex items-center justify-start w-5/6">
                            <div class="course_list">
                                <input type="text" id="course_list_for_email" name="course_id"
                                    class="cursor-pointer p-1 bg-white border rounded-lg border-gray-300">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-1 items-center justify-between pl-4 pr-2 bg-blue-gray-50 border-b py-1 w-full">
                    <div class="flex items-center justify-start w-full studentListBoxBorder">
                        <span class="text-sm leading-5 text-gray-500 pt-2.5 pb-2.5 w-16">To:</span>
                        <div id="mailToUser"
                            class="overflow-y-auto studentNameList bg-transparent border-none text-gray-700 py-1 leading-tight text-sm">
                        </div>
                        <input name="selected_stud_id" value="" class="bg-transparent border-none w-full"
                            id="student_name_email_list" />
                        {{-- <div id="mailToUser"
                            class="studentNameList bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight text-sm">
                        </div> --}}
                        <input type="hidden" class="studentIds" name="student_id" id="studentIds" value="" />
                    </div>
                    <div class="justify-end flex space-x-2">
                        <button class="ccmail flex space-x-2 items-center justify-end cursor-pointer" type="button">
                            <span class="text-sm leading-5 font-normal text-primary-blue-500">Cc</span>
                        </button>
                        <button class="bccmail flex items-center justify-end cursor-pointer" type="button">
                            <span class="text-sm leading-5 font-normal text-primary-blue-500">Bcc</span>
                        </button>
                    </div>
                </div>
                <x-v2.templates.list-item class="mail__course" label="">
                    <div class="w-full h-5 justify-start items-center gap-1 flex">
                        <div class="w-4 h-4 relative"><svg width="12" height="12" viewBox="0 0 12 12"
                                fill="none" xmlns="http://www.w3.org/2000/svg" class="glob-tooltip" title="Help"
                                data-role="tooltip">
                                <path
                                    d="M6 0C9.31371 0 12 2.68629 12 6C12 9.31371 9.31371 12 6 12C2.68629 12 0 9.31371 0 6C0 2.68629 2.68629 0 6 0ZM6 1C3.23858 1 1 3.23858 1 6C1 8.76142 3.23858 11 6 11C8.76142 11 11 8.76142 11 6C11 3.23858 8.76142 1 6 1ZM6 8.5C6.41421 8.5 6.75 8.83579 6.75 9.25C6.75 9.66421 6.41421 10 6 10C5.58579 10 5.25 9.66421 5.25 9.25C5.25 8.83579 5.58579 8.5 6 8.5ZM6 2.5C7.10457 2.5 8 3.39543 8 4.5C8 5.23053 7.78822 5.63969 7.24605 6.20791L6.98196 6.47745C6.60451 6.87102 6.5 7.0831 6.5 7.5C6.5 7.77614 6.27614 8 6 8C5.72386 8 5.5 7.77614 5.5 7.5C5.5 6.76947 5.71178 6.36031 6.25395 5.79209L6.51804 5.52255C6.89549 5.12898 7 4.9169 7 4.5C7 3.94772 6.55228 3.5 6 3.5C5.44772 3.5 5 3.94772 5 4.5C5 4.77614 4.77614 5 4.5 5C4.22386 5 4 4.77614 4 4.5C4 3.39543 4.89543 2.5 6 2.5Z"
                                    fill="#9CA3AF"></path>
                            </svg></div>
                        <div class="text-gray-500 text-xs font-normal leading-tight tracking-wide">
                            Only students with selected course will receive this email.
                        </div>
                        <div class="justify-start items-center flex">
                            <div class="text-sky-500 text-xs font-normal leading-tight tracking-wide">
                                <a
                                    class="notReceivedEmailStudentListBtn text-sky-500 text-xs font-normal leading-tight tracking-wide"><span
                                        class="notReceivedEmailStudentCount">12</span> <span>students will not receive
                                        this email.</span></a>
                            </div>
                        </div>
                    </div>
                </x-v2.templates.list-item>
                <div class="flex items-center justify-start pl-4 pr-2 bg-blue-gray-50 border-b py-2" id="emailccbox"
                    style="display: none;">
                    <div class="flex items-center justify-start w-full">
                        <span class="text-sm leading-5 text-gray-500 w-16">CC:</span>
                        <div class="flex items-center justify-center py-0.5 w-full">
                            <input
                                class="w-full appearance-none text-sm bg-transparent border-none text-gray-700 mr-3 py-1 px-2 leading-tight focus:outline-none"
                                id="email_cc" placeholder="Enter CC Email" name="email_cc" type="text">
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-start pl-4 pr-2 bg-blue-gray-50 border-b py-2" id="emailbccbox"
                    style="display: none;">
                    <div class="flex items-center justify-start w-full">
                        <span class="text-sm leading-5 text-gray-500 w-16">BCC:</span>
                        <div class="flex items-center justify-center py-0.5 w-full">
                            <input
                                class="w-full appearance-none text-sm bg-transparent border-none text-gray-700 mr-3 py-1 px-2 leading-tight focus:outline-none"
                                id="email_bcc" placeholder="Enter BCC Email" name="email_bcc" type="text">
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-start pl-4 pr-2 bg-blue-gray-50 border-b py-2 w-full">
                    <div class="flex items-center justify-start w-full">
                        <span class="text-sm leading-5 text-gray-500 w-16">Subject:</span>
                        <div class="flex items-center justify-center py-0.5 w-full">
                            {{ Form::text('email_subject', null, [
                                'class' => 'w-full text-sm appearance-none
                                                        bg-transparent border-none text-gray-700 mr-3 py-1 px-2
                                                        leading-tight',
                                'id' => 'email_subject',
                                'placeholder' => 'Enter Subject',
                            ]) }}
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-start pl-4 pr-2 bg-blue-gray-50 border-b py-2 w-full">
                    <div class="flex items-center justify-start w-full">
                        <span class="text-sm leading-5 text-gray-500 w-16">From:</span>
                        <div class="flex items-center justify-start py-1">
                            <p class="text-sm font-medium text-gray-700 px-2">Academic Email</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-between px-4 bg-blue-gray-50 py-1">
                    <div class="flex items-center justify-start w-4/6">
                        <span class="text-sm leading-5 text-gray-500 w-16">Template:</span>
                        <div class="flex items-center justify-start">
                            <p class="text-sm text-gray-700 px-2 isTemplateSelect">No Template Selected</p>
                        </div>
                        <a href="javascript:void(0);"
                            class="cursor-pointer inline-flex space-x-2 items-center justify-end py-2 px-3 bg-white border rounded-lg border-gray-300"
                            id="insertEmailTemplate">
                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M1.66675 2.33335C1.66675 1.96516 1.96522 1.66669 2.33341 1.66669H11.6667C12.0349 1.66669 12.3334 1.96516 12.3334 2.33335V3.66669C12.3334 4.03488 12.0349 4.33335 11.6667 4.33335H2.33341C1.96522 4.33335 1.66675 4.03488 1.66675 3.66669V2.33335Z"
                                    stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path
                                    d="M1.66675 7.66669C1.66675 7.2985 1.96522 7.00002 2.33341 7.00002H6.33341C6.7016 7.00002 7.00008 7.2985 7.00008 7.66669V11.6667C7.00008 12.0349 6.7016 12.3334 6.33341 12.3334H2.33341C1.96522 12.3334 1.66675 12.0349 1.66675 11.6667V7.66669Z"
                                    stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path
                                    d="M9.66675 7.66669C9.66675 7.2985 9.96522 7.00002 10.3334 7.00002H11.6667C12.0349 7.00002 12.3334 7.2985 12.3334 7.66669V11.6667C12.3334 12.0349 12.0349 12.3334 11.6667 12.3334H10.3334C9.96522 12.3334 9.66675 12.0349 9.66675 11.6667V7.66669Z"
                                    stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                            <span class="text-sm font-medium leading-5 text-gray-700">Insert Template</span>
                        </a>
                    </div>
                </div>
                <textarea class="inline-flex overflow-y-auto h-auto" id="comments" name="email_content"></textarea>
            </div>
            <div class="flex flex-col w-full">
                <div class="flex items-center justify-start p-4 bg-gray-50 border-t py-4">
                    <input type="hidden" class="email_template_id" name="email_template_id" />
                    <input type="hidden" class="existing_attachment_id" name="existing_attachment_id" />
                    <input class="" id="email_attachment" name="email_attachment" type="file" multiple
                        style="display: none;">
                    <label for="email_attachment"
                        class="flex space-x-2 items-center justify-center py-2 px-3 bg-white border rounded-lg border-gray-300">
                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M9.11434 3.66667L4.72382 8.05719C4.20312 8.57789 4.20312 9.42211 4.72382 9.94281C5.24452 10.4635 6.08874 10.4635 6.60944 9.94281L10.8856 5.55228C11.927 4.51089 11.927 2.82245 10.8856 1.78105C9.84418 0.73965 8.15574 0.73965 7.11434 1.78105L2.8382 6.17157C1.2761 7.73367 1.2761 10.2663 2.8382 11.8284C4.4003 13.3905 6.93296 13.3905 8.49505 11.8284L12.6666 7.66667"
                                stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <span class='block text-sm font-medium leading-5 text-gray-700'>Attach Files</span>
                    </label>
                    <!-- <span class="ml-2 selected_file"></span> -->

                </div>
                <div class="items-center justify-start bg-gray-50 pl-4 pb-4">
                    <span id="templateFilesContainer" class="flex flex-wrap gap-2 selected_file"></span>
                    <span id="attachedFilesContainer" class="flex flex-wrap gap-2 selected_file"></span>
                </div>
                <div class="flex items-center justify-start p-4 bg-white border-t py-4">
                    <div class="flex space-x-2 items-center justify-center py-1.5 pl-2 pr-2.5">
                        {{ Form::checkbox('offer_comm_log', null, false, [
                            'class' => 'flat-red rounded-sm
                                                border-gray-300',
                            'id' => 'student_comm_log',
                        ]) }}
                        <div for="student_comm_log"
                            class="student_comm_log text-sm font-normal leading-tight text-gray-700 cursor-pointer">Add
                            to Student Communication Log</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-6 border-t bg-gray-50">
            <div class="float-right flex space-x-4 items-center justify-end pr-6">
                <button type="button"
                    class="resetEmail flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">
                    <p class="text-sm font-medium leading-5 text-gray-700">Reset</p>
                </button>
                <button id="sendMail"
                    class="sendmail flex justify-center h-full px-6 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600"
                    id="load1" type="button">
                    <p class="text-sm font-medium leading-5 text-white">Send Email</p>
                </button>
            </div>
        </div>
    </form>
</div>


{{-- Agent Commission Old Template  --}}
<div class="flex flex-row w-full">
    <div class="filter-result flex flex-col z-10 w-full bg-white" id="toggleFilterDiv">
        <div class="flex flex-col space-y-2 items-start justify-center py-4 pl-8 pr-6 shadow-inner w-full">
            <div class="searchdata flex space-x-2 items-center justify-between w-full">
                <div class="flex space-x-2 items-center justify-start h-9">
                    <button type="button" id="filterBtn"
                        class="active flex space-x-2 items-center justify-center w-9 h-full px-2.5 py-2 bg-primary-blue-500 border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                        <svg width="14" height="14" viewBox="0 0 14 14" class="w-3 h-3" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M1 2C1 1.44772 1.44772 1 2 1H12C12.5523 1 13 1.44772 13 2V3.25245C13 3.51767 12.8946 3.77202 12.7071 3.95956L8.62623 8.04044C8.43869 8.22798 8.33333 8.48233 8.33333 8.74755V10.3333L5.66667 13V8.74755C5.66667 8.48233 5.56131 8.22798 5.37377 8.04044L1.29289 3.95956C1.10536 3.77202 1 3.51767 1 3.25245V2Z"
                                stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </button>
                    <p class="text-base font-medium leading-tight text-gray-700 filter_title"></p>
                </div>
                <input type="hidden" name="user_id" value="{{ $user_id }}" id="user_id" />
                <div class="flex space-x-2 items-center justify-end h-9">
                    <div
                        class="flex space-x-2 items-center justify-start w-40 h-full px-3 py-2 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M19 19L13 13M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z"
                                stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <input type="text" id="" data-grid-id="agentCommisonList"
                            class="searchInputField text-sm leading-5 font-normal text-gray-400" placeholder="Search">
                    </div>
                    <button type="button" id="exportData"
                        class="flex space-x-2 items-center justify-center h-full py-2 px-4 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                        <span class="text-sm font-normal leading-tight text-gray-900 k-grid-excel">Export</span>
                    </button>
                    <div>
                        <button type="button" id="manageColumns"
                            class="flex space-x-2 items-center justify-center w-9 h-full px-2.5 py-2.5 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7.00033 9.3335V2.66683M7.00033 9.3335C7.00033 10.0699 6.20439 10.6668 5.22255 10.6668H3.44477C2.46293 10.6668 1.66699 10.0699 1.66699 9.3335V2.66683C1.66699 1.93045 2.46293 1.3335 3.44477 1.3335H5.22255M7.00033 9.3335C7.00033 10.0699 7.79626 10.6668 8.7781 10.6668H10.5559C11.5377 10.6668 12.3337 10.0699 12.3337 9.3335V2.66683C12.3337 1.93045 11.5377 1.3335 10.5559 1.3335H8.7781M5.22255 1.3335C6.20439 1.3335 7.00033 1.93045 7.00033 2.66683M5.22255 1.3335H8.7781M7.00033 2.66683C7.00033 1.93045 7.79626 1.3335 8.7781 1.3335"
                                    stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </button>
                        <div class="manageColumnBox">
                            <div class="relative w-full">
                                <div
                                    class="absolute top-2 right-0 w-auto pt-4 bg-white shadow border rounded-lg border-gray-200 space-y-4">
                                    <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                                        <div class="inline-flex space-x-4 items-center justify-between w-full">
                                            <p class="text-sm font-medium leading-tight text-gray-700 pb-2">Columns
                                            </p>
                                        </div>
                                        <div class="flex flex-col space-y-2 items-start justify-start w-full">
                                            @foreach ($column_arr as $column)
                                                <div
                                                    class="inline-flex items-center justify-start w-full pr-2 rounded">
                                                    <div class="flex space-x-2 items-center justify-start">
                                                        <input type="checkbox"
                                                            class="fc-checkbox cursor-pointer rounded"
                                                            id="fc_{{ $column['id'] }}" value="{{ $column['id'] }}"
                                                            {{ $column['default'] }} />
                                                        <label for="fc_{{ $column['id'] }}"
                                                            class="text-sm leading-none text-gray-700">{{ $column['title'] }}</label>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <button
                                            class="text-sm font-medium leading-tight pb-2 pt-1 text-blue-500 reset column_filter">Reset
                                            Columns</button>
                                    </div>
                                    <div
                                        class="inline-flex space-x-4 items-center justify-end w-full py-4 pl-2 pr-2 border border-gray-200">
                                        <div class="flex space-x-4 items-center justify-end">
                                            <button type="button"
                                                class="flex items-center justify-center w-24 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300 text-xs font-medium leading-none text-gray-700 clear column_filter">Cancel</button>
                                            <button type="button"
                                                class="flex items-center justify-center w-28 h-full px-3 py-2 bg-primary-blue-500 shadow rounded-lg text-xs font-medium leading-tight text-white save column_filter">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-full bg-gray-100">
            <div id="agentCommisonList"></div>
        </div>
    </div>
</div>
