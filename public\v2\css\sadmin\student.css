/* Start Loader for Grid */
.k-loading-mask .k-loading-image {
    background-image: none !important;
}
div.k-loading-image {
    display: none;
}
span.k-loading-text {
    text-indent: 0;
    top: 50%;
    left: calc(50% - 1rem);
    z-index: 9999;
}
.k-loading-mask {
    z-index: 99999;
}
.k-widget.k-window.k-display-inline-flex.blur-modal {
    z-index: 9999;
}
.k-loading-color {
    z-index: 99;
}
.k-grid-content {
    /*min-height: 460px;*/
    /*overflow: hidden;*/
}
/* End Loader for Grid */

/* Start manage column bar CSS */
.manageColumnBox {
    z-index: 1;
    position: absolute;
    /* display: none; */
}
.active .manageColumnBox {
    z-index: 9999;
    position: absolute;
    /* display: block; */
}
.manageColumnBox .w-full .manage-column-box__dropdown {
    margin-right: -35px;
}

.manage-column-box__dropdown {
    opacity: 0;
    visibility: hidden;
    animation: popupAnimation 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top right;
    overflow: hidden;
}

.manageColumnBox.active .manage-column-box__dropdown {
    display: block;
    opacity: 1;
    visibility: visible;
}
/* End manage column bar CSS */

/* Start first column hide */
/* th:nth-child(1) {width: 0px;}
td:nth-child(1) {width: 0px;} */
.k-grid .k-hierarchy-col {
    width: 0px;
}
/* End first column hide */

/* Start arrow-down OR selected-progress can use for triangle*/
.selected-progress {
    /*position: absolute;*/
    width: 5px;
    height: 5px;
    margin-top: -8px !important;
    margin-right: -8px !important;
    background: var(--color-primary-blue-500);
    border-radius: 0.3px;
    transform: rotate(-180deg);
}
.arrow-down {
    width: 0;
    height: 0;
    position: relative;
    margin-left: -2px !important;
    margin-top: -32px !important;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #3b82f6;
}
.k-grid tr td .arrow-down.represent {
    margin-top: -6px !important;
}
/* End arrow-down OR selected-progress can use for triangle*/

/* Start bottom popup open */
.bottomaction {
    position: sticky;
    bottom: 0px;
    z-index: 1000;
    /* overflow-y: auto; */
    width: 100%;
    left: 0;
    box-shadow:
        0px -4px 6px -1px rgba(0, 0, 0, 0.1),
        0px -2px 4px -1px rgba(0, 0, 0, 0.06);
}
/* End bottom popup open */

/* Start custom tooltip */
.k-widget.k-tooltip.k-popup.k-group.k-reset {
    padding: 0px;
    background-color: white;
}
/* End custom tooltip */

/* Start action list */
.action-menu {
    /*position: absolute;*/
}
/* End action list */

/* Start sidebar filter hide-show*/
.widthzero {
    width: 0px;
}
.heightzero {
    overflow: hidden;
    height: 0px;
}

/* End Sidebar filter hide-show */

/* Start Sidebar filter */
.k-panelbar {
    border-color: transparent;
    color: inherit;
    background-color: transparent;
}
.k-panelbar-group > .k-panelbar-item > .k-link label {
    /* color: #374151 !important; */
    /* height: 20px !important; */
    /* font-size: 13px !important; */
    cursor: pointer;
}
.k-panelbar-group {
    max-height: 150px !important;
    overflow-y: auto !important;
    padding-left: 10px;
}
.k-panelbar-group.custom-panel-size {
    max-height: 230px !important;
}
.custom-panel-size-ul {
    max-height: 230px !important;
}
.k-panelbar-group.custom-intake-size {
    max-height: 380px !important;
}
.k-panelbar .k-panelbar-group > .k-item > .k-link:hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link:hover {
    cursor: pointer;
}

.student-details .grid {
    min-height: 350px;
}
.k-panelbar > .k-item > .k-link .k-icon {
    color: red;
}
.k-panelbar > .k-item > .k-link .k-icon,
.k-panelbar > .k-item > .k-link .k-panelbar-item-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-panelbar-item-icon {
    color: var(--color-primary-blue-500);
}
.k-panelbar > .k-item > .k-link.k-state-selected,
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected {
    /* background-color: #fff !important; */
    color: var(--color-primary-blue-500);
}
k-panelbar > .k-item > .k-link.k-state-focused,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focused {
    box-shadow: none;
}
/* End Sidebar filter */

/* Start tooltip popup manage */
.custom-option option {
    padding: 15px 0px !important;
}
.k-link.k-state-hover button span {
    color: #fff;
}
/* End tooltip popup manage */

/* Start switch as toggle */
input:checked ~ .dot {
    transform: translateX(100%);
}
input:checked ~ .outer-dot {
    background: var(--color-primary-blue-500) !important;
}
/* End switch as toggle */

#sendMailStudentModal,
#sendSmsStudentModal,
#inviteStudentToGalaxyModal {
    padding: 0px;
}

.k-widget * {
    box-sizing: border-box !important;
}

/* table check box */
.k-checkbox:checked {
    background-color: var(--color-primary-blue-500) !important;
    border-color: var(--color-primary-blue-500) !important;
}
.k-checkbox.k-checked,
.k-checkbox:checked {
    background-image: none !important;
}
.k-checkbox:checked:focus {
    box-shadow: none;
}
[type='checkbox']:checked:hover,
[type='checkbox']:checked:focus,
[type='radio']:checked:hover,
[type='radio']:checked:focus {
    background-color: var(--color-primary-blue-500) !important;
    box-shadow: none;
    outline: 2px solid var(--color-primary-blue-500);
    outline-offset: 2px;
}

.k-checkbox:checked:focus {
    outline: 2px solid var(--color-primary-blue-500);
    outline-offset: 2px;
}

/* [type='checkbox']:checked{
    background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
    background:var(--color-primary-blue-500);
} */

/* filter dropdown*/

.k-filter-menu .k-dropdown,
.k-action-buttons .k-button {
    border: 1px solid #b6b6b6;
    /* background-color: #e5e7eb; */
}
.k-filter-menu .k-textbox,
.k-filter-menu .k-picker-wrap {
    border: 1px solid #b6b6b6;
}

.k-popup .k-list .k-state-hover {
    background-color: #e5e7eb;
    cursor: pointer;
}

.k-fieldselector .k-list .k-item,
.k-list-optionlabel.k-state-focused,
.k-list-optionlabel.k-state-selected,
.k-listbox .k-item,
.k-popup .k-list .k-state-focused,
.k-popup .k-list .k-state-selected,
.k-action-buttons .k-primary {
    /* background-color: var(--color-primary-blue-500); */
    cursor: pointer;
    /* color: white; */
}

/* radio button color*/
[type='radio'] {
    color: var(--color-primary-blue-500);
}

/*  */
.k-panelbar > .k-item > .k-link,
.k-panelbar > .k-panelbar-header > .k-link:hover {
    background-color: transparent !important;
    cursor: pointer;
}

#panelbar li.k-level-0 {
    position: relative;
    border: 0px !important;
}
#panelbar li.k-level-0::after {
    content: '';
    position: absolute;
    width: 90%;
    height: 1px;
    background-color: #e5e7eb;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0 auto;
}
#panelbar li.k-level-0:last-child:after {
    display: none;
}

#appliedFilterList .k-i-close::before,
.remove_student_intake.k-i-close::before {
    color: #9ca3af !important;
}

/* icon for kendo */
.copy-icon {
    font-size: 17px;
    font-weight: bold;
    color: #9ca3af;
}

/* on hover row effect */
.k-grid tr:hover td {
    background-color: rgba(249, 250, 251, 1);
}

.k-grid tr:hover td .action-only {
    display: block;
}
input[type='text']:focus {
    box-shadow: none;
}

/* tooltip shadow */
.k-tooltip-content {
    /*box-sizing: border-box;*/
    /* box-shadow: 0px 20px 25px 5px rgba(0, 0, 0, 0.1), 0px 10px 10px -5px rgba(0, 0, 0, 0.04); */
}

.k-progressbar .k-state-selected {
    border-color: var(--color-primary-blue-500) !important;
    color: #fff;
    background-color: var(--color-primary-blue-500) !important;
}

.k-stepper .k-step-done .k-step-indicator {
    border-color: var(--color-primary-blue-500) !important;
    color: #fff;
    background-color: var(--color-primary-blue-500) !important;
}
.k-stepper .k-step-current .k-step-indicator {
    border-color: var(--color-primary-blue-500);
    color: #fff;
    background-color: var(--color-primary-blue-500);
}

.k-step-current:hover .k-step-indicator {
    background-color: var(--color-primary-blue-500) !important;
    background: var(--color-primary-blue-500) !important;
}

.k-stepper .k-step-done.k-step-hover .k-step-indicator,
.k-stepper .k-step-done:hover .k-step-indicator {
    background-color: var(--color-primary-blue-500);
}

/* ===== EMPLOYEEMENT HISTROY STEPPER ===== */

.emp-history .k-step-list.k-step-list-horizontal {
    display: flex !important;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.emp-history .k-step-list.k-step-list-horizontal li {
    max-width: 25% !important;
    padding: 10px 0px;
    flex-basis: 25% !important;
}

.emp-history .k-step-text {
    font-size: 12px !important;
}
.emp-history .k-progressbar {
    display: none !important;
}

/* =====  SWICTH   ====== */

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 25px;
    overflow: hidden;
}

.switch input {
    display: none;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ca2222;
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

.slider:before {
    position: absolute;
    content: '';
    height: 15px;
    width: 15px;
    left: 4px;
    bottom: 5px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

input:checked + .slider {
    background-color: #2ab934;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(39px);
    -ms-transform: translateX(39px);
    transform: translateX(39px);
}

.on {
    display: none;
}

.off {
    color: white;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    font-size: 10px;
    font-family: Verdana, sans-serif;
}

.on {
    color: white;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 25px;
    font-size: 10px;
    font-family: Verdana, sans-serif;
}

input:checked + .slider .on {
    display: block;
}

input:checked + .slider .off {
    display: none;
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

#studHistoryData .k-stepper {
    width: 100%;
}

.titlebar-sms-modal .k-window-action {
    opacity: 1;
}

.titlebar-sms-modal .k-window-action .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}

.k-notification {
    border-radius: 8px;
    padding: 0px 0px;
    border-width: 0px;
}

/* multiselect */
.k-multiselect {
    border-width: 1px !important;
}
.k-multiselect-wrap {
    flex-direction: column !important;
}

.k-multiselect-wrap .k-input {
    width: 100% !important;
}
.k-multiselect-wrap .k-input:focus-within {
    box-shadow: none !important;
}

.action-div,
.student-filter .k-checkbox-item label {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    color: #374151;
    size: 14px;
    line-height: 20px;
    font-weight: 400;
}

.action-div {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

/* radio  select filter */
.k-filter-menu-container .k-checkbox-label {
    padding: 5px 0px !important;
    width: 100%;
}

.k-filter-menu-container .k-checkbox-label span {
    /* overflow: hidden;
  text-overflow: ellipsis; */
    white-space: normal;
}

.k-filter-menu-container .k-checkbox-label .k-checkbox {
    margin-left: 4px !important;
    margin-right: 0px !important;
}

.k-filter-menu-container .k-checkbox-label:hover {
    background-color: #e5e7eb !important;
    border-radius: 4px;
}
.k-filter-menu.k-popup .k-multicheck-wrap {
    padding: 0px !important;
}

.widthfitcontent {
    max-width: fit-content;
}

/* // text area border */

textarea {
    border: none !important;
}

textarea:focus {
    border: none !important;
}

/* // ck editor border hide */
.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
    border-color: var(--ck-color-base-border);
    border-bottom: none;
    border-right: none;
    border-left: none;
}

.ck.ck-editor__editable:not(.ck-editor__nested-editable).ck-focused {
    border-bottom: none !important;
    border-right: none !important;
    border-left: none !important;
    box-shadow: none !important;
}

.ck .ck-editor__main {
    overflow-y: auto;
    max-height: 300px;
}

/* // currennt course tooltip conetent circle font // */

.dayfont {
    font-size: 11px !important;
}

.student-name-hover:hover {
    text-decoration: underline !important;
    color: var(--color-primary-blue-500);
}

.k-tooltip .k-callout {
    color: rgb(229 231 235);
}
/* .k-fieldselector .k-list .k-item, .k-list-optionlabel.k-state-focused, .k-list-optionlabel.k-state-selected, .k-listbox .k-item, .k-popup .k-list .k-state-focused, .k-popup .k-list .k-state-hover, .k-popup .k-list .k-state-selected, .k-action-buttons .k-primary{
    background-color: #fff;
    color: black !important;
    font-weight: bold !important;

}
.k-dropdown{
    width: 90%;
} */

.k-filter-row th,
.k-grid-header th.k-header {
    border-width: 1px 0px 1px 1px !important;
}

.k-grid .k-grid-header .k-header {
    height: 40px !important;
}

.k-grid .k-grid-header .k-header a {
    padding-bottom: 4px !important;
}

/* Tagify css */
.tagify__tag {
    border: 1px solid #ddd !important;
    border-radius: 30px !important;
    height: 24px !important;
}
.tagify__tag__removeBtn {
    font: 20px Arial !important;
    color: #9ca3af !important;
}
.tag-div {
    border-radius: 30px !important;
}
.tagify__input {
    /* / margin-top: 12px !important; /
    / font-size: 12px !important; / */
}

.tagify {
    --tag-bg: none;
    --tag-hover: #bae7ff !important;
    --tags-disabled-bg: #fff !important;
    --tags-border-color: #ddd !important;
    --tags-hover-border-color: #ddd !important;
    --tag-remove-btn-bg--hover: none !important;
    --tag-remove-btn-color: black;
    --tag-remove-bg: none !important;
}

.student-list {
    z-index: 100009 !important;
}

.tagify__dropdown__wrapper {
    /* / border: none !important; / */
    border: 1px solid #ffffff;
    max-height: 400px !important;
    overflow-y: hidden !important;
    border-radius: 5px;
    border-color: rgba(229, 231, 235);
}

.tagify__dropdown__item--active {
    background: #e5e7eb;
    color: #fff;
}

.studentListBoxBorder .tagify {
    border: none !important;
}

.cusInput:hover,
.cusInput:focus {
    border: 1px solid var(--color-primary-blue-500);
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

.k-panelbar .k-item .k-content {
    background-color: transparent;
    padding: 4px 16px;
}

.student-filter.k-checkbox-list .k-checkbox-item {
    margin: 2px 0px 0px 4px;
}

#studentsFilterPanelbar .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 3px 2px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#studentsFilterPanelbar .k-dropdown {
    width: 100%;
}

.k-list-filter .k-textbox {
    width: 100% !important;
}

.k-list-filter > .k-icon {
    right: 15px !important;
}

#studentsFilterPanelbar .k-i-plus {
    color: var(--color-primary-blue-500);
}

.k-i-arrow-60-down::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
    /*margin-top: 3px;*/
}

/* #studentList.k-grid tr.k-state-selected > td:nth-child(2) {
    border-width: 0px 0 1px 4px !important;
    border-left-color: var(--color-primary-blue-500);
} */

.course_list > span {
    width: 100% !important;
}
/*#emailTemplateAddForm .course_list .k-i-arrow-60-down { margin-top: -4px !important;}*/
/*#issueEmailForm .course_list .k-i-arrow-60-down { margin-top: -8px !important;}*/
.actionButtonHide {
    display: none;
}

.flex-wrapper {
    display: flex;
    flex-flow: row nowrap;
}

.single-chart {
    width: 33%;
    justify-content: space-around;
}

.circular-chart {
    display: block;
    margin: -7px auto;
    max-width: 100%;
    max-height: 100px;
}

.circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}

.circle {
    fill: none;
    stroke-width: 3.8;
    stroke-linecap: round;
    animation: progress 1s ease-out forwards;
}

@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}

.circular-chart.orange .circle {
    stroke: #ff9f00;
}

.circular-chart.green .circle {
    stroke: #4cc790;
}

.circular-chart.blue .circle {
    stroke: #3c9ee5;
}

.percentage {
    fill: #666;
    font-family: sans-serif;
    font-size: 0.5em;
    text-anchor: middle;
}
.student-filter {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
}

/*.k-widget.k-tooltip.k-popup.k-group.k-reset {*/
/*    position: absolute;*/
/*    height: 300px;*/
/*    left: 100px;*/
/*    display: none;*/
/*    opacity: 0;*/
/*}*/

.filter-left-sidebar {
    height: calc(100vh - 4rem);
    overflow-y: auto;
}

.splitter .k-splitbar-horizontal.k-splitbar-draggable-horizontal .k-i-arrow-60-left,
.splitter .k-splitbar-horizontal.k-splitbar-draggable-horizontal .k-i-arrow-60-right {
    display: none;
}

.k-grid-pager {
    bottom: 0.5rem;
}

.student-list-loading {
    overflow: hidden;
}
.tagify {
    border: none !important;
}

#studentsFilterPanelbar.k-panelbar > .k-item.k-state-active > .k-link,
#studentsFilterPanelbar.k-panelbar > .k-panelbar-header.k-state-active > .k-link {
    padding-bottom: 0.25rem;
}

#studentsFilterPanelbar li.k-panelbar-header:nth-child(3) > .k-link > .k-panelbar-item-text {
    width: 100%;
}
#statusForSendEmailModal.k-window-content {
    padding-bottom: 16px !important;
}

/* Tagify css for Email not send student list. */
#notReceivedEmailStudentListModal .tagify__tag,
#OtherEmailStudentListModal .tagify__tag {
    border: 1px solid #ddd !important;
    border-bottom: none !important;
    border-radius: 0px !important;
    height: 38px !important;
    margin: 0px;
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    -ms-border-radius: 0px !important;
    -o-border-radius: 0px !important;
}

#notReceivedEmailStudentListModal .tagify__tag:hover,
#OtherEmailStudentListModal .tagify__tag:hover {
    background: rgba(249, 250, 251, 1);
}

#notReceivedEmailStudentListModal .tagify__tag:first-child,
#OtherEmailStudentListModal .tagify__tag:first-child {
    border-top-left-radius: 8px !important; /* Adjust this value as needed */
    border-top-right-radius: 8px !important; /* Adjust this value as needed */
}

#notReceivedEmailStudentListModal .tagify__tag:last-of-type,
#OtherEmailStudentListModal .tagify__tag:last-of-type {
    border-bottom-left-radius: 8px !important; /* Adjust this value as needed */
    border-bottom-right-radius: 8px !important; /* Adjust this value as needed */
    border-bottom: 1px solid #ddd !important;
}
#notReceivedEmailStudentListModal .tagify__tag__removeBtn,
#OtherEmailStudentListModal .tagify__tag__removeBtn {
    font: 20px Arial !important;
    color: #9ca3af !important;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease-out;
    -webkit-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    -ms-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
}

#notReceivedEmailStudentListModal .tagify__tag:hover .tagify__tag__removeBtn,
#OtherEmailStudentListModal .tagify__tag:hover .tagify__tag__removeBtn {
    visibility: visible;
    opacity: 1;
}

#notReceivedEmailStudentListModal .tag-div,
#OtherEmailStudentListModal .tag-div {
    border-radius: 0px !important;
}

#notReceivedEmailStudentListModal .tagify,
#OtherEmailStudentListModal .tagify {
    --tag-bg: none;
    --tag-hover: none !important;
    --tags-disabled-bg: #fff !important;
    --tags-border-color: #ddd !important;
    --tags-hover-border-color: #ddd !important;
    --tag-remove-btn-bg--hover: none !important;
    --tag-remove-btn-color: black;
    --tag-remove-bg: none !important;
}
#notReceivedEmailStudentListModal .tagify__dropdown__wrapper,
#OtherEmailStudentListModal .tagify__dropdown__wrapper {
    /* / border: none !important; / */
    border: 1px solid #ffffff;
    max-height: 400px !important;
    overflow-y: hidden !important;
    border-radius: 5px;
    border-color: rgba(229, 231, 235);
}

#notReceivedEmailStudentListModal .tagify__dropdown__item--active,
#OtherEmailStudentListModal .tagify__dropdown__item--active {
    background: #e5e7eb;
    color: #fff;
}

#studentList .k-pager-numbers-wrap select.k-dropdown {
    display: none !important;
}

#studentList .k-pager-sm .k-pager-numbers {
    display: flex !important;
}

.checkCurrentCampus {
    display: none;
}
.action-div:hover .checkCurrentCampus {
    display: inline;
}
.studentListBoxBorder .tagify .tagify__tag--more {
    order: 2;
}

.studentListBoxBorder .tagify span.tagify__input {
    order: 3;
}
.student_name_remove_email_list {
    max-height: 450px !important;
}
input.newParameter.error {
    border-color: #ef4444 !important;
}
