<template>
    <Dialog
        v-if="visible"
        :width="1080"
        :height="720"
        @close="handleClose"
        :title="previewTitle"
        dialog-class="tw-content-p-0 tw-dialog"
    >
        <div class="modal-content h-full p-6">
            <template v-if="certificateEmbedeUrl">
                <div class="grid grid-cols-5 gap-6">
                    <div :class="type === 'image' ? 'col-span-8' : 'col-span-8'">
                        <iframe
                            :src="certificateEmbedeUrl"
                            class="w-full"
                            type="text/html"
                            width="1024px"
                            height="600px"
                            @load="handleIframeLoad"
                        />
                    </div>
                </div>
            </template>
            <template v-else>
                <NoData
                    title="No preview available."
                    subtitle="Student course data is missing or invalid."
                    :pt="{ root: 'h-full' }"
                />
            </template>
        </div>
        <template v-if="type === 'html'">
            <DialogActionsBar>
                <div class="item-center flex w-full justify-end gap-4">
                    <Button variant="secondary" @click="handleClose">Close</Button>
                    <!-- <Button @click="handleSave">Save</Button> -->
                </div>
            </DialogActionsBar>
        </template>
    </Dialog>
</template>
<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import NoData from '@spa/components/NoData/NoData.vue';

let hasHandledLoad = false;

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    visible: {
        type: Boolean,
        required: true,
    },
    formData: {
        type: Object,
    },
    type: {
        type: String,
        default: 'html',
    },
    certificateEmbedeUrl: {
        type: String,
        default: '',
    },
    previewItem: {
        type: Object,
        default: () => ({}),
    },
});
const emit = defineEmits(['close', 'save', 'pick', 'stopLoading']);

// Determine preview title based on URL type parameter
const previewTitle = computed(() => {
    const url = new URL(window.location.href);
    const type = url.searchParams.get('type');
    return type === 'student-id' ? 'Student Card Preview' : 'Certificate Preview';
});

const handleSave = () => {
    emit('save');
};

const handleClose = () => {
    emit('close');
};

const handleIframeLoad = () => {
    emit('stopLoading');
};

const handlePick = () => {
    window.location.href = `/spa/certificate-builder?certificateId=${props.previewItem.secureId}`;
};
</script>
<style lang=""></style>
