import { ref, onMounted, watch, computed, reactive } from 'vue';
import useConfirm from '@spa/services/useConfirm';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper';
import { router } from '@inertiajs/vue3';
import { useCertificateStore } from '@spa/stores/modules/certificate.store';
import { formatErrorMessage } from '../../helpers';

export default function useCertificateResource(url, config = {}) {
    const $confirm = useConfirm();
    const $loader = useLoaderStore();
    const defaultUrl = url;
    const certificateStore = useCertificateStore();

    const state = reactive({
        error: null,
        uploadProgress: 0,
        filters: {},
        previewId: null,
        previewUrl: false,
    });

    const fetch = (params = {}) => {
        params = { ...state.filters, ...params };
        if (state.filters.search === '') {
            state.filters.search = null;
        }

        params = Object.fromEntries(
            Object.entries(params).filter(([key, value]) => value !== null)
        );

        router.get(`${defaultUrl}/templates`, params, {
            only: [...only, 'query', 'params'],
            preserveState: true,
            onBefore: (visit) => {},
            onError: (error) => {},
            onFinish: (visit) => {},
        });
    };

    const executeAction = async (action, data = {}, options = {}) => {
        const {
            url = defaultUrl,
            method = 'post',
            reFetch = false,
            onSuccess = () => {},
            onError = () => {},
            onBefore = () => {},
            onFinish = () => {},
            showToast = false,
        } = options;

        try {
            onBefore();
            const resp = await $http[method](url, data);

            if (resp.success) {
                if (onSuccess) onSuccess(resp);
                if (reFetch) fetch();
                if (showToast) {
                    globalHelper.methods.showPopupSuccess(resp.message, 'Success');
                }
            } else {
                if (onError) onError(resp);
                if (showToast) {
                    globalHelper.methods.showPopupError(resp.message, 'Error');
                }
            }
        } catch (error) {
            console.log('error', error);
            onError(error);
            if (showToast) {
                const formattedError = formatErrorMessage(error.data.errors);
                globalHelper.methods.showPopupError(formattedError, 'Error');
            }
            console.error(`[useCertificateResource] ${action} error:`, error);
        } finally {
            onFinish();
        }
    };

    const saveTemplate = async (data = {}, options = {}, type = 'certificate') => {
        $loader.startContextLoading(options.isPreview ? 'button1' : 'button');
        executeAction('saveTemplate', data, {
            ...options,
            url: options.url || `${defaultUrl}/save-template`,
            onSuccess: (resp) => {
                certificateStore.setTemplates(resp.template);
                if (options.redirection) {
                    router.visit(route('spa.certificate-builder'), {
                        preserveState: true,
                        preserveScroll: true,
                        only: ['certificate'],
                        data: {
                            certificateId: resp.encrypt_id,
                            type: type,
                        },
                    });
                } else {
                    router.visit(route('spa.certificate.templates'));
                }
                if (options.handleAdd) {
                    options.handleAdd(resp);
                }
                $loader.stopContextLoading('button');
                $loader.stopContextLoading('button1');
            },
            onFinish: () => {
                $loader.stopContextLoading('button');
                $loader.stopContextLoading('button1');
            },
            showToast: true,
        });
    };

    const updateTemplate = async (data = {}, options = {}) => {
        if (!options.autoSave) {
            $loader.startContextLoading(options.isPreview ? 'button1' : 'button');
        }
        $loader.startContextLoading('auto-save');
        executeAction('updateTemplate', data, {
            ...options,
            method: 'put',
            url: options.url || `${defaultUrl}/update-template/${data.id}`,
            onSuccess: (resp) => {
                $loader.stopContextLoading('auto-save');
                // globalHelper.methods.showPopupSuccess("Auto Saved", "info");
                if (options.autoSave) {
                    return;
                } else {
                    router.visit(route('spa.certificate.templates'));
                }
            },
            onFinish: () => {
                $loader.stopContextLoading(options.isPreview ? 'button1' : 'button');
                $loader.stopContextLoading('auto-save');
            },
            showToast: false,
        });
    };

    const deleteTemplate = async (data = {}, options = {}) => {
        $confirm.require({
            message: 'Are you sure you want to delete this template?',
            header: 'Delete Template?',
            icon: 'pi pi-exclamation-triangle',
            variant: 'danger',
            acceptLabel: 'Delete',
            rejectLabel: 'Cancel',
            width: 500,
            accept: async () => {
                executeAction('deleteTemplate', data, {
                    ...options,
                    method: 'delete',
                    url: options.url || `${defaultUrl}/delete-template/${data.id}`,
                    onSuccess: (resp) => {
                        router.reload();
                    },
                    showToast: true,
                });
            },
            reject: () => {
                return false;
            },
            onHide: () => {
                return false;
            },
        });
    };

    const getTemplates = async (options = {}) => {
        executeAction(
            'getTemplates',
            {},
            {
                ...options,
                url: options.url || `${defaultUrl}/templates`,
                onSuccess: (resp) => {
                    return resp.data;
                },
            }
        );
    };
    const getPreviewUrl = async (data = {}, options = {}) => {
        $loader.startContextLoading('generate');
        executeAction('getPreviewUrl', data, {
            ...options,
            url: options.url || `${defaultUrl}/get-preview-url`,
            onSuccess: (resp) => {
                $loader.stopContextLoading('generate');
                state.previewUrl = resp.embedeUrl;
                if (options.handlePreview) {
                    options.handlePreview(resp);
                }
            },
            onFinish: () => {
                $loader.stopContextLoading('generate');
            },
            showToast: false,
        });
    };

    return {
        state,
        saveTemplate,
        updateTemplate,
        deleteTemplate,
        getTemplates,
        getPreviewUrl,
        executeAction,
    };
}
