<?php

namespace App\Http\Controllers\Spa;

use App\Http\Controllers\Controller;
use App\Model\v2\CertificateIdFormate;
use App\Model\v2\CertificateTemplate;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Traits\CertificateGenerationTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CertificateGenerateController extends Controller
{
    use CertificateGenerationTrait;

    public function generteCertificate(Request $request)
    {
        $d = $request->all();

        $dataValue = json_decode($d['data'], true)[0];

        $studCourseId = $dataValue['studCourseId'];
        $isDownload = isset($dataValue['isDownload']) && $dataValue['isDownload'];

        $collegeId = Auth::user()->college_id;
        $objStudentCourse = new StudentCourses;
        $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

        $dataValue['courseId'] = $studentCourseInfo[0]['courseId'];
        $studentId = $studentCourseInfo[0]['studentId'];
        $courseId = $studentCourseInfo[0]['courseId'];
        $certificateId = $dataValue['certificateId'];

        $templates = CertificateTemplate::find($certificateId);
        $certificateFormat = CertificateIdFormate::find($templates->certificate_number_formate_id);

        $uuid = null;
        if ($certificateFormat !== null) {
            $newNumber = $certificateFormat->last_auto_increment_number + 1;
            $uuid = $certificateFormat->prefix.$newNumber.$certificateFormat->suffix;
        }

        $student = $this->getStudentData($studentId, $courseId);
        $otherData = $this->getGradeData($collegeId);
        $enrolments = $this->getEnrolments($studentId, $courseId);

        $returnData = $this->handleCertificateGeneration([
            'student' => $student,
            'templates' => $templates,
            'dataValue' => $dataValue,
            'collegeId' => $collegeId,
            'studentId' => $studentId,
            'uuid' => $uuid,
            'otherData' => $otherData,
            'enrolments' => $enrolments,
            'isDownload' => $isDownload,
        ]);
        if ($isDownload) {
            return redirect()->away($returnData['pdf']);
        }

        return $returnData['pdf'];
    }

    public function generateBulkCertificates(Request $request)
    {
        $data = $request->input();

        $studentsData = json_decode($data['hidData'], true);
        $certificateId = $studentsData['certificateId'];
        $isDownload = isset($studentsData['isDownload']) && $studentsData['isDownload'];

        $collegeId = Auth::user()->college_id;
        $templates = CertificateTemplate::find($certificateId);
        $certificateFormat = CertificateIdFormate::find($templates->certificate_number_formate_id);

        $results = [];
        foreach ($studentsData['studCourseId'] as $studentData) {
            $studCourseId = $studentData;
            $objStudentCourse = new StudentCourses;
            $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

            if ($studentCourseInfo->isEmpty()) {
                continue;
            }

            $studentCourseInfo = $studentCourseInfo[0];
            $studentId = $studentCourseInfo['studentId'];
            $courseId = $studentCourseInfo['courseId'];

            $dataValue = [
                'studCourseId' => $studCourseId,
                'studentId' => $studentCourseInfo['studentId'],
                'courseId' => $studentCourseInfo['courseId'],
                'subject_enrollment_id' => '',
                'certificateId' => $studentsData['certificateId'],
            ];

            if ($certificateFormat !== null) {
                $newNumber = $certificateFormat->last_auto_increment_number + 1;
                $uuid = $certificateFormat->prefix.$newNumber.$certificateFormat->suffix;
                $certificateFormat->last_auto_increment_number = $newNumber;
                $certificateFormat->save();
            }

            $student = $this->getStudentData($studentId, $courseId);
            $otherData = $this->getGradeData($collegeId);
            $enrolments = $this->getEnrolments($studentId, $courseId);
            $result = $this->handleCertificateGeneration([
                'student' => $student,
                'templates' => $templates,
                'dataValue' => $dataValue,
                'collegeId' => $collegeId,
                'studentId' => $studentId,
                'uuid' => $uuid,
                'otherData' => $otherData,
                'enrolments' => $enrolments,
                'isDownload' => $isDownload,
            ]);

            $results[] = $result;
        }

        if ($isDownload) {
            return $this->handleBulkDownload($results);
        }

        return response()->json(['success' => true, 'results' => $results]);
    }

    public function generateStudentCard(Request $request)
    {
        $req = $request->all();
        $dataValue = json_decode($req['data'], true);

        // Check if this is bulk generation
        if (isset($dataValue['isBulk']) && $dataValue['isBulk']) {
            return $this->generateBulkStudentCards($dataValue, $request);
        }

        $dataValue = $dataValue[0]; // Get first element for single generation
        $studentId = $dataValue['studentId'];
        $certificateId = $dataValue['certificateId'];
        $isDownload = isset($dataValue['isDownload']) && $dataValue['isDownload'];

        $templates = CertificateTemplate::find($certificateId);
        $student = Student::with(['college'])->where('id', $studentId)->first();

        $returnData = $this->handleStudentCardGeneration([
            'student' => $student,
            'templates' => $templates,
            'dataValue' => $dataValue,
            'collegeId' => \auth()->user()->college_id,
            'studentId' => $studentId,
            'isDownload' => $isDownload,
        ]);
        if ($isDownload) {
            return redirect()->away($returnData['pdf']);
        }

        return $returnData['pdf'];
    }

    private function generateBulkStudentCards($dataValue, $request)
    {
        $certificateId = $dataValue['certificateId'];
        $studentIds = $dataValue['studentIds'];
        $isDownload = $request->has('download') && $request->get('download') == '1';

        $template = CertificateTemplate::find($certificateId);
        if (! $template) {
            return response('Template not found', 404);
        }

        $students = Student::with(['college'])->whereIn('id', $studentIds)->get();
        if ($students->isEmpty()) {
            return response('No students found', 404);
        }

        $fileName = 'bulk_student_cards_'.time();
        $returnData = $this->handleBulkStudentCardGeneration([
            'students' => $students,
            'template' => $template,
            'fileName' => $fileName,
            'isDownload' => $isDownload,
        ]);

        if ($isDownload) {
            return redirect()->away($returnData);
        }

        return $returnData;
    }
}
