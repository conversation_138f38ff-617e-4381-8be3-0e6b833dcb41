<?php

namespace App\Http\Controllers\Spa;

use App\Http\Controllers\Controller;
use App\Model\v2\CertificateIdFormate;
use App\Model\v2\CertificateTemplate;
use App\Model\v2\StudentCourses;
use App\Traits\CertificateGenerationTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CertificateGenerateController extends Controller
{
    use CertificateGenerationTrait;

    public function generteCertificate(Request $request)
    {
        $d = $request->all();

        $dataValue = json_decode($d['data'], true)[0];

        $studCourseId = $dataValue['studCourseId'];
        $isDownload = isset($dataValue['isDownload']) && $dataValue['isDownload'];

        $collegeId = Auth::user()->college_id;
        $objStudentCourse = new StudentCourses;
        $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

        $dataValue['courseId'] = $studentCourseInfo[0]['courseId'];
        $studentId = $studentCourseInfo[0]['studentId'];
        $courseId = $studentCourseInfo[0]['courseId'];
        $certificateId = $dataValue['certificateId'];

        $templates = CertificateTemplate::find($certificateId);
        $certificateFormat = CertificateIdFormate::find($templates->certificate_number_formate_id);

        $uuid = null;
        if ($certificateFormat !== null) {
            $newNumber = $certificateFormat->last_auto_increment_number + 1;
            $uuid = $certificateFormat->prefix.$newNumber.$certificateFormat->suffix;
        }

        $student = $this->getStudentData($studentId, $courseId);
        $otherData = $this->getGradeData($collegeId);
        $enrolments = $this->getEnrolments($studentId, $courseId);

        $returnData = $this->handleCertificateGeneration([
            'student' => $student,
            'templates' => $templates,
            'dataValue' => $dataValue,
            'collegeId' => $collegeId,
            'studentId' => $studentId,
            'uuid' => $uuid,
            'otherData' => $otherData,
            'enrolments' => $enrolments,
            'isDownload' => $isDownload,
        ]);
        if ($isDownload) {
            return redirect()->away($returnData['pdf']);
        }

        return $returnData['pdf'];
    }

    public function generateBulkCertificates(Request $request)
    {
        $data = $request->input();

        $studentsData = json_decode($data['hidData'], true);
        $certificateId = $studentsData['certificateId'];
        $isDownload = isset($studentsData['isDownload']) && $studentsData['isDownload'];

        $collegeId = Auth::user()->college_id;
        $templates = CertificateTemplate::find($certificateId);
        $certificateFormat = CertificateIdFormate::find($templates->certificate_number_formate_id);

        $results = [];
        foreach ($studentsData['studCourseId'] as $studentData) {
            $studCourseId = $studentData;
            $objStudentCourse = new StudentCourses;
            $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

            if ($studentCourseInfo->isEmpty()) {
                continue;
            }

            $studentCourseInfo = $studentCourseInfo[0];
            $studentId = $studentCourseInfo['studentId'];
            $courseId = $studentCourseInfo['courseId'];

            $dataValue = [
                'studCourseId' => $studCourseId,
                'studentId' => $studentCourseInfo['studentId'],
                'courseId' => $studentCourseInfo['courseId'],
                'subject_enrollment_id' => '',
                'certificateId' => $studentsData['certificateId'],
            ];

            if ($certificateFormat !== null) {
                $newNumber = $certificateFormat->last_auto_increment_number + 1;
                $uuid = $certificateFormat->prefix.$newNumber.$certificateFormat->suffix;
                $certificateFormat->last_auto_increment_number = $newNumber;
                $certificateFormat->save();
            }

            $student = $this->getStudentData($studentId, $courseId);
            $otherData = $this->getGradeData($collegeId);
            $enrolments = $this->getEnrolments($studentId, $courseId);
            $result = $this->handleCertificateGeneration([
                'student' => $student,
                'templates' => $templates,
                'dataValue' => $dataValue,
                'collegeId' => $collegeId,
                'studentId' => $studentId,
                'uuid' => $uuid,
                'otherData' => $otherData,
                'enrolments' => $enrolments,
                'isDownload' => $isDownload,
            ]);

            $results[] = $result;
        }

        if ($isDownload) {
            return $this->handleBulkDownload($results);
        }

        return response()->json(['success' => true, 'results' => $results]);
    }
}
