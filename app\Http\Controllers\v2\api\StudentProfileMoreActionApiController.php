<?php

namespace App\Http\Controllers\v2\api;

use App;
use App\Http\Controllers\Controller;
use App\Http\Requests\FormValidation\StudentProfile\EditStudentSanctionRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveCourseVariantRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveExitInterviewRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentCourseInformationRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentOsHelpRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentSaHelpRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentSanctionRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentTcsiDetailsRequest;
use App\Http\Requests\FormValidation\StudentProfile\TcsiStudentCreditOfferRequest;
use App\Model\v2\Student;
use App\Process\StudentProfile\SaveCourseVariantProcess;
use App\Services\StudentProfileMoreActionServicess;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use App\Users;
use Config;
use Helpers;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Password;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;

class StudentProfileMoreActionApiController extends Controller implements UpdatesUserProfileInformation
{
    use CommonTrait;
    use MustVerifyEmail;
    use ResponseTrait;

    private $studentProfileMoreActionServicess;

    public function __construct(
        StudentProfileMoreActionServicess $studentProfileMoreActionServicess
    ) {
        $this->studentProfileMoreActionServicess = $studentProfileMoreActionServicess;
        ini_set('memory_limit', '-1');
    }

    public function saveStudentSanction(SaveStudentSanctionRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentSanctionData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentSanctionList(Request $request)
    {
        $result = $this->studentProfileMoreActionServicess->getStudentSanctionListData($request);
        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentSanctionDetail(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getStudentSanctionDetailData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateStudentSanction(EditStudentSanctionRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->editStudentSanctionData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteStudentSanction(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required',
        ]);
        $data = $this->studentProfileMoreActionServicess->deleteStudentSanctionData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentExitInterviewList(Request $request)
    {
        $result = $this->studentProfileMoreActionServicess->getStudentExitInterviewListData($request);

        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveExitInterview(SaveExitInterviewRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveExitInterviewData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function updateExitInterview(SaveExitInterviewRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->updateExitInterviewData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getExitInterview(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getExitInterviewData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function deleteStudentExitInterview(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required',
        ]);
        $data = $this->studentProfileMoreActionServicess->deleteStudentExitInterviewData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentCard($studentId)
    {
        if (($studentId > 0)) {
            $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentDetail($studentId);
            if (isset($arrStudentInfo)) {
                $profilePicPath = Config::get('constants.displayProfilePicture');
                $filePath = Config::get('constants.uploadFilePath.StudentPics');
                $profilePicDestinationPath = Helpers::changeRootPath($filePath, $studentId);
                $profilePicPath = $profilePicDestinationPath['view'];
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $logoPath = $destinationPath['view'];

                $data['clg_logo'] = $logoPath.$arrStudentInfo[0]->college_logo;
                $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $profilePicFullPath = public_path($profilePicPath.$arrStudentInfo[0]->profile_picture);
                if (File::exists($profilePicFullPath)) {
                    $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                } else {
                    $data['profile_pic'] = 'dist/img/avatar6.png';
                }
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('v2.sadmin.student.pages.student-card-pdf', $data);

                return $pdf->download('student-card.pdf');
            }
        }
    }

    public function studentResetPasswordEmail(Request $request)
    {
        $student = Student::findOrFail($request->input('student_id'));
        try {

            $status = Password::sendResetLink([
                'email' => $student->email,
            ]);
            // $returnData['type'] = $status === Password::RESET_LINK_SENT ? 'alert-success' : 'alert-error';
            // $returnData['message'] = $status === Password::RESET_LINK_SENT ? 'Password reset link sent to user email' : __($status);

            if ($status === Password::RESET_LINK_SENT) {
                return $this->successResponse('Password reset link sent to user email', 'data', '');
            } else {
                return $this->errorResponse(__($status), 'data', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', [], 200);
        }
    }

    public function getTcsiStudentDetail(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getTcsiStudentDetailData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveStudentTcsiDetails(SaveStudentTcsiDetailsRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentTcsiDetailsData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function studentReActivationEmail(Request $request)
    {
        $student = Student::findOrFail($request->input('student_id'));
        try {
            $user = Users::where('username', $student->generated_stud_id)->first();
            if ($user) {
                $user->sendEmailVerificationNotification();

                return $this->successResponse('Student Re-activation email sent successfully', 'data', '');
            } else {
                return $this->errorResponse('Something want wrong', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', [], 200);
        }
    }

    public function getSaHelpFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getSaHelpFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveSaHelpData(SaveStudentSaHelpRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveSaHelpDetailData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getOsHelpFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getOsHelpFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveOsHelpData(SaveStudentOsHelpRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveOsHelpDetailData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentCourseInformationFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentCourseInformationFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getStudentCourseInformationFromCourseId(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentCourseInformationFromCourseIdData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getNarrowTypeList(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getNarrowTypeListData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getSubNarrowTypeList(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getSubNarrowTypeListData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveStudentCourseInformation(SaveStudentCourseInformationRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentCourseInformationData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getDisabilityInformationFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getDisabilityInformationFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveDisabilityInformationDetails(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveDisabilityInformationDetailsData($request->input(), $request->user()->id);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getOShelpInformationFromCourseId(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getOShelpInformationFromCourseIdData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    /* Manage Course Variant */
    public function getCourseVariantList(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseVariantLogs(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantLogData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseVariantDetail(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantDetailData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function saveCourseVariant(SaveCourseVariantRequest $request, SaveCourseVariantProcess $process)
    {
        // $data = $this->studentProfileMoreActionServicess->saveCourseVariantData($request);
        $requestData = $request->DTO();
        $postData = $requestData->toArray();

        DB::beginTransaction();
        try {
            $processData = [
                'studentId' => $postData['student_id'],
                'studCourseId' => $postData['student_course_id'],
                'newStatus' => $postData['course_status'],
                'courseVariantData' => $postData,
            ];

            // Run the course variant process
            $result = $process->run($processData);

            if ($result['status'] == 'success') {
                DB::commit();

                return $this->successResponse('Course Variant details saved successfully.', 'data', $result);
            } else {
                DB::rollBack();

                return $this->errorResponse($result['message'], 'data', $result);
            }
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), 'data', []);
        }
    }

    public function updateCourseVariant(SaveCourseVariantRequest $request, SaveCourseVariantProcess $process)
    {
        $requestData = $request->DTO();
        $postData = $requestData->toArray();

        try {
            $processData = [
                'is_update' => true,
                'course_variant_id' => $request->input('id'),
                'studentId' => $postData['student_id'],
                'studCourseId' => $postData['student_course_id'],
                'newStatus' => $postData['course_status'],
                'courseVariantData' => $postData,
            ];

            $result = $process->run($processData);
            if ($result['status'] == 'success') {
                DB::commit();

                return $this->successResponse('Course variant updated successfully', 'data', $result);
            } else {
                DB::rollBack();

                return $this->errorResponse($result['message'], 'data', $result);
            }

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', []);
        }
    }

    public function printCourseVariant($courseVariantId)
    {
        // $courseVariantId = $request->input('id');

        if ($courseVariantId > 0) {
            $courseVariantInfo = $this->studentProfileMoreActionServicess->printCourseVariantData($courseVariantId);
            if ($courseVariantInfo) {
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $data['clg_logo'] = $destinationPath['view'].$courseVariantInfo->logo;
                $data['arrCourseVariantInfo'] = $courseVariantInfo;
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('v2.sadmin.student.pages.course-variant-pdf-view', $data);

                return $pdf->download('invoice.pdf');
            } else {
                return $this->errorResponse('Data not found', 'data', [], 200);
            }
        }

        return $this->errorResponse('ID not found', 'data', [], 200);
    }

    /* TCSI Credit Offer */
    public function getTcsiCreditOfferData(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getTcsiCreditOfferData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTcsiCreditOfferInfo(Request $request)
    {
        $res = $this->studentProfileMoreActionServicess->getTcsiCreditOfferInfo($request->input('id'));

        return $this->successResponse('Data found successfully', 'data', $res);
    }

    public function saveTcsiCreditOfferData(TcsiStudentCreditOfferRequest $request)
    {
        $res = $this->studentProfileMoreActionServicess->saveTcsiCreditOfferData($request->DTO());
        if ($res['status'] == 'success') {
            return $this->successResponse($res['message'], 'data', $res);
        } else {
            return $this->errorResponse($res['message'], 'data', $res);
        }
    }

    public function updateTcsiCreditOfferData(TcsiStudentCreditOfferRequest $request)
    {
        $res = $this->studentProfileMoreActionServicess->updateTcsiCreditOfferData($request->DTO());
        if ($res['status'] == 'success') {
            return $this->successResponse($res['message'], 'data', $res);
        } else {
            return $this->errorResponse($res['message'], 'data', $res);
        }
    }

    public function deleteTcsiCreditOfferData(Request $request)
    {
        $res = $this->studentProfileMoreActionServicess->deleteTcsiCreditOfferData($request->input('id'));

        return $this->successResponse('Deleted Successfully', 'data', $res);
    }
}
