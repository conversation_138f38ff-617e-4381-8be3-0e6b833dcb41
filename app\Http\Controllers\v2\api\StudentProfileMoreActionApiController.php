<?php

namespace App\Http\Controllers\v2\api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\FormValidation\StudentProfile\EditStudentSanctionRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveCourseVariantRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveExitInterviewRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentCourseInformationRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentOsHelpRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentSaHelpRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentSanctionRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentTcsiDetailsRequest;
use App\Http\Requests\FormValidation\StudentProfile\TcsiStudentCreditOfferRequest;
use App\Model\v2\CertificateTemplate;
use App\Model\v2\Student;
use App\Process\StudentProfile\SaveCourseVariantProcess;
use App\Services\StudentProfileMoreActionServicess;
use App\Traits\CertificateGenerationTrait;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use App\Users;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Password;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;
use Support\Services\UploadService;

class StudentProfileMoreActionApiController extends Controller implements UpdatesUserProfileInformation
{
    use CertificateGenerationTrait;
    use CommonTrait;
    use MustVerifyEmail;
    use ResponseTrait;

    private $studentProfileMoreActionServicess;

    public function __construct(
        StudentProfileMoreActionServicess $studentProfileMoreActionServicess
    ) {
        $this->studentProfileMoreActionServicess = $studentProfileMoreActionServicess;
        ini_set('memory_limit', '-1');
    }

    public function saveStudentSanction(SaveStudentSanctionRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentSanctionData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentSanctionList(Request $request)
    {
        $result = $this->studentProfileMoreActionServicess->getStudentSanctionListData($request);
        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentSanctionDetail(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getStudentSanctionDetailData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateStudentSanction(EditStudentSanctionRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->editStudentSanctionData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteStudentSanction(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required',
        ]);
        $data = $this->studentProfileMoreActionServicess->deleteStudentSanctionData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentExitInterviewList(Request $request)
    {
        $result = $this->studentProfileMoreActionServicess->getStudentExitInterviewListData($request);

        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveExitInterview(SaveExitInterviewRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveExitInterviewData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function updateExitInterview(SaveExitInterviewRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->updateExitInterviewData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getExitInterview(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getExitInterviewData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function deleteStudentExitInterview(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required',
        ]);
        $data = $this->studentProfileMoreActionServicess->deleteStudentExitInterviewData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentCard_OLD($studentId)
    {
        if (($studentId > 0)) {
            $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentDetail($studentId);
            if (isset($arrStudentInfo)) {
                $profilePicPath = Config::get('constants.displayProfilePicture');
                $filePath = Config::get('constants.uploadFilePath.StudentPics');
                $profilePicDestinationPath = Helpers::changeRootPath($filePath, $studentId);
                $profilePicPath = $profilePicDestinationPath['view'];
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $logoPath = $destinationPath['view'];

                $data['clg_logo'] = $logoPath.$arrStudentInfo[0]->college_logo;
                $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $profilePicFullPath = public_path($profilePicPath.$arrStudentInfo[0]->profile_picture);
                if (File::exists($profilePicFullPath)) {
                    $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                } else {
                    $data['profile_pic'] = 'dist/img/avatar6.png';
                }
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('v2.sadmin.student.pages.student-card-pdf', $data);

                return $pdf->download('student-card.pdf');
            }
        }
    }

    public function getStudentCard($studentId)
    {
        try {
            $collegeId = auth()->user()->college_id;

            // Get student ID templates
            $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD)
                ->orderBy('created_at', 'desc')
                ->get();

            if ($templates->isEmpty()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No student ID card template found. Please create a template first in Galaxy Settings > Student ID Templates.',
                ], 404);
            }

            // Use the first available template (or you could make this configurable)
            $template = $templates->first();

            // Get student course data for proper context
            // $student = Student::with(['college'])->where('id', $studentId)->first();
            $student = Student::find($studentId);

            if (! $student) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Student data not found.',
                ], 404);
            }

            // Generate preview URL instead of direct PDF
            $previewUrl = $this->generateStudentCardPreviewUrl($template->id, $studentId);

            return response()->json([
                'status' => 'success',
                'preview_url' => $previewUrl,
                'template_name' => $template->name,
            ]);

        } catch (\Exception $e) {
            \Log::error('Student card generation error: '.$e->getMessage(), [
                'student_id' => $studentId,
                'college_id' => auth()->user()->college_id ?? null,
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate student ID card. Please try again.',
            ], 500);
        }
    }

    public function bulkGenerateStudentCards(Request $request)
    {
        try {
            $studentIds = $request->input('student_ids', []);

            if (empty($studentIds)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No students selected for student card generation.',
                ], 400);
            }

            $collegeId = auth()->user()->college_id;
            $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD)
                ->orderBy('created_at', 'desc')
                ->get();

            if ($templates->isEmpty()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No student ID card template found. Please create a template first in Galaxy Settings > Student ID Templates.',
                ], 404);
            }

            $template = $templates->first();

            $students = Student::with(['college'])->whereIn('id', $studentIds)->get();

            if ($students->isEmpty()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No student data found for the selected students.',
                ], 404);
            }

            // Generate preview URL instead of direct PDF
            $previewUrl = $this->generateBulkStudentCardsPreviewUrl($template->id, $students);

            return response()->json([
                'status' => 'success',
                'message' => 'Student ID cards preview generated for '.count($students).' student(s).',
                'preview_url' => $previewUrl,
                'student_count' => count($students),
                'template_name' => $template->name,
            ]);

        } catch (\Exception $e) {
            \Log::error('Bulk student card generation error: '.$e->getMessage(), [
                'student_ids' => $request->input('student_ids', []),
                'college_id' => auth()->user()->college_id ?? null,
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate student ID cards. Please try again.',
            ], 500);
        }
    }

    private function generateStudentCardPreviewUrl($certificateId, $studentId)
    {
        $data = [
            [
                'studentId' => $studentId,
                'certificateId' => $certificateId,
                'type' => 'student-id',
            ],
        ];

        $jsonData = json_encode($data);
        $encodedData = urlencode($jsonData);

        return route('spa.generate-student-card').'?data='.$encodedData;
    }

    private function generateBulkStudentCardsPreviewUrl($certificateId, $students)
    {
        $studentIds = $students->pluck('id')->toArray();
        $data = [
            'certificateId' => $certificateId,
            'studentIds' => $studentIds,
            'type' => 'bulk-student-id',
            'isBulk' => true,
        ];
        $jsonData = json_encode($data);
        $encodedData = urlencode($jsonData);

        return route('spa.generate-student-card').'?data='.$encodedData;
    }

    private function getStudentDataForCard($studentId, $collegeId)
    {
        return Student::from('rto_students as rs')
            ->leftJoin('rto_colleges as college', 'college.id', '=', 'rs.college_id')
            ->where(['rs.college_id' => $collegeId, 'rs.id' => $studentId])
            ->select(
                'rs.id as student_id',
                'rs.generated_stud_id',
                'rs.first_name',
                'rs.family_name',
                'rs.profile_picture',
                'rs.email',
                'rs.current_mobile_phone',
                'rs.DOB',
                'college.college_name',
                'college.college_logo'
            )
            ->first();
    }

    private function generateStudentCardPdf($template, $student)
    {
        // Prepare data for template replacement
        $data = $this->prepareStudentCardData($student);

        // Replace placeholders in HTML template
        $processedHtml = $this->replaceStudentCardPlaceholders($template->html_data, $data);

        // Generate PDF
        $fileName = 'student_card_'.$student->generated_stud_id.'_'.time();

        // Set up PDF dimensions (standard ID card size: 3.375" x 2.125")
        $width_px = 243; // 3.375 inches * 72 DPI
        $height_px = 153; // 2.125 inches * 72 DPI
        $width_pt = $width_px * 0.75;
        $height_pt = $height_px * 0.75;

        $pdf = App::make('dompdf.wrapper');
        $pdf->setPaper([0, 0, $width_pt, $height_pt]);
        $pdf->setOptions([
            'isRemoteEnabled' => true,
            'fontDir' => Config::get('constants.certificateTempFontPath', storage_path('fonts')),
        ]);

        $pdf->loadHTML($processedHtml);

        // Save to temporary location and return download URL
        $filePath = Config::get('constants.uploadFilePath.StudentCard');
        $destinationPath = Helpers::changeRootPath($filePath);

        $pdfContent = $pdf->output();
        $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tmpPath, $pdfContent);

        $upload_success = UploadService::uploadAs(
            $destinationPath['view'],
            new \Illuminate\Http\File($tmpPath),
            $fileName.'.pdf'
        );

        @unlink($tmpPath);

        return UploadService::download($destinationPath['view'].$fileName.'.pdf');
    }

    /*private function prepareStudentCardData2($student)
    {
        $profilePicPath = asset('dist/img/avatar6.png');
        if ($student->profile_picture) {
            $profilePicPath = $this->getStudentProfilePicPath($student->id, $student->profile_picture);
        }

        $collegeLogoPath = '';
        if ($student->college_logo) {
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath);
            $collegeLogoPath = UploadService::imageEmbed($destinationPath['view'].$student->college_logo);
            // $collegeLogoPath = asset($destinationPath['view'].$student->college_logo);
        }

        return [
            'student_id' => $student->generated_stud_id,
            'full_name' => trim($student->first_name.' '.$student->family_name),
            'first_name' => $student->first_name,
            'last_name' => $student->family_name,
            'email' => $student->email,
            'phone' => $student->current_mobile_phone,
            'date_of_birth' => $student->DOB ? date('d/m/Y', strtotime($student->DOB)) : 'N/A',
            'college_name' => $student->college_name,
            'profile_picture' => $profilePicPath,
            'college_logo' => $collegeLogoPath,
            'issue_date' => date('d/m/Y'),
        ];
    }*/

    private function replaceStudentCardPlaceholders($htmlTemplate, $data)
    {
        $html = $htmlTemplate;

        // Replace all placeholders with actual data
        foreach ($data as $key => $value) {
            $placeholder = "[$key]";
            if ($key == 'profile_picture' || $key == 'college_logo') {
                continue;
            }
            $html = str_replace($placeholder, $value, $html);
        }

        // Handle image tags specifically for profile picture and logo
        if (isset($data['profile_picture']) && ! empty($data['profile_picture'])) {
            $html = preg_replace(
                '/\[profile_picture\]/',
                '<img src="'.$data['profile_picture'].'" alt="Profile Picture" style="max-width: 100px; max-height: 100px;">',
                $html
            );
        }

        if (isset($data['college_logo']) && ! empty($data['college_logo'])) {
            $html = preg_replace(
                '/\[college_logo\]/',
                '<img src="'.$data['college_logo'].'" alt="College Logo" style="max-width: 150px; max-height: 80px;">',
                $html
            );
        }

        return $html;
    }

    public function studentResetPasswordEmail(Request $request)
    {
        $student = Student::findOrFail($request->input('student_id'));
        try {

            $status = Password::sendResetLink([
                'email' => $student->email,
            ]);
            // $returnData['type'] = $status === Password::RESET_LINK_SENT ? 'alert-success' : 'alert-error';
            // $returnData['message'] = $status === Password::RESET_LINK_SENT ? 'Password reset link sent to user email' : __($status);

            if ($status === Password::RESET_LINK_SENT) {
                return $this->successResponse('Password reset link sent to user email', 'data', '');
            } else {
                return $this->errorResponse(__($status), 'data', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', [], 200);
        }
    }

    public function getTcsiStudentDetail(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getTcsiStudentDetailData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveStudentTcsiDetails(SaveStudentTcsiDetailsRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentTcsiDetailsData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function studentReActivationEmail(Request $request)
    {
        $student = Student::findOrFail($request->input('student_id'));
        try {
            $user = Users::where('username', $student->generated_stud_id)->first();
            if ($user) {
                $user->sendEmailVerificationNotification();

                return $this->successResponse('Student Re-activation email sent successfully', 'data', '');
            } else {
                return $this->errorResponse('Something want wrong', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', [], 200);
        }
    }

    public function getSaHelpFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getSaHelpFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveSaHelpData(SaveStudentSaHelpRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveSaHelpDetailData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getOsHelpFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getOsHelpFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveOsHelpData(SaveStudentOsHelpRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveOsHelpDetailData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentCourseInformationFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentCourseInformationFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getStudentCourseInformationFromCourseId(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentCourseInformationFromCourseIdData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getNarrowTypeList(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getNarrowTypeListData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getSubNarrowTypeList(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getSubNarrowTypeListData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveStudentCourseInformation(SaveStudentCourseInformationRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentCourseInformationData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getDisabilityInformationFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getDisabilityInformationFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveDisabilityInformationDetails(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveDisabilityInformationDetailsData($request->input(), $request->user()->id);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getOShelpInformationFromCourseId(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getOShelpInformationFromCourseIdData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    /* Manage Course Variant */
    public function getCourseVariantList(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseVariantLogs(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantLogData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseVariantDetail(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantDetailData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function saveCourseVariant(SaveCourseVariantRequest $request, SaveCourseVariantProcess $process)
    {
        // $data = $this->studentProfileMoreActionServicess->saveCourseVariantData($request);
        $requestData = $request->DTO();
        $postData = $requestData->toArray();

        DB::beginTransaction();
        try {
            $processData = [
                'studentId' => $postData['student_id'],
                'studCourseId' => $postData['student_course_id'],
                'newStatus' => $postData['course_status'],
                'courseVariantData' => $postData,
            ];

            // Run the course variant process
            $result = $process->run($processData);

            if ($result['status'] == 'success') {
                DB::commit();

                return $this->successResponse('Course Variant details saved successfully.', 'data', $result);
            } else {
                DB::rollBack();

                return $this->errorResponse($result['message'], 'data', $result);
            }
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), 'data', []);
        }
    }

    public function updateCourseVariant(SaveCourseVariantRequest $request, SaveCourseVariantProcess $process)
    {
        $requestData = $request->DTO();
        $postData = $requestData->toArray();

        try {
            $processData = [
                'is_update' => true,
                'course_variant_id' => $request->input('id'),
                'studentId' => $postData['student_id'],
                'studCourseId' => $postData['student_course_id'],
                'newStatus' => $postData['course_status'],
                'courseVariantData' => $postData,
            ];

            $result = $process->run($processData);
            if ($result['status'] == 'success') {
                DB::commit();

                return $this->successResponse('Course variant updated successfully', 'data', $result);
            } else {
                DB::rollBack();

                return $this->errorResponse($result['message'], 'data', $result);
            }

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', []);
        }
    }

    public function printCourseVariant($courseVariantId)
    {
        // $courseVariantId = $request->input('id');

        if ($courseVariantId > 0) {
            $courseVariantInfo = $this->studentProfileMoreActionServicess->printCourseVariantData($courseVariantId);
            if ($courseVariantInfo) {
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $data['clg_logo'] = $destinationPath['view'].$courseVariantInfo->logo;
                $data['arrCourseVariantInfo'] = $courseVariantInfo;
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('v2.sadmin.student.pages.course-variant-pdf-view', $data);

                return $pdf->download('invoice.pdf');
            } else {
                return $this->errorResponse('Data not found', 'data', [], 200);
            }
        }

        return $this->errorResponse('ID not found', 'data', [], 200);
    }

    /* TCSI Credit Offer */
    public function getTcsiCreditOfferData(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getTcsiCreditOfferData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTcsiCreditOfferInfo(Request $request)
    {
        $res = $this->studentProfileMoreActionServicess->getTcsiCreditOfferInfo($request->input('id'));

        return $this->successResponse('Data found successfully', 'data', $res);
    }

    public function saveTcsiCreditOfferData(TcsiStudentCreditOfferRequest $request)
    {
        $res = $this->studentProfileMoreActionServicess->saveTcsiCreditOfferData($request->DTO());
        if ($res['status'] == 'success') {
            return $this->successResponse($res['message'], 'data', $res);
        } else {
            return $this->errorResponse($res['message'], 'data', $res);
        }
    }

    public function updateTcsiCreditOfferData(TcsiStudentCreditOfferRequest $request)
    {
        $res = $this->studentProfileMoreActionServicess->updateTcsiCreditOfferData($request->DTO());
        if ($res['status'] == 'success') {
            return $this->successResponse($res['message'], 'data', $res);
        } else {
            return $this->errorResponse($res['message'], 'data', $res);
        }
    }

    public function deleteTcsiCreditOfferData(Request $request)
    {
        $res = $this->studentProfileMoreActionServicess->deleteTcsiCreditOfferData($request->input('id'));

        return $this->successResponse('Deleted Successfully', 'data', $res);
    }
}
