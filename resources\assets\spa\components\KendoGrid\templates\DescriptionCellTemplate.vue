<template lang="">
    <td
        :class="rootClass"
        :colspan="props.colspan"
        :role="props.role"
        :title="props.dataItem.subject"
    >
        <div class="flex items-center gap-4">
            <span :class="wrapperClass">
                <strong>{{ props.dataItem.subject || '-' }}</strong> :
                <span>{{ truncatedContent.replace(/<\/?[^>]+(>|$)/g, '') }}</span>
                <slot />
            </span>

            <div
                v-if="props.dataItem.attachments?.length > 0"
                class="flex cursor-pointer justify-end"
                @click="handleAttachment(props.dataItem)"
            >
                <div class="flex flex-wrap items-center gap-4">
                    <a :href="props.dataItem.attachments[0].url" target="_blank" class="group">
                        <Badge
                            v-if="props.dataItem.attachments.length > 0"
                            :variant="'outline'"
                            shape="full"
                            :class="'flex max-w-40 gap-1 border border-gray-300 bg-white group-hover:bg-gray-50'"
                        >
                            <span class="shrink-0">
                                <icon
                                    :name="getIconText(props.dataItem.attachments[0].file_type)"
                                    :height="'20'"
                                    :width="'20'"
                                />
                            </span>
                            <span
                                class="truncate group-hover:text-primary-blue-500 group-hover:underline"
                                >{{ props.dataItem.attachments[0].file_name }}</span
                            >
                        </Badge>
                    </a>
                </div>

                <span v-if="props.dataItem.attachments.length > 1">
                    <div
                        class="inline-flex h-7 items-center justify-start gap-1 rounded-3xl border border-gray-200 bg-white px-2 py-1"
                    >
                        <div class="flex items-center justify-start gap-2.5 overflow-hidden">
                            <div
                                class="text-xs font-normal leading-none tracking-wide text-gray-700"
                            >
                                +{{ props.dataItem.attachments.length - 1 }}
                            </div>
                        </div>
                    </div>
                </span>
            </div>
        </div>
    </td>
</template>
<script>
import { twMerge } from 'tailwind-merge';
import Badge from '@spa/components/badges/Badge.vue';

export default {
    components: {
        Badge,
    },
    props: {
        props: Object,
        textOverwrite: { type: String, default: null },
        pt: {
            type: Object,
            default: {},
        },
        truncate: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        rootClass() {
            return twMerge(this.props.class, this.pt.root, this.truncate ? 'truncate' : '');
        },
        wrapperClass() {
            return twMerge(
                'flex items-center gap-1 inline',
                this.pt.wrapper,
                this.truncate ? 'truncate' : ''
            );
        },
        getText() {
            return this.textOverwrite ? this.textOverwrite : this.props.dataItem.subject;
        },
        truncatedContent() {
            if (!this.truncate || !this.props.dataItem.letter_content) {
                return this.props.dataItem.letter_content || '';
            }
            const content = this.props.dataItem.letter_content;
            const maxLength = 100; // Adjust the max length as needed
            return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
        },
    },
    methods: {
        getIconText(file_type) {
            return `icon-${file_type}`;
        },
        handleViewDetail(dataItem) {
            this.$emit('viewDetail', dataItem);
        },
        handleAttachment(dataItem) {
            this.$emit('attachment', dataItem);
        },
    },
};
</script>
<style lang=""></style>
