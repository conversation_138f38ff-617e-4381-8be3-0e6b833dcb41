<?php

namespace App;

use App\Model\Staff;
use App\Model\v2\Student;
use App\Model\v2\UserRoleType;
use App\Traits\UserAuth;
use Database\Factories\UsersFactory;
use Domains\Customers\Billing\Collections\ShortCourseChargeCollection;
use Domains\Customers\Models\CentralUser;
use Domains\Moodle\Contracts\CanConnectToMoodle;
use Domains\Moodle\Entities\Entity;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Repositories\MoodleRepository;
use Domains\Moodle\Traits\MoodleSyncableItem;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\Hash;
use Laravel\Cashier\Billable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Sanctum\HasApiTokens;
use Notifications\Notifiable;
use Spatie\Activitylog\Traits\CausesActivity;
use SSO\Contracts\HasSingleSignOn;
use SSO\Traits\HasKeycloakIdentity;
use Zoha\Metable;

class Users extends Authenticatable implements CanConnectToMoodle, HasSingleSignOn, MustVerifyEmail
{
    use Billable;
    use CausesActivity;
    use HasApiTokens, Notifiable, UserAuth;
    use HasFactory;
    use HasKeycloakIdentity;
    use Metable;
    use MoodleSyncableItem;
    use TwoFactorAuthenticatable;

    protected $table = 'rto_users';

    protected $fillable = ['email', 'name', 'username', 'college_id', 'uuid', 'phone', 'mobile'];

    protected $hidden = [
        'password',
        'sso_login_token',
        'two_factor_cookies',
        'two_factor_recovery_codes',
        'two_factor_secret',
        'stripe_id',
    ];

    protected $casts = [
        'sso_data' => 'json',
    ];

    public function roles()
    {
        // return $this->belongsTo('App\Roles');
        return $this->hasOne('App\Roles', 'id', 'role_id');
    }

    // bellow code is used to remove remember token
    public function getRememberToken()
    {
        return null; // not supported
    }

    public function setRememberToken($value)
    {
        // not supported
    }

    public function getRememberTokenName()
    {
        return null; // not supported
    }

    /**
     * Overrides the method to ignore the remember token.
     */
    public function setAttribute($key, $value)
    {
        $isRememberTokenAttribute = $key == $this->getRememberTokenName();
        if (! $isRememberTokenAttribute) {
            parent::setAttribute($key, $value);
        }
    }

    public function getUserList($collegeId)
    {
        $arrUsers = Users::select('id', 'username')
            ->where('college_id', $collegeId)
            ->pluck('username', 'id')->toArray();

        $userRecord['0'] = '--Select Account Manager--';

        return $returnUserRecord = array_merge($userRecord, $arrUsers);
    }

    public function saveFrontendUsers($request)
    {

        $users = new Users;
        $users->username = $request['username'];
        $users->email = $request['email'];
        $users->role_id = $request['roles_id'];
        $users->name = $request['name'];
        $users->phone = $request['phone'];
        $users->mobile = $request['mobile'];
        $users->college_id = $request['college_id'];
        $users->password = Hash::make($request['password']);
        $users->first_time_reset_pwd = 0;

        if ($users->save()) {
            return $users->id;
        }
        // $request->session()->flash('session_success', 'User Was Successfully Added.');
    }

    public function editFrontendUsers($request, $id)
    {

        $users = Users::find($id);

        $users->username = $request['username'];
        $users->email = $request['email'];
        $users->role_id = $request['roles_id'];
        $users->name = $request['name'];
        $users->phone = $request['phone'];
        $users->mobile = $request['mobile'];
        $users->college_id = $request['college_id'];
        $users->first_time_reset_pwd = $users->first_time_reset_pwd;

        $users->save();

        // $request->session()->flash('session_success', 'User Was Successfully Updated.');
    }

    public function getUserInfoList($perPage)
    {
        return Users::from('rto_users as ru')
            ->leftjoin('rto_roles as rr', 'rr.id', '=', 'ru.role_id')
            ->select('ru.*', 'rr.role_name')
            ->orderBy('ru.id', 'DESC')
            ->paginate($perPage);
    }

    public function staffAdd($lastInsertUserId, $request)
    {

        $objStaff = new Staff;

        $objStaff->college_id = $request['college_id'];
        $objStaff->user_id = $lastInsertUserId;
        $objStaff->first_name = ($request['name'] != '') ? $request['name'] : null;
        $objStaff->phone = ($request['phone'] != '') ? $request['phone'] : null;
        $objStaff->mobile = ($request['mobile'] != '') ? $request['mobile'] : null;
        $objStaff->email = ($request['email'] != '') ? $request['email'] : null;
        $objStaff->position = ($request['roles_id'] != '') ? $request['roles_id'] : null;
        $objStaff->staff_type = ($request['roles_id'] == '12') ? 'Staff-Teacher' : 'Staff';

        $objStaff->created_by = Auth()->guard('admin')->user()->id;
        $objStaff->updated_by = Auth()->guard('admin')->user()->id;

        if ($objStaff->save()) {
            return $objStaff->id;
        }
    }

    public function addUserRoleType($lastInsertStaffId, $lastInsertUserId, $request)
    {

        $objRoleType = new UserRoleType;

        $objRoleType->role_type = $request['roles_id'];
        $objRoleType->staff_id = $lastInsertStaffId;
        $objRoleType->user_id = $lastInsertUserId;
        $objRoleType->role_priority = 1;

        $objRoleType->created_by = Auth()->guard('admin')->user()->id;
        $objRoleType->updated_by = Auth()->guard('admin')->user()->id;

        $objRoleType->save();

        return true;
    }

    public function editUserRoleType($userId, $request)
    {

        // echo "$userId";
        $staffInfo = Staff::where('user_id', $userId)->get(['id']);
        $staffId = $staffInfo[0]->id;
        // echo $staffId; exit;
        UserRoleType::where('staff_id', $staffId)->delete();

        $objRoleType = new UserRoleType;
        $objRoleType->role_type = $request['roles_id'];
        $objRoleType->staff_id = $staffId;
        $objRoleType->user_id = $userId;
        $objRoleType->role_priority = 1;

        $objRoleType->created_by = Auth()->guard('admin')->user()->id;
        $objRoleType->updated_by = Auth()->guard('admin')->user()->id;

        $objRoleType->save();

        return true;
    }

    /* Relation with central db user */
    public function owner()
    {
        return $this->belongsTo(CentralUser::class, 'uuid', 'uuid');
    }

    /* Billing Route for user if exists */
    public function getBillingUrl()
    {
        return $this->owner ? route('user_profile').'?tab=billing' : url('/');
    }

    public function associatedStudent()
    {
        return $this->belongsTo(Student::class, 'username', 'generated_stud_id');
    }

    public function getFirstLastNameAttribute($val)
    {
        $name = trim($this->name);
        $last_name = (strpos($name, ' ') === false) ? '' : preg_replace('#.*\s([\w-]*)$#', '$1', $name);
        $first_name = trim(preg_replace('#'.preg_quote($last_name, '#').'#', '', $name));
        if (trim($first_name) == '') {
            $first_name = $last_name;
        }

        return [$first_name, $last_name];
    }

    public function getMoodlePayload($params = null): Entity
    {
        return \Domains\Moodle\Entities\User::FromUser($this, $params);
    }

    public function getMoodleRepository(): MoodleRepository
    {
        return Moodle::users();
    }

    public function updateFromMoodle(Entity $user)
    {

        $this->fill([
            'username' => $user->username,
            'name' => $user->fullname,
            'email' => $user->email,
        ]);

        $this->save();

        $this->saveMoodleItem($this->moodleItem()->firstOrCreate([
            'name' => 'moodle',
        ]), $user);
    }

    /* HANDLE COURSE CREATING LOGIC FROM MOODLE */
    public static function CreateFromMoodle(Entity $user)
    {
        $model = Users::create([
            'username' => $user->username,
            'name' => $user->fullname,
            'email' => $user->email,
        ]);

        $model->saveMoodleItem($model->moodleItem()->firstOrCreate([
            'name' => 'moodle',
        ]), $user);
    }

    public function generateUserToken($tokenKey = null, $updateUser = false, $removeOldToken = false)
    {
        if (! $tokenKey) {
            return false;
        }
        if ($removeOldToken) {
            $this->tokens()->where('name', $tokenKey)->delete();
        }
        $token = $this->createToken($tokenKey);
        $tokenText = $token->plainTextToken;
        if ($updateUser) {

            $this->api_token = $tokenText;
            $this->save();
        }

        return ['token' => $tokenText];
    }

    /* Create multiple stripe charges */
    public function checkoutCharges(ShortCourseChargeCollection $lineItems, array $sessionOptions = [], array $customerOptions = [])
    {
        if ($this->isAutomaticTaxEnabled()) {
            throw new \LogicException('For now, you cannot use checkout charges in combination automatic tax calculation.');
        }

        return $this->checkout($lineItems->map(function (\Domains\Customers\Billing\DTO\ShortCourseCharge $payload) {
            return [
                'price_data' => [
                    'currency' => $this->preferredCurrency(),
                    'product_data' => [
                        'name' => $payload->name,
                        'description' => $payload->description,
                    ],
                    'unit_amount' => $payload->amount,
                ],
                'quantity' => $payload->quantity,
            ];
        })->toArray(), $sessionOptions, $customerOptions);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return UsersFactory::new();
    }

    /**
     * Determine if the user has verified their email address.
     *
     * @return bool
     */
    public function hasVerifiedEmail()
    {
        return ! is_null($this->email_verified_at);
    }

    /**
     * Mark the given user's email as verified.
     *
     * @return bool
     */
    public function markEmailAsVerified()
    {
        return $this->forceFill([
            'email_verified_at' => $this->freshTimestamp(),
        ])->save();
    }
}
