$(document).ready(function () {
    customLoader();
    $.ajaxSetup({
        headers: {
            Authorization: api_token,
        },
    });

    $('#tabstrip').kendoTabStrip({
        animation: { open: { effects: 'fadeIn' } },
    });
    $(document).find('html').addClass('overflow-hidden');

    $('#studentMailHistoryList').kendoGrid({
        dataSource: customDataSource('api/student-mail-history-data', {
            student_name: { type: 'string' },
            subject: { type: 'string' },
            sent_date: { type: 'date' },
            from_mail: { type: 'string' },
        }),
        selectable: 'multiple',
        height: getGridTableHeight('#studentMailHistoryList'),
        change: studentmailhistory,
        pageable: customPageableArr(),
        dataBound: function (e) {
            manageGridPagerTemplate('#studentMailHistoryList');
        },
        sortable: true,
        columns: [
            {
                template: function (dataItem) {
                    return manageProfilePic(dataItem.id, '', dataItem.student_name);
                },
                field: 'student_name',
                title: 'TO',
            },
            {
                template: function (dataItem) {
                    return manageCourseWithsubject(dataItem.subject);
                },
                field: 'subject',
                title: 'SUBJECT',
            },
            {
                template:
                    '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-500\'> #: kendo.toString(sent_date, "dd MMM yyyy") # </div>',
                field: 'sent_date',
                title: 'SENT DATE',
                format: '{0:dd/MM/yyyy}',
            },
            {
                template:
                    '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-500\' onclick="mailhistory(this)">#: from_mail #</div>',
                field: 'from_mail',
                title: 'FROM',
            },
        ],
        noRecords: noRecordTemplate(),
    });
    customGridHtml('#studentMailHistoryList');

    function manageCourseWithsubject(subject) {
        if (subject) {
            let subjectName = subject.length > 50 ? subject.substr(0, 50) + '...' : subject;
            return (
                "<div class='flex items-center text-sm leading-5 font-normal text-gray-500'>" +
                subjectName +
                '</div>'
            );
        }
        return '';
    }

    $('#studentSmsHistoryList').kendoGrid({
        dataSource: customDataSource('api/student-sms-history-data', {
            student_name: { type: 'string' },
            sent_date: { type: 'date' },
        }),
        height: getGridTableHeight('#studentSmsHistoryList'),
        change: studentSmsHistory,
        pageable: customPageableArr(),
        dataBound: function (e) {
            manageGridPagerTemplate('#studentSmsHistoryList');
        },
        selectable: 'multiple',
        sortable: true,
        columns: [
            {
                template: function (dataItem) {
                    return manageProfilePic(dataItem.id, '', dataItem.student_name);
                },
                field: 'student_name',
                title: 'TO',
            },
            {
                template:
                    '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-500\' onclick="mailhistory(this)">#: kendo.toString(sent_date, "dd MMM yyyy")#</div>',
                field: 'sent_date',
                title: 'SENT DATE',
                format: '{0:dd/MM/yyyy}',
            },
        ],
        noRecords: noRecordTemplate(),
    });
    customGridHtml('#studentSmsHistoryList');

    $('#staffMailHistoryList').kendoGrid({
        dataSource: customDataSource('api/staff-mail-history-data', {
            staff_name: { type: 'string' },
            subject: { type: 'string' },
            sent_date: { type: 'date' },
            from_mail: { type: 'string' },
        }),
        height: getGridTableHeight('#staffMailHistoryList'),
        change: staffmailhistory,
        pageable: customPageableArr(),
        dataBound: function (e) {
            manageGridPagerTemplate('#staffMailHistoryList');
        },
        selectable: 'multiple',
        sortable: true,
        columns: [
            {
                template: function (dataItem) {
                    return manageProfilePic(dataItem.id, '', dataItem.staff_name);
                },
                field: 'staff_name',
                title: 'TO',
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-500'>#: subject #</div>",
                field: 'subject',
                title: 'SUBJECT',
            },
            {
                template:
                    '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-500\'>#: kendo.toString(sent_date, "dd MMM yyyy") #</div>',
                field: 'sent_date',
                title: 'SENT DATE',
                format: '{0:dd/MMM/yyyy}',
            },
            {
                template:
                    '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-500\' onclick="mailhistory(this)">#: from_mail #</div>',
                field: 'from_mail',
                title: 'FROM',
            },
        ],
        noRecords: noRecordTemplate(),
    });
    customGridHtml('#staffMailHistoryList');

    function mailhistory(i) {
        var row = $(i).closest('tr');
        row.addClass('k-state-selected');
    }

    function manageStaffprofile(id, nameStr) {
        let name = nameStr.toUpperCase().split(/\s+/);
        let html =
            "<div class='flex items-center stud_" +
            id +
            "'><div class='user-profile-pic h-6 w-6 rounded-full bg-primary-blue-500'><span class='text-xs leading-6'>" +
            name[0].charAt(0) +
            name[1].charAt(0) +
            "</span></div>&nbsp;<div class='student-first-name text-sm leading-4 text-gray-500'>" +
            nameStr +
            '</div></div>';
        return html;
    }

    function studentmailhistory(e) {
        for (let i = 0; i < 5; i++) {
            $(document)
                .find('#file_' + i + '')
                .text('');
            $('#file_' + i + '')
                .parent()
                .hide();
        }
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $('#studentMailHistoryList').data('kendoGrid');
            var data = grid.dataItem(this);
            $(document).find('#staffname').text(data.student_name);
            $(document).find('#templatename').text(data.subject);
            $(document).find('#send_date').text(kendo.toString(data.sent_date, 'dd MMM yyyy'));
            $(document).find('#sentfrom').text(data.from_mail);
            $(document).find('.messagebody').html(data.content);
            if (data.email_attachment_1) {
                $(document).find('#file_1').text(data.email_attachment_1);
                $('#file_1').parent().show();
            }
            if (data.email_attachment_2) {
                $(document).find('#file_2').text(data.email_attachment_2);
                $('#file_2').parent().show();
            }
            if (data.email_attachment_3) {
                $(document).find('#file_3').text(data.email_attachment_3);
                $('#file_3').parent().show();
            }
            if (data.email_attachment_4) {
                $(document).find('#file_4').text(data.email_attachment_4);
                $('#file_4').parent().show();
            }
        });
        kendowindowOpen('#emaildetailsmodel');
    }

    function studentSmsHistory(e) {
        for (let i = 0; i < 6; i++) {
            $(document)
                .find('#file_' + i + '')
                .text('');
            $('#file_' + i + '')
                .parent()
                .hide();
        }
        $(document).find('#templatename').text('');
        $(document).find('#sentfrom').text('');
        $(document).find('.messagebody').html('');
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $('#studentSmsHistoryList').data('kendoGrid');
            var data = grid.dataItem(this);
            $(document).find('#staffname').text(data.student_name);
            $(document).find('#send_date').text(kendo.toString(data.sent_date, 'dd MMM yyyy'));
            $(document).find('.messagebody').html(data.log);
        });
        kendowindowOpen('#emaildetailsmodel');
    }

    function staffmailhistory(e) {
        for (let i = 0; i < 6; i++) {
            $(document)
                .find('#file_' + i + '')
                .text('');
            $('#file_' + i + '')
                .parent()
                .hide();
        }
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $('#staffMailHistoryList').data('kendoGrid');
            var data = grid.dataItem(this);
            $(document).find('#staffname').text(data.staff_name);
            $(document).find('#templatename').text(data.subject);
            $(document).find('#send_date').text(kendo.toString(data.sent_date, 'dd MMM yyyy'));
            $(document).find('#sentfrom').text(data.from_mail);
            $(document).find('.messagebody').html(data.message);
            if (data.file_name_1) {
                $(document).find('#file_1').text(data.file_name_1);
                $('#file_1').parent().show();
            }
            if (data.file_name_2) {
                $(document).find('#file_2').text(data.file_name_2);
                $('#file_2').parent().show();
            }
            if (data.file_name_3) {
                $(document).find('#file_3').text(data.file_name_3);
                $('#file_3').parent().show();
            }
            if (data.file_name_4) {
                $(document).find('#file_4').text(data.file_name_4);
                $('#file_4').parent().show();
            }
            if (data.file_name_5) {
                $(document).find('#file_5').text(data.file_name_5);
                $('#file_5').parent().show();
            }
        });
        kendowindowOpen('#emaildetailsmodel');
    }

    $('#emaildetailsmodel').kendoWindow({
        title: 'Email Details',
        width: '40%',
        height: '100%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: 0,
            left: '60%',
        },
        animation: {
            open: {
                effects: 'slideIn:left',
                duration: 500,
            },
            close: {
                effects: 'slideIn:left',
                reverse: true,
                duration: 500,
            },
        },
        visible: false,
    });

    $('body').on('click', '.checkmail', function () {
        var courseType = $('.checkmail:checked').val();
        if (courseType == 'SMS') {
            $('#smstable').show();
            $('#studentSmsSearch').show();
            $('#studentMailSearch').hide();
            $('#mailtable').hide();
        } else if (courseType == 'Mail') {
            $('#mailtable').show();
            $('#studentMailSearch').show();
            $('#studentSmsSearch').hide();
            $('#smstable').hide();
        }
    });

    function kendowindowOpen(windowID) {
        let kendoWindow = $(document).find(windowID);
        kendoWindow.getKendoWindow().open();
        kendoWindow
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    }
});
