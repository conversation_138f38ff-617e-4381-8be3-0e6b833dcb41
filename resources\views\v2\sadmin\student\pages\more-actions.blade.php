{{-- More actions from header --}}

<div id="viewSanctionModal" style="display: none">
    <div class="inline-flex flex-col items-start justify-start bg-gray-50 h-full w-full">
        <div class="studentCourseDetailHeader w-full"></div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full noDataSanction hidden mb-5">
            <div class="flex flex-col space-y-6 items-start justify-start w-full">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-700">Sanction</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                </div>
                <div class="flex flex-col space-y-6 items-center justify-center py-8 bg-gray-50 rounded-lg w-full">
                    <div class="flex flex-col space-y-4 items-center justify-start">
                        <img src="{{ asset('v2/img/agent-commission.svg') }}" class="" alt="" />
                        <p class="text-sm leading-5 text-gray-700">You have not added sanction.</p>
                    </div>
                    <button class="addSanctionBtn btn-primary" fdprocessedid="yhfujj">
                        <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                        <p class="text-sm font-medium leading-4 text-white uppercase">Add Sanction</p>
                    </button>
                </div>
            </div>
        </div>
        <div class="inline-flex space-x-2 items-end justify-end w-full my-5 sanctionData hidden">
            <button class="addSanctionBtn btn-primary" fdprocessedid="yhfujj">
                <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                <p class="text-sm font-medium leading-4 text-white uppercase">Add Sanction</p>
            </button>
        </div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full sanctionData hidden">
            <div class="flex space-x-2 items-start justify-between w-full pb-6 ">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-700">Sanction</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                </div>
                <div class="inline-flex space-x-2 items-end justify-end">
                    <button type="button" id="exportStudentSanctionBtn"
                        class="inline-flex space-x-2 items-center justify-center h-8 pl-2 pr-2.5 bg-white border rounded-lg border-gray-300 hover:shadow">
                        <svg width="10" height="13" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M0.5 12H9.5C9.77614 12 10 12.2239 10 12.5C10 12.7455 9.82312 12.9496 9.58988 12.9919L9.5 13H0.5C0.223858 13 0 12.7761 0 12.5C0 12.2545 0.176875 12.0504 0.410124 12.0081L0.5 12H9.5H0.5ZM4.91012 0.00805569L5 0C5.24546 0 5.44961 0.176875 5.49194 0.410124L5.5 0.5V9.292L8.18198 6.61091C8.35555 6.43735 8.62497 6.41806 8.81984 6.55306L8.88909 6.61091C9.06265 6.78448 9.08194 7.0539 8.94694 7.24877L8.88909 7.31802L5.35355 10.8536C5.17999 11.0271 4.91056 11.0464 4.71569 10.9114L4.64645 10.8536L1.11091 7.31802C0.915651 7.12276 0.915651 6.80617 1.11091 6.61091C1.28448 6.43735 1.5539 6.41806 1.74877 6.55306L1.81802 6.61091L4.5 9.292V0.5C4.5 0.25454 4.67688 0.0503916 4.91012 0.00805569L5 0L4.91012 0.00805569Z"
                                fill="#9CA3AF" />
                        </svg>
                        <p class="text-xs leading-4 text-gray-700 uppercase">export</p>
                    </button>
                </div>
            </div>
            <div class="flex flex-col space-y-6 items-start justify-start w-full">
                <div id="studentSanctionGrid" class="tw-table tw-table__borderless"></div>
            </div>
        </div>

    </div>

</div>
<div id="addSanctionModal" style="display: none">
    <div class="inline-flex flex-col space-y-6 items-start justify-start bg-gray-50 pb-20 w-full">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex space-x-4 items-center w-full">
                <div class="flex w-16 h-16 studentProfilePic">
                </div>
                <div class="flex flex-col items-start space-y-1 justify-center">
                    <p class="text-sm font-bold leading-5 text-gray-900 studentProfilePicName"></p>
                    <p class="text-xs leading-4 text-gray-400 studentStudId"></p>
                </div>
            </div>
            <form id="addSanctionDetailsForm" class="w-full"></form>
            <div
                class="w-full h-12 p-4 bg-blue-50 rounded-md border border-blue-500 justify-start items-start gap-3 inline-flex">
                <div class="grow shrink basis-0 h-5 justify-start items-start gap-4 flex">
                    <div class="w-5 h-5 relative"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM9 4C9 4.55228 8.55228 5 8 5C7.44772 5 7 4.55228 7 4C7 3.44772 7.44772 3 8 3C8.55228 3 9 3.44772 9 4ZM7 7C6.44772 7 6 7.44772 6 8C6 8.55229 6.44772 9 7 9V12C7 12.5523 7.44772 13 8 13H9C9.55228 13 10 12.5523 10 12C10 11.4477 9.55228 11 9 11V8C9 7.44772 8.55228 7 8 7H7Z"
                                fill="#1890FF" />
                        </svg>
                    </div>
                    <div class="grow shrink basis-0 text-blue-600 text-sm font-normal leading-tight tracking-tight">If
                        sanction status is active, Student portal access will be disabled from the above date.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="editSanctionModal" style="display: none">
    <div class="inline-flex flex-col space-y-6 items-start justify-start bg-gray-50 pb-20 w-full">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex space-x-4 items-center w-full">
                <div class="flex w-16 h-16 studentProfilePic">
                </div>
                <div class="flex flex-col items-start space-y-1 justify-center">
                    <p class="text-sm font-bold leading-5 text-gray-900 studentProfilePicName"></p>
                    <p class="text-xs leading-4 text-gray-400 studentStudId"></p>
                </div>
            </div>
            <form id="editSanctionDetailsForm" class="w-full"></form>
            <div
                class="w-full h-12 p-4 bg-blue-50 rounded-md border border-blue-500 justify-start items-start gap-3 inline-flex">
                <div class="grow shrink basis-0 h-5 justify-start items-start gap-4 flex">
                    <div class="w-5 h-5 relative"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM9 4C9 4.55228 8.55228 5 8 5C7.44772 5 7 4.55228 7 4C7 3.44772 7.44772 3 8 3C8.55228 3 9 3.44772 9 4ZM7 7C6.44772 7 6 7.44772 6 8C6 8.55229 6.44772 9 7 9V12C7 12.5523 7.44772 13 8 13H9C9.55228 13 10 12.5523 10 12C10 11.4477 9.55228 11 9 11V8C9 7.44772 8.55228 7 8 7H7Z"
                                fill="#1890FF" />
                        </svg>
                    </div>
                    <div class="grow shrink basis-0 text-blue-600 text-sm font-normal leading-tight tracking-tight">If
                        sanction status is active, Student portal access will be disabled from the above date.</div>
                </div>
            </div>
        </div>
    </div>
</div>


<script id="studentSanctionActionTemplate" type="text/html">
    <div class="action-menu">
        <div class="px-1 space-y-1 py-2">
            <button type="button" data-id="#=id#"
                class="editStudentSanctionBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <span class="k-icon k-i-edit k-icon-edit grow-1 shrink-0"></span>
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Modified</span>
            </button>
            <button type="button" data-id="#=id#"
                class="deleteStudentSanctionBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/delete-gray.svg') }}" class="w-5 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Delete</span>
            </button>
        </div>
    </div>
</script>

<div id="deleteStudentSanctionModal"></div>
<div id="viewExitInterviewModal" style="display: none">
    <div class="inline-flex flex-col items-start justify-start bg-gray-50 h-full w-full space-y-6">
        <div class="studentCourseDetailHeader w-full"></div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full noDataExitInterview hidden mb-5">
            <div class="flex flex-col space-y-6 items-start justify-between w-full">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-700">Exit Interview</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                </div>
                <div class="flex flex-col space-y-6 items-center justify-center py-8 bg-gray-50 rounded-lg w-full">
                    <div class="flex flex-col space-y-4 items-center justify-start">
                        <img src="{{ asset('v2/img/agent-commission.svg') }}" class="" alt="" />
                        <p class="text-sm leading-5 text-gray-700">You have not added exit interview.</p>
                    </div>
                    <button class="addExitInterviewBtn btn-primary" fdprocessedid="yhfujj">
                        <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                        <p class="text-sm font-medium leading-4 text-white uppercase">Add Exit Interview</p>
                    </button>
                </div>
            </div>
        </div>
        <div class="inline-flex space-x-2 items-end justify-end w-full sanctionData hidden">
            <button class="addExitInterviewBtn btn-primary" fdprocessedid="yhfujj">
                <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                <p class="text-sm font-medium leading-4 text-white uppercase">Add Exit Interview</p>
            </button>
        </div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full sanctionData hidden">
            <div class="flex space-x-2 items-start justify-between w-full pb-6 ">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-700">Exit Interview</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                </div>
                <div class="inline-flex space-x-2 items-end justify-end">
                    <button type="button" id="exportStudentExitInterviewBtn"
                        class="inline-flex space-x-2 items-center justify-center h-8 pl-2 pr-2.5 bg-white border rounded-lg border-gray-300 hover:shadow">
                        <svg width="10" height="13" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M0.5 12H9.5C9.77614 12 10 12.2239 10 12.5C10 12.7455 9.82312 12.9496 9.58988 12.9919L9.5 13H0.5C0.223858 13 0 12.7761 0 12.5C0 12.2545 0.176875 12.0504 0.410124 12.0081L0.5 12H9.5H0.5ZM4.91012 0.00805569L5 0C5.24546 0 5.44961 0.176875 5.49194 0.410124L5.5 0.5V9.292L8.18198 6.61091C8.35555 6.43735 8.62497 6.41806 8.81984 6.55306L8.88909 6.61091C9.06265 6.78448 9.08194 7.0539 8.94694 7.24877L8.88909 7.31802L5.35355 10.8536C5.17999 11.0271 4.91056 11.0464 4.71569 10.9114L4.64645 10.8536L1.11091 7.31802C0.915651 7.12276 0.915651 6.80617 1.11091 6.61091C1.28448 6.43735 1.5539 6.41806 1.74877 6.55306L1.81802 6.61091L4.5 9.292V0.5C4.5 0.25454 4.67688 0.0503916 4.91012 0.00805569L5 0L4.91012 0.00805569Z"
                                fill="#9CA3AF" />
                        </svg>
                        <p class="text-xs leading-4 text-gray-700 uppercase">export</p>
                    </button>
                </div>
            </div>
            <div class="flex flex-col space-y-6 items-start justify-start w-full">
                <div id="studentExitInterviewGrid" class="tw-table tw-table__borderless"></div>
            </div>
        </div>
    </div>
</div>
<div id="addExitInterviewModal" style="display: none">
    <div class="inline-flex flex-col space-y-6 items-start justify-start bg-gray-50 pb-20 w-full">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex space-x-4 items-center w-full">
                <div class="flex w-16 h-16 studentProfilePic">
                </div>
                <div class="flex flex-col items-start space-y-1 justify-center">
                    <p class="text-sm font-bold leading-5 text-gray-900 studentProfilePicName"></p>
                    <p class="text-xs leading-4 text-gray-400 studentStudId"></p>
                </div>
            </div>
            <form id="addExitInterviewDetailsForm" class="w-full"></form>
        </div>
    </div>
</div>
<div id="editExitInterviewModal" style="display: none">
    <div class="inline-flex flex-col space-y-6 items-start justify-start bg-gray-50 pb-20 w-full">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex space-x-4 items-center w-full">
                <div class="flex w-16 h-16 studentProfilePic">
                </div>
                <div class="flex flex-col items-start space-y-1 justify-center">
                    <p class="text-sm font-bold leading-5 text-gray-900 studentProfilePicName"></p>
                    <p class="text-xs leading-4 text-gray-400 studentStudId"></p>
                </div>
            </div>
            <form id="editExitInterviewDetailsForm" class="w-full"></form>
        </div>
    </div>
</div>
<script id="studentExitInterviewActionTemplate" type="text/html">
    <div class="action-menu">
        <div class="px-1 space-y-1 py-2">

            <button type="button" data-id="#=id#"
                class="editStudentExitInterviewBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <span class="text-gray-400">
                    <x-v2.icons name="icon-edit" width="16" height="16" />
                </span>
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Edit</span>
            </button>
            <button type="button" data-id="#=id#"
                class="deleteStudentExitInterviewBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <span class=" text-red-500">
                    <x-v2.icons name="icon-delete" width="16" height="16" />
                </span>
                <span class="text-xs leading-5 text-red-500 w-full">Delete</span>
            </button>
        </div>
    </div>
</script>
<div id="deleteStudentExitInterviewModal"></div>



<div id="resetPasswordModal"></div>
<div id="reSendActivationModal"></div>
<div id="Modal"></div>

<script id="studentCourseStatusTemplate" type="text/html">
    <div class="flex">
        <div class="bg-white flex-col inline-flex items-start justify-start rounded-lg space-y-4 w-full">
            <div class="inline-flex items-center justify-start">
                <div class="flex space-x-2 items-start justify-start">
                    # if (arr.profile_picture == '') { let name = arr.student_name.toUpperCase().split(/\s+/); let
                    shortName = name[0].charAt(0) + name[1].charAt(0); #
                    <div class='w-14 h-14 flex user-profile-pic rounded-full bg-primary-blue-500 items-center'><span
                            class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                            #</span></div>
                    # } else { #
                    <img class="w-16 h-16 rounded-full" src="#= arr.profile_picture #" />
                    # } #
                    <div class="inline-flex flex-col items-start justify-center h-full w-full m-1">
                        <p class="text-base font-medium leading-6 text-gray-900">#= arr.student_name #</p>
                        <div class="flex items-center justify-start">
                            <p class="text-xs leading-5 text-gray-400">ID:#= arr.generated_stud_id #</p>
                            <x-v2.copy width="16" height="16" data-text="#: arr.generated_stud_id #" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="inline-flex space-x-1 items-center justify-between">
                <p class="text-xs leading-5 text-gray-700 truncate">#= arr.course_name #</p>
                <div
                    class="flex items-center justify-center px-2.5 py-0.5 bg-#=  arr.bgcolor #-100 rounded-lg truncate">
                    <p class="text-xs leading-5 text-center text-#=  arr.textcolor #-800">#= arr.status #</p>
                </div>
            </div>
        </div>
    </div>
</script>

<div id="viewStudentTCSIModal" style="display: none">
    <div class="inline-flex flex-col space-y-6 items-start justify-start bg-gray-50 pb-20 w-full">
        <div class="inline-flex flex-col items-start justify-start bg-gray-50 w-full">
            <div class="studentCourseDetailHeader w-full"></div>
        </div>
        <div
            class="flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">

            <div class="w-full bg-white tw-tabstrip" id="tcsiTabStrip">
                <ul class=" flex bg-white" id="" data-tabs-toggle="" role="tablist">
                    <li data-value="tcsi" class="k-state-active">
                        <span class="k-loading k-complete"></span>
                        <span unselectable="on" class="k-link" data-name="tcsi">
                            <span class="inline-block text-sm leading-none font-medium pb-2 text-gray-500"
                                style="font-weight: 500; font-size: 0.875rem">TCSI Information</span>
                        </span>
                    </li>
                    <li data-value="student_course_information">
                        <span class="k-loading k-complete"></span>
                        <span unselectable="on" class="k-link" data-name="student_course_information">
                            <span class="inline-block text-sm leading-none font-medium pb-2 text-gray-500">Student
                                Course Information</span>
                        </span>
                    </li>
                    <li data-value="disability_information">
                        <span class="k-loading k-complete"></span>
                        <span class="k-link" data-name="disability_information">
                            <span class="inline-block text-sm leading-none font-medium pb-2 text-gray-500"
                                style="font-weight: 500; font-size: 0.875rem">Disability Information</span>
                        </span>
                    </li>
                    <li data-value="sa_help">
                        <span class="k-loading k-complete"></span>
                        <span class="k-link" data-name="sa_help">
                            <span class="inline-block text-sm leading-none font-medium pb-2 text-gray-500"
                                style="font-weight: 500; font-size: 0.875rem">SA-Help</span>
                        </span>
                    </li>
                    <li data-value="os_help">
                        <span class="k-loading k-complete"></span>
                        <span class="k-link" data-name="os_help">
                            <span class="inline-block text-sm leading-none font-medium pb-2 text-gray-500"
                                style="font-weight: 500; font-size: 0.875rem">OS-Help</span>
                        </span>
                    </li>
                    <li data-value="credit_offer">
                        <span class="k-loading k-complete"></span>
                        <span class="k-link" data-name="credit_offer">
                            <span class="inline-block text-sm leading-none font-medium pb-2 text-gray-500"
                                style="font-weight: 500; font-size: 0.875rem">Additional Previous Study RPL or Credit
                                Offered</span>
                        </span>
                    </li>
                </ul>
                <div class="tcsi-li-detail">
                    <form id="addTCSIDetailsForm" class="w-full mt-5 max-w-4xl"></form>
                </div>
                <div id="student_course_information" class="student_course_information-li-detail space-y-6">
                    <form id="addCourseList" class="w-full mt-5"></form>
                    <div
                        class="flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">
                        <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                            <div class="inline-flex items-center justify-between">
                                <div class="flex space-x-2 items-center justify-start">
                                    <p class="text-lg font-medium leading-6 text-gray-900">Course Information</p>
                                </div>
                            </div>
                        </div>
                        <form id="addStudentCourseInformationForm" class="w-full mt-5"></form>
                    </div>
                    <div
                        class="flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">
                        <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                            <div class="inline-flex items-center justify-between">
                                <div class="flex space-x-2 items-center justify-start">
                                    <p class="text-lg font-medium leading-6 text-gray-900">Specialisation Code</p>
                                </div>
                            </div>
                        </div>
                        <form id="addStudentSpecialisationCodeForm" class="w-full mt-5"></form>
                    </div>
                    <div
                        class="flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">
                        <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                            <div class="inline-flex items-center justify-between">
                                <div class="flex space-x-2 items-center justify-start">
                                    <p class="text-lg font-medium leading-6 text-gray-900">Higher Degree Research
                                        Information</p>
                                </div>
                            </div>
                        </div>
                        <form id="addHigherDegreeResearchInformationForm" class="w-full mt-5"></form>
                    </div>
                    <div
                        class="flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">
                        <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                            <div class="inline-flex items-center justify-between">
                                <div class="flex space-x-2 items-center justify-start">
                                    <p class="text-lg font-medium leading-6 text-gray-900">Additional Information</p>
                                </div>
                            </div>
                        </div>
                        <form id="addAdditionalInformationForm" class="w-full mt-5"></form>
                    </div>
                    <div
                        class="flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">
                        <form id="addPreviousStudyRPLForm" class="w-full mt-5"></form>
                    </div>
                </div>
                <div class="disability_information-li-detail">
                    <form class="space-y-6" action="" method="post">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">

                        <div id="disabilityFormDiv" class="flex flex-col w-full border rounded-lg border-gray-200">

                        </div>
                        <div class="k-form-buttons">
                            <div
                                class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 fixed border-t bg-white px-6">
                                <div class="float-right flex space-x-4 items-center justify-end">
                                    <button type="button" class="btn-secondary px-6 py-2 cancelBtn">
                                        <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
                                    </button>
                                    <button type="button" class="saveDisabilityInformationBtn btn-primary px-3 py-2">
                                        <p class="text-sm font-medium leading-4 text-white">SAVE</p>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="sa_help-li-detail">
                    <form id="addSaHelpForm" class="w-full mt-5"></form>
                </div>
                <div id="os_help_div" class="os_help-li-detail">
                    <form id="addCourseOsHelpForm" class="w-full mt-5"></form>
                    <form id="addOsHelpForm" class="w-full mt-5"></form>
                </div>
                <div id="credit_offer_div" class="credit_offer-li-detail">
                    <div class="inline-flex flex-col items-start justify-start pt-4 w-full">
                        <div class="flex flex-col space-y-4 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-center justify-start w-full">
                                <p class="text-lg font-medium leading-6 text-gray-900 w-full"></p>
                                <div class="inline-flex pr-4 items-center justify-end w-full">
                                    <button type="button"
                                        class="addTcsiOfferCreditBtn flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.00039 0.400391C6.44222 0.400391 6.80039 0.758563 6.80039 1.20039V5.20039H10.8004C11.2422 5.20039 11.6004 5.55856 11.6004 6.00039C11.6004 6.44222 11.2422 6.80039 10.8004 6.80039H6.80039V10.8004C6.80039 11.2422 6.44222 11.6004 6.00039 11.6004C5.55856 11.6004 5.20039 11.2422 5.20039 10.8004V6.80039H1.20039C0.758563 6.80039 0.400391 6.44222 0.400391 6.00039C0.400391 5.55856 0.758563 5.20039 1.20039 5.20039H5.20039V1.20039C5.20039 0.758563 5.55856 0.400391 6.00039 0.400391Z"
                                                fill="white" />
                                        </svg>
                                        <p class="text-sm font-medium leading-4 text-white">Add</p>
                                    </button>
                                </div>
                            </div>
                            {{--<div id="tcsiCreditOfferList"></div>--}}
                            <x-v2.grid-table id="tcsiCreditOfferList" :rounded="true"></x-v2.grid-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

{{-- Add Student Tcsi Credit Offer Modal --}}
<div id="addTcsiCreditOfferModal" class="wizardModal" style="display: none">
    <form id="addTcsiCreditOfferForm"></form>
</div>

{{-- Edit Student Tcsi Credit Offer Modal --}}
<div id="editTcsiCreditOfferModal" class="wizardModal" style="display: none">
    <form id="editTcsiCreditOfferForm"></form>
</div>

{{-- Delete Student Tcsi Credit Offer Modal --}}
<div id="deleteTcsiCreditOfferModal"></div>

{{-- Tcsi Credit Offer List Action Template --}}
<script id="studTcsiCreditOfferActionTemplate" type="text/html">
    <div class="action-menu z-10 w-36">
        <div class="space-y-1 px-1 py-2">
            <x-v2.dropdown-menuitem class="editTcsiCreditOfferBtn" data-id="#= id #">
                <img src="{{ asset('v2/img/edit.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700">Edit</span>
            </x-v2.dropdown-menuitem>
            <x-v2.dropdown-menuitem class="deleteTcsiCreditOfferBtn" data-id="#= id #">
                <x-v2.icons name="icon-delete" width="16" height="16" class="text-red-500" />
                <span class="text-xs leading-5 text-red-500">Delete</span>
            </x-v2.dropdown-menuitem>
        </div>
    </div>
</script>

<script id="disabilityFormTemplate" type="text/html">
    <div class="w-full grid grid-cols-5 gap-2 px-4 border-b">
        <div class="flex items-center w-full col-span-2 border-r py-2">
            <label class="text-xs font-medium leading-5 text-gray-500">Disability</label>
        </div>
        <div class="flex items-center w-full col-span-1 border-r py-2">
            <label class="text-xs font-medium leading-5 text-gray-500">From Date</label>
        </div>
        <div class="flex items-center w-full col-span-1 py-2">
            <label class="text-xs font-medium leading-5 text-gray-500">To Date</label>
        </div>
    </div>

    # console.log(data) #
    # for(var i=0; i < data.length; i++){ # <div class="w-full grid grid-cols-5 gap-2 py-2 px-4">
        <div class="flex items-center w-full col-span-2">
            <input class="k-checkbox" data-id="#: data[i].Id #" id="name_#: data[i].Id #" type="checkbox"
                name="name[#: data[i].Id #]">
            <label class="ml-2 text-sm font-medium leading-5 text-gray-700">#: data[i].Name #</label>
        </div>
        <div class="flex flex-col  w-full col-span-1">
            <input type="date" data-id="#: data[i].Id #" id="start_#: data[i].Id #" name="start[#: data[i].Id #]"
                placeholder="Select Date" class="datePickerDisabilityInfo">
        </div>
        <div class="flex flex-col w-full col-span-1">
            <input type="date" data-id="#: data[i].Id #" id="end_#: data[i].Id #" name="end[#: data[i].Id #]"
                placeholder="Select Date" class="datePickerDisabilityInfo">
        </div>
        </div>
        <div class="self-stretch h-px bg-gray-200">

        </div>
        # } #
</script>



<div id="viewCourseVariantModal" style="display: none">
    <div class="inline-flex flex-col items-start justify-start bg-gray-50 h-full w-full space-y-6">
        <div class="studentCourseDetailHeader w-full"></div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full noDataCourseVariant hidden mb-5">
            <div class="flex flex-col space-y-6 items-start justify-between w-full">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-700">Manage Student Course Variants</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                </div>
                <div class="flex flex-col space-y-6 p-6 items-center justify-center bg-gray-50 rounded-lg w-full">
                    <div class="flex flex-col space-y-4 items-center justify-start">
                        <img src="{{ asset('v2/img/agent-commission.svg') }}" class="" alt="" />
                        <p class="text-sm leading-5 text-gray-700">You have not added student course variant.</p>
                    </div>
                    <button class="addCourseVariantBtn btn-primary">
                        <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                        <p class="text-sm font-medium leading-4 text-white uppercase">Add Course Variant</p>
                    </button>
                </div>
            </div>
        </div>
        <div class="inline-flex space-x-2 items-end justify-end w-full mb-5 courseVariantDetails hidden">
            <button class="addCourseVariantBtn btn-primary">
                <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                <p class="text-sm font-medium leading-4 text-white uppercase">Add Course Variant</p>
            </button>
            <button type="button" title="Course Variant log" data-page="1"
                class="courseVariantLog inline-flex h-8 items-center justify-center p-2 rounded-lg border border-gray-300 bg-white hover:shadow">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.08813 4C1.09498 4.00014 1.10184 4.00014 1.10868 4H3.49909C3.77524 4 3.99909 3.77614 3.99909 3.5C3.99909 3.22386 3.77524 3 3.49909 3H1.99863C2.91128 1.78495 4.36382 1 5.99909 1C8.76052 1 10.9991 3.23858 10.9991 6C10.9991 8.76142 8.76052 11 5.99909 11C3.39117 11 1.2491 9.003 1.0195 6.45512C0.994714 6.1801 0.751668 5.97723 0.47664 6.00202C0.201613 6.0268 -0.00125024 6.26985 0.0235339 6.54488C0.299161 9.6035 2.86898 12 5.99909 12C9.3128 12 11.9991 9.31371 11.9991 6C11.9991 2.68629 9.3128 0 5.99909 0C4.20656 0 2.59815 0.786128 1.49909 2.03138V1C1.49909 0.723858 1.27524 0.5 0.999094 0.5C0.722952 0.5 0.499094 0.723858 0.499094 1V3.5C0.499094 3.77614 0.722952 4 0.999094 4H1.08813ZM5.49909 3C5.77524 3 5.99909 3.22386 5.99909 3.5V6H7.49909C7.77524 6 7.99909 6.22386 7.99909 6.5C7.99909 6.77614 7.77524 7 7.49909 7H5.49909C5.22295 7 4.99909 6.77614 4.99909 6.5V3.5C4.99909 3.22386 5.22295 3 5.49909 3Z"
                        fill="#9CA3AF" />
                </svg>
            </button>
        </div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white shadow border rounded-lg border-gray-200 w-full courseVariantDetails hidden">
            <div class="flex space-x-2 items-start justify-between w-full pb-6 ">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-700">Course Variant History</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                </div>
            </div>
            <div class="flex flex-col space-y-4 items-start justify-start w-full">
                <div class="relative w-full">
                    <div id="courseVariantDataLoader" class="">
                        <div class="space-y-4">
                            <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm animate-pulse">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-2">
                                        <div class="h-6 w-20 bg-gray-200 rounded-full"></div>
                                        <div class="h-4 w-24 bg-gray-200 rounded"></div>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <div class="h-8 w-8 bg-gray-200 rounded"></div>
                                        <div class="h-8 w-8 bg-gray-200 rounded"></div>
                                        <div class="h-8 w-8 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    <div class="h-4 w-full bg-gray-200 rounded"></div>
                                    <div class="h-4 w-3/4 bg-gray-200 rounded"></div>
                                    <div class="h-4 w-1/2 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="studentStatusCardsContainer" class="w-full space-y-4"></div>

                    <!-- Pagination Controls -->
                    <div id="courseVariantPagination"
                        class="flex items-center justify-between mt-6 pt-4 border-t border-gray-200 hidden">
                        <div class="flex items-center text-sm text-gray-500">
                            <span id="paginationInfo">Showing 1 to 15 of 0 results</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button id="prevPageBtn"
                                class="flex px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 19l-7-7 7-7"></path>
                                </svg>
                                Previous
                            </button>
                            <div id="pageNumbers" class="flex items-center space-x-1">
                                <!-- Page numbers will be dynamically generated -->
                            </div>
                            <button id="nextPageBtn"
                                class="flex px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                                Next
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script id="studentStatusCardTemplate" type="text/html">
    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
        <!-- Header with status transition and actions -->
        <div class="flex items-start justify-between mb-4">
            <div class="flex space-x-2 items-center justify-start">
                <div class="flex items-center justify-center rounded">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                        # if (previous_course_status === 'Deferred') { #
                            bg-yellow-100 text-yellow-800
                        # } else if (previous_course_status === 'Suspended') { #
                            bg-orange-100 text-orange-800
                        # } else if (previous_course_status === 'Withdrawn') { #
                            bg-pink-100 text-pink-800
                        # } else if (previous_course_status === 'Cancelled') { #
                            bg-red-100 text-red-800
                        # } else { #
                            bg-green-100 text-green-800
                        # } #">
                        #= previous_course_status ? previous_course_status.charAt(0).toUpperCase() +
                        previous_course_status.slice(1) : 'Unknown' #
                    </span>
                </div>
                <img class="w-6 h-4 rounded-full" src="{{ asset('v2/img/left_arrow_history.svg') }}" />
                <div class="flex items-center justify-center rounded">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                        # if (course_status === 'Deferred') { #
                            bg-yellow-100 text-yellow-800
                        # } else if (course_status === 'Suspended') { #
                            bg-orange-100 text-orange-800
                        # } else if (course_status === 'Withdrawn') { #
                            bg-pink-100 text-pink-800
                        # } else if (course_status === 'Cancelled') { #
                            bg-red-100 text-red-800
                        # } #">
                        #= course_status ? course_status.charAt(0).toUpperCase() + course_status.slice(1) : 'Unknown' #
                    </span>
                </div>
                <span class="text-sm text-gray-500">
                    #= created_at ? kendo.toString(new Date(created_at), 'dd MMM yyyy') : '' #
                </span>
            </div>
            <div class="flex items-center space-x-1">
                <button type="button" data-id="#=id#" data-status="#=course_status#"
                    class="editCourseVariantBtn p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="Edit Course Variant">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                </button>
                <button type="button" data-id="#=id#"
                    class="printCourseVariantBtn p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="Print Course Variant">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                        </path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Detailed Information Section -->
        <div class="space-y-3 text-sm">
            <!-- Deferred/Suspended Status Details -->
            # if (course_status === 'Deferred' || course_status === 'Suspended') { #
            # if (defer_from_date || defer_to_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600 font-medium">#= course_status # Period:</span>
                <span class="text-gray-900">
                    #= defer_from_date ? kendo.toString(new Date(defer_from_date), 'dd MMM yyyy') : 'N/A' # -
                    #= defer_to_date ? kendo.toString(new Date(defer_to_date), 'dd MMM yyyy') : 'N/A' #
                </span>
            </div>
            # } #

            # if (original_course_end_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Original Course End Date:</span>
                <span class="text-gray-900">#= kendo.toString(new Date(original_course_end_date), 'dd MMM yyyy')
                    #</span>
            </div>
            # } #

            # if (is_course_end_date_impact === '1' || is_course_end_date_impact === 1) { #
            # if (new_course_end_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600">New Course End Date:</span>
                <span class="text-gray-900 font-medium text-blue-600">#= kendo.toString(new Date(new_course_end_date),
                    'dd MMM yyyy') #</span>
            </div>
            # } #
            # } #

            # if (student_location) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Student Location:</span>
                <span class="text-gray-900">#= student_location #</span>
            </div>
            # } #

            # if (next_class_start_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Next Class Start Date:</span>
                <span class="text-gray-900">#= kendo.toString(new Date(next_class_start_date), 'dd MMM yyyy') #</span>
            </div>
            # } #
            # } #

            <!-- Withdrawal Status Details -->
            # if (course_status === 'Withdrawn') { #
            # if (withdrawal_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Withdrawal Date:</span>
                <span class="text-gray-900">#= kendo.toString(new Date(withdrawal_date), 'dd MMM yyyy') #</span>
            </div>
            # } #

            # if (is_approve === '1' || is_approve === 1) { #
            # if (approved_by) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Approved By:</span>
                <span class="text-gray-900">#= approved_by #</span>
            </div>
            # } #

            # if (approved_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Approved Date:</span>
                <span class="text-gray-900">#= kendo.toString(new Date(approved_date), 'dd MMM yyyy') #</span>
            </div>
            # } #
            # } #

            # if (is_student_release === '1' || is_student_release === 1) { #
            # if (is_study_completed === '1' || is_study_completed === 1) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Study Completed (6+ months):</span>
                <span class="text-green-600 font-medium">Yes</span>
            </div>
            # } else if (is_granting_release === '1' || is_granting_release === 1) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Release Granted:</span>
                <span class="text-green-600 font-medium">Yes</span>
            </div>
            # if (release_approved_by) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Release Approved By:</span>
                <span class="text-gray-900">#= release_approved_by #</span>
            </div>
            # } #

            # if (release_comment) { #
            <div class="flex flex-col space-y-1">
                <span class="text-gray-600">Release Comment:</span>
                <span class="text-gray-900 text-xs bg-gray-50 p-2 rounded">#= release_comment #</span>
            </div>
            # } #
            # } else { #
            <div class="flex justify-between">
                <span class="text-gray-600">Release Status:</span>
                <span class="text-red-600 font-medium">Not Granted</span>
            </div>
            # } #
            # } #
            # } #

            <!-- Cancellation Status Details -->
            # if (course_status === 'Cancelled') { #
            # if (reason_for_cancellation) { #
            <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Cancellation Reason:</span>
                <span class="text-gray-900">
                    #
                    var reasonText = reason_for_cancellation;
                    if (typeof arrCourseCancelReason !== 'undefined' && arrCourseCancelReason.length > 0) {
                    var foundReason = arrCourseCancelReason.find(function(item) {
                    return item.Id == reason_for_cancellation;
                    });
                    if (foundReason) {
                    reasonText = foundReason.Name;
                    }
                    }
                    #
                    #= reasonText #
                </span>
            </div>
            # } #

            # if (cancellation_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Cancellation Date:</span>
                <span class="text-gray-900">#= kendo.toString(new Date(cancellation_date), 'dd MMM yyyy') #</span>
            </div>
            # } #

            # if (last_attended_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Last Attended Date:</span>
                <span class="text-gray-900">#= kendo.toString(new Date(last_attended_date), 'dd MMM yyyy') #</span>
            </div>
            # } #

            # if (cancellation_comment) { #
            <div class="flex flex-col space-y-1">
                <span class="text-gray-600">Comment:</span>
                <span class="text-gray-900 text-xs bg-gray-50 p-2 rounded">#= cancellation_comment #</span>
            </div>
            # } #

            # if (is_notice_period_completed === '1' || is_notice_period_completed === 1) { #
            <div class="flex justify-between">
                <span class="text-gray-600">20-Day Notice Period:</span>
                <span class="text-green-600 font-medium">Completed</span>
            </div>
            # } #

            # if (is_appeal_internally === '1' || is_appeal_internally === 1) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Internal Appeal:</span>
                <span class="text-blue-600 font-medium">Yes</span>
            </div>
            # } #

            # if (is_approve === '1' || is_approve === 1) { #
            # if (approved_by) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Approved By:</span>
                <span class="text-gray-900">#= approved_by #</span>
            </div>
            # } #

            # if (approved_date) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Approved Date:</span>
                <span class="text-gray-900">#= kendo.toString(new Date(approved_date), 'dd MMM yyyy') #</span>
            </div>
            # } #
            # } #

            # if (is_student_release === '1' || is_student_release === 1) { #
            # if (is_study_completed === '1' || is_study_completed === 1) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Study Completed (6+ months):</span>
                <span class="text-green-600 font-medium">Yes</span>
            </div>
            # } else if (is_granting_release === '1' || is_granting_release === 1) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Release Granted:</span>
                <span class="text-green-600 font-medium">Yes</span>
            </div>
            # if (release_approved_by) { #
            <div class="flex justify-between">
                <span class="text-gray-600">Release Approved By:</span>
                <span class="text-gray-900">#= release_approved_by #</span>
            </div>
            # } #

            # if (release_comment) { #
            <div class="flex flex-col space-y-1">
                <span class="text-gray-600">Release Comment:</span>
                <span class="text-gray-900 text-xs bg-gray-50 p-2 rounded">#= release_comment #</span>
            </div>
            # } #
            # } else { #
            <div class="flex justify-between">
                <span class="text-gray-600">Release Status:</span>
                <span class="text-red-600 font-medium">Not Granted</span>
            </div>
            # } #
            # } #
            # } #

            <!-- Document Information (Common for all statuses) -->
            # if (new_coe_code || new_coe_file || approval_evidence_file) { #
            <div class="border-t pt-3 mt-3">
                <div class="text-gray-600 font-medium mb-2">Document Information:</div>

                # if (new_coe_code) { #
                <div class="flex justify-between">
                    <span class="text-gray-600">CoE Code:</span>
                    <span class="text-gray-900 font-mono text-xs bg-blue-50 px-2 py-1 rounded">#= new_coe_code #</span>
                </div>
                # } #

                # if (new_coe_file) { #
                <div class="flex justify-between">
                    <span class="text-gray-600">CoE Document:</span>
                    # if (download_url) { #
                    <span class="text-blue-600 hover:text-blue-800 cursor-pointer previewBtnNote"
                        data-file-path="#= download_url #" data-preview-id="courseVariantDocumentPreview"
                        title="View CoE Document">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                            </path>
                        </svg>
                        View Document
                    </span>
                    # } else { #
                    <span class="text-red-500 cursor-not-allowed" title="No file attached">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                            </path>
                        </svg>
                        No Document
                    </span>
                    # } #
                </div>
                # } #

                # if (approval_evidence_file) { #
                <div class="flex justify-between">
                    <span class="text-gray-600">Evidence Document:</span>
                    # if (approval_evidence_download_url) { #
                    <span class="text-blue-600 hover:text-blue-800 cursor-pointer previewBtnNote"
                        data-file-path="#= approval_evidence_download_url #"
                        data-preview-id="courseVariantDocumentPreview" title="View Evidence Document">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                            </path>
                        </svg>
                        View Document
                    </span>
                    # } else { #
                    <span class="text-red-500 cursor-not-allowed" title="No file attached">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                            </path>
                        </svg>
                        No Document
                    </span>
                    # } #
                </div>
                # } #
            </div>
            # } #

            <!-- Additional Information -->
            # if (updated_at && updated_at !== created_at) { #
            <div class="border-t pt-2 mt-3">
                <div class="flex justify-between text-xs text-gray-500">
                    <span>Last Updated:</span>
                    <span>#= kendo.toString(new Date(updated_at), 'dd MMM yyyy HH:mm') #</span>
                </div>
            </div>
            # } #
        </div>
    </div>
</script>

<div id="addCourseVariantModal" class="!bg-gray-50" style="display: none;">
    <div class="inline-flex flex-col space-y-6 items-start justify-start bg-gray-50 pb-20 w-full">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div id="studentCourseStatusHtml"></div>
            <form id="addCourseVariantForm" class="w-full"></form>
        </div>
    </div>
</div>

<div id="editCourseVariantModal" style="display: none">
    <div class="inline-flex flex-col space-y-6 items-start justify-start bg-gray-50 pb-20 w-full">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div id="studentCourseStatusHtml"></div>
            <form id="editCourseVariantForm" class="w-full"></form>
        </div>
    </div>
</div>

<div id="courseVariantLogModal" style="display: none;">
    <div class="px-2 py-6">
        <input class="hidden" id="cvLogTabPage" value="1" />
        <div id="courseVariantLogInfo" class="flex flex-col max-h-screen overflow-y-auto">

        </div>
        <div id="courseVariantLogBlankDiv" style="overflow: auto; height: 500px;">
            <div
                class="flex items-center justify-center p-6 bg-blue-50 shadow-sm border rounded-lg border-gray-200 w-full">
                <p class="flex text-sm leading-5 font-medium text-gray-500">No log found</p>
            </div>
        </div>
    </div>
</div>


<div id="studentCardPreviewModal" style="display: none;">
    <div id="studentCardPreviewDiv"></div>
</div>