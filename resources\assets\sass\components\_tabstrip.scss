/* Tab */

.k-tabstrip-items-wrapper,
.k-tabstrip-items-wrapper .k-item.k-state-active,
.k-tabstrip-items-wrapper .k-item.k-state-selected,
.k-tabstrip-items .k-item,
.k-tabstrip > .k-content {
    border: none;
}

.k-tabstrip-items-wrapper .k-item.k-state-active span,
.k-tabstrip-items-wrapper .k-item.k-state-active svg,
.k-tabstrip-items-wrapper .k-item.k-state-selected span {
    color: var(--color-primary-blue-500);
}

.k-tabstrip-items-wrapper .k-item span {
    color: rgb(107 114 128);
}

.k-tabstrip {
    padding: 4px 0px;
}

.k-tabstrip-content,
.k-tabstrip > .k-content {
    padding: 4px 0px;
}

.tw-tabstrip {
    .k-tabstrip-items-wrapper {
        border-bottom: 1px solid var(--color-gray-200);
        padding-inline: 0.5rem;
        .k-tabstrip-items {
            gap: 1.5rem;
            @include responsive(smartphone-portrait) {
                gap: 1rem;
                flex-wrap: nowrap;
                overflow-y: auto;
            }
        }
        .k-item .k-link {
            padding-inline: 0;
            padding-block: 1rem;
            gap: 0.5rem;
        }
    }

    &--compact {
        .k-tabstrip-items-wrapper {
            .k-tabstrip-items {
                gap: 0.5rem;
            }
            .k-item .k-link {
                padding-inline: 0;
            }
        }
    }

    &__hide-mbl-tabs {
        .k-tabstrip-items-wrapper {
            display: none;
            @media (screen(md)) {
                display: block;
            }
        }
    }
}

.k-tabstrip-items {
    &.bottomBorder {
        border-bottom: 1px solid var(--color-gray-200);
    }
}
