#panelbar .k-i-arrow-chevron-up {
    display: none !important;
}

#wizard .k-stepper {
    display: none;
}

.k-wizard-vertical .k-wizard-steps {
    margin-left: 0px;
}

.k-wizard {
    padding: 0px;
}

.k-wizard .k-wizard-step {
    padding: 0px;
}

.k-widget * {
    /* box-sizing: border-box !important; */
}

/*#panelbar .k-step .k-step-link .k-step-indicator {
    height: 20px; width: 20px;
}*/
/*#panelbar .k-step-list-vertical~.k-progressbar {
    left: 13px;
}*/

.titlebar-sms-modal {
    background: linear-gradient(270deg, #06b6d4 20.35%, #1e93ff 75.64%);
}

/* #addContractModal,
#addContractFundingSourceModal,
#addCourseSiteModal,
#addElearningLinkModal,
#editElearningLinkModal,
#addBankInfoModal,
#editBankInfoModal,
#generateReportModal,
#generateReportLetterModal,
#addHolidayModal,
#editHolidayModal,
#addCourseTemplateModal,
#editCourseTemplateModal,
#addPasswordModal,
#addCoursesIntakeDateModal,
#addCourseUpfrontFeeModal,
#editCourseUpfrontFeeModal {
    padding: 0px;
} */

#addCoursesIntakeDateModal .k-form-layout,
#addCourseUpfrontFeeModal .k-form-layout,
#editCourseUpfrontFeeModal .k-form-layout {
    overflow-y: auto !important;
    max-height: 400px !important;
}

#generateReportLetterModal .letterReport .k-dropdown {
    width: 50%;
}

#updateReportModal .k-textbox > input {
    padding: 6px !important;
}

.k-dialog .k-dialog-titlebar {
    background: var(--color-primary-blue-500) !important;
}

.k-i-delete::before {
    content: close-quote !important;
    background-image: url('../../img/delete-new-gray.svg') !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.75rem;
}

.k-i-edit::before {
    content: close-quote !important;
    background-image: url('../../img/edit-gray-pencil.svg') !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.75rem;
}

.k-i-structure::before {
    content: close-quote !important;
    background-image: url('../../img/template-structure.svg') !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.75rem;
}

.action-div,
.k-form-field-wrap .k-checkbox-item {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.k-form-field-wrap .k-textbox {
    border-width: 1px;
    border-radius: 0.5rem;
    height: 36px;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.k-form-field-wrap .k-textbox:focus {
    border: 1px solid var(--color-primary-blue-500);
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

.k-numerictextbox {
    border-width: 1px;
    border-radius: 0.5rem;
    height: 36px;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.k-numeric-wrap {
    height: 36px;
}

.k-numeric-wrap .k-input {
    border-radius: 0.5rem;
}

.k-numeric-wrap .k-input:focus-within {
    box-shadow: none;
}

/* [type='text']:focus, [type='password']:focus {
    border-color: #2ea2f8!important;
    box-shadow: none;
    color: #354052!important;
    outline: 0;
    border-width: 1px;
} */

.campus-li-detail,
.k-listview,
.room-li-detail,
.venue-li-detail {
    background-color: transparent !important;
}

.k-tabstrip-items-wrapper .k-item.k-state-active {
    background-color: transparent !important;
}

.k-tabstrip-content.k-state-focused,
.k-tabstrip-content:focus,
.k-tabstrip > .k-content.k-state-focused,
.k-tabstrip > .k-content:focus {
    outline-color: transparent !important;
}

.sortable.open .dropdown-menu.dm-toggle {
    /* top: inherit !important;
    left: inherit !important; */
    display: block;
}

.k-menu-expand-arrow.k-i-arrow-60-down {
    display: none !important;
}

.k-icon-add {
    color: rgb(24, 144, 255);
}

.k-icon-edit {
    color: rgb(55 65 81);
}

.k-i-more-vertical {
    color: #9ca3af;
}

.k-icon-delete {
    color: rgb(185 28 28);
}

/* .k-icon-plus {
    color: white;
} */
.k-icon-gray {
    color: #9ca3af;
    /*color: gray;*/
}

.k-icon-blue {
    color: blue;
}

/* Start at 04-06-2022 */

/* ===== FORM WIZARD ===== */
.k-wizard .k-wizard-step.k-state-focused,
.k-wizard .k-wizard-step:focus {
    outline-width: 0px;
    outline-style: dotted;
    outline-offset: -1px;
    outline: none;
}

#wizard .k-wizard-step.general_info .k-wizard-buttons-right {
    justify-content: flex-end;
}

#wizard .k-wizard-buttons {
    position: fixed;
    bottom: 0;
    /* left: 0; */
    width: 80%;
    right: 0;
    background: #fff;
    padding: 15px;
}

#wizard .k-wizard-buttons-right {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding-left: 18%;
}

#wizard .k-wizard-buttons-right button:first-child {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 16px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
}

#wizard .k-wizard-buttons-right button:last-child {
    font-weight: 500;
    font-size: 1rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 9px 36px;
    color: #fff;
    background: var(--color-primary-blue-500);
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    border: none;
}

#wizard .k-wizard-buttons-right button:last-child:hover,
#wizard .k-wizard-buttons-right button:first-child:hover {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

#wizard .k-wizard-buttons-right button:first-child:focus {
    --tw-ring-offset-color: #ffffff;
    --tw-ring-offset-width: 2px;
    --tw-ring-opacity: 1;
    --tw-ring-color: rgba(156, 163, 175, var(--tw-ring-opacity));
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
        var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
        var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

#wizard .k-wizard-buttons-right button:last-child:focus {
    --tw-ring-offset-color: #ffffff;
    --tw-ring-offset-width: 2px;
    --tw-ring-opacity: 1;
    --tw-ring-color: rgba(9, 109, 217, var(--tw-ring-opacity));
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
        var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
        var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.k-label.k-form-label {
    color: #374151;
    /* font-size: 0.875rem; */
    font-weight: 500;
    margin-bottom: 0.25rem;
}

/* .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 5px 4px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
} */

.defaultHide .k-dropdown-wrap.k-state-hover {
    border: 1px solid var(--color-primary-blue-500);
}

.k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

.k-pager-sizes .k-widget.k-dropdown {
    border: 0px;
    background-color: transparent;
}

.k-switch {
    width: 56px;
    border: 0px;
    padding: 6px 0px;
    background: #fff;
    display: flex;
    align-items: center;
}

.k-switch-handle {
    background: #fff;
    width: 25px;
    height: 27px;
    position: relative;
    box-shadow:
        0px 1px 3px rgba(0, 0, 0, 0.1),
        0px 1px 2px rgba(0, 0, 0, 0.06);
    top: -3px;
}

.k-switch-off .k-switch-handle {
    left: -1px;
}

.k-switch .k-switch-container {
    padding: 0;
    width: 0;
    height: 25px;
}

.k-switch.k-widget.k-switch-off .k-switch-container {
    background-color: rgba(209, 213, 219, 1) !important;
}

.k-switch.k-switch-on .k-switch-container {
    background-color: var(--color-primary-blue-500) !important;
}

.k-switch-label-on,
.k-switch-label-off {
    display: none;
}

.k-invalid {
    border-radius: 0.5rem;
    border: 1px solid red;
}

.k-textbox.k-invalid {
    border: 1px solid red;
}

.k-form-error.k-invalid-msg {
    display: none;
}

[type='file'] {
    height: 0;
    overflow: hidden;
    width: 0;
}

[type='file'] + label {
    padding: 7px 11px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    font-size: 12px;
    line-height: 16px;
    color: #374151;
    font-weight: 500;
    cursor: pointer;
}

.gridInfo .k-pager-sizes {
    border: 0px;
}

.k-checkbox:checked:focus {
    box-shadow: none;
}

.k-form-field {
    margin-bottom: 0px;
}

.k-listview {
    border: 0px;
}

/* ===== SIDEBAR CSS ==== */

#stepper .k-step-list-vertical ~ .k-progressbar {
    left: 23px;
    height: 460px !important;
    margin-top: -478px !important;
}

/* === MAIN INDICATORS START ==== */

#stepper ol li:nth-child(1),
#stepper ol li:nth-child(7),
#stepper ol li:nth-child(8),
#stepper ol li:nth-child(13),
#stepper ol li:nth-child(14) {
    height: auto;
    border-radius: 4px;
    padding: 4px 8px 4px 4px;
    min-height: auto;
}

#stepper ol li:nth-child(1) .k-step-indicator,
#stepper ol li:nth-child(7) .k-step-indicator,
#stepper ol li:nth-child(8) .k-step-indicator,
#stepper ol li:nth-child(13) .k-step-indicator,
#stepper ol li:nth-child(14) .k-step-indicator {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
}

#stepper ol li:nth-child(1) .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(7) .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(8) .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(13) .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(14) .k-step-indicator .k-step-indicator-icon {
    color: #9ca3af;
}

#stepper ol li:nth-child(1) .k-step-label .k-step-text,
#stepper ol li:nth-child(7) .k-step-label .k-step-text,
#stepper ol li:nth-child(8) .k-step-label .k-step-text,
#stepper ol li:nth-child(13) .k-step-label .k-step-text,
#stepper ol li:nth-child(14) .k-step-label .k-step-text {
    color: #6b7280;
}

#stepper ol li:nth-child(1).k-step-first.k-step-disabled.k-step-success.k-step-done,
#stepper ol li:nth-child(1).k-step-current.k-step-focus,
#stepper ol li:nth-child(7).k-step-current.k-step-focus,
#stepper ol li:nth-child(8).k-step-current.k-step-focus,
#stepper ol li:nth-child(13).k-step-current.k-step-focus,
#stepper ol li:nth-child(14).k-step-current.k-step-focus {
    background: #e6f7ff;
    border: 1px solid var(--color-primary-blue-500);
    border-radius: 4px;
}

#stepper
    ol
    li:nth-child(1).k-step-first.k-step-disabled.k-step-success.k-step-done
    .k-step-indicator,
#stepper ol li:nth-child(1).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(7).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(8).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(13).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(14).k-step-current.k-step-focus .k-step-indicator {
    background-color: var(--color-primary-blue-500) !important;
    border: 2px solid #fff !important;
    outline: var(--color-primary-blue-500) solid 2px !important;
}

#stepper ol li:nth-child(1).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(7).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(8).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(13).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(14).k-step-current.k-step-focus .k-step-indicator::after {
    display: none;
}

#stepper ol li:nth-child(1).k-step-current.k-step-focus .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(7).k-step-current.k-step-focus .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(8).k-step-current.k-step-focus .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(13).k-step-current.k-step-focus .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(14).k-step-current.k-step-focus .k-step-indicator .k-step-indicator-icon {
    color: #fff;
}

#stepper ol li:nth-child(1).k-step-current.k-step-focus .k-step-label .k-step-text,
#stepper ol li:nth-child(7).k-step-current.k-step-focus .k-step-label .k-step-text,
#stepper ol li:nth-child(8).k-step-current.k-step-focus .k-step-label .k-step-text,
#stepper ol li:nth-child(13).k-step-current.k-step-focus .k-step-label .k-step-text,
#stepper ol li:nth-child(14).k-step-current.k-step-focus .k-step-label .k-step-text {
    color: var(--color-primary-blue-500);
}

#stepper ol li:nth-child(1).k-step-success.k-step-done .k-step-indicator,
#stepper ol li:nth-child(7).k-step-success.k-step-done .k-step-indicator,
#stepper ol li:nth-child(8).k-step-success.k-step-done .k-step-indicator,
#stepper ol li:nth-child(13).k-step-success.k-step-done .k-step-indicator,
#stepper ol li:nth-child(14).k-step-success.k-step-done .k-step-indicator {
    background-color: var(--color-primary-blue-500) !important;
    border: 2px solid transparent !important;
    outline: var(--color-primary-blue-500) solid transparent !important;
}

#stepper ol li:nth-child(1).k-step-success.k-step-done .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(7).k-step-success.k-step-done .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(8).k-step-success.k-step-done .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(13).k-step-success.k-step-done .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(14).k-step-success.k-step-done .k-step-indicator .k-step-indicator-icon {
    color: #fff;
}

#stepper ol li:nth-child(1).k-step-success.k-step-done .k-step-label .k-step-text,
#stepper ol li:nth-child(7).k-step-success.k-step-done .k-step-label .k-step-text,
#stepper ol li:nth-child(8).k-step-success.k-step-done .k-step-label .k-step-text,
#stepper ol li:nth-child(13).k-step-success.k-step-done .k-step-label .k-step-text,
#stepper ol li:nth-child(14).k-step-success.k-step-done .k-step-label .k-step-text {
    color: #374151;
}

/* === MAIN INDICATORS ENDS ==== */

/* === SUB INDICATORS START ==== */
#stepper ol li:nth-child(2),
#stepper ol li:nth-child(3),
#stepper ol li:nth-child(4),
#stepper ol li:nth-child(5),
#stepper ol li:nth-child(6),
#stepper ol li:nth-child(9),
#stepper ol li:nth-child(10),
#stepper ol li:nth-child(11),
#stepper ol li:nth-child(12) {
    min-height: auto;
    height: auto;
    padding: 4px 0px 4px 30px;
}

#stepper ol li:nth-child(2) .k-step-link,
#stepper ol li:nth-child(3) .k-step-link,
#stepper ol li:nth-child(4) .k-step-link,
#stepper ol li:nth-child(5) .k-step-link,
#stepper ol li:nth-child(6) .k-step-link,
#stepper ol li:nth-child(9) .k-step-link,
#stepper ol li:nth-child(10) .k-step-link,
#stepper ol li:nth-child(11) .k-step-link,
#stepper ol li:nth-child(12) .k-step-link {
    padding-left: 10px;
}

#stepper ol li:nth-child(2) .k-step-text,
#stepper ol li:nth-child(3) .k-step-text,
#stepper ol li:nth-child(4) .k-step-text,
#stepper ol li:nth-child(5) .k-step-text,
#stepper ol li:nth-child(6) .k-step-text,
#stepper ol li:nth-child(9) .k-step-text,
#stepper ol li:nth-child(10) .k-step-text,
#stepper ol li:nth-child(11) .k-step-text,
#stepper ol li:nth-child(12) .k-step-text {
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #6b7280;
}

#stepper ol li:nth-child(2) .k-step-indicator-text,
#stepper ol li:nth-child(3) .k-step-indicator-text,
#stepper ol li:nth-child(4) .k-step-indicator-text,
#stepper ol li:nth-child(5) .k-step-indicator-text,
#stepper ol li:nth-child(6) .k-step-indicator-text,
#stepper ol li:nth-child(9) .k-step-indicator-text,
#stepper ol li:nth-child(10) .k-step-indicator-text,
#stepper ol li:nth-child(11) .k-step-indicator-text,
#stepper ol li:nth-child(12) .k-step-indicator-text {
    display: none;
}

#stepper ol li:nth-child(2) .k-step-indicator,
#stepper ol li:nth-child(3) .k-step-indicator,
#stepper ol li:nth-child(4) .k-step-indicator,
#stepper ol li:nth-child(5) .k-step-indicator,
#stepper ol li:nth-child(6) .k-step-indicator,
#stepper ol li:nth-child(9) .k-step-indicator,
#stepper ol li:nth-child(10) .k-step-indicator,
#stepper ol li:nth-child(11) .k-step-indicator,
#stepper ol li:nth-child(12) .k-step-indicator {
    width: 8px;
    height: 8px;
    background: #d1d5db;
    border: none;
}

#stepper ol li:nth-child(2).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(3).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(4).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(5).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(6).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(9).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(10).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(11).k-step-done.k-step-success .k-step-link,
#stepper ol li:nth-child(12).k-step-done.k-step-success .k-step-link {
    padding-left: 7px;
}

#stepper ol li:nth-child(2).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(3).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(4).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(5).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(6).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(9).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(10).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(11).k-step-current.k-step-focus .k-step-indicator,
#stepper ol li:nth-child(12).k-step-current.k-step-focus .k-step-indicator {
    background: var(--color-primary-blue-500);
    box-shadow: 0px 0px 0px 5px #91d5ff;
}

.k-stepper .k-step-current:hover .k-step-indicator,
.k-stepper .k-step-current .k-step-indicator,
.k-stepper .k-step:hover .k-step-indicator,
.k-stepper .k-step-current .k-step-indicator {
    /* background-color: var(--color-primary-blue-500) !important; */
}

.k-stepper .k-step:hover .k-step-indicator .k-step-indicator-icon {
    color: #fff !important;
}

.k-stepper .k-step:hover .k-step-label,
.k-stepper .k-step-current .k-step-label,
.k-stepper .k-step-focus .k-step-label {
    color: #6b7280 !important;
    font-weight: 400 !important;
}

#stepper ol li:nth-child(2).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(3).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(4).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(5).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(6).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(9).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(10).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(11).k-step-current.k-step-focus .k-step-indicator::after,
#stepper ol li:nth-child(12).k-step-current.k-step-focus .k-step-indicator::after {
    display: none;
}

#stepper ol li:nth-child(2).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(3).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(4).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(5).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(6).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(9).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(10).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(11).k-step-current.k-step-focus .k-step-text,
#stepper ol li:nth-child(12).k-step-current.k-step-focus .k-step-text {
    color: var(--color-primary-blue-500);
}

#stepper ol li:nth-child(2).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(3).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(4).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(5).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(6).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(9).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(10).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(11).k-step-done.k-step-success .k-step-indicator,
#stepper ol li:nth-child(12).k-step-done.k-step-success .k-step-indicator {
    width: 16px;
    height: 16px;
    background: var(--color-primary-blue-500);
    border: none;
}

#stepper ol li:nth-child(2).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(3).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(4).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(5).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(6).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(9).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(10).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(11).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(12).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon {
    font-size: 9px;
}

#stepper ol li:nth-child(3).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(5).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(9).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon,
#stepper ol li:nth-child(11).k-step-done.k-step-success .k-step-indicator .k-step-indicator-icon {
    top: 1px;
}

.k-step-list-vertical .k-step-indicator + .k-step-label {
    display: inline-block;
}

/* === SUB INDICATORS ENDS ==== */

/* ===== OUTTER LINE ===== */

#stepper .k-progressbar .k-state-selected {
    background-color: var(--color-primary-blue-500);
    border-color: var(--color-primary-blue-500);
    border-width: 2px;
}

/* =======  GENERAL INFORMATION ========*/

.training-organisation span.k-dropdown,
.coursesIntakeDateListDiv .k-dropdown {
    width: 100%;
}

/*
.training-organisation  .k-dropdown-wrap .k-select{
    line-height: 1.5;
} */
.k-list-filter > .k-textbox {
    width: 100% !important;
}

.k-list-filter > .k-icon {
    right: 15px;
}

/* tab srtop horizontal */

.k-tabstrip-items-wrapper.k-hstack {
    border-bottom: none !important;
}

.k-tabstrip-items-wrapper.k-hstack .k-item.k-state-active .k-link {
    border-bottom: 2px solid var(--color-primary-blue-500);
}

.custom-cvr-tab .k-state-active .k-link {
    border-bottom: transparent !important;
}

.k-tabstrip-items-wrapper .k-item .k-link {
    padding: 15px 10px;
}

.custom-cvr-tab {
    padding: 0px !important;
}

.custom-cvr-tab ul li {
    padding: 0 !important;

    .k-link {
        padding: 0 !important;
    }
}

.k-tabstrip-items-wrapper .k-item .k-link span {
    padding: 0px;
}

.custom-cvr-tab .k-tabstrip-items-wrapper {
    padding: 0;
}

.custom-cvr-tab .cvr-ul li.k-state-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 3px;
    background-color: var(--color-primary-blue-500) !important;
    display: none !important;
}

.custom-cvr-tab .cvr-ul li.k-state-active {
    border-bottom: 3px solid var(--color-primary-blue-500) !important;
}

.custom-cvr-tab .cvr-ul li {
    border-bottom: 3px solid #fff !important;
}

.custom-cvr-tab .cvr-ul .k-item {
    /* margin-bottom: -1px !important; */
    border-bottom: 3px solid #fff !important;
}

.custom-cvr-tab .cvr-ul .k-item:hover {
    border-bottom: 3px solid var(--color-primary-blue-500) !important;
}

.custom-cvr-tab .cvr-ul .k-item:active {
    border-bottom-width: 3px !important;
}

.custom-cvr-tab .cvr-ul .k-item:active::after {
    display: none !important;
}

/* ========== upload button  ============ */

.upload-wrapper .k-upload {
    display: none;
}

#display_logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

#signature_logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.college_logo {
    padding: 7px 11px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    font-size: 12px;
    line-height: 16px;
    color: #374151;
    font-weight: 500;
    cursor: pointer;
}

.upload_picture {
    padding: 0.5rem 0.75rem;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    font-size: 0.75rem;
    line-height: 1;
    color: #374151;
    font-weight: normal;
    cursor: pointer;
}

.college_signature {
    padding: 7px 11px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    font-size: 12px;
    line-height: 16px;
    color: #374151;
    font-weight: 500;
    cursor: pointer;
}

/* ============ FILTER MENU ======== */

.k-filter-menu-container .k-filter-menu .k-widget.k-dropdown {
    border: 1px solid #b6b6b6 !important;
    background-color: #e5e7eb !important;
}

.k-filter-menu-container .k-widget.k-dropdown .k-dropdown-wrap {
    border: 1px solid #b6b6b6 !important;
    background-color: #e5e7eb !important;
    border-radius: 0;
    padding: 0px;
}

.k-filter-menu-container .k-dropdown-wrap .k-select {
    display: flex;
    align-items: center;
    justify-content: center;
}

.k-filter-menu .k-action-buttons .k-button {
    border: 1px solid #b6b6b6;
    background-color: #e5e7eb;
}

.k-filter-menu .k-action-buttons .k-button.k-primary {
    background-color: var(--color-primary-blue-500);
    color: white;
}

/* ========  PAGE SIZER ======== */
.k-pager-wrap.k-grid-pager .gridInfo {
    display: flex;
    align-items: center;
}

/* .k-pager-sizes .k-icon {
    position: relative;
    top: 1px;
} */

.titlebar-sms-modal .k-window-action {
    color: #fff;
    opacity: 1;
}

.titlebar-sms-modal .k-window-action .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}

/* .k-window-titlebar .k-window-action {

} */

/* =================== */

/* #addAddressForm .k-widget.k-dropdown .k-dropdown-wrap,
#editAddressForm .k-widget.k-dropdown .k-dropdown-wrap,
#addPostalAddressForm .k-widget.k-dropdown .k-dropdown-wrap,
#editPostalAddressForm .k-widget.k-dropdown .k-dropdown-wrap,
#addVenueDetailsForm .k-widget.k-dropdown .k-dropdown-wrap,
#addVenueAddressForm .k-widget.k-dropdown .k-dropdown-wrap
{
    padding: 3px 13px !important;
} */

/* ================ */
.k-animation-container .k-link.k-menu-link {
    padding: 10px !important;
}

.bottomaction {
    position: fixed;
    bottom: 0px;
    z-index: 1000;
    width: 100%;
    overflow-y: auto;
    box-shadow:
        0px -4px 6px -1px rgb(0 0 0 / 10%),
        0px -2px 4px -1px rgb(0 0 0 / 6%);
}

/* k-notification */

.k-notification {
    border-radius: 8px;
    padding: 0px 0px;
    border-width: 0px;
}

.k-listview-content {
    height: auto !important;
}

/* radio button */

.k-radio-item .k-radio[type='radio']:checked {
    border-color: rgba(24, 144, 255, 1) !important;
    border-radius: 0.5rem !important;
    /* background-image: none !important; */
    background-color: var(--color-primary-blue-500) !important;
}

.k-radio-item .k-radio[type='radio']:checked:focus {
    outline: 2px solid var(--color-primary-blue-500);
    outline-offset: 2px;
}

.k-radio.k-checked.k-state-focus,
.k-radio:checked:focus {
    box-shadow: none;
}

.k-radio-item .k-radio {
    margin-right: 5px !important;
}

/* hover */

/* #general_info input:hover, #training_identify input:hover, #current_college_address input:hover,
#inserted_college_address input:hover, #addBankDetailsForm input:hover, #editBankDetailsForm input:hover,
#addEnrollmentFeesForm input:hover, #editEnrollmentFeesForm input:hover, #addedServicesFeeForm input:hover,
#editServicesFeeForm input:hover, #addAgentStatusForm input:hover, #editAgentStatusForm input:hover,
#addCountryForm input:hover, #editCountryForm input:hover, #addLanguageForm input:hover,
#editLanguageForm input:hover, #addResultGradeForm input:hover, #editResultGradeForm input:hover,
#addSectionForm input:hover, #addOfferDocumentForm input:hover, #editOfferDocumentForm input:hover,
#addAgentDocumentForm input:hover, #editAgentDocumentForm input:hover, #addOfferTrackingStatusForm input:hover,
#editOfferTrackingStatusForm input:hover, #addCustomChecklistForm input:hover,#editCustomChecklistForm input:hover,
#save_smtp_info input:hover, #testSmtpConnectionModalForm input:hover, #addCampusForm input:hover,
#addVenueAddressForm input:hover, #addVenueDetailsForm input:hover, #editVenueAddressForm input:hover,
#addRoomsForm input:hover, #editRoomsForm input:hover, #addGteDocumentForm input:hover,
#editGteDocumentForm input:hover, #addPlacementProviderForm input:hover, #editPlacementProviderForm input:hover,
#addInterventionStrategyForm input:hover, #dueDateSetupForm input:hover {
    border: 1px solid var(--color-primary-blue-500) !important;
    box-shadow: 0 4px 6px -1px rgb(24 144 255 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}  */

/* select:hover {
	border: 1px solid var(--color-primary-blue-500) !important;
    box-shadow: 0 4px 6px -1px rgb(24 144 255 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
} */

.k-i-arrow-60-up::before {
    transform: rotate(180deg);
    content: close-quote !important;
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-i-arrow-60-down::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-menu-expand-arrow.k-i-arrow-60-right {
    display: none;
}

/* .k-i-arrow-60-left::before{
    content:close-quote !important;
    background-image: url("../../img/arrow-down.svg");
    background-position: center;
    background-repeat: no-repeat;
} */

/* .k-i-arrow-60-right::before{
    content:close-quote !important;
    background-image: url("../../img/arrow-down.svg");
    background-position: center;
    background-repeat: no-repeat;
} */

span.k-state-active .k-i-arrow-60-down::before {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-popup .k-list .k-state-hover {
    background-color: #e5e7eb;
    cursor: pointer;
}

/* .k-grid-content {
    min-height: 90px;
} */

.k-list-container {
    font-size: 14px !important;
}

.cusInput:focus {
    border: 1px solid var(--color-primary-blue-500);
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

/* loader */
.maincontainer {
    position: relative;
    /* z-index: 8; */
}

span.k-loading-text {
    position: unset !important;
}

#wizard1 .k-loading-mask {
    background-color: #f1f5f9;
}

#wizard1 {
    position: relative;
}

/* filter input */

.k-filter-menu .k-textbox,
.k-filter-menu .k-picker-wrap {
    border: 1px solid #b6b6b6;
}

/* //filter dropdwon */
.k-filter-menu .k-dropdown,
.k-action-buttons .k-button {
    border: 1px solid #b6b6b6;
    background-color: #e5e7eb;
}

/*
#globalSearchText[type='text']:focus, [type='password']:focus {
    border-color: #ffffff!important;
} */
.pac-container.pac-logo.hdpi {
    z-index: 99999;
}

.pac-container {
    z-index: 10005 !important;
}

.k-form-layout .k-checkbox-item .k-checkbox-label {
    margin-left: 6px !important;
}

/*enroll course switch css*/
.k-switch {
    width: 44px;
    height: auto;
}

.k-switch span.k-switch-handle {
    background-color: white;
    width: 1.25rem;
    height: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
}

.customSwitchButton .k-switch span.k-switch.k-switch-on {
    background-color: var(--color-primary-blue-500);
}

.customSwitchButton .k-switch .k-switch-container {
    border: 2px;
}

.customSwitchButton .k-switch span.k-switch.k-switch-off {
    background-color: #e5e7eb;
}

.customSwitchButton .k-switch span.k-switch-label-on,
span.k-switch-label-off {
    display: none;
}

.k-switch-on .k-switch-handle {
    left: calc(100% - 1.35rem);
}

.k-switch-off .k-switch-handle {
    left: 0.1rem;
}

.customSwitchButton .k-switch-container {
    padding-block: 2.5px;
}

.alphabet-only {
    padding: 7px;
    width: 100%;
    border-width: 1px;
    border-radius: 0.5rem;
    height: 2.25rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.queueProcessBar .k-state-selected {
    background-color: #10b981;
    border-color: #10b981;
    color: white;
}

.queueProcessBar {
    height: 15px;
}

#viewDetailModal {
    padding: 0px;
}
