<script id="StudentInfoTemplate" type="text/html">
    <div class="inline-flex space-x-1 items-center justify-between w-full">
        <div class="flex space-x-2 items-center justify-start">
            {{-- <div class="w-8 h-8 rounded-full">
                <img class="w-full flex-1 rounded-full" src="{{ $studentDetails[0]->profile_pic }}" />
            </div> --}}
            # if (sDetail.profile_pic == '') { let name = sDetail.student_name.toUpperCase().split(/\s+/); let shortName
            = name[0].charAt(0) + name[1].charAt(0); #
            <div class="rounded-full">
                <div class='flex user-profile-pic w-12 h-12 rounded-full bg-primary-blue-500 items-center'>
                    <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName #</span>
                </div>
            </div>
            # } else { #
            <div class="w-12 h-12 rounded-full">
                <img class="w-12 h-12 flex-1 rounded-full" src="#= sDetail.profile_pic #" />
            </div>
            # } #
            <div class="inline-flex flex-col items-start justify-end">
                <p class="text-sm font-bold leading-5 text-gray-900">#= sDetail.student_name #</p>
                <p class="text-xs leading-5 text-gray-400">{{ $studentDetails[0]->generated_stud_id}}</p>
            </div>
        </div>
        <div class="flex space-x-2 items-center justify-start">
            <p class="text-xs leading-5 text-gray-700">#= sDetail.course_name #</p>
            <div class="flex items-center justify-center px-2.5 py-0.5 bg-primary-blue-100 rounded">
                <p class="text-xs leading-5 text-center text-primary-blue-800">#= sDetail.status #</p>
            </div>
        </div>
    </div>
</script>