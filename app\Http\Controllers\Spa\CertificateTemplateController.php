<?php

namespace App\Http\Controllers\Spa;

use App\Http\Controllers\Controller;
use App\Model\v2\CertificateAttribute;
use App\Model\v2\CertificateIdFormate;
use App\Model\v2\CertificateTemplate;
use App\Model\v2\StudentSubjectEnrolment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class CertificateTemplateController extends Controller
{
    public function index(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE)->orderBy('created_at', 'desc')->get();
        $attributes = CertificateAttribute::all();
        $id = '';
        if ($request->input('certificateId')) {
            $id = decryptIt(($request->input('certificateId')));
            $selectedCertificate = CertificateTemplate::find($id);
        } else {
            $selectedCertificate = null;
        }
        $certificateType = Config::get('constants.certificateType');

        $certificateIdFormate = CertificateIdFormate::where('college_id', $collegeId)->get()->map(function ($item) use ($certificateType) {
            return [
                'id' => $item->id,
                'name' => $certificateType[$item->type], // or whatever field you want as the name
            ];
        })->values(); // optional: resets keys to be 0-indexed

        return Inertia::render('certificate-builder/CertificateBuilder', [
            'templates' => $templates,
            'attributes' => $attributes,
            'selectedCertificate' => $selectedCertificate,
            'embedeUrl' => ($id != '' && $this->certificatePreviewUrl($id)) ? $this->certificatePreviewUrl($id) : '',
            'certificateIdFormate' => $certificateIdFormate,
        ]);
    }

    public function templateList(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $certificateType = Config::get('constants.certificateType');

        $certificateIdFormate = CertificateIdFormate::where('college_id', $collegeId)->get()->map(function ($item) use ($certificateType) {
            return [
                'id' => $item->id,
                'name' => $certificateType[$item->type], // or whatever field you want as the name
            ];
        })->values(); // optional: resets keys to be 0-indexed

        $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE)->orderBy('created_at', 'desc')->get()->map(function ($template) {
            $template->secureId = encryptIt($template->id);

            return $template;
        });

        return Inertia::render('certificate-builder/CertificateTemplates', [
            'templates' => $templates,
            'certificateIdFormate' => $certificateIdFormate,
        ]);
    }

    public function getStudentIdTemplates()
    {
        $collegeId = Auth::user()->college_id;
        $certificateType = Config::get('constants.certificateType');

        //        $certificateIdFormate = CertificateIdFormate::where('college_id', $collegeId)->get()->map(function ($item) use ($certificateType) {
        //            return [
        //                'id' => $item->id,
        //                'name' => $certificateType[$item->type], // or whatever field you want as the name
        //            ];
        //        })->values(); // optional: resets keys to be 0-indexed

        $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD)->orderBy('created_at', 'desc')->get()->map(function ($template) {
            $template->secureId = encryptIt($template->id);

            return $template;
        });

        return Inertia::render('student-id/StudentIdTemplateListComponent', [
            'templates' => $templates,
        ]);
    }

    public function getPreviewUrl(Request $request)
    {
        $data['embedeUrl'] = $this->certificatePreviewUrl($request->input('id'));

        return ajaxSuccess($data, '');
    }

    public function certificatePreviewUrl($certificateId)
    {
        $certificateTemplate = CertificateTemplate::find($certificateId);

        if (strpos($certificateTemplate->html_data, '[unit.trascript.table]') !== false) {
            $studentCourse = StudentSubjectEnrolment::inRandomOrder()->whereNotNull('student_course_id')->where('mark_outcome', '!=', '')->first();
        } else {
            $studentCourse = StudentSubjectEnrolment::inRandomOrder()->whereNotNull('student_course_id')->first();
        }

        if (! $studentCourse) {
            return null;
        }

        $data = [
            [
                'studCourseId' => $studentCourse?->student_course_id,
                'studentId' => $studentCourse?->student_id,
                'certificateId' => $certificateId,
            ],
        ];

        $jsonData = json_encode($data);
        $encodedData = urlencode($jsonData);

        // Assuming 'spa.generte-certificate' accepts 'data' as a query parameter
        return route('spa.generte-certificate').'?data='.$encodedData;
    }

    public function store(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                Rule::unique('certificate_templates')->where(function ($query) use ($request) {
                    return $query->where('template_type', $request->input('template_type', 'certificate'));
                })
            ],
            'json_data' => 'required|json',
            'paper_size' => 'required|string',
            'orientation' => 'required|string',
            'thumbnail' => 'nullable|string',
            'is_default' => 'boolean',
            'metadata' => 'nullable|json',
            'template_type' => 'required|string|in:certificate,student-id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if (is_string($request->json_data)) {
            $request->merge([
                'json_data' => json_decode($request->json_data, true),
                'metadate' => json_encode(['fonts' => $request->fonts_used]),
            ]);
        }
        $template = CertificateTemplate::create($request->all());
        $data['template'] = $template->fresh();
        $data['encrypt_id'] = encrypt($template->id);

        return ajaxSuccess($data, 'Template Created Successfully');
    }

    public function show(CertificateTemplate $template)
    {
        return response()->json($template);
    }

    public function update(Request $request, CertificateTemplate $template)
    {
        if (! $template || ! $template->exists) {
            return response()->json(['error' => 'Template not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => [
                'string',
                Rule::unique('certificate_templates')->where(function ($query) use ($request, $template) {
                    return $query->where('template_type', $request->input('template_type', $template->template_type));
                })->ignore($template->id)
            ],
            'json_data' => 'json',
            'paper_size' => 'string',
            'orientation' => 'string',
            'thumbnail' => 'nullable|string',
            'is_default' => 'boolean',
            'metadata' => 'nullable|json',
            'certificate_number_formate_id' => 'required',
            'template_type' => 'string|in:certificate,student-id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if (is_string($request->json_data)) {
            $request->merge([
                'json_data' => json_decode($request->json_data, true),
                'metadate' => json_encode(['fonts' => $request->fonts_used]),
            ]);
        }

        $updated = $template->update($request->all());

        if (! $updated) {
            return response()->json(['error' => 'Update failed'], 500);
        }
        $data['template'] = $template->fresh();

        return ajaxSuccess($data, '');
    }

    public function delete(CertificateTemplate $template)
    {
        try {
            $template->delete();

            return ajaxSuccess([], 'Certificate Template deleted successfully.');
        } catch (\Exception $e) {
            return ajaxError([], 'An error occurred while deleting the certificate template.');
        }
    }
}
