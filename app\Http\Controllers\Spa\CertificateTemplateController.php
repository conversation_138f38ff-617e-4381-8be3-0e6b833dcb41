<?php

namespace App\Http\Controllers\Spa;

use App\Http\Controllers\Controller;
use App\Model\v2\Student;
use App\Model\v2\CertificateAttribute;
use App\Model\v2\CertificateIdFormate;
use App\Model\v2\CertificateTemplate;
use App\Model\v2\StudentSubjectEnrolment;
use App\Services\CertificateContentReplacer;
use App\Helpers\Helpers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class CertificateTemplateController extends Controller
{
    public function index(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $certificateType = Config::get('constants.certificateType');

        // Determine template type from request (for student ID templates)
        $templateType = $request->input('type', 'certificate');
        $isStudentId = $templateType === 'student-id';

        // Get templates and attributes based on template type
        if ($isStudentId) {
            $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD)->orderBy('created_at', 'desc')->get();
            $attributes = CertificateAttribute::where('template_type', CertificateAttribute::TEMPLATE_TYPE_STUDENT_CARD)->get();
        } else {
            $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE)->orderBy('created_at', 'desc')->get();
            $attributes = CertificateAttribute::where('template_type', CertificateAttribute::TEMPLATE_TYPE_CERTIFICATE)->get();
        }

        $id = '';
        if ($request->input('certificateId')) {
            $id = decryptIt(($request->input('certificateId')));
            $selectedCertificate = CertificateTemplate::find($id);
        } else {
            $selectedCertificate = null;
        }

        $certificateIdFormate = CertificateIdFormate::where('college_id', $collegeId)->get()->map(function ($item) use ($certificateType) {
            return [
                'id' => $item->id,
                'name' => $certificateType[$item->type], // or whatever field you want as the name
            ];
        })->values(); // optional: resets keys to be 0-indexed

        return Inertia::render('certificate-builder/CertificateBuilder', [
            'templates' => $templates,
            'attributes' => $attributes,
            'selectedCertificate' => $selectedCertificate,
            'embedeUrl' => ($id != '' && $this->certificatePreviewUrl($id)) ? $this->certificatePreviewUrl($id) : '',
            'certificateIdFormate' => $certificateIdFormate,
        ]);
    }

    public function templateList(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $certificateType = Config::get('constants.certificateType');

        $certificateIdFormate = CertificateIdFormate::where('college_id', $collegeId)->get()->map(function ($item) use ($certificateType) {
            return [
                'id' => $item->id,
                'name' => $certificateType[$item->type], // or whatever field you want as the name
            ];
        })->values(); // optional: resets keys to be 0-indexed

        $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE)->orderBy('created_at', 'desc')->get()->map(function ($template) {
            $template->secureId = encryptIt($template->id);

            return $template;
        });

        return Inertia::render('certificate-builder/CertificateTemplates', [
            'templates' => $templates,
            'certificateIdFormate' => $certificateIdFormate,
        ]);
    }

    public function getStudentIdTemplates()
    {
        $collegeId = Auth::user()->college_id;
        $certificateType = Config::get('constants.certificateType');

        //        $certificateIdFormate = CertificateIdFormate::where('college_id', $collegeId)->get()->map(function ($item) use ($certificateType) {
        //            return [
        //                'id' => $item->id,
        //                'name' => $certificateType[$item->type], // or whatever field you want as the name
        //            ];
        //        })->values(); // optional: resets keys to be 0-indexed

        $templates = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD)->orderBy('created_at', 'desc')->get()->map(function ($template) {
            $template->secureId = encryptIt($template->id);

            return $template;
        });

        $attributes = CertificateAttribute::where('template_type', CertificateAttribute::TEMPLATE_TYPE_STUDENT_CARD)->get();

        return Inertia::render('student-id/StudentIdTemplateListComponent', [
            'templates' => $templates,
            'attributes' => $attributes,
        ]);
    }

    public function getPreviewUrl(Request $request)
    {
        $certificateId = $request->input('id');
        $template = CertificateTemplate::find($certificateId);

        // Check if it's a student ID template
        if ($template && $template->template_type === CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD) {
            $data['embedeUrl'] = $this->studentIdPreviewUrl($certificateId);
        } else {
            $data['embedeUrl'] = $this->certificatePreviewUrl($certificateId);
        }

        return ajaxSuccess($data, '');
    }

    public function studentIdPreviewUrl($certificateId)
    {
        // Get a random student for preview
        $student = Student::with(['college'])->inRandomOrder()->first();

        if (!$student) {
            return null;
        }

        $data = [
            [
                'studentId' => $student->id,
                'certificateId' => $certificateId,
                'type' => 'student-id'
            ],
        ];

        $jsonData = json_encode($data);
        $encodedData = urlencode($jsonData);

        return route('spa.generate-student-id-preview') . '?data=' . $encodedData;
    }

    public function certificatePreviewUrl($certificateId)
    {
        $certificateTemplate = CertificateTemplate::find($certificateId);

        if (strpos($certificateTemplate->html_data, '[unit.trascript.table]') !== false) {
            $studentCourse = StudentSubjectEnrolment::inRandomOrder()->whereNotNull('student_course_id')->where('mark_outcome', '!=', '')->first();
        } else {
            $studentCourse = StudentSubjectEnrolment::inRandomOrder()->whereNotNull('student_course_id')->first();
        }

        if (! $studentCourse) {
            return null;
        }

        $data = [
            [
                'studCourseId' => $studentCourse?->student_course_id,
                'studentId' => $studentCourse?->student_id,
                'certificateId' => $certificateId,
            ],
        ];

        $jsonData = json_encode($data);
        $encodedData = urlencode($jsonData);

        // Assuming 'spa.generte-certificate' accepts 'data' as a query parameter
        return route('spa.generte-certificate').'?data='.$encodedData;
    }

    public function store(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                Rule::unique('certificate_templates')->where(function ($query) use ($request) {
                    $templateType = $request->input('template_type', CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE);

                    return $query->where('template_type', $templateType)
                        ->whereRaw('LOWER(name) = ?', [strtolower($request->input('name'))]);
                }),
            ],
            'json_data' => 'required|json',
            'paper_size' => 'required|string',
            'orientation' => 'required|string',
            'thumbnail' => 'nullable|string',
            'is_default' => 'boolean',
            'metadata' => 'nullable|json',
            'template_type' => 'required|string|in:'.CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE.','.CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD,
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if (is_string($request->json_data)) {
            $request->merge([
                'json_data' => json_decode($request->json_data, true),
                'metadate' => json_encode(['fonts' => $request->fonts_used]),
            ]);
        }
        $template = CertificateTemplate::create($request->all());
        $data['template'] = $template->fresh();
        $data['encrypt_id'] = encrypt($template->id);

        return ajaxSuccess($data, 'Template Created Successfully');
    }

    public function show(CertificateTemplate $template)
    {
        return response()->json($template);
    }

    public function update(Request $request, CertificateTemplate $template)
    {
        if (! $template || ! $template->exists) {
            return response()->json(['error' => 'Template not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => [
                'string',
                Rule::unique('certificate_templates')->where(function ($query) use ($request, $template) {
                    $templateType = $request->input('template_type', $template->template_type);

                    return $query->where('template_type', $templateType)
                        ->whereRaw('LOWER(name) = ?', [strtolower($request->input('name', $template->name))]);
                })->ignore($template->id),
            ],
            'json_data' => 'json',
            'paper_size' => 'string',
            'orientation' => 'string',
            'thumbnail' => 'nullable|string',
            'is_default' => 'boolean',
            'metadata' => 'nullable|json',
            'certificate_number_formate_id' => 'required',
            'template_type' => 'string|in:'.CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE.','.CertificateTemplate::TEMPLATE_TYPE_STUDENT_CARD,
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if (is_string($request->json_data)) {
            $request->merge([
                'json_data' => json_decode($request->json_data, true),
                'metadate' => json_encode(['fonts' => $request->fonts_used]),
            ]);
        }

        $updated = $template->update($request->all());

        if (! $updated) {
            return response()->json(['error' => 'Update failed'], 500);
        }
        $data['template'] = $template->fresh();

        return ajaxSuccess($data, '');
    }

    public function delete(CertificateTemplate $template)
    {
        try {
            $template->delete();

            return ajaxSuccess([], 'Certificate Template deleted successfully.');
        } catch (\Exception $e) {
            return ajaxError([], 'An error occurred while deleting the certificate template.');
        }
    }

    public function generateStudentIdPreview(Request $request)
    {
        $d = $request->all();
        $dataValue = json_decode($d['data'], true)[0];

        $studentId = $dataValue['studentId'];
        $certificateId = $dataValue['certificateId'];

        // Get student data with college information
        $student = Student::with(['college'])->find($studentId);

        if (!$student) {
            return response('Student not found', 404);
        }

        // Get template
        $template = CertificateTemplate::find($certificateId);
        if (!$template) {
            return response('Template not found', 404);
        }

        // Prepare student ID data
        $data = $this->prepareStudentIdPreviewData($student);

        // Replace placeholders in template
        $processedHtml = $this->replaceStudentIdPlaceholders($template->html_data, $data);

        // Generate PDF for preview
        return $this->generateStudentIdPreviewPdf($processedHtml);
    }

    private function prepareStudentIdPreviewData($student)
    {
        // Get profile picture path
        $profilePicPath = '';
        if ($student->profile_picture) {
            $filePath = Config::get('constants.uploadFilePath.StudentPics');
            $profilePicDestinationPath = Helpers::changeRootPath($filePath, $student->id);
            $profilePicFullPath = public_path($profilePicDestinationPath['view'] . $student->profile_picture);

            if (File::exists($profilePicFullPath)) {
                $profilePicPath = asset($profilePicDestinationPath['view'] . $student->profile_picture);
            } else {
                $profilePicPath = asset('dist/img/avatar6.png');
            }
        } else {
            $profilePicPath = asset('dist/img/avatar6.png');
        }

        // Get college logo path
        $logoPath = '';
        if ($student->college && $student->college->college_logo) {
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath);
            $logoPath = asset($destinationPath['view'] . $student->college->college_logo);
        }

        return [
            'student' => [
                'id' => $student->generated_stud_id ?? 'PREVIEW-ID',
                'firstname' => $student->first_name ?? 'John',
                'lastname' => $student->family_name ?? 'Doe',
                'fullname' => trim(($student->first_name ?? 'John') . ' ' . ($student->family_name ?? 'Doe')),
                'email' => $student->email ?? '<EMAIL>',
                'dob' => $student->date_of_birth ? date('d/m/Y', strtotime($student->date_of_birth)) : '01/01/1990',
                'profile_picture' => $profilePicPath,
            ],
            'college' => [
                'name' => $student->college->college_name ?? 'Preview College',
                'logo' => $logoPath,
            ],
            'course' => [
                'code' => 'PREVIEW-001',
                'name' => 'Preview Course Name',
            ],
            'certificate' => [
                'issued_on' => date('d M Y'),
                'uuid' => 'PREVIEW-CERT-001',
            ],
        ];
    }

    private function replaceStudentIdPlaceholders($htmlTemplate, $data)
    {
        $replacer = new CertificateContentReplacer($data);
        return $replacer->replace($htmlTemplate);
    }

    private function generateStudentIdPreviewPdf($processedHtml)
    {
        // Set up PDF dimensions for student ID card (3.375" x 2.125")
        $width_px = 243; // 3.375 inches * 72 DPI
        $height_px = 153; // 2.125 inches * 72 DPI
        $width_pt = $width_px * 0.75;
        $height_pt = $height_px * 0.75;

        if (!is_dir(Config::get('constants.certificateTempFontPath'))) {
            mkdir(Config::get('constants.certificateTempFontPath'), 0777);
        }

        $pdf = App::make('dompdf.wrapper');
        $pdf->setPaper([0, 0, $width_pt, $height_pt]);
        $pdf->setOptions([
            'isRemoteEnabled' => true,
            'fontDir' => Config::get('constants.certificateTempFontPath'),
        ]);

        $pdf->loadHTML($processedHtml);

        return $pdf->stream('student_id_preview.pdf');
    }
}
