<x-v2.layouts.default>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/student.css') }}">
    </x-slot>

    <style type="text/css">
        .sidebar-menu li.active>.treeview-menu {
            display: block;
        }
    </style>

    {{-- Filter panelbar template --}}
    <script id="filterManageofferTemplate" type="text/kendo-ui-template">

        <div class="flex flex-col items-start justify-start w-full #: item.value #">

            # if (item.id != 0 && typeof item.text != 'undefined') { #
            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-#: item.id # ">
                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.text #</span>
            </div>

            # } #

            # if (item.type == 'switch') { #
            <div class="flex items-center justify-center">
                <label for="switch_#: item.value.toLowerCase() #_course" class="flex items-center cursor-pointer">
                    <div class="relative">
                        <input type="checkbox" class="sr-only external-filter" role="switch" id="switch_#: item.value.toLowerCase() #_course" data-category="#: item.field #" value="#: item.value #" data-val="#: item.original #" checked/>
                        <div class="w-10 h-5 bg-gray-200 rounded-full shadow-inner outer-dot"></div>
                        <div class="dot absolute w-5 h-5 bg-white rounded-full shadow left-0 top-0 transition"></div>
                    </div>
                    <div class="ml-3 text-gray-700 font-medium">
                        #: item.value #
                    </div>
                </label>
            </div>
            # } #

            # if (item.type == 'input') { #
            <div class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
                <img src="{{ asset('v2/img/search.png') }}" class="h-4 w-4" alt="searchIcon" />
                # if(item.field == 'course'){ #
                    <input type="text" data-value="#: item.value #" class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } else { #
                    <input type="text" data-value="#: item.value #" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } #

            </div>
            # } #

            # if (item.type == 'checkbox') { #
            <div class="inline-flex space-x-2 items-center justify-start">
                <div class="form-check flex items-center">
                    <input class="form-check-input external-filter k-checkbox" type="checkbox" value="#: item.value #" data-category="#: item.field #" data-val="#: item.original #" id="#: item.category_id #_checkbox_#: item.id #">
                    <label class="text-sm leading-5 text-gray-700 h-full" for="#: item.category_id #_checkbox_#: item.id #" data-val="#: item.original #" >
                        #: item.subtext #
                    </label>
                </div>
            </div>
            # } #

            # if (item.type == 'dropdown') { #
            <select class="external-filter inline-flex space-x-2 items-center w-full justify-start inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 text-gray-500 custom-option leading-6 text-sm #: item.field #" id="#: item.value #">
                # if(item.field == 'Course') { #
                    <option value="">Select Course</option>
                # } #

                # for(var i=0; i < item.arr.length; i++){ #
                    <option value="#: item.arr[i].id #" title="#: item.arr[i].text #">#: ((item.arr[i].text.length > 30) ? (item.arr[i].text.substring(0,30) + '...') : item.arr[i].text) #</option>
                # } #
            </select>
            # } #

            # if (item.type == 'button') { #
            <button class="inline-flex space-x-2 items-center justify-center w-full h-10 py-1.5 bg-white border rounded-lg border-gray-300 hover:bg-primary-blue-500 hover:text-white" id="#: item.value #">
                <span class="text-sm font-medium leading-none text-blue-500 hover:text-white">
                    <span class="k-icon k-i-plus text-blue-500 hover:text-white"></span>
                    Add Course
                </span>
            </button>
            # } #

        </div>

    </script>

    {{-- Tooltip/template for action list --}}
    <script id="actionTemplate" type="text/x-kendo-template">
        <div class="inline-flex flex-col items-start justify-start bg-white shadow-xl rounded-md action-menu z-10">
            <div class="flex flex-col items-start justify-start w-full py-1">
                <a href="/student-profile/#: id #" target="_blank" class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">Download Offer Letter</span>
                </a>
                <button class="single_email single-row inline-flex items-center justify-between w-full hover:bg-primary-blue-500 text-left">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">View Application</span>
                </button>
                <button class="single_sms single-row inline-flex items-center justify-between w-full hover:bg-primary-blue-500 text-left">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">Re-Approve Offer Letter</span>
                </button>
                <a href="/student-send-letter/#: id #" class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">Send Email</span>
                </a>
                <a href="/student-profile/#: id #" class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">View Documents</span>
                </a>
                <a href="/student-profile/#: id #" class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">Add COE number for this offer</span>
                </a>
                <a href="/student-profile/#: id #" class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">Generate Student ID</span>
                </a>
                <a href="/gte-dashboard/#: id #" target="_blank" class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">Start Genuine Student (GS) Requirement Process</span>
                </a>
                <a href="{{ route('student', ['action'=> 'more-action']) }}/#: id #" target="_blank" class="inline-flex items-center justify-between w-full hover:bg-primary-blue-500 hover:text-white">
                    <span class="text-xs leading-tight text-gray-700 hover:text-white w-full px-4 py-2">More Actions</span>
                    <span class="k-icon k-i-arrow-chevron-right"></span>
                </a>
            </div>
            <div class="w-full h-0.5 bg-gray-100"></div>
        </div>
    </script>

    <div class="flex flex-row w-full">

        <div
            class="relative toggelfilter w-80 transition-all duration-1000 filter-left-sidebar inline-flex flex-col  space-y-4 items-start justify-start pt-4 bg-gray-50 shadow-inner widthzero">

            <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                <div
                    class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
                    <img src="{{ asset('v2/img/search.png') }}" class="h-4 w-4" alt="searchIcon" />
                    <input type="text"
                        class="sidebarTopSearch w-full h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400"
                        placeholder="Search keywords">
                </div>
            </div>

            <ul id="filterbar" class="w-64 flex items-start justify-start">
            </ul>
            <div class="filterFooterBox flex flex-col items-center justify-start w-full py-3.5 bg-white border inset-x-0"
                style="display: none;">
                <div class="flex space-x-4 items-center justify-end">
                    <div
                        class="flex items-center justify-center w-20 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300">
                        <button type="button" id="clearFilter"
                            class="text-xs font-medium leading-none text-gray-700">Clear</button>
                    </div>
                    <div
                        class="flex items-center justify-center w-24 h-full px-3 py-2 bg-primary-blue-500 shadow rounded-lg">
                        <button id="applyFilter" class="text-xs font-medium leading-tight text-white">Apply
                            <span></span></button>
                    </div>
                </div>
            </div>
        </div>

        <div class="filter-result flex flex-col z-10 w-full bg-white" id="">
            <div class="flex flex-col space-y-2 items-start justify-center py-4 pl-8 pr-6 shadow-inner w-full">
                <div class="searchdata flex space-x-2 items-center justify-between w-full">
                    <div class="flex space-x-2 items-center justify-start h-9">
                        <button type="button" id="filterBtn"
                            class="active bg-white border border-gray-300 flex h-full hover:shadow-lg items-center justify-center px-2.5 py-2 rounded-lg space-x-2 w-9">
                            <img src="{{ asset('v2/img/f_icon.svg') }}" class="" alt="">
                            {{-- <img src="https://roghtgriftentel.galaxy360.com.au/v2/img/filter.png" class="" alt="">
                            --}}
                        </button>
                        <p class="text-base font-medium leading-tight text-gray-700 filter_title"></p>
                    </div>
                    <div class="flex space-x-2 items-center justify-end h-9">
                        <div
                            class="flex space-x-2 items-center justify-start w-40 h-full px-3 py-2 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M19 19L13 13M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z"
                                    stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <input type="text" id="" data-grid-id="manageOfferList"
                                class="searchInputField text-sm leading-5 font-normal text-gray-400"
                                placeholder="Search">
                        </div>
                        <button type="button" id="exportData"
                            class="flex space-x-2 items-center justify-center h-full py-2 px-4 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                            <span class="text-sm font-normal leading-tight text-gray-900 k-grid-excel">Export</span>
                        </button>
                        <div>
                            <button type="button" id="manageColumns"
                                class="flex space-x-2 items-center justify-center w-9 h-full px-2.5 py-2.5 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                                <svg width="14" height="12" viewBox="0 0 14 12" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7.00033 9.3335V2.66683M7.00033 9.3335C7.00033 10.0699 6.20439 10.6668 5.22255 10.6668H3.44477C2.46293 10.6668 1.66699 10.0699 1.66699 9.3335V2.66683C1.66699 1.93045 2.46293 1.3335 3.44477 1.3335H5.22255M7.00033 9.3335C7.00033 10.0699 7.79626 10.6668 8.7781 10.6668H10.5559C11.5377 10.6668 12.3337 10.0699 12.3337 9.3335V2.66683C12.3337 1.93045 11.5377 1.3335 10.5559 1.3335H8.7781M5.22255 1.3335C6.20439 1.3335 7.00033 1.93045 7.00033 2.66683M5.22255 1.3335H8.7781M7.00033 2.66683C7.00033 1.93045 7.79626 1.3335 8.7781 1.3335"
                                        stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </button>
                            <div class="manageColumnBox">
                                <div class="relative w-full">
                                    <div
                                        class="manage-column-box__dropdown absolute top-2 right-0 w-auto pt-4 bg-white shadow border rounded-lg border-gray-200 space-y-4">
                                        <!--                                        <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                                            <div class="inline-flex space-x-4 items-center justify-between w-full">
                                                <p class="text-sm font-medium leading-tight text-gray-700 pb-2">View Type</p>
                                            </div>
                                            <div class="flex flex-row items-center justify-start w-full">
                                                <div class="form-check">
                                                    <input type="radio" name="view_type_radio" value="1" id="view_type_checkbox_1" class="form-check-input f-radio appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer" checked>
                                                    <label class="text-sm leading-none text-gray-700" for="view_type_checkbox_1">Default</label>
                                                </div>
                                                <div class="form-check ml-2">
                                                    <input type="radio" name="view_type_radio" value="2" id="view_type_checkbox_2" class="form-check-input f-radio appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer" >
                                                    <label class="text-sm leading-none text-gray-700" for="view_type_checkbox_2">Cozy</label>
                                                </div>
                                            </div>
                                        </div>-->
                                        <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                                            <div class="inline-flex space-x-4 items-center justify-between w-full">
                                                <p class="text-sm font-medium leading-tight text-gray-700 pb-2">Columns
                                                </p>
                                            </div>
                                            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                                                @foreach($column_arr as $column)
                                                <div class="inline-flex items-center justify-start w-full pr-2 rounded">
                                                    <div class="flex space-x-2 items-center justify-start">
                                                        <input type="checkbox" class="k-checkbox cursor-pointer rounded"
                                                            id="fc_{{ $column['id'] }}" value="{{ $column['id'] }}" {{
                                                            $column['default'] }} />
                                                        <label for="fc_{{ $column['id'] }}"
                                                            class="text-sm leading-none text-gray-700">{{
                                                            $column['title'] }}</label>
                                                    </div>
                                                </div>
                                                @endforeach
                                            </div>
                                            <button
                                                class="text-sm font-medium leading-tight pb-2 pt-1 text-blue-500 reset column_filter">Reset
                                                Columns</button>
                                        </div>
                                        <div
                                            class="inline-flex space-x-4 items-center justify-end w-full py-4 pl-2 pr-2 border border-gray-200">
                                            <div class="flex space-x-4 items-center justify-end">
                                                <button type="button"
                                                    class="flex items-center justify-center w-24 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300 text-xs font-medium leading-none text-gray-700 clear column_filter">Cancel</button>
                                                <button type="button"
                                                    class="flex items-center justify-center w-28 h-full px-3 py-2 bg-primary-blue-500 shadow rounded-lg text-xs font-medium leading-tight text-white save column_filter">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="appliedFilterList"></div>
            </div>
            <div class="h-full bg-gray-100">
                <div id="manageOfferList"></div>
            </div>
        </div>
    </div>

    {{-- Notification Templates --}}
    @include('v2.sadmin.notification')

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/jszip.min.js') }}"></script>
        <script src="{{ asset('v2/js/sadmin/manageoffer.js') }}"></script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ (isset($api_token) ? "Bearer $api_token" : "") }}";
    </x-slot>

</x-v2.layouts.default>