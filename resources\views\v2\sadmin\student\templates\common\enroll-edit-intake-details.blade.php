<script id="editIntakeDetailsTemplate" type="text/html">
    <div id="2" class="w-full holder">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col w-full border-b pb-2 space-y-2">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center space-x-2">
                        <p class="text-lg font-medium leading-6 text-gray-900">Intake Details</p>
                        <img src="{{ asset('v2/img/question.svg') }}" alt="Help">
                    </div>
                    #if (isScheduleExist && isValidForExtend){#
                    <button type="button"
                        class="extendCourseDateBtn flex justify-center h-full px-6 py-2 bg-primary-blue-500 hover:shadow-md rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <p class="text-sm font-medium leading-4 text-white">Extend Course Date</p>
                    </button>
                    #}#
                </div>
                #if (isScheduleExist){#
                <p class="text-xs italic text-red-500">
                    The schedule has already been generated. Therefore, the course date range and number of weeks cannot
                    be changed.
                </p>
                #}#
            </div>
            <div class="inline-flex  items-start justify-start w-full grid grid-cols-2 gap-4">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Year Intake</p>
                    <div class="w-full">
                        <input name="intake_year" #if (isScheduleExist){# disabled #} else {# required #}#
                            id="edit_intake_year" data-message="Select Intake Year" />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Intake Date</p>
                    <div class="w-full">
                        <input name="intake_date" id="edit_intake_date" #if (isScheduleExist){# disabled #} else {#
                            required #}# data-message="Select Intake Date" />
                    </div>
                </div>
            </div>
            <div class="inline-flex  items-start justify-start w-full grid grid-cols-2 gap-4">
                <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Course Template</p>
                    <div class="w-full">
                        <input name="course_template" id="edit_course_template" />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Offer Issued Date</p>
                    <input name="issued_date" id="edit_issued_date" class="w-full" required
                        data-message="Offer Issued Date" />
                </div>
            </div>
            <div class="inline-flex  items-start justify-start w-full grid grid-cols-4 gap-4">
                <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-2">
                    <p class="text-sm font-medium leading-5 text-gray-700">Start Date</p>
                    <div class="w-full">
                        <input name="start_date" id="edit_start_date" required data-message="Course Start Date" #if
                            (isScheduleExist){# disabled #} else {# required #}# />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Duration Type</p>
                    <div class="w-full">
                        <input type="text" name="course_duration_type" id="edit_course_duration_type" #if
                            (isScheduleExist){# disabled #}# />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Study duration</p>
                    <div class="w-full">
                        <input type="text" name="total_weeks" id="edit_total_weeks" data-message="Number of Weeks"
                            value="0"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            #if (isScheduleExist){# disabled #}# />
                    </div>
                </div>
            </div>
            <div class="inline-flex space-x-6 items-start justify-start w-1/2">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700">Finish Date</p>
                    <div class="w-full">
                        <input name="finish_date" id="edit_finish_date" data-message="Course Finish Date" #if
                            (isScheduleExist){# disabled #} else {# required #}# />
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>

<script id="studentCourseExtendTemplate" type="text/html">
    <div class="flex">
        <form id="studentCourseExtendForm" class="w-full">
            <input id="selectedStudCourseIDForExtend" name="selectedStudCourseIDForExtend"
                value="#= arr.selectedStudCourseID #" hidden />
            <div class="bg-white flex flex-col items-start justify-start rounded-lg w-full min-h-0">
                <div class="flex flex-col justify-start px-6 pt-4 pb-2 space-y-4 w-full flex-1 overflow-y-auto">
                    <div class="inline-flex items-center justify-start">
                        <div class="flex space-x-2 items-start justify-start">
                            # if (arr.profile_picture == '') { let name = arr.student_name.toUpperCase().split(/\s+/);
                            let shortName = name[0].charAt(0) + name[1].charAt(0); #
                            <div class='w-14 h-14 flex user-profile-pic rounded-full bg-primary-blue-500 items-center'>
                                <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#=
                                    shortName#</span>
                            </div>
                            # } else { #
                            <img class="w-16 h-16 rounded-full" src="#= arr.profile_picture #" />
                            # } #
                            <div class="inline-flex flex-col items-start justify-center h-full w-full m-1">
                                <p class="text-base font-medium leading-6 text-gray-900">#= arr.student_name #</p>
                                <div class="flex items-center justify-start">
                                    <p class="text-xs leading-5 text-gray-400">ID:#= arr.generated_stud_id #</p>
                                    <x-v2.copy width="16" height="16" data-text="#: arr.generated_stud_id #" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex space-x-1 items-center justify-between">
                        <p class="text-xs leading-5 text-gray-700 truncate">#= arr.course_name #</p>
                        <div
                            class="flex items-center justify-center px-2.5 py-0.5 bg-#= arr.bgcolor #-100 rounded truncate">
                            <p class="text-xs leading-5 text-center text-#=  arr.textcolor #-800">#= arr.status #</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-10 gap-3 w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-3">
                            <p class="text-sm font-medium leading-5 text-gray-700">Start Date</p>
                            <div class="w-full">
                                {{--<p
                                    class="text-sm leading-5 text-gray-900 px-3 py-2 bg-gray-50 border rounded-lg border-gray-300 w-full"
                                    id="extend_start_date_display">#= kendo.toString(new Date(arr.start_date),
                                    'dd-MM-yyyy') #</p>--}}
                                {{--<input type="hidden" name="extend_start_date" id="extend_start_date"
                                    value="#= arr.start_date #" />--}}
                                <input name="extend_start_date" id="extend_start_date" data-message="Course Start Date"
                                    #if(arr.isValidForExtendStartDate){# required #} else {# disabled #}# />
                            </div>
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-3">
                            <p class="text-sm font-medium leading-5 text-gray-700">Finish Date</p>
                            <div class="w-full">
                                <input name="extend_finish_date" id="extend_finish_date"
                                    data-message="Course Finish Date" required />
                            </div>
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-2">
                            <p class="text-sm font-medium leading-5 text-gray-700">Duration Type</p>
                            <div class="w-full">
                                <p
                                    class="text-sm leading-5 text-gray-900 px-3 py-2 bg-gray-50 border rounded-lg border-gray-300 w-full">
                                    #= getDurationTypeText(arr.course_duration_type) #</p>
                                <input type="hidden" name="extend_course_duration_type" id="extend_course_duration_type"
                                    value="#= arr.course_duration_type #" />
                            </div>
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start col-span-2">
                            <p class="text-sm font-medium leading-5 text-gray-700">Study Duration</p>
                            <div class="w-full">
                                <p class="text-sm leading-5 text-gray-900 px-3 py-2 bg-gray-50 border rounded-lg border-gray-300 w-full"
                                    id="extend_total_weeks_display">#= arr.total_weeks #</p>
                                <input type="hidden" name="extend_total_weeks" id="extend_total_weeks"
                                    value="#= arr.total_weeks #" />
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-5 text-gray-700">Reason for Extension</p>
                        <div class="inline-flex flex-col p-2 bg-white border rounded-lg border-gray-300 w-full">
                            <textarea name="extension_reason" id="extension_reason"
                                placeholder="Enter Reason for Extension..." rows="4" required></textarea>
                        </div>
                    </div>
                    <button class="w-48 h-7 showHideExtensionHistory" type="button">
                        <div
                            class="w-48 h-7 pl-2 pr-2.5 py-1.5 bg-sky-100 rounded-lg justify-center items-center gap-2 inline-flex">
                            <div class="w-4 h-4 relative">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M3.08813 6C3.09498 6.00014 3.10184 6.00014 3.10868 6H5.49909C5.77524 6 5.99909 5.77614 5.99909 5.5C5.99909 5.22386 5.77524 5 5.49909 5H3.99863C4.91128 3.78495 6.36382 3 7.99909 3C10.7605 3 12.9991 5.23858 12.9991 8C12.9991 10.7614 10.7605 13 7.99909 13C5.39117 13 3.2491 11.003 3.0195 8.45512C2.99471 8.1801 2.75167 7.97723 2.47664 8.00202C2.20161 8.0268 1.99875 8.26985 2.02353 8.54488C2.29916 11.6035 4.86898 14 7.99909 14C11.3128 14 13.9991 11.3137 13.9991 8C13.9991 4.68629 11.3128 2 7.99909 2C6.20656 2 4.59815 2.78613 3.49909 4.03138V3C3.49909 2.72386 3.27524 2.5 2.99909 2.5C2.72295 2.5 2.49909 2.72386 2.49909 3V5.5C2.49909 5.77614 2.72295 6 2.99909 6H3.08813ZM7.49909 5C7.77524 5 7.99909 5.22386 7.99909 5.5V8H9.49909C9.77524 8 9.99909 8.22386 9.99909 8.5C9.99909 8.77614 9.77524 9 9.49909 9H7.49909C7.22295 9 6.99909 8.77614 6.99909 8.5V5.5C6.99909 5.22386 7.22295 5 7.49909 5Z"
                                        fill="rgb(24, 144, 255)"></path>
                                </svg>
                            </div>
                            <div class="text-sky-500 text-xs font-normal capitalize">
                                <span class="extensionHistoryDivTitle">View</span> Extension History
                            </div>
                        </div>
                    </button>
                    <div id="studentCourseExtensionHistoryHtml"
                        class="max-h-[300px] overflow-y-auto hidden extensionHistoryDiv">

                    </div>
                </div>
                <div
                    class="inline-flex space-x-4 items-center justify-between px-6 py-4 bg-white w-full border-t mt-auto flex-shrink-0">
                    <div class="inline-flex flex-col items-start justify-start p-1"></div>
                    <div class="flex space-x-4 items-center justify-end">
                        <button type="button" data-modal-name="studentCourseExtendModal"
                            class="closeModal flex items-center justify-center w-24 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300 hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">
                            <p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>
                        </button>
                        <button type="button"
                            class="submitStudentCourseExtend flex items-center justify-center h-8 px-3 py-2 bg-primary-blue-500 shadow rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                            <p class="text-sm font-medium leading-4 text-white">Update</p>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="studentCourseExtensionHistoryTemplate" type="text/html">
    <div class="inline-flex flex-col items-start justify-start bg-white rounded-lg overflow-y-auto w-full">
        <div class="space-y-0">
            # if(arr.length > 0){ #
            #for(var i=0; i< arr.length; i++){# <div
                class="inline-flex space-x-2 items-center justify-start w-full py-2">
                <div class="flex items-center justify-start bg-light-blue-500 rounded-full">
                    <img class="rounded-lg" src="{{ asset('v2/img/history.svg') }}" />
                </div>
                <div class="flex flex-col items-start justify-center">
                    <p class="text-xs leading-5 text-gray-500">Course Date Range Extended</p>
                    <p class="text-xs leading-5 text-gray-400">by #= arr[i]['created_by_name'] # on #=
                        arr[i]['createAt'] #</p>
                </div>
                <div class="flex flex-col space-y-1 items-end justify-end">
                    # if(arr[i]['new_start_date'] && arr[i]['start_date'] !== arr[i]['new_start_date']) { #
                    <div class="flex space-x-1 items-center justify-end">
                        <span class="text-xs text-gray-600">Start:</span>
                        <div class="flex items-center justify-center px-2 py-0.5 bg-blue-100 rounded">
                            <p class="text-xs leading-5 text-center text-blue-900">#= (arr[i]['start_date'] ?
                                kendo.toString(new Date(arr[i]['start_date']), 'dd MMM yyyy') : 'NA') #</p>
                        </div>
                        <img class="w-4 h-2/3 rounded-full" src="{{ asset('v2/img/left_arrow_history.svg') }}" />
                        <div class="flex items-center justify-center px-2 py-0.5 bg-green-100 rounded">
                            <p class="text-xs leading-5 text-center text-green-800">#= (arr[i]['new_start_date'] ?
                                kendo.toString(new Date(arr[i]['new_start_date']), 'dd MMM yyyy') : 'NA') #</p>
                        </div>
                    </div>
                    # } #

                    # if(arr[i]['new_finish_date'] && arr[i]['finish_date'] !== arr[i]['new_finish_date']) { #
                    <div class="flex space-x-1 items-center justify-end">
                        <span class="text-xs text-gray-600">Finish:</span>
                        <div class="flex items-center justify-center px-2 py-0.5 bg-blue-100 rounded">
                            <p class="text-xs leading-5 text-center text-blue-900">#= (arr[i]['finish_date'] ?
                                kendo.toString(new Date(arr[i]['finish_date']), 'dd MMM yyyy') : 'NA') #</p>
                        </div>
                        <img class="w-4 h-2/3 rounded-full" src="{{ asset('v2/img/left_arrow_history.svg') }}" />
                        <div class="flex items-center justify-center px-2 py-0.5 bg-green-100 rounded">
                            <p class="text-xs leading-5 text-center text-green-800">#= (arr[i]['new_finish_date'] ?
                                kendo.toString(new Date(arr[i]['new_finish_date']), 'dd MMM yyyy') : 'NA') #</p>
                        </div>
                    </div>
                    # } #
                </div>
        </div>

        #if(arr[i]['extension_reason'] != null && arr[i]['extension_reason'] != ''){#
        <div class="inline-flex space-x-2 items-center justify-start w-full py-2">
            <div class="bg-gray-100 rounded gap-1 ml-8 px-2">
                <div class="grow shrink basis-0">
                    <span class="text-xs leading-5 text-center text-gray-900">Reason for Extension:</span>
                    <span class="text-gray-400 text-xs font-normal leading-tight tracking-wide"> </span>
                    <span class="text-gray-500 text-xs font-normal leading-tight tracking-wide">#=
                        arr[i]['extension_reason'] #</span>
                </div>
            </div>
        </div>
        # } #

        # } #
        # } else { #
        <p class="flex text-sm leading-5 font-medium text-gray-900">No Extension History Found</p>
        # } #
    </div>
    </div>
</script>

<div id="studentCourseExtendModal" style="display: none;">
    <div id="studentCourseExtendHtml"></div>
</div>