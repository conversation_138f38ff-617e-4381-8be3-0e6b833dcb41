<x-v2.layouts.onboard>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/onboard-settings.css') }}">
        <style>
            .k-wizard {
                padding: 0px !important;
            }

            .k-overlay {
                z-index: 2 !important;
            }

            .k-window {
                z-index: 3 !important;
            }
        </style>
    </x-slot>
    <livewire:onboarding.progress form="templates_letters.add_edit_email_template" />

    <div class="flex flex-row h-full">
    </div>

    <script id="commonTemplate" type="text/x-kendo-template">
        <div class="flex flex-col items-start justify-start w-full">
            <div class="inline-flex flex-col bg-white w-full">
            <div class="inline-flex space-x-6 items-start justify-between w-full">
                <div class="flex space-x-4 items-center justify-start">
                   # if(locked == 1){ #
                        <img src="{{ asset('v2/img/Lock_white.svg') }}" class=" " alt=" ">
                   # } #
                    <div class="inline-flex flex-col space-y-2 items-start justify-start">
                        <p class="text-2xl font-bold leading-7 text-gray-900">#=template_name# </p>
                        <input type="hidden"  id="email_subject" value="#=email_subject#" />
                        <input type="hidden"  id="template_name" value="#=template_name#" />
                        <input type="hidden"  id="recipient" value="#=recipient#" />
                        <input type="hidden"  id="locked" value="#=locked#" />
                        <input type="hidden"  id="id" value="#=id#" />
                        <div id="content" class="hidden">
                            #=content#
                        </div>

                    </div>
                </div>
                <div>
                    <button class="tw-btn-secondary h-9 showEmailContentTags" type="button" wire:loading.class="disabled">
                        <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11.0009 1.75019V0.750223C11.0009 0.551316 10.9219 0.360556 10.7812 0.219908C10.6406 0.0792594 10.4498 0.000244141 10.2509 0.000244141H3.75109C3.55219 0.000244141 3.36143 0.0792594 3.22078 0.219908C3.08013 0.360556 3.00111 0.551316 3.00111 0.750223V1.75019C3.00111 1.9491 3.08013 2.13986 3.22078 2.28051C3.36143 2.42116 3.55219 2.50017 3.75109 2.50017C3.95 2.50017 4.14076 2.42116 4.28141 2.28051C4.42206 2.13986 4.50107 1.9491 4.50107 1.75019V1.5002H6.25102V8.5H6.00103C5.80212 8.5 5.61136 8.57902 5.47071 8.71966C5.33007 8.86031 5.25105 9.05107 5.25105 9.24998C5.25105 9.44889 5.33007 9.63965 5.47071 9.78029C5.61136 9.92094 5.80212 9.99996 6.00103 9.99996H8.00097C8.19988 9.99996 8.39064 9.92094 8.53129 9.78029C8.67193 9.63965 8.75095 9.44889 8.75095 9.24998C8.75095 9.05107 8.67193 8.86031 8.53129 8.71966C8.39064 8.57902 8.19988 8.5 8.00097 8.5H7.75098V1.5002H9.50093V1.75019C9.50093 1.9491 9.57994 2.13986 9.72059 2.28051C9.86124 2.42116 10.052 2.50017 10.2509 2.50017C10.4498 2.50017 10.6406 2.42116 10.7812 2.28051C10.9219 2.13986 11.0009 1.9491 11.0009 1.75019ZM3.24911 3.68914C3.17549 3.62351 3.08964 3.57305 2.9965 3.54064C2.90335 3.50823 2.80472 3.49451 2.70626 3.50027C2.60781 3.50603 2.51145 3.53115 2.42271 3.5742C2.33398 3.61725 2.25461 3.67737 2.18914 3.75114L0.189195 6.00107C0.0673149 6.13832 0 6.3155 0 6.49906C0 6.68261 0.0673149 6.85979 0.189195 6.99704L2.18914 9.24698C2.25431 9.32116 2.33355 9.38168 2.42226 9.42505C2.51098 9.46841 2.60741 9.49376 2.70598 9.49961C2.80455 9.50547 2.90331 9.49173 2.99653 9.45918C3.08976 9.42663 3.1756 9.37592 3.24911 9.30998C3.32272 9.24452 3.38272 9.16521 3.42568 9.07656C3.46864 8.98792 3.49371 8.89167 3.49947 8.79334C3.50523 8.695 3.49156 8.59649 3.45924 8.50343C3.42692 8.41038 3.37658 8.32461 3.31111 8.25101L1.75415 6.49906L3.31011 4.74711C3.44203 4.59852 3.5096 4.40366 3.49797 4.2053C3.48635 4.00694 3.39748 3.8213 3.24911 3.68914ZM11.8109 3.75214L13.8108 6.00207V6.00307C13.9327 6.14032 14 6.3175 14 6.50106C14 6.68461 13.9327 6.86179 13.8108 6.99904L11.8109 9.24898C11.7463 9.32536 11.667 9.38805 11.5779 9.43335C11.4887 9.47865 11.3913 9.50565 11.2916 9.51277C11.1918 9.51989 11.0916 9.50697 10.9969 9.47478C10.9022 9.44259 10.8149 9.39178 10.7401 9.32534C10.6653 9.2589 10.6046 9.17817 10.5615 9.08791C10.5183 8.99765 10.4937 8.89967 10.489 8.79975C10.4844 8.69983 10.4997 8.59998 10.5342 8.50608C10.5687 8.41219 10.6216 8.32614 10.6899 8.25301L12.2469 6.50106L10.6899 4.74911C10.5577 4.60046 10.4899 4.40538 10.5016 4.20678C10.5132 4.00819 10.6032 3.82235 10.7519 3.69014C10.9005 3.55793 11.0956 3.49019 11.2942 3.50182C11.4928 3.51344 11.6787 3.60349 11.8109 3.75214Z" fill="rgb(156, 163, 175)"></path>
                        </svg>
                        <p class="text-sm font-normal text-black">View Parameters</p>
                    </button>
                </div>
            </div>
        </div>
    </script>

    <div class="flex flex-row w-full p-6 defaultHide hidden">
        {{--
        <livewire:onboarding.progress form="templates_letters.add_edit_email_template" /> --}}
        <x-v2.onboardsetting.template-container>
            <x-slot name="sidebar">
                <div class="px-4 pb-4 space-y-4 border-b border-gray-200">
                    <p class="text-2xl font-bold leading-7 text-gray-900">Email Template</p>
                    <x-v2.button class="addEmailTemplate h-9" variant="primary" size="sm">
                        <x-v2.icons width="12" height="12" viewBox="0 0 12 12" name="icon-plus" class="text-white" />
                        <p class="text-sm text-white">New Email Template</p>
                    </x-v2.button>
                </div>
                <div class="space-y-4 py-4 px-4">
                    <div class="flex space-x-2 items-center justify-end h-9">
                        <x-v2.search-input wrapperClass="w-full" id="" class="searchInputEmailTemplateMaster w-full"
                            placeholder="Search">
                            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                        </x-v2.search-input>
                    </div>
                    <div id="emailDocumentStepper" class="w-full emailDocumentStepper"></div>
                </div>
            </x-slot>
            <x-slot name="content">
                <div id="emailTemplateWizard"></div>
                <div class="rounded border border-gray-200 bg-white p-6">
                    <div id="editEmailTemplateForm" class="w-2/3">
                    </div>
                    <div class="p-6 showUploadedDocuments" style="display: none;">
                        <div id="uploadedDocuments" class="w-2/3"></div>
                    </div>
                </div>
            </x-slot>
        </x-v2.onboardsetting.template-container>
    </div>

    <div id="addEmailTemplateModal" style="display:none;">
        <form id="addEmailTemplateForm" class="w-full">
        </form>
    </div>
    <div id="emailPreviewFileManagerModal" style="display:none;"></div>
    <script id="emailFilePreviewTemplate" type="text/html">
        <div id="documentId"></div>
    </script>
    <div id="deleteEmailDocumentModal"></div>
    <div id="viewEmailContentTagParameterModal" style="display: none;">
        <div class="w-full justify-start items-start gap-4 inline-flex mb-4">
            <div
                class="flex space-x-2 items-center justify-start h-full px-3 py-2 bg-white border rounded-lg border-gray-300 hover:shadow duration-200 w-50">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M19 19L13 13M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z"
                        stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <input type="text" id="offscreen" autocomplete="off" data-grid-id="studentList"
                    class="searchInputEmailTags text-sm leading-5 font-normal text-gray-400 w-full"
                    placeholder="Search">
            </div>
        </div>
        <div class="w-full justify-start items-start gap-4 inline-flex mb-2">
            <div id="viewEmailContentTagParameterHtml" class="w-full rounded-lg border border-gray-200 "></div>
        </div>
    </div>
    <script id="viewEmailContentTagParameterTemplate" type="text/html">
        <div class="w-full justify-start items-start grid grid-cols-4">
            <div class="w-full flex-col justify-start items-start inline-flex border-r">
                <div class="self-stretch px-2 py-3 bg-white shadow-inner justify-start items-center gap-3 inline-flex">
                    <div class="text-gray-500 text-xs font-normal capitalize">Slug</div>
                </div>
            </div>
            <div class="w-full flex-col justify-start items-start inline-flex col-span-3">
                <div class="self-stretch px-2 py-3 bg-white shadow-inner justify-start items-center gap-3 inline-flex">
                    <div class="text-gray-500 text-xs font-normal capitalize">Details</div>
                </div>
            </div>
        </div>
        <div class="viewEmailContentTagParameterSearch">
            # if(data.length > 0){ #
            # for(let i=0; i < data.length; i++) { # <div class="w-full justify-start items-start grid grid-cols-4">
                <div class="w-full flex-col justify-start items-start inline-flex border-r searchEmailDiv">
                    <div
                        class="self-stretch px-2 py-3 bg-white shadow-inner justify-start items-center gap-3 inline-flex">

                        <div class="text-gray-500 text-xs font-normal capitalize"><span>#=
                                data[i].parameter_value #</span></div>
                        <x-v2.copy data-text="#= data[i].parameter_value #" />
                    </div>
                </div>
                <div class="w-full flex-col justify-start items-start inline-flex col-span-3 searchEmailDiv">
                    <div
                        class="self-stretch px-2 py-3 bg-white shadow-inner justify-start items-center gap-3 inline-flex">
                        # if(data[i].parameter_detail && typeof data[i].parameter_detail.value !== "undefined") { #
                        # if(data[i].parameter_detail.value == "text") { #
                        <div class="text-gray-500 text-xs font-normal capitalize">
                            #= data[i].parameter_detail.value #
                        </div>
                        # } else { #
                        <div class="w-full">#= data[i].parameter_detail.value #</div>
                        # } #
                        # } else { #
                        <div class="text-gray-400 italic">-</div>
                        # } #
                    </div>
                </div>


        </div>

        #}#
        #}#
        </div>
    </script>
    <script>
        window.tagJson = JSON.parse('<?php echo $tagarray; ?>');
    </script>

    <div id="confirmDeleteModal"></div>
    <script id="listMenuItem" type="text/html">
        <span class="w-full truncate">
        <strong class="action-div">#:label#</strong>
    </span>
        # if(!locked) { #
        <button id="deleteEmailTemplate" class="tw-btn-icon cursor-pointer deleteEmailTemplate action-autohide" data-id="#:id#">
            <x-v2.icons name="icon-delete" width="16" height="16" class="text-red-500" />
        </button>
        # } else { #
        <button id="" class="tw-btn-icon cursor-pointer text-gray-500" title="Default Template">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock-keyhole-icon lucide-lock-keyhole"><circle cx="12" cy="16" r="1"/><rect x="3" y="10" width="18" height="12" rx="2"/><path d="M7 10V7a5 5 0 0 1 10 0v3"/></svg>
        </button>
        # } #
    </script>

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/sadmin/onboard-settings/templates-letter/email-template.js') }}"></script>
        {{-- <script src="{{ asset('plugins/ckeditor5/ckeditor.js') }}"></script> --}}
        <script src="https://cdn.ckeditor.com/ckeditor5/35.3.2/super-build/ckeditor.js"></script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ (isset($api_token) ? "Bearer $api_token" : "") }}"
    </x-slot>


</x-v2.layouts.onboard>