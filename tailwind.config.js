/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './resources/**/*.blade.php',
        './resources/**/*.js',
        './resources/**/*.vue',
        './public/**/*.js',
        './src/**/*.blade.php',
    ],
    safelist: [
        'grid',
        'grid-cols-4',
        'gap-4',
        {
            pattern:
                /(bg|text|border)-(orange|indigo|pink|cyan|purple|red|gray|yellow|green|blue|light-blue|primary-blue)-(50|100|200|300|400|500|600|700|800|900)/,
            variants: ['lg', 'hover', 'focus', 'lg:hover'],
        },
    ],
    theme: {
        extend: {
            borderColor: {
                default: '#D1D5DB',
            },
            fontSize: {
                xxs: ['10px', '16px'],
                13: ['0.8125rem', '1'],
            },
            colors: {
                transparent: 'transparent',
                current: 'currentColor',
                'primary-blue': {
                    25: 'var(--color-primary-blue-25)',
                    50: 'var(--color-primary-blue-50)',
                    100: 'var(--color-primary-blue-100)',
                    200: 'var(--color-primary-blue-200)',
                    300: 'var(--color-primary-blue-300)',
                    400: 'var(--color-primary-blue-400)',
                    500: 'var(--color-primary-blue-500)',
                    600: 'var(--color-primary-blue-600)',
                    700: 'var(--color-primary-blue-700)',
                    800: 'var(--color-primary-blue-800)',
                    900: 'var(--color-primary-blue-900)',
                },
                secondary: {
                    25: 'var(--color-secondary-25)',
                    50: 'var(--color-secondary-50)',
                    100: 'var(--color-secondary-100)',
                    200: 'var(--color-secondary-200)',
                    300: 'var(--color-secondary-300)',
                    400: 'var(--color-secondary-400)',
                    500: 'var(--color-secondary-500)',
                    600: 'var(--color-secondary-600)',
                    700: 'var(--color-secondary-700)',
                    800: 'var(--color-secondary-800)',
                    900: 'var(--color-secondary-900)',
                },
                bluegray: {
                    50: 'var(--color-bluegray-50)',
                    100: 'var(--color-bluegray-100)',
                    200: 'var(--color-bluegray-200)',
                    300: 'var(--color-bluegray-300)',
                    400: 'var(--color-bluegray-400)',
                    500: 'var(--color-bluegray-500)',
                    600: 'var(--color-bluegray-600)',
                    700: 'var(--color-bluegray-700)',
                    800: 'var(--color-bluegray-800)',
                    900: 'var(--color-bluegray-900)',
                },
                'green-teal': {
                    500: 'var(--color-green-teal-500)',
                },
            },
            boxShadow: {
                'inner-line': '0px -1px 0px 0px #E5E7EB inset',
                'inner-top': '0px 1px 0px 0px #E5E7EB inset',
                popup: '0px 4px 6px -2px rgba(0, 0, 0, 0.05),0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.05)',
                top: '0 -4px 6px -1px rgba(0, 0, 0, 0.1)',
            },
            screens: {
                '3xl': '1600px',
            },
            flex: {
                10: '1 0 auto',
            },
            animation: {
                'spin-fast': 'spin 0.7s linear infinite',
                'spin-faster': 'spin 0.5s linear infinite',
            },
        },
    },
    plugins: [require('tailwindcss-animate')],
};
