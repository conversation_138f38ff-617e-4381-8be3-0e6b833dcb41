<?php

namespace App\Http\Controllers\Frontend;

use App;
use App\Http\Controllers\Controller;
use App\Model\CertificateIdFormate;
use App\Model\CollegeDetails;
use App\Model\Courses;
use App\Model\CourseType;
use App\Model\StudentCertificateRegister;
use App\Model\StudentCourse;
use App\Model\StudentSubjectEnrolment;
use App\Model\v2\CertificateTemplate;
use App\Model\v2\StudentCertificateRegister as StudentCertificateRegisterV2;
use App\Traits\CertificateGenerationTrait;
use App\Traits\CommonTrait;
use Auth;
use Helpers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Support\Services\UploadService;

class BulkCertificateController extends Controller
{
    use CertificateGenerationTrait, CommonTrait;

    public function __construct()
    {
        $this->middleware(['onboarded:organization.avetmiss_training_organization_identifier,organization.general_info']);
        // $this->rememberToken
    }

    public function generateBulkCertificateInfo(Request $request)
    {
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = $sessionPermission && is_array($sessionPermission) ? $sessionPermission['generate_bulk_certificate'] : null;
        $collegeId = Auth::user()->college_id;
        $arrStatus = Config::get('constants.arrCourseStatus');
        $defaultStatus[0] = 'All Status';
        $finalArrStatus = array_slice($arrStatus, 1) + $defaultStatus;

        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseTypeV2($collegeId);

        reset($arrCourseType);
        $firstCourseTypeKey = key($arrCourseType);

        $certificateTemplate = CertificateTemplate::where('template_type', CertificateTemplate::TEMPLATE_TYPE_CERTIFICATE)->pluck('name', 'id')->toArray();
        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseList($collegeId, $firstCourseTypeKey);
        reset($arrCourseList);
        $data['header'] = [
            'title' => 'Generate Bulk Certificate',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Compliance' => '',
                'Generate Bulk Certificate ' => '',
            ]];
        $data['pagetitle'] = 'Generate Bulk Certificate';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['bulkCertificate.js'];
        $data['funinit'] = ['bulkCertificate.initGenerate()'];
        $data['arrCourseType'] = $arrCourseType;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrStatus'] = $finalArrStatus;
        $data['certificateTemplate'] = $certificateTemplate;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['mainmenu'] = 'clients';

        return view('frontend.bulk_certificate.generate-bulk-certificate', $data);
    }

    public function generateBulkCertificateWithTemplate(Request $request)
    {
        $data = $request->all();
        $studentsData = json_decode($data['data'], true);
        $certificateId = $data['certificateId'];
        $isDownload = isset($data['isDownload']) && $data['isDownload'];

        $collegeId = Auth::user()->college_id;
        $templates = CertificateTemplate::find($certificateId);
        $certificateFormat = CertificateIdFormate::find($templates->certificate_number_formate_id);

        $results = [];
        foreach ($studentsData as $studentData) {
            $studCourseId = $studentData['studCourseId'];
            $objStudentCourse = new StudentCourse;
            $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

            if ($studentCourseInfo->isEmpty()) {
                continue;
            }

            $studentCourseInfo = $studentCourseInfo[0];
            $studentId = $studentCourseInfo['studentId'];
            $courseId = $studentCourseInfo['courseId'];

            if ($certificateFormat !== null) {
                $newNumber = $certificateFormat->last_auto_increment_number + 1;
                $uuid = $certificateFormat->prefix.$newNumber.$certificateFormat->suffix;
                $certificateFormat->last_auto_increment_number = $newNumber;
                $certificateFormat->save();
            }

            $student = $this->getStudentData($studentId, $courseId);
            $otherData = $this->getGradeData($collegeId);
            $enrolments = $this->getEnrolments($studentId, $courseId);

            $result = $this->handleCertificateGeneration([
                'student' => $student,
                'templates' => $templates,
                'dataValue' => $studentData,
                'collegeId' => $collegeId,
                'studentId' => $studentId,
                'uuid' => $uuid,
                'otherData' => $otherData,
                'enrolments' => $enrolments,
                'isDownload' => $isDownload,
            ]);

            $results[] = $result;
        }

        if ($isDownload) {
            return $this->handleBulkDownload($results);
        }

        return response()->json(['success' => true, 'results' => $results]);
    }

    public function ajaxAction(Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $action = $request->input('action');

        switch ($action) {
            case 'getStudentCourseList':
                $dataArr = $request->input('data');
                $objStudentCourse = new StudentCourse;
                $studentArr = $objStudentCourse->getStudentCourseList($collegeId, $dataArr);
                echo json_encode($studentArr);
                break;
        }
        exit;
    }

    public function generateTranscriptPdf($type, $studCourseId, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $data['collegeId'] = $collegeId;

        if ($request->isMethod('get')) {

            $postData = $request->query();

            if (! isset($postData['studCourseId']) || ! isset($postData['transcriptType']) || ! isset($postData['issueDate']) || ! isset($postData['completionDate'])) {
                $request->session()->flash('session_error', 'Something is wrong. Please try again.');

                return redirect(route('generate-bulk-certificate'));
            }
            $studCourseId = $postData['studCourseId'];
            $type = $postData['transcriptType'];

            $objStudentCourse = new StudentCourse;
            $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

            $dataArr = [];
            if ($studentCourseInfo->count() > 0) {
                $studentCourseInfo = $studentCourseInfo->toArray();

                $studentId = $studentCourseInfo[0]['studentId'];
                $courseId = $studentCourseInfo[0]['courseId'];

                $postData['courseId'] = $courseId;

                $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                $studentCourseInfo[0]['enrollList'] = $objStudentSubjectEnrolment->getStudentCourseListForPdf($studentId, $courseId, $postData);

                if ($type == 'C' && $postData['isCompleted'] == 'Y') {
                    $objStudentCourse->completeStudentCourseInfo($studCourseId);
                }

                $studentCourseInfo[0]['issueDate'] = $postData['issueDate'];
                $studentCourseInfo[0]['completionDate'] = $postData['completionDate'];

                $objStudentCertificateRegister = new StudentCertificateRegister;
                $studentCourseInfo[0]['certificateType'] = $objStudentCertificateRegister->previewCertificateRegisterFromPdf($collegeId, $postData, $studentId);

                $dataArr[] = $studentCourseInfo[0];
            }
            $objRtoCollegeDetails = new CollegeDetails;
            $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId)[0];
            $data['objCollegeDetails'] = $objCollegeDetails;
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath);
            // $data['college_signature'] = str_replace("\\", "/", $destinationPath['view']).$objCollegeDetails->college_signature;
            $data['college_signature'] = UploadService::imageEmbed($destinationPath['view'].$objCollegeDetails->college_signature);

            $data['studentCourseInfo'] = $dataArr;

            $pdf = App::make('dompdf.wrapper');

            if ($type == 'C') {
                $pdf->loadView('frontend.bulk_certificate.certificate-pdf-multiple', $data);
            } elseif ($type == 'SOA') {
                $pdf->loadView('frontend.bulk_certificate.statement-attainment-pdf-multilpe', $data);
            } elseif ($type == 'TOCA') {
                $pdf->loadView('frontend.bulk_certificate.cometencies-achieved-pdf-multiple', $data);
            } elseif ($type == 'TOGA') {
                $pdf->loadView('frontend.bulk_certificate.grades-achieved-pdf-multiple', $data);
            } elseif ($type == 'CL') {
                $data['user_name'] = Auth::user()->name;
                $pdf->loadView('frontend.bulk_certificate.completion-letter-pdf-multiple', $data);
            }

            return $pdf->stream();

            // $typeArr = array('C' => 'certificate', 'SOA' => 'Statement of Attainment', 'TOCA' => 'Transcript of Cometencies Achieved', 'TOGA' => 'Transcript of Grades Achieved', 'CL' => 'Completion Letter');
            // return $pdf->download($typeArr[$type] . '.pdf');
        }
    }

    public function generateTranscriptPdfMultiple($type, $studCourseId, Request $request)
    {
        $dataArray = [];
        $collegeId = Auth::user()->college_id;
        $data['collegeId'] = $collegeId;

        if ($request->isMethod('post')) {
            $postData = $request->input('hidData');
            $postData = json_decode($postData, true);

            $studentArray = $postData['studCourseId'];
            $type = $postData['transcriptType'];

            $objStudentCourse = new StudentCourse;
            for ($i = 0; $i < count($studentArray); $i++) {
                $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studentArray[$i]);

                if ($studentCourseInfo->count() > 0) {
                    $studentCourseInfo = $studentCourseInfo->toArray();

                    $studentId = $studentCourseInfo[0]['studentId'];
                    $courseId = $studentCourseInfo[0]['courseId'];

                    $postData['courseId'] = $courseId;

                    $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                    $studentCourseInfo[0]['enrollList'] = $objStudentSubjectEnrolment->getStudentCourseListForPdf($studentId, $courseId, $postData);

                    if ($type == 'C' && $postData['isCompleted'] == 'Y') {
                        $objStudentCourse->completeStudentCourseInfo($studentArray[$i]);
                    }

                    $studentCourseInfo[0]['issueDate'] = $postData['issueDate'];
                    $studentCourseInfo[0]['completionDate'] = $postData['completionDate'];

                    $objStudentCertificateRegister = new StudentCertificateRegister;
                    $studentCourseInfo[0]['certificateType'] = $objStudentCertificateRegister->saveCertificateRegisterFromPdf($collegeId, $postData, $studentId);

                    $dataArray[] = $studentCourseInfo[0];
                }
            }

            $objRtoCollegeDetails = new CollegeDetails;
            $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId)[0];
            $data['objCollegeDetails'] = $objCollegeDetails;
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath);
            // $data['college_signature'] = str_replace("\\", "/", $destinationPath['view']).$objCollegeDetails->college_signature;
            $data['college_signature'] = UploadService::imageEmbed($destinationPath['view'].$objCollegeDetails->college_signature);

            $data['studentCourseInfo'] = $dataArray;

            $pdf = App::make('dompdf.wrapper');

            if ($type == 'C') {
                $pdf->loadView('frontend.bulk_certificate.certificate-pdf-multiple', $data);
            } elseif ($type == 'SOA') {
                $pdf->loadView('frontend.bulk_certificate.statement-attainment-pdf-multilpe', $data);
            } elseif ($type == 'TOCA') {
                $pdf->loadView('frontend.bulk_certificate.cometencies-achieved-pdf-multiple', $data);
            } elseif ($type == 'TOGA') {
                $pdf->loadView('frontend.bulk_certificate.grades-achieved-pdf-multiple', $data);
            } elseif ($type == 'CL') {
                $data['user_name'] = Auth::user()->name;
                $pdf->loadView('frontend.bulk_certificate.completion-letter-pdf-multiple', $data);
            }
            // return $pdf->stream();

            $typeArr = ['C' => 'certificate', 'SOA' => 'Statement of Attainment', 'TOCA' => 'Transcript of Cometencies Achieved', 'TOGA' => 'Transcript of Grades Achieved', 'CL' => 'Completion Letter'];

            // For save to new
            for ($i = 0; $i < count($data['studentCourseInfo']); $i++) {

                $dataItems = $data['studentCourseInfo'][$i];
                $dataItems['studentCourseInfo'][0] = $data['studentCourseInfo'][$i];

                $studentId = $dataItems['studentId'];
                $courseId = $dataItems['courseId'];
                $postData['courseId'] = $courseId;
                $postData['certificateType'] = $dataItems['certificateType'];

                $dataItems['isDownload'] = 1;
                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId)[0];
                $dataItems['objCollegeDetails'] = $objCollegeDetails;
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $dataItems['college_signature'] = str_replace('\\', '/', $destinationPath['view']).$objCollegeDetails->college_signature;

                $pdfNew = App::make('dompdf.wrapper');
                $typeArr = Config::get('constants.generateCertificateList');
                if ($type == 'C') {
                    $pdfNew->loadView('v2.sadmin.student.bulk_certificate.certificate-pdf-multiple', $dataItems);
                } elseif ($type == 'SOA') {
                    $pdfNew->loadView('v2.sadmin.student.bulk_certificate.statement-attainment-pdf-multilpe', $dataItems);
                } elseif ($type == 'TOCA') {
                    $pdfNew->loadView('v2.sadmin.student.bulk_certificate.cometencies-achieved-pdf-multiple', $dataItems);
                } elseif ($type == 'TOGA') {
                    $pdfNew->loadView('v2.sadmin.student.bulk_certificate.grades-achieved-pdf-multiple', $dataItems);
                } elseif ($type == 'CL') {
                    $dataItems['user_name'] = Auth::user()->name;
                    $pdfNew->loadView('v2.sadmin.student.bulk_certificate.completion-letter-pdf-multiple', $dataItems);
                }
                $filePath = Config::get('constants.uploadFilePath.StudentCertificate');
                // $destinationPath = Helpers::changeRootPath($filePath);
                $destinationPath = $this->changeRootPath($filePath, $collegeId);
                $fileName = str_replace(' ', '_', $typeArr[$type]).'_'.time();
                $pdfNew->save($destinationPath['default'].$fileName.'.pdf');
                $objStudentCertificateRegister = new StudentCertificateRegisterV2;
                $objStudentCertificateRegister->saveCertificateRegisterFromPdfV2($collegeId, $postData, $studentId, $fileName);

            }

            return $pdf->download($typeArr[$type].'.pdf');
        }
    }

    public function generateElicosPdf($type, $studCourseId, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $data['collegeId'] = $collegeId;

        if ($request->isMethod('post')) {
            $postData = $request->input('hidData');
            $postData = json_decode($postData, true);

            $studCourseId = $postData['studCourseId'];
            $type = $postData['transcriptType'];

            $objStudentCourse = new StudentCourse;
            $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

            $dataArr = [];
            if ($studentCourseInfo->count() > 0) {
                $studentCourseInfo = $studentCourseInfo->toArray();

                $postData['courseId'] = $studentCourseInfo[0]['courseId'];

                $studentCourseInfo[0]['issueDate'] = $postData['issueDate'];
                $studentCourseInfo[0]['startDate'] = $studentCourseInfo[0]['start_date'];
                $studentCourseInfo[0]['completionDate'] = $studentCourseInfo[0]['finish_date'];
                $studentCourseInfo[0]['completionDateSingle'] = $postData['completionDateSingle'];
                $studentCourseInfo[0]['isSpecify'] = $postData['isSpecify'];

                $objStudentCertificateRegister = new StudentCertificateRegister;
                $studentCourseInfo[0]['certificateType'] = $objStudentCertificateRegister->saveCertificateRegisterFromPdf($collegeId, $postData, $studentCourseInfo[0]['studentId']);

                $dataArr = $studentCourseInfo[0];
            }

            $data['studentCourseInfo'] = $dataArr;

            $pdf = App::make('dompdf.wrapper');
            if ($type == 'C') {
                $pdf->loadView('frontend.bulk_certificate.elicos-certificate-pdf', $data);
            } elseif ($type == 'CL') {
                $data['user_name'] = Auth::user()->name;
                $pdf->loadView('frontend.bulk_certificate.completion-letter-pdf', $data);
            }

            return $pdf->stream();

            $typeArr = ['C' => 'EILCOS certificate', 'CL' => 'Completion Letter'];

            return $pdf->download($typeArr[$type].'.pdf');
        }
    }

    public function generateElicosPdfMultiple($type, $studCourseId, Request $request)
    {
        $dataArray = [];
        $collegeId = Auth::user()->college_id;
        $data['collegeId'] = $collegeId;

        if ($request->isMethod('post')) {
            $postData = $request->input('hidData');
            $postData = json_decode($postData, true);

            $studentArray = $postData['studCourseId'];
            $type = $postData['transcriptType'];

            $objStudentCourse = new StudentCourse;
            for ($i = 0; $i < count($studentArray); $i++) {
                $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studentArray[$i]);

                if ($studentCourseInfo->count() > 0) {
                    $studentCourseInfo = $studentCourseInfo->toArray();

                    $postData['courseId'] = $studentCourseInfo[0]['courseId'];
                    $studentCourseInfo[0]['issueDate'] = $postData['issueDate'];
                    $studentCourseInfo[0]['startDate'] = $studentCourseInfo[0]['start_date'];
                    $studentCourseInfo[0]['completionDate'] = $studentCourseInfo[0]['finish_date'];
                    $studentCourseInfo[0]['completionDateElocos'] = $postData['completionDateHidden'];
                    $studentCourseInfo[0]['isSpecify'] = $postData['isSpecify'];

                    $objStudentCertificateRegister = new StudentCertificateRegister;
                    $studentCourseInfo[0]['certificateType'] = $objStudentCertificateRegister->saveCertificateRegisterFromPdf($collegeId, $postData, $studentCourseInfo[0]['studentId']);

                    $dataArray[] = $studentCourseInfo[0];
                }
            }

            $data['studentCourseInfo'] = $dataArray;

            $pdf = App::make('dompdf.wrapper');

            if ($type == 'C') {
                $pdf->loadView('frontend.bulk_certificate.elicos-certificate-pdf-multiple', $data);
            } elseif ($type == 'CL') {
                $data['user_name'] = Auth::user()->name;
                $pdf->loadView('frontend.bulk_certificate.completion-letter-pdf-multiple', $data);
            }
            //            return $pdf->stream();

            $typeArr = ['C' => 'ELICOS certificate', 'CL' => 'Completion Letter'];

            return $pdf->download($typeArr[$type].'.pdf');
        }
    }
}
