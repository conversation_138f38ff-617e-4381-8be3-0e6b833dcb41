<template>
    <Scheduler
        :data-items="sampleData"
        :default-date="displayDate"
        :default-view="'day'"
        :views="views"
        :view="view"
        :class="'tw-scheduler tw-scheduler--default mb-8'"
        :footer="false"
        height="1150px"
        :header="'headerRender'"
        :item="'itemRender'"
        :slotRender="'slotRender'"
        @viewchange="handleViewChange"
        @datechange="handleDateChange"
    >
        <template v-slot:dateHeaderCell="{ props }">
            <span>{{ getAbbrDate(props.default) }}</span>
        </template>
        <template v-slot:headerRender="{ props }">
            <SchedulerHeader>
                <div class="flex w-full items-center justify-end gap-2">
                    <SchedulerViewSelector />
                    <div class="flex items-center rounded-md">
                        <button
                            class="btn-secondary cursor-pointer rounded-e-none border-r p-2 focus:ring-0 focus:ring-offset-0"
                            @click="props.onPrevclick"
                        >
                            <icon-chevron-left />
                        </button>
                        <Popover
                            :open="show"
                            @click="toggleDatePicker"
                            @close="closeDatePicker"
                            :pt="{ popover: 'tw-popover__timetable-calendar' }"
                        >
                            <template #picker>
                                <div
                                    id="schedulerCalendar"
                                    class="flex h-[2.125rem] cursor-pointer items-center justify-center gap-2 border-y border-gray-200 bg-white"
                                    :class="view === 'week' ? 'w-44' : 'w-24'"
                                    ref="button"
                                    @click.stop="toggleDatePicker"
                                >
                                    <!-- <NavigationDatePicker
                                :value="props.currentDate"
                                @change="props.onDatepickerchange"
                                :title="'goto calen'"
                                :popup-settings="{
                                    animate: false,
                                    offset: { left: 150, top: 50 },
                                }"
                            /> -->
                                    <IconCalendar class="h-5 w-5 text-gray-400"></IconCalendar>
                                    <span class="text-xs font-medium leading-4 text-gray-700">
                                        {{ getFormattedLabel(props.currentDate) }}
                                    </span>
                                </div>
                            </template>
                            <template #popup>
                                <Calendar
                                    :value="props.currentDate"
                                    @change="
                                        props.onDatepickerchange($event);
                                        closeDatePicker();
                                    "
                                >
                                </Calendar>
                            </template>
                        </Popover>
                        <button
                            class="btn-secondary cursor-pointer rounded-s-none border-l p-2 focus:ring-0 focus:ring-offset-0"
                            @click="props.onNextclick"
                        >
                            <icon-chevron-right />
                        </button>
                    </div>
                </div>
            </SchedulerHeader>
        </template>

        <template v-slot:slotRender="{ props }">
            <SchedulerSlot
                :style="{
                    transition: 'background-color 500ms ease',
                    'background-color': dayjs(selectedSlot).isSame(dayjs(props.start), 'day')
                        ? 'rgba(239, 68, 68, 0.3)'
                        : 'white',
                }"
                v-bind="props"
                @showmoreitems="props.onShowmoreitems"
            >
            </SchedulerSlot>
        </template>
        <template v-slot:itemRender="{ props }">
            <SchedulerItem
                v-bind="props"
                itemRef="componentRef"
                :itemStyle="{
                    backgroundColor: getBorderColor(props.description.bgColor),
                    marginLeft: '4px',
                    pointerEvents: 'cursor',
                    height: 'auto',
                    borderLeft: '2px solid',
                    marginTop: '0.875rem',
                    borderLeftColor: getBorderColor(props.description.borderColor),
                    // display: schedulerDisplayStyle,
                }"
            >
                <CalendarMarkCell
                    :item="props"
                    :popoverItem="'timetable'"
                    :showIcon="false"
                    :pt="{ root: 'space-y-0' }"
                >
                    <template #features>
                        <div class="ps-1 text-xxs" :class="[props.description.bgColor]">
                            <div class="text-gray-900" :class="{ truncate: view === 'month' }">
                                {{ props.description.subject }}
                            </div>
                            <div
                                class="flex flex-wrap items-center gap-0.5 text-gray-700"
                                :class="{ truncate: view === 'month' }"
                            >
                                <span class="min-w-fit">{{ props.description.batchTime }}</span>
                                <div class="h-2 w-px bg-gray-700"></div>
                                <span class="min-w-fit">{{ props.description.roomName }}</span>
                                <div class="h-2 w-px bg-gray-700"></div>
                                <span class="min-w-fit">{{ props.description.teacherName }}</span>
                            </div>
                        </div>
                    </template>
                </CalendarMarkCell>
            </SchedulerItem>
        </template>
    </Scheduler>
</template>

<script setup>
import { ToolbarSpacer } from '@progress/kendo-vue-buttons';
import { ref, defineProps, watch, computed, onMounted } from 'vue';
import {
    Scheduler,
    SchedulerHeader,
    SchedulerFooter,
    BusinessHours,
    SchedulerNavigation,
    NavigationDatePicker,
    SchedulerViewSelector,
    SchedulerItem,
    SchedulerSlot,
    SchedulerViewSlot,
    SchedulerViewItem,
} from '@progress/kendo-vue-scheduler';
// import { prepareCalendarData } from "../../services/attendanceResource";
import CalendarMarkCell from '@spa/components/Calendar/CalendarMarkCell.vue';
import { createDate } from '@progress/kendo-date-math';
import {
    IconChevronLeft24Regular as IconChevronLeft,
    IconChevronRight24Regular as IconChevronRight,
    IconCalendar24Regular as IconCalendar,
} from '@iconify-prerendered/vue-fluent';
import { useStudentTimetable } from '@spa/stores/modules/studentprotal/timetable.store';
import { DatePicker } from '@progress/kendo-vue-dateinputs';
import dayjs from 'dayjs';
import { Calendar } from '@progress/kendo-vue-dateinputs';
import { Popup } from '@progress/kendo-vue-popup';
import { prevDayOfWeek, nextDayOfWeek, Day } from '@progress/kendo-date-math';
import Popover from '@spa/components/Popover/Popover.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';

const selectedEvent = ref(false);
const moreInfoRef = ref(null);
const open = ref(false);

const loaderStore = useLoaderStore();

const props = defineProps({
    attenDanceDataCalendar: Array,
});

const emit = defineEmits(['dateChange']);

const show = ref(false);

const schedulerDisplayStyle = computed(() => {
    return loaderStore.contextLoaders['timetable-loader'] ? 'none' : 'block';
});

const popupOptions = {
    popupClass: 'tw-daterange-popup mt-1',
    popupAlign: {
        horizontal: 'right',
        vertical: 'top',
    },
    anchorAlign: {
        horizontal: 'right',
        vertical: 'bottom',
    },
    animate: false,
    offset: {
        top: 10,
    },
    style: {
        left: 'unset',
        right: '2rem',
    },
};

const componentRef = ref(null);

onMounted(() => {
    if (componentRef.value) {
        const offsetTopValue = componentRef.value.offsetTop + 30;
    }
});
const selectedSlot = ref(null);

const view = ref('month');
const views = ref([
    {
        name: 'day',
        title: 'Day',
        startTime: '05:00',
        endTime: '19:00',
        workDayStart: '05:00',
        workDayEnd: '19:00',
    },
    {
        name: 'week',
        title: 'Week',
        startTime: '05:00',
        endTime: '19:00',
        workDayStart: '05:00',
        workDayEnd: '19:00',
    },
    {
        name: 'month',
        title: 'Month',
        dateHeaderCell: 'dateHeaderCell',
        itemsPerSlot: 2,
    },
]);

const store = useStudentTimetable();

const currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth();
const currentDay = new Date().getDate();

const parseAdjust = (eventDate, isEnd = false) => {
    const date = new Date(eventDate);
    let year = date.getFullYear();
    let month = date.getMonth(); // Month is 0-based
    let day = date.getDate();
    if (isEnd) {
        day = day + 1;
    }
    const newDate = createDate(year, month, day);
    return newDate;
};

// const displayDate = new Date(Date.UTC(currentYear, 5, 24));

const displayDate = computed(() => {
    // const data = this.sampleData;

    if (Array.isArray(store.data) && store.data.length > 0) {
        const currentDate = new Date();
        const item = store.data[0];
        const itemStartDate = new Date(item.startDate);
        const itemEndDate = new Date(item.endDate);
        if (currentDate >= itemStartDate && currentDate <= itemEndDate) {
            return new Date(
                Date.UTC(
                    currentDate.getUTCFullYear(),
                    currentDate.getUTCMonth(),
                    currentDate.getUTCDate()
                )
            );
        } else {
            return new Date(
                Date.UTC(
                    itemStartDate.getUTCFullYear(),
                    itemStartDate.getUTCMonth(),
                    itemStartDate.getUTCDate()
                )
            );
        }
    } else {
        return new Date(Date.UTC(currentYear, currentMonth, currentDay)); // Default display date
    }
});

const sampleData = computed(() => {
    if (Array.isArray(store.data) && store.data.length > 0) {
        return store.data.map((dataItem) => ({
            id: dataItem.TaskId,
            // Start: new Date(dayjs(dataItem?.Start).toISOString()),
            start: new Date(dataItem?.Start),
            startTimezone: null,
            // End: new Date(dayjs(dataItem?.End).toISOString()),
            end: new Date(dataItem?.End),
            endTimezone: null,
            isAllDay: false,
            title: dataItem.Title,
            description: {
                subject: dataItem.subject_name,
                roomName: dataItem.room_name ?? null,
                batchTime: dataItem.timetable_time,
                teacherName: dataItem.trainer_name,
                batch: dataItem.batch,
                semester_name: dataItem.semester_name,
                term: dataItem.term,
                student_attendance_id: dataItem.id,
                timetable_id: dataItem.TaskId,
                timetable_detail_id: dataItem.TaskId,
                bgColor: dataItem.color1,
                borderColor: dataItem.color2,
                mode: dataItem.trainer_name,
                type: dataItem.attendance_type,
                break: dataItem.break,
            },
            recurrenceRule: null,
            recurrenceId: null,
            recurrenceExceptions: null,
            roomId: dataItem.room_name ?? null,
            ownerID: dataItem.room_name ?? null,
            personId: dataItem.room_name ?? null,
            roomName: dataItem.room_name ?? null,
        }));
    } else {
        return []; // Return an empty array if baseData is empty or not an array
    }
});

const handleDateChange = (e) => {
    const date = e.date;
    if (e.event.value) {
        selectedSlot.value = date;
    }
    setTimeout(() => {
        selectedSlot.value = null;
    }, 1000);
    emit('dateChange', date, view);
};

const showTooltip = () => {
    open.value = true;
};

const toggleDatePicker = () => {
    show.value = !show.value;
};

const closeDatePicker = () => {
    show.value = false;
};

const filterElements = (element) => {
    if (element.tagName === 'DIV') {
        return true;
    }
    return false;
};

const handleClose = () => {
    open.value = false;
};

const getAbbrDate = (day) => {
    const dayMappings = {
        Sunday: 'Sun',
        Monday: 'Mon',
        Tuesday: 'Tue',
        Wednesday: 'Wed',
        Thursday: 'Thu',
        Friday: 'Fri',
        Saturday: 'Sat',
    };

    return dayMappings[day];
};

const getBorderColor = (color) => {
    let colorMappings = {
        'bg-yellow-500': '#f59e0b',
        'bg-yellow-50': '#fffbeb',
        'bg-red-500': '#ef4444',
        'bg-red-50': '#fef2f2',
        'bg-primary-blue-500': '#1890ff',
        'bg-blue-50': '#e6f7ff',
        'bg-green-500': '#10b981',
        'bg-green-50': '#ecfdf5',
        'bg-purple-500': '#8b5cf6',
        'bg-purple-50': '#f5f3ff',
    };
    return colorMappings[color] || '#1890ff';
};

const handleViewChange = (e) => {
    view.value = e.viewName;
};

const getFormattedLabel = (date) => {
    if (view.value === 'month') {
        return dayjs(date).format('MMM YYYY');
    } else if (view.value === 'week') {
        const currDate = new Date(date);
        let startDate = prevDayOfWeek(currDate, Day.Monday);
        let endDate = nextDayOfWeek(currDate, Day.Sunday);
        return `${dayjs(startDate).format('MMM D YYYY')} - ${dayjs(endDate).format('MMM D YYYY')}`;
    } else if (view.value === 'day') {
        return dayjs(date).format('MMM D YYYY');
    }
    return date;
};
</script>

<style>
.tw-filter-picker {
    height: 100% !important;
    display: flex;
    align-items: center;
    width: 100%;
    /* margin-left: 0.25rem; */
}
</style>
