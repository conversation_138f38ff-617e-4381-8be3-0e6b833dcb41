<?php

namespace App\Http\Controllers\v2\sadmin;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Model\v2\Agent;
use App\Model\v2\AgentDocumentChecklist;
use App\Model\v2\AgentEmailTemplateSetting;
use App\Model\v2\AgentStatus;
use App\Model\v2\AssessmentDueDate;
use App\Model\v2\CampusVenue;
use App\Model\v2\Checklist;
use App\Model\v2\Classroom;
use App\Model\v2\CollegeCampus;
use App\Model\v2\CollegeDetails;
use App\Model\v2\Colleges;
use App\Model\v2\Country;
use App\Model\v2\Courses;
use App\Model\v2\CourseType;
use App\Model\v2\EmailParameter;
use App\Model\v2\EmailTemplate;
use App\Model\v2\LetterParameter;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\OfferTrackingStatus;
use App\Model\v2\Report;
use App\Model\v2\ReportCategory;
use App\Model\v2\Roles;
use App\Model\v2\ServicesFee;
use App\Model\v2\SetupSection;
use App\Model\v2\SetupSectionOption;
use App\Model\v2\SmtpSetup;
use App\Model\v2\StudentIdFormate;
use App\Model\v2\UserRoleType;
use App\Repositories\OnboardRepository;
use App\Traits\CommonTrait;
use App\Users;
use Domains\Customers\Billing\Models\StripeConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Support\Services\UploadService;
use Support\Traits\HasCentralMeta;

class OnboardSettingController extends Controller
{
    use CommonTrait;
    use HasCentralMeta;

    protected $colleges;

    protected $collegeDetails;

    protected $setupSection;

    protected $setupSectionOption;

    protected $offerDocumentChecklist;

    protected $agentDocumentChecklist;

    protected $offerTrackingStatus;

    protected $checklist;

    protected $campusVenue;

    protected $classroom;

    protected $campus;

    protected $smtpSetup;

    protected $country;

    protected $ServicesFee;

    protected $AssessmentDueDate;

    protected $AgentEmailTemplateSetting;

    protected $EmailTemplate;

    public function __construct(
        Colleges $collegesModel,
        CollegeDetails $collegeDetails,
        SetupSection $setupSection,
        SetupSectionOption $setupsectionoption,
        OfferDocumentChecklist $offerDocumentChecklist,
        AgentDocumentChecklist $agentDocumentChecklist,
        OfferTrackingStatus $offerTrackingStatus,
        Checklist $checklist,
        CampusVenue $campusVenue,
        Classroom $classroom,
        CollegeCampus $campus,
        SmtpSetup $smtpSetup,
        Country $country,
        ServicesFee $ServicesFee,
        AssessmentDueDate $assessmentDueDate,
        AgentEmailTemplateSetting $agentemaileemplatesetting,
        EmailTemplate $emailtemplate

    ) {
        parent::__construct();
        $this->colleges = new OnboardRepository($collegesModel);
        $this->collegeDetails = new OnboardRepository($collegeDetails);
        $this->setupSection = new OnboardRepository($setupSection);
        $this->setupSectionOption = new OnboardRepository($setupsectionoption);
        $this->offerDocumentChecklist = new OnboardRepository($offerDocumentChecklist);
        $this->agentDocumentChecklist = new OnboardRepository($agentDocumentChecklist);
        $this->offerTrackingStatus = new OnboardRepository($offerTrackingStatus);
        $this->checklist = new OnboardRepository($checklist);
        $this->campusVenue = new OnboardRepository($campusVenue);
        $this->classroom = new OnboardRepository($classroom);
        $this->campus = new OnboardRepository($campus);
        $this->smtpSetup = new OnboardRepository($smtpSetup);
        $this->country = new OnboardRepository($country);
        $this->ServicesFee = new OnboardRepository($ServicesFee);
        $this->AssessmentDueDate = new OnboardRepository($assessmentDueDate);
        $this->AgentEmailTemplateSetting = new OnboardRepository($agentemaileemplatesetting);
        $this->EmailTemplate = new OnboardRepository($emailtemplate);
    }

    private function isAuthorized()
    {
        if (empty($this->loginUser->college_id)) {
            return redirect(route('user_login'));
        }
        // dd($this->colleges->show($this->loginUser->college_id)->onboard_setup);
        if ($this->colleges->show($this->loginUser->college_id)->onboard_setup == '0') {

            redirect(route('onboard-setup'));
            exit;
        }

        return true;
    }

    public function generalInfo(Request $request)
    {
        // dd($request->all());
        $arrTrainingOrg = Config::get('constants.arrTraningOrgazinationType');
        $arrTimeZoneList = Config::get('constants.arrTimeZoneList');
        $defaultCollegeTimezone = Config::get('constants.default_college_timezone');

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath, null, $data['id']);

        $college_dean_signature_path = $destinationPath['view'].$data['dean_signature'];
        $college_signature_path = $destinationPath['view'].$data['college_signature'];
        $logo_picture_path = $destinationPath['view'].$data['college_logo'];
        $college_admission_signature = $destinationPath['view'].$data['admission_manager_signature'];
        $college_student_support_signature = $destinationPath['view'].$data['student_support_signature'];

        $college_dean_signature_url = $this->getUploadedFileUrl($college_dean_signature_path);
        $college_signature_url = $this->getUploadedFileUrl($college_signature_path);
        $logo_picture_url = $this->getUploadedFileUrl($logo_picture_path);
        $college_admission_signature_url = $this->getUploadedFileUrl($college_admission_signature);
        $college_student_support_signature_url = $this->getUploadedFileUrl($college_student_support_signature);

        // if (file_exists($logo_picture) && !empty($data['college_logo'])) {
        //     $logo_picture_url = asset($destinationPath['view'] . $data['college_logo']);
        //     $logo_picture_url = UploadService::url($destinationPath['view'] . $data['college_logo']);
        // } else {
        //     $logo_picture_url = asset('v2/img/user.png');
        // }

        // $college_signature = $destinationPath['default'] . $data['college_signature'];
        // if (file_exists($college_signature) && !empty($data['college_signature'])) {
        //     $college_signature_url = asset($destinationPath['view'] . $data['college_signature']);
        // } else {
        //     $college_signature_url = asset('v2/img/user.png');
        // }

        // $college_admission_signature = $destinationPath['default'] . $data['admission_manager_signature'];
        // if (file_exists($college_admission_signature) && !empty($data['admission_manager_signature'])) {
        //     $college_admission_signature_url = asset($destinationPath['view'] . $data['admission_manager_signature']);
        // } else {
        //     $college_admission_signature_url = asset('v2/img/user.png');
        // }
        // $college_student_support_signature = $destinationPath['default'] . $data['student_support_signature'];
        // if (file_exists($college_student_support_signature) && !empty($data['student_support_signature'])) {
        //     $college_student_support_signature_url = asset($destinationPath['view'] . $data['student_support_signature']);
        // } else {
        //     $college_student_support_signature_url = asset('v2/img/user.png');
        // }
        $arrCourseTypesList = CourseType::getDropdownList()->pluck('label', 'value')->toArray();
        $flag = (empty($data['RTO_code'])) ? 'insert' : 'update';
        $apiCourseTypes = $data['course_type_for_api'] ?? null;
        $enableShortCourseApi = $data['enable_shortcourse_api'] ?? null;
        $apiCourseTypes = (! empty($apiCourseTypes)) ? explode(',', $apiCourseTypes) : [];

        $enableShortCourseApi = tenant()->getMeta('short_course_api_enabled');
        $shortcourseDomains = getCentralMeta('short_course_api_domains');
        $shortcourseDomains = ($shortcourseDomains) ? explode(',', $shortcourseDomains) : [];

        $currentTenant = tenant();
        $tenantDomain = $currentTenant->domain ?? $currentTenant->id ?? null;

        $shortcourseDomain = config('app.shortcourse_domain');
        $defaultShortCourseDomain = $tenantDomain.$shortcourseDomain;

        if (! in_array($defaultShortCourseDomain, $shortcourseDomains)) {
            $shortcourseDomains = implode('],[', [...[$defaultShortCourseDomain], ...$shortcourseDomains]);
            $shortcourseDomains = "[{$shortcourseDomains}]";
            tenant()->setMeta('short_course_api_domains', $shortcourseDomains);
        }
        /* check whether stripe is connected */
        $stripeConfig = new StripeConfig;
        $stripeConnected = $stripeConfig->isConnected();

        $data['is_stripe_connected'] = $stripeConnected ?? false;

        $data['allowed_domains'] = $shortcourseDomains;
        $data['default_shortcourse_domain'] = $defaultShortCourseDomain;
        $data['course_type_for_api'] = $apiCourseTypes;
        $data['enable_shortcourse_api'] = $enableShortCourseApi === 'enabled';
        $data['college_url'] = (empty($data['college_url'])) ? url('/') : $data['college_url'];
        $data['title'] = 'General Information';
        $data['keywords'] = 'General Information';
        $data['description'] = 'General Information';
        $data['arrTrainingOrg'] = $arrTrainingOrg;
        $data['arrTimeZoneList'] = $arrTimeZoneList;
        $data['arrCourseTypesList'] = $arrCourseTypesList;
        $data['logo_picture_url'] = $logo_picture_url;
        $data['college_signature_url'] = $college_signature_url;
        $data['college_dean_signature_url'] = $college_dean_signature_url;
        $data['college_admission_signature_url'] = $college_admission_signature_url;
        $data['college_student_support_signature_url'] = $college_student_support_signature_url;
        $data['college_timezone_default'] = $defaultCollegeTimezone;
        $data['api_token'] = $this->loginUser->api_token;
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.organisation.generalinfo', $data);
    }

    public function usiSetups(Request $request)
    {
        $data['title'] = 'USI Setups';
        $data['keywords'] = 'USI Setups';
        $data['description'] = 'USI Setups';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.organisation.usi-setup', $data);
    }

    public function trainingOrg(Request $request)
    {
        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $flag = (empty($data['RTO_code'])) ? 'insert' : 'update';
        $data['title'] = 'Training Organization Identifier';
        $data['keywords'] = 'Training Organization Identifier';
        $data['description'] = 'Training Organization Identifier';
        $data['api_token'] = $this->loginUser->api_token;
        $data['mainmenu'] = 'settings';
        $data['onboardSetupStateList'] = Config::get('constants.onboardSetupStateList');

        return view('v2.sadmin.onboardsetting.organisation.training-organization', $data);
    }

    public function mainLocation(Request $request)
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['api_token'] = $this->loginUser->api_token;
        $data['title'] = 'Main Location';
        $data['keywords'] = 'Main Location';
        $data['description'] = 'Main Location';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.organisation.main-location', $data);
    }

    public function bankDetails()
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['api_token'] = $this->loginUser->api_token;
        $data['title'] = 'Bank Details';
        $data['keywords'] = 'Bank Details';
        $data['description'] = 'Bank Details';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.organisation.bank-details', $data);
    }

    public function VSLinfo()
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['title'] = 'Vsl Info';
        $data['keywords'] = 'Vsl Info';
        $data['description'] = 'Vsl Info';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.organisation.vsl-info', $data);
    }

    public function manageSection(Request $request)
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['data'] = $this->setupSection->getSectionData($request);
        $data['total'] = $this->setupSection->getSectionData($request, true);
        $data['title'] = 'Manage Section';
        $data['keywords'] = 'Manage Section';
        $data['description'] = 'Manage Section';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.manage-section.index', $data);
    }

    public function offerLetter(Request $request)
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['data'] = $this->offerDocumentChecklist->getOfferLetterData($request);
        $data['total'] = $this->offerDocumentChecklist->getOfferLetterData($request, true);
        $data['title'] = 'Offer Letter';
        $data['keywords'] = 'Offer Letter';
        $data['description'] = 'Offer Letter';
        $data['type'] = 'offerLetter';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.setup-checklist.offer-letter', $data);
    }

    public function agentsDocument(Request $request)
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['data'] = $this->agentDocumentChecklist->getAgentDocumentsData($request);
        $data['total'] = $this->agentDocumentChecklist->getAgentDocumentsData($request, true);
        $data['title'] = 'Agent Document';
        $data['keywords'] = 'Agent Document';
        $data['description'] = 'Agent Document';
        $data['type'] = 'agentsDocument';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.setup-checklist.agent-document', $data);
    }

    public function offerTracking(Request $request)
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['data'] = $this->offerTrackingStatus->getOfferTrackingData($request);
        $data['total'] = $this->offerTrackingStatus->getOfferTrackingData($request, true);
        $data['title'] = 'Offer Status';
        $data['keywords'] = 'Offer Status';
        $data['description'] = 'Offer Status';
        $data['type'] = 'offerTracking';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.setup-checklist.offer-tracking', $data);
    }

    public function customChecklist(Request $request)
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['data'] = $this->checklist->getCustomCheckListData($request);
        $data['total'] = $this->checklist->getCustomCheckListData($request, true);
        $data['title'] = 'Custom Checklist';
        $data['keywords'] = 'Custom Checklist';
        $data['description'] = 'Custom Checklist';
        $data['type'] = 'customChecklist';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.setup-checklist.custom-checklist', $data);
    }

    public function CVRlist(Request $request)
    {

        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $data['isHigherEd'] = (new CourseType)->checkHigherEdGradInCourseType();
        $data['data'] = $this->campusVenue->getVenueData($request);
        $data['total'] = $this->campusVenue->getVenueData($request, true);
        $data['data'] = $this->classroom->getRoomData($request);
        $data['total'] = $this->classroom->getRoomData($request, true);
        $data['title'] = 'Campus, Venues & Rooms';
        $data['keywords'] = 'Campus, Venues & Rooms';
        $data['description'] = 'Campus, Venues & Rooms';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.campus-venue-rooms.index', $data);
    }

    public function smtpSetup(Request $request)
    {
        $this->collegeID = (isset(Auth::user()->college_id)) ? Auth::user()->college_id : '';
        $data = $this->colleges->withId(['collegedetail'], $this->loginUser->college_id)->toArray();
        $arrSmtpData = $this->smtpSetup->getData(['college_id' => $this->collegeID])[0];
        $data['arrSmtpData'] = $arrSmtpData;
        $data['title'] = 'Smtp Setup';
        $data['keywords'] = 'Smtp Setup';
        $data['description'] = 'Smtp Setup';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.smtp-setup.index', $data);
    }

    public function courseType(Request $request)
    {
        $data['title'] = 'Course Types';
        $data['keywords'] = 'Course Types';
        $data['description'] = 'Course Types';
        $data['mainmenu'] = 'settings';
        $data['college_id'] = $this->loginUser->college_id;

        return view('v2.sadmin.onboardsetting.course-type.index', $data);
    }

    public function countryList(Request $request)
    {

        $data['title'] = 'Country List';
        $data['keywords'] = 'Country List';
        $data['description'] = 'Countr yList';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.country-list.index', $data);
    }

    public function getGteDocument(Request $request)
    {

        $data['title'] = 'GTE Document';
        $data['keywords'] = 'GTE Document';
        $data['description'] = 'GTE Document';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.gte-document.index', $data);
    }

    public function getAddedServicesFeeList(Request $request)
    {

        $data['title'] = 'Student Services Information';
        $data['keywords'] = 'Student Services Information';
        $data['description'] = 'Student Services Information';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.added-services-fee.index', $data);
    }

    public function getLanguageView(Request $request)
    {

        $data['title'] = 'Language List ';
        $data['keywords'] = 'Language List';
        $data['description'] = 'Language List';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.language-list.index', $data);
    }

    public function getAgentStatus(Request $request)
    {

        $data['title'] = 'Agent Status ';
        $data['keywords'] = 'Agent Status';
        $data['description'] = 'Agent Status';
        $data['mainmenu'] = 'settings';
        $data['college_id'] = $this->loginUser->college_id;

        return view('v2.sadmin.onboardsetting.agent-status.index', $data);
    }

    public function getCompetencyResultGrade(Request $request)
    {

        $data['title'] = 'Competency Result Grade ';
        $data['keywords'] = 'Competency Result Grade';
        $data['description'] = 'Competency Result Grade';
        $data['mainmenu'] = 'settings';
        $data['college_id'] = $this->loginUser->college_id;

        return view('v2.sadmin.onboardsetting.competency-result-grade.index', $data);
    }

    public function getOSHCInfo(Request $request)
    {

        $data['title'] = 'OSHC Info ';
        $data['keywords'] = 'OSHC Info';
        $data['description'] = 'OSHC Info';
        $data['mainmenu'] = 'settings';
        $data['college_id'] = $this->loginUser->college_id;

        return view('v2.sadmin.onboardsetting.oshc-info.index', $data);
    }

    public function failedJobs(Request $request)
    {

        $data['title'] = 'Failed Jobs';
        $data['keywords'] = 'Failed Jobs';
        $data['description'] = 'Failed Jobs';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.failed-jobs.index', $data);
    }

    public function getInterventionStrategy(Request $request)
    {

        $data['title'] = 'Intervention Strategy ';
        $data['keywords'] = 'Intervention Strategy';
        $data['description'] = 'Intervention Strategy';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.intervention-strategy.index', $data);
    }

    public function getCertificateIdFormate(Request $request)
    {

        $data['title'] = 'Certificate Id Formate ';
        $data['keywords'] = 'Certificate Id Formate';
        $data['description'] = 'Certificate Id Formate';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.certificate-id-formate.index', $data);
    }

    public function getAssessmentDueDateExtention(Request $request)
    {

        $collegeId = $this->collegeID = (isset(Auth::user()->college_id)) ? Auth::user()->college_id : '';
        $data['title'] = 'Assessment Due Date Extention ';
        $data['keywords'] = 'Assessment Due Date Extention ';
        $data['description'] = 'Assessment Due Date Extention ';
        $data['mainmenu'] = 'settings';
        $data['college_id'] = $collegeId;

        return view('v2.sadmin.onboardsetting.assessment-due-date.index', $data);
    }

    public function invoiceSetting(Request $request)
    {
        $data['title'] = 'Invoice Settings';
        $data['keywords'] = 'Invoice Settings';
        $data['description'] = 'Invoice Settings';
        $data['mainmenu'] = 'settings';
        $data['college_id'] = (isset(Auth::user()->college_id)) ? Auth::user()->college_id : '';

        return view('v2.sadmin.onboardsetting.invoice-settings.index', $data);
    }

    public function getAgentEmailTemplateSetting(Request $request)
    {

        $collegeId = $this->collegeID = (isset(Auth::user()->college_id)) ? Auth::user()->college_id : '';
        $data['college_id'] = $collegeId;
        $data['title'] = 'Notification Setting';
        $data['keywords'] = 'Agent Email Template Setting, Notification';
        $data['description'] = 'Agent Email Template Setting';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.agent-email-template-setting.index', $data);
    }

    public function getAgentLetterSetting(Request $request)
    {

        $collegeId = $this->collegeID = (isset(Auth::user()->college_id)) ? Auth::user()->college_id : '';
        $data['college_id'] = $collegeId;
        $data['title'] = 'Letter Setting';
        $data['keywords'] = 'Letter Setting';
        $data['description'] = 'Letter Setting';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.letter-setting-view.index', $data);
    }

    public function enrollmentFees()
    {

        $data['title'] = 'Enrollment Fees';
        $data['keywords'] = 'Enrollment Fees';
        $data['description'] = 'Enrollment Fees';
        $data['mainmenu'] = 'settings';
        $data['column_arr'] = [
            ['id' => 'fee_amount',     'title' => 'ENROLLMENT FEES', 'default' => 'checked'],
            ['id' => 'course_code',    'title' => 'COURSE ID',    'default' => 'checked'],
            ['id' => 'valid_upto',     'title' => 'VALID TO',    'default' => 'checked'],
            ['id' => 'national_code',  'title' => 'NATIONAL CODE', 'default' => ''],
            ['id' => 'cricos_code',    'title' => 'CRICOS CODE', 'default' => ''],
        ];

        return view('v2.sadmin.onboardsetting.college-enrollment-fees.index', $data);
    }

    public function offerLabel(Request $request)
    {

        $data['title'] = 'Offer Label';
        $data['keywords'] = 'Offer Label';
        $data['description'] = 'Offer Label';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.offerlabel.index', $data);
    }

    public function contractCode(Request $request)
    {
        $data['title'] = 'Manage Contract';
        $data['keywords'] = 'Manage Contract';
        $data['description'] = 'Manage Contract';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.training.view-contract-code', $data);
    }

    public function contractFundingSource(Request $request)
    {
        $data['title'] = 'Manage Contract Schedule';
        $data['keywords'] = 'Manage Contract Schedule';
        $data['description'] = 'Manage Contract Schedule';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.training.contract-funding-source', $data);
    }

    public function courseSite(Request $request)
    {
        $data['title'] = 'View Course Site';
        $data['keywords'] = 'View Course Site';
        $data['description'] = 'View Course Site';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.training.view-course-site', $data);
    }

    public function learningLinkList(Request $request)
    {
        $data['title'] = 'Forms';
        $data['keywords'] = 'Forms';
        $data['description'] = 'Forms';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.e-from.elearning-link', $data);
    }

    public function failedEmail(Request $request)
    {
        $data['title'] = 'Failed Email';
        $data['keywords'] = 'Failed Email';
        $data['description'] = 'Failed Email';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.e-from.fail-email', $data);
    }

    public function manageReport(Request $request)
    {
        $collegeId = $this->loginUser->college_id;
        $dropdownData = $this->prepareManageReportDropdownData($collegeId);
        $statusArr = $this->prepareStatusArray();

        return view('v2.sadmin.onboardsetting.e-from.manage-report', [
            'title' => 'Manage Report',
            'keywords' => 'Manage Report',
            'description' => 'Manage Report',
            'mainmenu' => 'settings',
            'api_token' => $this->loginUser->api_token,
            'jsData' => array_merge($dropdownData, ['statusArr' => $statusArr]),
        ]);
    }

    private function prepareManageReportDropdownData(int $collegeId): array
    {
        // Define "All" option for dropdowns
        $allOption = ['Id' => 'All', 'Name' => 'All'];

        // Fetch all dropdown data in parallel using collections
        $data = collect([
            'category' => fn () => ReportCategory::select('category as Name', 'id as Id')->get()->toArray(),
            'courseType' => fn () => CourseType::where('status', '1')
                ->whereIn('college_id', [0, $collegeId])
                ->select('title as Name', 'id as Id')
                ->get()->toArray(),
            'agents' => fn () => Agent::select('id as Id', 'agency_name as Name')
                ->where('college_id', $collegeId)
                ->orderBy('agency_name')
                ->get()->toArray(),
            'agentStatus' => fn () => AgentStatus::where('college_id', $collegeId)
                ->select('status_type as Name', 'id as Id')
                ->get()->toArray(),
            'countries' => fn () => Country::whereIn('college_id', [0, $collegeId])
                ->select('name as Name', 'id as Id')
                ->orderBy('name')
                ->get()->toArray(),
        ])->map(fn ($callback) => $callback());

        return [
            'category' => $data['category'],
            'courseType' => $data['courseType'],
            'agentArr' => collect([$allOption])->merge($data['agents'])->toArray(),
            'agentStatusArr' => $data['agentStatus'],
            'countryArr' => collect([$allOption])->merge($data['countries'])->toArray(),
            'gradeArr' => kendify(Config::get('constants.arrSelectFinalOutcomeNew'), 'Id', 'Name'),
        ];
    }

    private function prepareStatusArray(): array
    {
        $courseStatuses = Config::get('constants.arrCourseStatus');

        // Prepare status array with "All" option and Skip first element
        $statusArray = array_slice($courseStatuses, 1);
        array_unshift($statusArray, 'All');
        $formattedStatuses = [];
        foreach ($statusArray as $key => $value) {
            if (! empty($key) || $value === 'All') {
                $formattedStatuses[] = ['Name' => $value, 'Id' => $value];
            }
        }

        return $formattedStatuses;
    }

    public function bankList(Request $request)
    {
        $data['title'] = 'Bank List';
        $data['keywords'] = 'Bank List';
        $data['description'] = 'Bank List';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['collegeId'] = Auth::user()->college_id;

        return view('v2.sadmin.onboardsetting.e-from.bank-list', $data);
    }

    public function holiDayList(Request $request)
    {
        $data['title'] = 'Public College Holidays';
        $data['keywords'] = 'Holiday';
        $data['description'] = 'Holiday';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.organisation-operations.calendar.holiday-list', $data);
    }

    public function courseTemplate(Request $request)
    {
        $courses = Courses::where('status', '1')->where('college_id', Auth::user()->college_id)->select('id as Id', DB::raw("CONCAT(course_code,':' ,course_name) as Name"))->get()->toArray();
        $data['title'] = 'Course Template';
        $data['keywords'] = 'Course Template';
        $data['description'] = 'Course Template';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['jsData'] = [
            'courses' => $courses,
        ];

        return view('v2.sadmin.onboardsetting.courses.course-template', $data);
    }

    public function smsTemplateList(Request $request)
    {
        $data['title'] = 'SMS Template';
        $data['keywords'] = 'SMS Template';
        $data['description'] = 'SMS Template';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.templates-letter.sms-template', $data);
    }

    public function emailTemplateList(Request $request)
    {
        $data['title'] = 'Email Template';
        $data['keywords'] = 'Email Template';
        $data['description'] = 'Email Template';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $arrLetterParam = EmailParameter::pluck('parameter_value')->toArray();
        $data['tagarray'] = json_encode($arrLetterParam);

        return view('v2.sadmin.onboardsetting.templates-letter.email-template', $data);
    }

    public function checkList(Request $request)
    {
        $data['college_id'] = $this->loginUser->college_id;
        $data['title'] = 'Checklist';
        $data['keywords'] = 'Checklist';
        $data['description'] = 'Checklist';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.onboardsetting.templates-letter.checklist', $data);
    }

    public function letterTemplateList(Request $request)
    {
        $category = ReportCategory::select('category as Name', 'id as Id')->get()->toArray();
        $report = Report::select('report_name as Name', 'id as Id')->get()->toArray();
        $data['college_id'] = $this->loginUser->college_id;
        $data['title'] = 'Letter Template';
        $data['keywords'] = 'Letter Template';
        $data['description'] = 'Letter Template';
        $data['mainmenu'] = 'settings';
        $arrLetterParam = LetterParameter::pluck('parameter_value')->toArray();
        $data['tagarray'] = json_encode($arrLetterParam);
        $data['api_token'] = $this->loginUser->api_token;
        $data['jsData'] = [
            'category' => $category,
            'report' => $report,
        ];

        return view('v2.sadmin.onboardsetting.templates-letter.letter-template', $data);
    }

    public function profileManage(Request $request)
    {
        $loginData = auth()->user();

        $userData = Users::where(['id' => $loginData->id, 'college_id' => $loginData->college_id])->first();

        if ($request->post('action') && $request->post('action') == 'resettoken') {
            $tokenName = $request->post('token');
            if ($tokenName == config('settings.studentapitokenname')) {
                $token = $userData->generateUserToken(config('settings.adminapitokenname'), true);
                echo json_encode(['status' => 'success', 'data' => $token]);  // send data as json format
                exit;
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Invalid Request']);  // send data as json format
                exit;
            }
        }
        $token = $userData->api_token;
        if (! $token) {
            $token = $userData->generateUserToken(config('settings.adminapitokenname'), true);
            $token = $token['token'];
        }
        $getUserRolesTypes = UserRoleType::where('user_id', $loginData->id)->select('role_type')->get()->toArray();
        $roles = Roles::whereIn('id', $getUserRolesTypes)->select('role_name')->get()->toArray();

        $filePath = Config::get('constants.uploadFilePath.Users');
        $thumbText = Config::get('constants.defaultThumbText');
        $destinationPath = Helpers::changeRootPath($filePath, null, $loginData->college_id);
        // $profile_picture = $destinationPath['default'] . $thumbText . $userData->profile_picture;

        // if (file_exists($profile_picture) && !empty($userData->profile_picture)) {
        // $picture_url = asset($destinationPath['view'] . $thumbText . $userData->profile_picture);
        // $original_picture_url = asset($destinationPath['view'] . $userData->profile_picture);
        // $original_picture_url = UploadService::preview($destinationPath['view'] . $userData->profile_picture);
        // $picture_url = UploadService::preview($destinationPath['view'] . $thumbText . $userData->profile_picture);
        // } else {
        //     $picture_url = $original_picture_url = asset('v2/img/user.png');
        // }
        $original_picture_url = $this->getUploadedFileUrl($destinationPath['view'].$userData->profile_picture);
        $picture_url = $this->getUploadedFileUrl($destinationPath['view'].$thumbText.$userData->profile_picture);
        $data['isAdmin'] = $loginData->isAdmin();
        $data['isSadmin'] = $loginData->isSadmin();

        $data['title'] = 'Account Settings';
        $data['keywords'] = 'Profile';
        $data['description'] = 'Profile';
        $data['mainmenu'] = 'clients';

        $data['api_token'] = @$token ?? '';
        $data['picture_url'] = $picture_url;
        $data['userData'] = $userData;
        $data['original_picture_url'] = $original_picture_url;
        $data['roles'] = $roles;
        $data['tokens'][] = [
            'name' => config('settings.studentapitokenname'),
            'value' => @$token ?? '',
            'label' => 'API Token',
            'allow_revoke' => false,
            'allow_regenerate' => true,
        ];
        // dd($data);

        $data['jsData'] = [
            'userData' => $userData,
            'timeZoneList' => Config::get('timezone.list'),
            'defaultProfileTimeZone' => Config::get('timezone.defaultProfileTimeZone'),
        ];

        return view('v2.sadmin.onboardsetting.account-settings.profile', $data);
    }

    public function failedTransaction(Request $request)
    {
        $data['title'] = 'Xero Sync Failed Transaction';
        $data['keywords'] = 'Xero Sync Failed Transaction';
        $data['description'] = 'Xero Sync Failed Transaction';
        $data['mainmenu'] = 'clients';
        $data['api_token'] = $this->loginUser->api_token;

        return view('v2.sadmin.payments.xero-failed-transactions', $data);
    }

    public function coursesUpfrontFee(Request $request)
    {
        $data['title'] = 'Course Up Front Fee';
        $data['keywords'] = 'Course Up Front Fee';
        $data['description'] = 'Course Up Front Fee';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['collegeId'] = Auth::user()->college_id;

        return view('v2.sadmin.onboardsetting.courses.course-upfront-fee', $data);
    }

    public function viewDiscountList(Request $request)
    {
        $data['title'] = 'ELICOS Discount Week';
        $data['keywords'] = 'ELICOS Discount Week';
        $data['description'] = 'ELICOS Discount Week';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['collegeId'] = Auth::user()->college_id;

        return view('v2.sadmin.onboardsetting.courses.elicos-discount-week', $data);
    }

    public function coursePromotionPrice(Request $request)
    {
        $data['title'] = 'Course Promotion Price';
        $data['keywords'] = 'Course Promotion Price';
        $data['description'] = 'Course Promotion Price';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['collegeId'] = Auth::user()->college_id;

        return view('v2.sadmin.onboardsetting.courses.course-promotion-price', $data);
    }

    public function coursesIntakeDate(Request $request)
    {
        $data['title'] = 'Course IntakeDate';
        $data['keywords'] = 'Course IntakeDate';
        $data['description'] = 'Course IntakeDate';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['collegeId'] = Auth::user()->college_id;

        return view('v2.sadmin.onboardsetting.courses.course-intake-date', $data);
    }

    public function studentIdFormate(Request $request)
    {
        $data['title'] = 'Student Id Format';
        $data['keywords'] = 'Student Id Format';
        $data['description'] = 'Student Id Format';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['collegeId'] = Auth::user()->college_id;
        $data['studentFormate'] = StudentIdFormate::where('college_id', Auth::user()->college_id)->get();
        $data['studentFormateArray'] = config('constants.studentIdFormate');

        return view('v2.sadmin.onboardsetting.organisation.student-id-formate', $data);
    }

    public function globalQueue(Request $request)
    {
        $data['title'] = 'Global Queue';
        $data['keywords'] = 'Global Queue';
        $data['description'] = 'Global Queue';
        $data['mainmenu'] = 'settings';
        $data['api_token'] = $this->loginUser->api_token;
        $data['collegeId'] = Auth::user()->college_id;

        return view('v2.sadmin.onboardsetting.global-queue.index', $data);
    }

    public function scheduleType(Request $request)
    {
        $data['title'] = 'Schedule Type';
        $data['keywords'] = 'Schedule Type';
        $data['description'] = 'Schedule Type';
        $data['mainmenu'] = 'settings';
        $data['college_id'] = (isset(Auth::user()->college_id)) ? Auth::user()->college_id : '';

        return view('v2.sadmin.onboardsetting.schedule-type.index', $data);
    }

    public function shortCourseTeamsCondition(Request $request)
    {
        $data['title'] = 'Terms and Conditions';
        $data['keywords'] = 'Terms and Conditions';
        $data['description'] = 'Terms and Conditions';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.teams-condition.sortcourse', $data);
    }

    public function shortCoursePrivacyPolicy(Request $request)
    {
        $data['title'] = 'Privacy Policy';
        $data['keywords'] = 'Privacy Policy';
        $data['description'] = 'Privacy Policy';
        $data['mainmenu'] = 'settings';

        return view('v2.sadmin.onboardsetting.privacy-policy.sortcourse', $data);
    }

    public function getUploadedFileUrl(string $path): string
    {
        return UploadService::exists($path)
            ? UploadService::preview($path)
            : asset('v2/img/user.png');
    }
}
