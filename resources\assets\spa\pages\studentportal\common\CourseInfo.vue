<template>
    <div :class="rootClass">
        <h2 :class="titleClass">
            {{ course || '-' }}
        </h2>
        <div class="flex flex-col items-start gap-3 md:flex-row md:items-center md:gap-4 lg:gap-8">
            <div
                class="flex min-w-24 items-start gap-x-2 gap-y-1 md:flex-col"
                v-for="(detail, index) in details"
                :key="index"
            >
                <div :class="labelClass">{{ detail.label }}</div>
                <div v-if="isBatchDuration">
                    <FormatDate
                        :date="computeDate('start')"
                        :endDate="computeDate('end')"
                        :infix="'To'"
                        :formatType="dateFormat"
                    />
                </div>
                <div :class="valueClass" v-else>{{ detail.value }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watchEffect, computed } from 'vue';
import { twMerge } from 'tailwind-merge';
import FormatDate from '@spa/components/FormatDate.vue';
import { DEFAULT_DATE_FORMAT } from '@spa/helpers/constants.js';

const props = defineProps({
    status: {
        type: String,
        required: true,
    },
    course: {
        type: String,
        required: true,
    },
    courseCode: {
        type: String,
        required: true,
    },
    courseDuration: {
        type: String,
        required: true,
    },
    totalWeeks: {
        type: String,
        required: true,
    },
    loadTable: Boolean,
    pt: {
        type: Object,
        default: {},
    },
});

const rootClass = computed(() => {
    return twMerge(
        'space-y-2 md:space-y-4 bg-white border border-gray-200 p-4 rounded-md',
        props.pt.root
    );
});

const titleClass = computed(() => {
    return twMerge(
        'text-lg md:text-xl lg:text-2xl font-medium leading-8 text-gray-700',
        props.pt.title
    );
});

const labelClass = computed(() => {
    return twMerge('text-gray-900 text-xs font-medium', props.pt.label);
});

const valueClass = computed(() => {
    return twMerge('text-gray-700 text-xs', props.pt.value);
});

const details = ref([]);
const dateFormat = DEFAULT_DATE_FORMAT;

watchEffect(() => {
    details.value = [
        { label: 'Status', value: props.status || '-' },
        { label: 'Course Duration', value: props.courseDuration || '-' },
        { label: 'Total Weeks', value: props.totalWeeks || '-' },
    ];
});
</script>

<style></style>
