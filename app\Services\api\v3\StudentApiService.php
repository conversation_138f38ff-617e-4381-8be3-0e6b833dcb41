<?php

namespace App\Services\api\v3;

use App\Classes\SiteConstants;
use App\DTO\api\v3\DocumentUploadDTO;
use App\DTO\api\v3\StudentApplicationDTO;
use App\DTO\api\v3\StudentApplicationEmploymentDTO;
use App\DTO\api\v3\StudentApplicationLanguageDTO;
use App\DTO\api\v3\StudentApplicationQualificationDTO;
use App\DTO\api\v3\StudentDisabilityDTO;
use App\DTO\api\v3\StudentEmergencyContactDTO;
use App\DTO\api\v3\StudentOshcDTO;
use App\DTO\api\v3\StudentSchoolingDTO;
use App\DTO\api\v3\StudentUSIDTO;
use App\Helpers\Helpers;
use App\Mail\sendStudentInviteMail;
use App\Model\v2\Agent;
use App\Model\v2\CollegeCampus;
use App\Model\v2\CollegeEnrollmentFees;
use App\Model\v2\Country;
use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\CourseSubjects;
use App\Model\v2\CoursesUpfrontFee;
use App\Model\v2\EmailTemplate;
use App\Model\v2\EmailTemplateDocuments;
use App\Model\v2\EmploymentStatus;
use App\Model\v2\Language;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\PromotionPrice;
use App\Model\v2\QualificationLevel;
use App\Model\v2\SetupSection;
use App\Model\v2\Student;
use App\Model\v2\StudentAdditionalServiceRequest;
use App\Model\v2\StudentApplicationProcess;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDetails;
use App\Model\v2\StudentEducation;
use App\Model\v2\StudentEmployment;
use App\Model\v2\StudentOfferDocuments;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentUnitEnrollment;
use App\Model\v2\StudyReason;
use App\Repositories\Repository;
use App\Users;
use Carbon\Carbon;
use Domains\Customers\Billing\Collections\ShortCourseChargeCollection;
use Domains\Customers\Billing\DTO\ShortCourseCheckoutPayload;
use Domains\Customers\Billing\Models\StudentGalaxyInvoice;
use Domains\Customers\Billing\Process\ShortCoursePaymentProcess;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Support\DTO\MailAddress;
use Support\Mails\EmailService;

class StudentApiService
{
    protected $allApplicationRelations = [
        'studentCourses',
        'studentDetails',
        'studentEducation',
        'studentEmployment',
        'appprocess',
    ];

    protected $SetupSection;

    protected $countrymodel;

    protected $documents;

    public function __construct(
        SetupSection $SetupSection,
        Country $Country,
        StudentOfferDocuments $documents,
    ) {
        $this->SetupSection = new Repository($SetupSection);
        $this->countrymodel = new Repository($Country);
        $this->documents = $documents;
    }

    public function getVisaType()
    {
        $offshoreData = $this->SetupSection->getWhereSelect(['type' => 14], ['id', 'value'])->toArray();
        $onshoreData = $this->SetupSection->getWhereSelect(['type' => 15], ['id', 'value'])->toArray();
        $domesticData = $this->SetupSection->getWhereSelect(['type' => 16], ['id', 'value'])->toArray();

        return ['Offshore' => $offshoreData, 'Onshore' => $onshoreData, 'Domestic' => $domesticData];

        // return $this->successResponse('Data saved successfully','data',$data);
    }

    public function getCountryList($search = '', $paginate = false)
    {
        $listQry = $this->countrymodel->getModel()->query();
        if ($search) {
            $listQry->where('name', 'like', "{$search}%");
        }
        if ($paginate) {
            $listQry->limit($paginate);
        }

        return $listQry->get(['id', 'name'])->toArray();
    }

    public function getNationality($search = '', $paginate = false)
    {
        $listQry = $this->countrymodel->getModel()->query();
        if ($search) {
            $listQry->where('nationality', 'like', "{$search}%");
        }
        if ($paginate) {
            $listQry->limit($paginate);
        }

        return $listQry->orderby('nationality', 'asc')->get(['id', 'nationality'])->toArray();
    }

    public function getLanguagesList($search = '', $paginate = false)
    {
        $dataQry = Language::orderByDesc('isdefault');
        if ($search) {
            $dataQry->where('name', 'like', "{$search}%");
        }
        if ($paginate) {
            $dataQry->limit($paginate);
        }
        $data = $dataQry->orderBy('name')->pluck('name', 'id');

        return kendify($data, 'value', 'label');
    }

    public function loadFormData($inputData = null)
    {
        $all = false;

        $requestedType = $inputData['type'] ?? null;
        $requestedExcept = $inputData['except'] ?? null;
        $search = $inputData['search'] ?? null;

        $types = null;

        if (! empty($requestedType) && $requestedType !== 'all') {
            $types = explode(',', trim($requestedType));
            $types = array_map('trim', $types);
            $types = array_filter($types);
        }

        $omit = explode(',', trim($requestedExcept));
        $omit = array_map('trim', $omit);
        $omit = array_filter($omit);

        $search = trim($search);

        if (empty($types)) {
            $all = true;
        }

        $returnData = [];

        if (($all || in_array('countries', $types)) && ! in_array('countries', $omit)) {
            $returnData['countries'] = $this->getCountryList($search, 25);
        }
        if (($all || in_array('phonecodes', $types)) && ! in_array('phonecodes', $omit)) {
            $returnData['phonecodes'] = countries_codes();
        }
        if (($all || in_array('nationalities', $types)) && ! in_array('nationalities', $omit)) {
            $returnData['nationalities'] = $this->getNationality($search, 25);
        }
        if (($all || in_array('visatypes', $types)) && ! in_array('visatypes', $omit)) {
            $returnData['visatypes'] = $this->getVisaType();
        }
        if (($all || in_array('englishspeakingrating', $types)) && ! in_array('englishspeakingrating', $omit)) {
            $returnData['englishspeakingrating'] = kendify(config('constants.arrEnglishSpeakingRating'), 'value', 'label');
        }
        if (($all || in_array('languages', $types)) && ! in_array('languages', $omit)) {
            $returnData['languages'] = $this->getLanguagesList($search, 25);
        }
        if (($all || in_array('disabilities', $types)) && ! in_array('disabilities', $omit)) {
            $disabilities = kendify(config('constants.arrDisabilityInformation'), 'value', 'label');
            $disabilities = array_map(function ($item) {
                return [
                    'value' => $item['value'],
                    'name' => StudentDisabilityDTO::getDisabilityAreaName($item['value']),
                    'label' => $item['label'],
                ];
            }, $disabilities);
            $returnData['disabilities'] = $disabilities;
        }
        if (($all || in_array('schoollevels', $types)) && ! in_array('schoollevels', $omit)) {
            $returnData['schoollevels'] = kendify(config('constants.arrSchoolLevel'), 'value', 'label');
        }
        if (($all || in_array('schooltype', $types)) && ! in_array('schooltype', $omit)) {
            $returnData['schooltype'] = kendify(config('constants.arrTraningOrgazinationType'), 'value', 'label');
        }
        if (($all || in_array('qualificationlevels', $types)) && ! in_array('qualificationlevels', $omit)) {
            $data = QualificationLevel::pluck('name', 'avaitmiss_id');
            $returnData['qualificationlevels'] = kendify($data, 'value', 'label');
        }
        if (($all || in_array('employmentstatus', $types)) && ! in_array('employmentstatus', $omit)) {
            $data = EmploymentStatus::where('status', '=', 1)->pluck('name', 'avaitmiss_id');
            $returnData['employmentstatus'] = kendify($data, 'value', 'label');
        }
        if (($all || in_array('studyreason', $types)) && ! in_array('studyreason', $omit)) {
            $data = StudyReason::pluck('title', 'avaitmiss_id');
            $returnData['studyreason'] = kendify($data, 'value', 'label');
        }
        if (($all || in_array('aboriginals', $types)) && ! in_array('aboriginals', $omit)) {
            $returnData['aboriginals'] = kendify(config('constants.arrAboriginalTorresStrait'), 'value', 'label');
        }
        if (($all || in_array('languagetests', $types)) && ! in_array('languagetests', $omit)) {
            $tests = SetupSection::where('type', 11)->distinct('value')->pluck('value', 'id');
            $returnData['languagetests'] = kendify($tests, 'value', 'label');
        }

        return $returnData;
    }

    public function getAdditionalService(Request $request)
    {
        $studentId = decrypt($request->student_id ?? '');
        $additionalservice = StudentAdditionalServiceRequest::with(['setupservice', 'serviceprovider', 'servicecategory', 'servicename', 'providername'])->where('student_id', $studentId)->get();

        return $additionalservice;
    }

    public function getStudentDetails(Request $request)
    {
        /*
        get the application details of the student
        */
        $studentId = decrypt($request->student_id ?? '');
        $loggedInUserId = $request->user()->id;
        // temp script
        $responseData = $this->getApplicantData($studentId);
        if (isset($responseData['application_status']['last_status']) && $responseData['application_status']['last_status'] == 'draft') {
            // update the current application process as in progress
            $application = StudentApplicationProcess::Where([
                'id' => $responseData['application_status']['id'],
                'user_id' => $loggedInUserId,
            ])->first();
            $application->last_status = 'inprogress';
            $application->save();
            $responseData['application_status']['last_status'] = 'inprogress';
        }

        return $responseData;
    }

    public function getStudentEnrolledCourse(Request $request)
    {

        $studentId = $request->student_id;
        $studentId = decrypt($studentId);
        $responseData = Student::getSelectedApplicantDetails($studentId, ['application_status', 'studentcourse']);

        return $responseData;
    }

    public function getEnrolledCourse(Request $request)
    {
        $data = [];
        $studentId = $request->student_id;
        $studentId = decrypt($studentId);
        $result = StudentCourses::with(['course', 'campus'])->where('student_id', $studentId)->get()->toArray();
        foreach ($result as $key => $value) {
            $data[] = [
                'id' => $result[$key]['course_id'],
                'name' => $result[$key]['course']['course_code'].' : '.$result[$key]['course']['course_name'],
            ];
        }

        return $data;
    }

    public function getSelectedApplicantDetails(int $studentId, $askFor = [])
    {
        $data = $this->getApplicantData($studentId, $askFor);
        $responseData = [];
        if (! empty($data)) {
            $responseData = $this->formatData($data, $askFor);
        }

        return $responseData;
    }

    public function getApplicantData(int $studentId, $areas = [], $selfRecord = false)
    {
        $hasAdditionalServices = $hasDocuments = false;
        if (empty($areas)) {
            $areas = $this->allApplicationRelations;
            $hasAdditionalServices = true;
            $hasDocuments = true;
        } else {
            $relations = ['studentDetails', 'creator', 'updater'];
            if (in_array('studentCourses', $areas)) {
                $relations[] = 'studentCourses';
            }
            if (in_array('studentEducation', haystack: $areas)) {
                $relations[] = 'studentEducation';
            }
            if (in_array('studentEmployment', $areas)) {
                $relations[] = 'studentEmployment';
            }
            if (in_array('application_status', $areas)) {
                $relations[] = 'appprocess';
            }
            if (in_array('additional_services', $areas)) {
                unset($areas['additional_services']);
                $hasAdditionalServices = true;
            }
            if (in_array('documents', $areas)) {
                unset($areas['documents']);
                $hasDocuments = true;
            }
            $areas = $relations;
        }
        $creatorId = auth()->id();
        $studentQry = Student::with($areas);
        if ($selfRecord) {
            $studentQry->where(['id' => $studentId]);
        } else {
            $studentQry->where(['id' => $studentId]);
        }
        $data = $studentQry->first();
        if (empty($data)) {
            return [];
        }
        if ($hasAdditionalServices) {
            $additionalservices = StudentAdditionalServiceRequest::with([
                'setupservice',
                'serviceprovider',
                'servicecategory',
                'servicename',
                'providername'])
                ->where('student_id', $studentId)->get()->toArray();
            $data->setRelation('additional_services', $additionalservices);
        }
        if ($hasDocuments) {
            $docs = $this->getDocuments($data);
            if ($docs) {
                $data->setRelation('documents', $docs);
            }
        }

        return $data;
    }

    public function getDocuments($student)
    {
        $studentId = $student->id ?? null;
        $studentType = $student->student_type ?? null;
        if (empty($studentId) || empty($studentType)) {
            return false;
        }
        $documentNames = OfferDocumentChecklist::Where(['student_origin' => $studentType, 'is_active' => '1'])->get();
        $responseArray = ['list' => [], 'gallery' => []];
        $docTypes = ['.jpeg', '.jpg', '.png', '.gif', '.pdf'];
        foreach ($documentNames as $document) {
            /* we need to accept certain types of documents only */
            $studentOfferDocuments = StudentOfferDocuments::with(['documentchecklist'])->Where(['rto_student_id' => $studentId, 'rto_offer_document_checklist_id' => $document['id']])->get();
            $document->setRelation('files', $studentOfferDocuments);
        }

        return $documentNames;
    }

    private function fileTypes($ext = '')
    {
        $fileType = 'file';
        $ext = strtolower($ext);
        switch ($ext) {
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
                $fileType = 'img';
                break;
            case 'xls':
            case 'xlsx':
                $fileType = 'img';
                break;
            case 'pdf':
                $fileType = 'pdf';
                break;
            case 'doc':
            case 'docx':
                $fileType = 'doc';
                break;
            case 'ppt':
            case 'pptx':
                $fileType = 'ppt';
                break;
            case 'csv':
                $fileType = 'csv';
                break;
            case 'zip':
            case 'rar':
                $fileType = 'zip';
                break;
            default:
                $fileType = 'file';
                break;

        }

        return $fileType;
    }

    private function formatData($data = [], $format = [])
    {
        if (empty($format)) {
            $format = [
                'application_status',
                'applicant',
                'emergencycontact',
                'language',
                'disability',
                'study_reason',
                'hear_about',
                'student_survey',
                'schooling',
                'studentcourse',
                'studenteducation',
                'studentemployment',
                'additional_services',
                'documents',
                'oshc',
            ];
        }
        $responseData = [];
        if (in_array('studentcourse', $format)) {
            $courses = ($data['studentcourse']) ?: [];
            $coursesData = $this->formatCourses($courses);
            unset($data['studentcourse']);
            $responseData['courses'] = $coursesData;
        }
        $details = $data['studentdetails'] ?: [];
        if (array_intersect(['emergencycontact', 'language', 'disability', 'schooling', 'study_reason', 'hear_about', 'student_survey', 'oshc'], $format)) {
            // more
            /* get only emergency contacts of the person */
            if (! empty($details)) {
                if (in_array('emergencycontact', $format)) {
                    $emergencyContacts = Arr::only($details, [
                        'student_id',
                        'emergency_contact_person',
                        'emergency_phone',
                        'emergency_relationship',
                        'emergency_email',
                        'emergency_address',
                    ]);
                    $responseData['emergencycontact'] = $emergencyContacts;
                }
                if (in_array('language', $format)) {
                    $languageDetails = Arr::only($details, [
                        'student_id',
                        'is_aus_aboriginal_or_islander',
                        'is_english_main_lang',
                        'main_lang',
                        'english_speak_level',
                        'was_english_previous_study',
                        'is_EPL_test',
                        'EPL_test_name',
                        'EPL_test_date',
                        'EPL_istening_score',
                        'EPL_reading_score',
                        'EPL_writing_score',
                        'EPL_speaking_score',
                        'EPL_overall_score',
                        'EPL_other_name',
                        'gettestname',
                    ]);
                    $responseData['language'] = $languageDetails;
                }
                if (in_array('disability', $format)) {
                    $disabilityData = Arr::only($details, [
                        'student_id',
                        'disability',
                        'area_of_disability',
                        'area_other_note',
                    ]);
                    $responseData['disability'] = $disabilityData;
                }
                if (in_array('schooling', $format)) {
                    $schoolingData = Arr::only($details, [
                        'student_id',
                        'highest_completed_school_level',
                        'completed_school_year',
                        'attending_secondary_school',
                        'school_type',
                        'funding_source_state',
                        'school_location_id',
                    ]);
                    $responseData['schooling'] = $schoolingData;
                }
                if (in_array('study_reason', $format)) {
                    $study_reason = ($details['study_reason']) ? $details['study_reason'] : null;
                    $responseData['study_reason'] = $study_reason;
                }
                if (in_array('hear_about', $format)) {
                    $hear_about_id = ($details['hear_about_id']) ? $details['hear_about_id'] : null;
                    $responseData['hear_about_id'] = $hear_about_id;
                }
                if (in_array('student_survey', $format)) {
                    $scs = ($details['scs']) ? $details['scs'] : null;
                    $responseData['scs'] = $scs;
                }
                if (in_array('oshc', $format)) {
                    $oshcData = Arr::only($details, [
                        'student_id',
                        'arrange_OSHC',
                        'OSHC_course_id',
                        'OSHC_provider',
                        'OSHC_type',
                        'OSHC_duration',
                        'OSHC_fee',
                        'applied_oshc_fee',
                        'OSHC_start_date',
                        'OSHC_end_date',
                    ]);
                    $responseData['oshc'] = $oshcData;
                }
            }
        }
        if (in_array('studenteducation', $format)) {
            $previous_qualifications = $details['previous_qualifications'] ?: 0;
            $education = $data['studenteducation'] ?: [];
            unset($data['studenteducation']);
            $responseData['education'] = ['previous_qualifications' => $previous_qualifications, 'education' => $education];
        }
        if (in_array('studentemployment', $format)) {
            $current_employment_status = $details['current_employment_status'] ?: 0;
            $employment = $data['studentemployment'] ?: [];
            unset($data['studentemployment']);
            $responseData['employment'] = ['current_employment_status' => $current_employment_status, 'employment' => $employment];
        }
        if (in_array('application_status', $format)) {
            $appprocess = $data['appprocess'] ?: [];
            unset($data['appprocess']);
            $responseData['application_status'] = $appprocess;
        }
        if (in_array('additional_services', $format)) {
            $additional_services = $data['additional_services'] ?: [];
            unset($data['additional_services']);
            $responseData['additional_services'] = $additional_services;
        }
        if (in_array('documents', $format)) {
            $documents = $data['documents'] ?: [];
            unset($data['documents']);
            $responseData['documents'] = $documents;
        }
        unset($data['studentdetails']);
        if (in_array('applicant', $format)) {
            $responseData['applicant'] = $data;
        }

        return $responseData;
    }

    public function createNewStudentApplication($studentData, $generateId = false)
    {

        $data = Student::create($studentData);
        $studentDetails = [...$studentData, ...[
            'student_id' => $data->id,
            'school_type' => 0,
            'school_type_identifier' => 0,
            'picup_fee' => 0,
            'service_fee' => 0,
            'placement_fee' => 0,
            'accommodation_fee' => 0,
            'applied_oshc_fee' => 0,
            'aboutus_specify' => '',
        ]];
        // add student details
        $details = StudentDetails::create($studentDetails);

        if ($data && $generateId) {
            $data->generateStudentId();
        }

        return $data;
    }

    public function updateApplicationProcessField($studentId, $field, $value)
    {
        // if(empty($studentId) || empty($field)) return;
        $aProcess = StudentApplicationProcess::where('student_id', $studentId)->first();
        if (empty($aProcess)) {
            $aProcess = new StudentApplicationProcess([
                'student_id' => $studentId,
            ]);
            $aProcess->save();
        }
        $aProcess->$field = $value;
        $aProcess->save();

        return $aProcess;
    }

    public function updateApplicationProcess($step, $student)
    {
        if (empty($student)) {
            return;
        }

        if ($step == 'personal_details') {
            // $fieldsToCheck = [
            //     'student_type', 'email', 'birth_country', 'nationality', 'passport_no', 'passport_expiry',
            //     'current_country', 'current_street_no', 'current_street_name', 'current_city', 'current_state',
            //     'current_postcode', 'current_mobile_phone'
            // ];
            // $fieldsToCheck = ['student_type','name_title','first_name','family_name','gender','email','passport_no','passport_expiry'];

            $fieldsToCheck = ['student_type', 'name_title', 'first_name', 'family_name', 'gender', 'email', 'current_mobile_phone'];

            // Find fields with empty values
            if ($student) {
                $emptyFields = collect($student->only($fieldsToCheck))->filter(function ($value) {
                    return empty($value);
                });
                $status = $emptyFields->isNotEmpty() ? 'inprocess' : 'complete';
                $this->updateApplicationProcessField($student->id, 'personal_details', $status);
            }
        } elseif ($step == 'usi') {
            $status = empty($student->USI) ? 'inprocess' : 'complete';
            $this->updateApplicationProcessField($student->id, 'unique_student_identification', $status);
        } elseif ($step == 'language_cultural_diversity') {
            $filled = true;

            $required = ['is_aus_aboriginal_or_islander', 'is_english_main_lang', 'was_english_previous_study', 'is_EPL_test', 'english_speak_level'];

            if (array_diff_key(array_flip($required), (array) $student)) {
                $filled = false;
            }

            if ($student->is_english_main_lang == 0 && empty($student->main_lang)) {
                $filled = false;
            }

            if (
                $student->EPL_test_name &&
                array_diff_key(array_flip([
                    'EPL_test_date',
                    'EPL_istening_score',
                    'EPL_reading_score',
                    'EPL_writing_score',
                    'EPL_speaking_score',
                ]), (array) $student)) {
                $filled = false;
            }

            $status = $filled ? 'inprocess' : 'complete';
            $this->updateApplicationProcessField($student->student_id, 'language_cultural_diversity', $status);

        } elseif ($step == 'previous_qualifications_achieved') {
            $hasQualifications = StudentEducation::where('student_id', $student->student_id)->count();
            $status = ($student->previous_qualifications === null || ($student->previous_qualifications == 1 && ! $hasQualifications)) ? 'inprocess' : 'complete';

            $this->updateApplicationProcessField($student->student_id, 'previous_qualifications_achieved', $status);

        } elseif ($step == 'disability') {

            $status = ($student->disability == 1 && empty($student->area_of_disability)) ? 'inprocess' : 'complete';

            $this->updateApplicationProcessField($student->student_id, 'disability', $status);

        } elseif ($step == 'schooling') {

            $status = (empty($student->highest_completed_school_level)) ? 'inprocess' : 'complete';

            $this->updateApplicationProcessField($student->student_id, 'schooling', $status);

        } elseif ($step == 'employment') {

            $status = empty($student->current_employment_status) ? 'inprocess' : 'complete';

            $this->updateApplicationProcessField($student->student_id, 'employment', $status);

        } elseif ($step == 'application_declared_agreed') {

            $status = ($student->read_and_agree != 'YES') ? 'inprocess' : 'complete';

            $this->updateApplicationProcessField($student->id, 'application_declared_agreed', $status);
        }

    }

    public function savePersonalDetails(StudentApplicationDTO $data)
    {
        $student = Student::find($data->student_id);
        if ($student) {
            $saved = $student->update($data->toArray());
            // check if the application process has been completed
            $this->updateApplicationProcess('personal_details', $student);

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function updateLanguage(StudentApplicationLanguageDTO $data, $mode = 'test')
    {
        $student = StudentDetails::where('student_id', $data->student_id)->first();
        if ($student) {
            $infoFields = [
                'is_aus_aboriginal_or_islander',
                'is_english_main_lang',
                'main_lang',
                'was_english_previous_study',
                'is_EPL_test',
                'english_speak_level',
            ];
            $testFields = [
                'EPL_test_name',
                'EPL_test_date',
                'EPL_istening_score',
                'EPL_reading_score',
                'EPL_writing_score',
                'EPL_speaking_score',
            ];
            if ($mode === 'info') {
                $saveData = Arr::only(
                    $data->toArray(),
                    $infoFields
                );
            } elseif ($mode == 'test') {
                $saveData = Arr::only(
                    $data->toArray(),
                    $testFields
                );
            } else {
                $saveData = $data->toArray();
            }
            if ($saveData['EPL_test_name']) {
                $saveData['is_EPL_test'] = 1;
            }
            $saved = $student->update($saveData);
            $this->updateApplicationProcess('language_cultural_diversity', $student);

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function deleteLanguage($studentId = null)
    {
        $student = StudentDetails::where('student_id', $studentId)->first();
        if ($student) {
            // dd($student);
            $data = StudentApplicationLanguageDTO::LazyFromArray([
                'student_id' => $studentId,
                'is_aus_aboriginal_or_islander' => $student->is_aus_aboriginal_or_islander ?? null,
                'is_english_main_lang' => $student->is_english_main_lang ?? null,
                'main_lang' => $student->main_lang ?? null,
                'was_english_previous_study' => $student->was_english_previous_study ?? null,
                'english_speak_level' => $student->english_speak_level ?? null,
                'is_EPL_test' => 0,
            ]);
            $saved = $student->update($data->toArray());

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function updateDisability(StudentDisabilityDTO $data)
    {
        $disability = $data->disability ?? false;
        $areas = ($disability) ? $data->getDisabilityAreas() : '';
        $student = StudentDetails::where('student_id', $data->student_id)->first();
        if ($student) {
            $saved = $student->update([
                'disability' => $disability,
                'area_of_disability' => $areas,
            ]);
            $this->updateApplicationProcess('disability', $student);

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function updateSchooling(StudentSchoolingDTO $data)
    {
        $student = StudentDetails::where('student_id', $data->student_id)->first();
        if ($student) {
            $saved = $student->update($data->toArray());
            $this->updateApplicationProcess('schooling', $saved);

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function addQualification(StudentApplicationQualificationDTO $data)
    {
        $saved = StudentEducation::create($data->toArray());
        $this->updateStudentDetails('qualification', $data);

        return ($saved) ? $saved : false;
    }

    public function updateQualification(StudentApplicationQualificationDTO $data)
    {
        $qualification = StudentEducation::where([
            'id' => $data->id,
            'student_id' => $data->student_id,
        ])->first();
        if ($qualification) {
            $saved = $qualification->update($data->toArray());
            $this->updateStudentDetails('qualification', $data);

            return ($saved) ? $qualification : false;
        }

        return false;
    }

    public function deleteQualification(StudentApplicationQualificationDTO $data)
    {
        $qualification = StudentEducation::where([
            'id' => $data->id,
            'student_id' => $data->student_id,
        ])->first();
        if ($qualification) {
            $deleted = $qualification->delete();
            $this->updateStudentDetails('qualification', $data);

            return $deleted;
        }

        return false;
    }

    public function updateStudentDetails($update, $data)
    {
        if ($update == 'qualification') {
            $hasQualifications = StudentEducation::where('student_id', $data->student_id)->count();
            $dataArray = [
                'student_id' => $data->student_id,
                'previous_qualifications' => ($hasQualifications > 0) ? 1 : 0,
            ];
            $profileUpdated = $this->updateStudentProfile($dataArray, ['previous_qualifications']);
            $this->updateApplicationProcess('previous_qualifications_achieved', $profileUpdated);

            return $profileUpdated;
        } elseif ($update == 'employment') {
            $hasEmployment = StudentEmployment::where('student_id', $data->student_id)->count();
            $dataArray = [
                'student_id' => $data->student_id,
                'current_employment_status' => ($hasEmployment > 0) ? 1 : 0,
            ];

            return $this->updateStudentProfile($dataArray, ['current_employment_status']);
        }

        return false;
    }

    public function updateStudentUSI(StudentUSIDTO $usiData)
    {
        $student = Student::where('id', $usiData->student_id)->first();
        if ($student) {
            $update = [
                'USI' => $usiData->USI ?? '',
            ];
            $saved = $student->update($update);
            $this->updateApplicationProcess('usi', $student);

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function updateStudentProfile($data, $fields)
    {
        $student = StudentDetails::where('student_id', $data['student_id'])->first();
        if ($student) {
            $update = [];
            foreach ($fields as $field) {
                $update[$field] = $data[$field] ?? null;
            }
            $saved = $student->update($update);

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function addEmployment(StudentApplicationEmploymentDTO $data)
    {
        $saved = StudentEmployment::create($data->toArray());

        // $this->updateStudentDetails("employment", $data);
        return ($saved) ? $saved : false;
    }

    public function updateEmployment(StudentApplicationEmploymentDTO $data)
    {
        $qualification = StudentEmployment::where([
            'id' => $data->id,
            'student_id' => $data->student_id,
        ])->first();
        if ($qualification) {
            $saved = $qualification->update($data->toArray());

            // $this->updateStudentDetails("employment", $data);
            return ($saved) ? $qualification : false;
        }

        return false;
    }

    public function deleteEmployment(StudentApplicationEmploymentDTO $data)
    {
        $qualification = StudentEmployment::where([
            'id' => $data->id,
            'student_id' => $data->student_id,
        ])->first();
        if ($qualification) {
            $deleted = $qualification->delete();

            return $deleted;
        }

        return false;
    }

    public function saveOshc(StudentOshcDTO $data)
    {
        $student = StudentDetails::where('student_id', $data->student_id)->first();
        if ($student) {
            $saved = $student->update($data->toArray());

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function saveEmergencyContact(StudentEmergencyContactDTO $data)
    {
        $student = StudentDetails::where('student_id', $data->student_id)->first();
        if ($student) {
            $saved = $student->update($data->toArray());

            return ($saved) ? $student : false;
        }

        return false;
    }

    public function uploadDocument(DocumentUploadDTO $data)
    {
        // now save the document
        return $this->documents->saveStudentDocument($data);
    }

    public function deleteDocument(DocumentUploadDTO $data)
    {
        return $this->documents->deleteStudentOfferDocumentsId($data->id);
    }

    public function registerNewUser($userData)
    {
        $policyTermsAccepted = $userData['read_and_agree'] ?? null;
        unset($userData['read_and_agree']);
        $userData['current_mobile_phone'] = $userData['phone'];

        $user = Users::where('email', $userData['email'])->first();
        $student = Student::where('email', $userData['email'])->first();

        $studentCreated = false;

        /* in case student is already registered; we will create a new user and send the password reset email */
        if (! $student) {
            $student = $this->createNewStudentApplication($userData, true);
            $studentCreated = true;
        }

        if ($student && ! $user) {
            $fullname = implode(' ', array_filter([$student->first_name, $student->middel_name, $student->family_name]));
            $password = ($userData['password']) ? Hash::make($userData['password']) : null;
            /* if the student doesn't have generated_stud_id, then generate it */
            $generatedId = $student->generated_stud_id;
            if (! $generatedId) {
                $generatedId = $student->generateStudentId();
            }

            $user = new Users;
            $user->college_id = $student->college_id;
            $user->name = $fullname;
            $user->username = $generatedId ?? $student->email;
            $user->role_id = SiteConstants::STUDENT_ROLE_ID;
            $user->email = $student->email;
            $user->phone = $student->current_home_phone ?? null;
            $user->mobile = $student->current_mobile_phone ?? null;
            $user->password = $password;
            $user->terms_policy_agreed = $policyTermsAccepted;
            /* FOR DEMO: will remove this */
            $user->email_verified_at = null;
            $user->status = 1;
            $user->save();
            if ($user) {
                $this->sendEmailVerificationEmail($user, $student, $userData['redirect_uri']);
                if (! $studentCreated) {
                    return ['token' => null, 'message' => 'Email verification sent. Please check your email.'];
                } else {
                    $token = $user->generateUserToken(config('settings.studentapitokenname'));

                    // $this->sendUserCreatedEmail($user, $student);
                    return [...['action_required' => 'User Registered and Email verification link has been sent to your email. Please check your email.'], ...$user->toArray(), ...$token, ...['student' => $student]];
                }
            }
        } elseif ($student && $user) {
            if ($user->email_verified_at == null) {
                $this->sendEmailVerificationEmail($user, $student, $userData['redirect_uri']);

                return ['token' => null, 'message' => 'This email is not verified. Check your email for verification. If you have not received the email, please send a request to resend the email.'];
            } else {
                return ['token' => null, 'message' => 'Your account is already verified. Please login to continue. If you have forgotten your password, please send a request to reset it.'];
            }
        }

        return false;
    }

    public function getDefaultRedirectUri()
    {
        $tenant = tenant();
        $tenantDomain = $tenant->domain ?? $tenant->id ?? null;
        $defaultRedirectUri = config('app.shortcourse_domain');

        return $tenantDomain.$defaultRedirectUri;
    }

    public function sendEmailVerificationEmail(Users $user, Student $student, $redirect_uri = null)
    {
        /* if verification link is just sent (in 2 hours) then don't send the email again */
        $lastVerificationRequestedAt = $user->last_login ?? null;
        $timeDiff = Carbon::now()->diffInHours($lastVerificationRequestedAt);
        if ($lastVerificationRequestedAt && $timeDiff < 2) {
            return;
        }
        if (empty($redirect_uri)) {
            $redirect_uri = $this->getDefaultRedirectUri();
        }
        $verifyToken = encryptIt($student->generated_stud_id.'|'.$redirect_uri.'|'.Carbon::now()->timestamp);
        $emailData = [
            'student_name' => $student->first_name,
            'student_full_name' => $user->name,
            'actionUrl' => route('shortcourse.verify.student', $verifyToken).'?email='.$user->email.'&redirect='.$redirect_uri,
        ];
        $user->last_login = Carbon::now();
        $user->save();
        (new EmailService)->to(new MailAddress($user->email))
            ->subject('Verify your email address')
            ->send('emails.sendShortCourseStudentVerifyEmail', ['data' => $emailData]);
    }

    public function sendPasswordResetEmail(Users $user, $redirect_uri = null)
    {
        $student = Student::where('email', $user->email)->orWhere('generated_stud_id', $user->username)->first();
        $lastPasswordResetRequestedAt = $student->password_reset_requested_at ?? null;
        $lastPasswordResetRequestedCount = $student->password_reset_requested_count ?? 0;
        $timeDiff = Carbon::now()->diffInMinutes($lastPasswordResetRequestedAt);
        $timeDiff = Carbon::now()->diffInSeconds($lastPasswordResetRequestedAt);
        // can not send more than 2 emails in 5 minutes, 5 emails in 30 minutes, 5 emails in 60 minutes
        $throttle = $timeDiff > 0 && $timeDiff < 30;
        if ($throttle) {
            throw new \Exception('Password reset link already sent. Please wait before requesting another password reset link.');
        }

        if (empty($redirect_uri)) {
            $redirect_uri = $this->getDefaultRedirectUri();
        }
        $passwordResetRequestedAt = Carbon::now();
        $verifyToken = encryptIt($user->id.'|'.$redirect_uri.'|'.$passwordResetRequestedAt->timestamp);
        $emailData = [
            'student_name' => $user->name,
            'actionUrl' => route('shortcourse.show.passwordform', $verifyToken).'?email='.$user->email.'&redirect='.$redirect_uri,
        ];
        (new EmailService)->to(new MailAddress($user->email))->subject('Reset your password')->send('emails.sendShortCoursePasswordResetEmail', ['data' => $emailData]);
        $student->password_reset_requested_at = $passwordResetRequestedAt;
        $student->password_reset_requested_count = $lastPasswordResetRequestedCount + 1;
        $student->save();
    }

    private function sendUserCreatedEmail(Users $user, Student $student)
    {
        $fullname = implode(' ', array_filter([$student->first_name, $student->middel_name, $student->family_name]));

        $token = Password::getRepository()->create($user);

        $emailTemplate = EmailTemplate::where('rto_email_template.college_id', $student->college_id)
            ->where('rto_email_template.template_name', config('constants.studentInviteEmail'))
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_email_template.updated_by')
            ->leftjoin('rto_email_template_documents', 'rto_email_template_documents.email_template_id', '=', 'rto_email_template.id')
            ->select('rto_email_template.*', 'ru.name', 'rto_email_template_documents.id as hasFile', DB::raw('GROUP_CONCAT(rto_email_template_documents.file SEPARATOR "<br>") AS fileNames'))
            ->groupBy('rto_email_template.id')
            ->orderBy('rto_email_template.template_name', 'ASC')
            ->first();

        $existFilePath = config('constants.uploadFilePath.Templates');

        $existDestinationPath = Helpers::changeRootPath($existFilePath, $emailTemplate->id);

        $docList = EmailTemplateDocuments::where('email_template_id', $emailTemplate->id)->get()->toArray();

        $existAttachment = [];

        foreach ($docList as $doc) {
            $existAttachment[] = $existDestinationPath['default'].$doc['file'];
        }
        $mailData = [
            'student_name' => $fullname,
            'contentData' => $emailTemplate->content,
            'subject' => $emailTemplate->email_subject,
            'actionUrl' => route('password.reset', $token).'?email='.$user->email,
            'attachments' => $existAttachment,
        ];
        try {
            Mail::to($user->email)->send(new sendStudentInviteMail($mailData));

            return [...$user->toArray(), ...['student' => $student], ...['mail' => $mailData]];
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getCourseFees(Courses $course, CoursesIntakeDate $intake, Student $student)
    {

        $studentType = $student->determineStudentOrigin();
        $domesticFee = $course->domestic_fee ?? 0;
        $tuitionFee = $course->tuition_fee ?? 0;
        if ($studentType == 'domestic') {
            $tuition_fee = ($domesticFee > 0) ? $domesticFee : $tuitionFee;
        } else {
            $tuition_fee = ($tuitionFee > 0) ? $tuitionFee : $domesticFee;
        }

        $targetOk = false;

        if (
            ($studentType == 'domestic' && in_array($course->delivery_target, [SiteConstants::BOTH_TYPE_NAME, SiteConstants::DOMESTIC_TYPE_NAME]))
            ||
            (in_array($course->delivery_target, [SiteConstants::BOTH_TYPE_NAME, SiteConstants::INTERNATIONAL_TYPE_NAME]))) {
            $targetOk = true;
        }
        $promotionPrice = $enrolmentFee = $upfrontFee = $materialFee = 0;
        $feeArray = [
            'enrolmentFee' => $enrolmentFee,
            'upfrontFee' => $upfrontFee,
            'materialFee' => $materialFee,
            'promotionPrice' => $promotionPrice,
            'tuition_fee' => $tuition_fee,
            'target_fine' => $targetOk,
        ];

        if ($course->course_type_id != SiteConstants::SHORT_COURSE_TYPE_ID) {

            $whereArr = ['college_id' => $course->college_id ?? null, 'course_id' => $course->id ?? null];

            $fees = CollegeEnrollmentFees::where($whereArr)->select('fee_amount')->first();
            $upFront = CoursesUpfrontFee::where($whereArr)->select(['material_fee', 'upfront_fee'])->first();

            $feeArray['enrolmentFee'] = $fees->fee_amount ?? 0;
            $feeArray['upfrontFee'] = $upFront->upfront_fee ?? 0;
            $feeArray['materialFee'] = $upFront->material_fee ?? 0;

            if ($intake->intake_start) {
                $start = date('Y-m-d', strtotime($intake->intake_start));

                $promotion = PromotionPrice::where($whereArr)
                    ->where('from_date', '>=', $start)
                    ->where('to_date', '<=', $start)
                    ->select(['price as special_price'])
                    ->first();

                $feeArray['promotionPrice'] = $promotion->special_price ?? 0;
            }
        }

        return $feeArray;
    }

    public function enrollStudent($enrollData = null, $addAsFavourite = false)
    {

        $course = Courses::find($enrollData['course']);
        $student = Student::with([
            'birthCountry',
            'studentNationality',
            'currentCountry',
            'premanentCountry',
            'postalCountry',
        ])->find($enrollData['student']);
        $intake = CoursesIntakeDate::find($enrollData['intake']);

        $campus = CollegeCampus::find($enrollData['campus']);

        /* get default agent for the short course */
        $agentId = Agent::where('is_tenant_default', 1)->first()->id ?? null;

        if (! $agentId) {
            $newAgent = Agent::CreateDefaultAgentForShortCourse();
            if ($newAgent) {
                $agentId = $newAgent->id;
            }
        }

        if ($course->couse_duration_type == 1) {
            // duration in days so convert it to weeks
            $weeks = ceil($course->course_duration / 7);
        } elseif ($course->couse_duration_type == 3) {
            $weeks = $course->course_duration * 4;
        } elseif ($course->couse_duration_type == 4) {
            $weeks = $course->course_duration * 52;
        } else {
            $weeks = $course->course_duration;
        }
        $fees = $this->getCourseFees($course, $intake, $student);

        if (! @$fees['tuition_fee']) {
            throw (new \Exception('Can not enroll to this course. Tuition fee is Zero.'));
        }

        $applyData = [
            'student_id' => $student->id ?? null,
            'agent_id' => $agentId ?? 0,
            'campus_id' => $campus->id ?? null,
            'course_type_id' => $course->course_type_id ?? null,
            'intake_id' => $intake->id ?? null,
            'intake_year' => $intake->intake_year ?? null,
            'intake_date' => $intake->intake_start ?? null,
            'course_id' => $course->id ?? null,
            'start_date' => $intake->class_start_date ?? $course->effective_start ?? null,
            'finish_date' => $intake->class_end_date ?? $course->effective_end ?? null,
            'total_weeks' => $weeks ?? null,
            'course_duration_type' => $course->couse_duration_type ?? null,
            'course_fee' => @$fees['tuition_fee'] ?? 0,
            'enroll_fee' => @$fees['enrolmentFee'] ?? 0,
            'course_upfront_fee' => @$fees['upfrontFee'] ?? 0,
            'course_material_fee' => @$fees['materialFee'] ?? 0,
            'enrolled_host_url' => $enrollData['redirect_uri'] ?? '',
        ];

        $studentCourse = StudentCourses::where([
            'student_id' => $student->id ?? null,
            'course_id' => $course->id ?? null,
            'campus_id' => $campus->id ?? null,
            'intake_id' => $intake->id ?? null,
        ])->first();

        if ($studentCourse) {

            /*
            student is already enrolled to the course just return the course doing nothing
            else update the existing record
            */
            if ($studentCourse->offer_status !== SiteConstants::ENROLLED_STATUS) {
                $studentCourse->update($applyData);
            }

        } else {

            $applyData['status'] = SiteConstants::NEW_APPLICATION_STATUS;
            $applyData['offer_status'] = SiteConstants::NEW_OFFER_STATUS;

            $studentCourse = new StudentCourses($applyData);
            $studentCourse->save();
        }
        if ($studentCourse) {
            /* enroll the student to the subject */
            $enrolledUnits = $this->enrollStudentToSubject($studentCourse);
            if ($addAsFavourite) {
                $student->addCourseAsFavourite($course, 'enroll');
            }
            $studentCourse->setRelation('course', $course);
            $studentCourse->setRelation('intake', $intake);
            $studentCourse->setRelation('campus', $campus);

            return $studentCourse;
        }

        return false;
    }

    public function enrollStudentToSubject(StudentCourses $studentCourse)
    {
        /* get course subjects */
        $courseSubjects = CourseSubjects::with(['subject', 'subject_units.unit'])->where('course_id', $studentCourse->course_id)->get();
        $enrolledUnits = [];
        foreach ($courseSubjects as $subject) {
            $subjectId = $subject->subject->id ?? null;
            if (! $subjectId) {
                continue;
            }
            $units = $subject->subject_units ?? [];
            if (empty($units)) {
                continue;
            }
            foreach ($units as $unit) {
                $unitId = $unit->unit->id ?? null;
                $unitFee = $unit->unit->tution_fees ?? null;
                if (! $unitId) {
                    continue;
                }
                $subjectEnrollment = StudentSubjectEnrolment::where([
                    'student_id' => $studentCourse->student_id,
                    'student_course_id' => $studentCourse->id,
                    'subject_id' => $subjectId,
                    'unit_id' => $unitId,
                ])->first();

                $saveData = [
                    'college_id' => auth()->user()->college_id,
                    'student_id' => $studentCourse->student_id,
                    'course_id' => $studentCourse->course_id,
                    'student_course_id' => $studentCourse->id,
                    'semester_id' => 0,
                    'term' => 0,
                    'enroll_type' => 'unit',
                    'course_stage' => 0,
                    'subject_id' => $subjectId,
                    'unit_id' => $unitId,
                    'activity_start_date' => $studentCourse->start_date ?? '',
                    'activity_finish_date' => $studentCourse->finish_date ?? '',
                    'study_from' => $studentCourse->start_date ?? '',
                    'study_to' => $studentCourse->finish_date ?? '',
                    'batch' => $this->getBatchForIntake($studentCourse->intake_id),
                    'subject_tution_fee' => $unitFee,
                    'tution_fee' => $unitFee,
                    'schedule_hours' => $unit->unit->nominal_hours ?? 0,
                    'delivery_mode' => $unit->unit->delivery_mode ?? 0,
                ];

                if ($subjectEnrollment) {
                    /* update the unit enrolment */
                    $subjectEnrollment->update($saveData);
                } else {
                    /* create the unit enrolment */
                    $subjectEnrollment = new StudentSubjectEnrolment($saveData);
                    $subjectEnrollment->save();
                }
                /* check if the unit is already enrolled in rto_student_unit_enrollment */
                $unitEnrollment = StudentUnitEnrollment::where([
                    'student_subject_enrollment_id' => $subjectEnrollment->id,
                    'unit_id' => $unitId,
                ])->first();
                $saveData['student_subject_enrollment_id'] = $subjectEnrollment->id;

                if (! $unitEnrollment) {
                    $unitEnrollment = new StudentUnitEnrollment($saveData);
                    $unitEnrollment->save();
                } else {
                    /* update the unit enrolment */
                    $unitEnrollment->update($saveData);
                }

                $enrolledUnits[] = $unitId;
            }
        }

        return $enrolledUnits;
    }

    public function getBatchForIntake($intakeId)
    {
        /* get the batch created for the intake */
        return '';
    }

    public function getLoggedInUserStudentRecord(Users $user, $select = null)
    {
        $userName = $user->username;
        $userEmail = $user->email;
        $emailCheck = null;
        $validatorEmail = Validator::make(['email' => $userEmail], [
            'email' => 'required|email',
        ]);
        $validatorUsername = Validator::make(['email' => $userName], [
            'email' => 'required|email',
        ]);

        if ($validatorEmail->passes()) {
            $emailCheck = $userEmail;
        } elseif ($validatorUsername->passes()) {
            $emailCheck = $userName;
        }
        $studentQuery = Student::where(['generated_stud_id' => $userName]);
        if ($emailCheck) {
            $studentQuery->orWhere(['email' => $emailCheck]);
        }

        if ($select) {
            return $studentQuery->value($select);
        }

        return $studentQuery->first();
    }

    public function startPaymentProcess(StudentCourses $data, $request, $checkoutOkay = false)
    {

        $paidInvoice = StudentGalaxyInvoice::GetPaidInvoices($data);

        if ($paidInvoice) {
            throw (new \Exception('The invoice for this enrollemnt is already paid.'));
        }

        if ($data->offer_status === SiteConstants::ENROLLED_STATUS && $paidInvoice) {
            throw (new \Exception('Student is already Enrolled and successfully paid for this course.'));
        }

        $process = new ShortCoursePaymentProcess;

        $checkoutData = $process->run(new ShortCourseCheckoutPayload(
            request: $request,
            user: authUser(),
            model: $data,
            collection: ShortCourseChargeCollection::FromStudentCourses($data),
            startPaymentProcess: $checkoutOkay
        ));
        $url = $checkoutData['checkout'] ?? null;
        $data = [
            'checkout' => $url,
            'amount' => number_format(($checkoutData['invoice']?->amount ?? 0) / 100, 2),
            'currency' => config('cashier.currency'),
            'booking_reference' => $checkoutData['invoice']?->uuid ?? '',
        ];
        if (! $url) {
            $data['checkout_issue'] = $checkoutData['checkout_issue'];
        }

        return $data;
    }
}
