<?php

namespace App\Http\Controllers\Frontend;

use App;
// use DB;
use App\Exports\SingleStudentInterventionExport;
use App\Exports\StudentsExport;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Model\Agent;
use App\Model\Checklist;
use App\Model\CollegeCampus;
use App\Model\CollegeDetails;
use App\Model\CollegeMaterials;
use App\Model\Colleges;
use App\Model\Country;
use App\Model\CourseCalendar;
use App\Model\Courses;
use App\Model\CourseSubject;
use App\Model\EmailTemplate;
use App\Model\EmailTemplateDocuments;
use App\Model\InterventionStrategy;
use App\Model\InterventionStrategyType;
use App\Model\LetterSetting;
use App\Model\PaymentMode;
use App\Model\PdfTemplate;
use App\Model\Report;
use App\Model\ReportCategory;
use App\Model\ReportLetterSetup;
use App\Model\ResultGrade;
use App\Model\SecurityQuestion;
use App\Model\Semester;
use App\Model\SendMail;
use App\Model\SetupSection;
use App\Model\SMSTemplate;
use App\Model\Staff;
use App\Model\StaffCommunicationLog;
use App\Model\StudentAdditionalServiceRequest;
use App\Model\StudentAgentCommission;
use App\Model\StudentAttendance;
use App\Model\StudentClaimTracking;
use App\Model\StudentCommunicationLog;
use App\Model\StudentCourse;
use App\Model\StudentCourseStatusHistory;
use App\Model\StudentDeferCourse;
use App\Model\StudentDetails;
use App\Model\StudentEducation;
use App\Model\StudentEmail;
use App\Model\StudentEmployment;
use App\Model\StudentIdFormate;
use App\Model\StudentInitialPayment;
use App\Model\StudentInitialPaymentDetails;
use App\Model\StudentInitialPaymentTransaction;
use App\Model\StudentIntervention;
use App\Model\StudentInterventionStrategy;
use App\Model\StudentInterview;
use App\Model\StudentMiscellaneousPayment;
use App\Model\StudentOfferChecklist;
use App\Model\StudentOfferDocuments;
use App\Model\Students;
use App\Model\StudentServicePayment;
use App\Model\StudentSubjectEnrolment;
use App\Model\StudentUnitEnrollment;
use App\Model\StudentUploadFile;
use App\Model\Task;
use App\Model\TaskNew;
use App\Model\Teacher;
use App\Model\Timetable;
use App\Model\UnitModule;
use App\Model\UserRoleType;
use App\Model\Users;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Repositories\Repository;
use App\Roles;
use App\Traits\OfferLetterNameTrait;
use App\Traits\ResponseTrait;
use Domains\Xero\Facades\Xero;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Integrations\Zoho\Jobs\SyncStudentToZoho;
use Integrations\Zoho\Traits\ZohoTrait;
use Maatwebsite\Excel\Facades\Excel;
use Mail;
use Support\Services\UploadService;

class StudentsController extends Controller
{
    use OfferLetterNameTrait;
    use ResponseTrait;
    use ZohoTrait;

    public function __construct()
    {
        parent::__construct();
        $this->newLimit = 5;
    }

    public function searchStudents(Request $request)
    {
        $arrCampus = [];
        $perPage = Config::get('constants.pagination.perPage');
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = isset($sessionPermission['delete_student_account']) ? $sessionPermission['delete_student_account'] : '';
        $sessionPermissionDataEdit = isset($sessionPermission['deny_staff_to_update_student_profile_info']) ? $sessionPermission['deny_staff_to_update_student_profile_info'] : '';

        $collegeId = Auth::user()->college_id;
        $arrRoleType = Config::get('constants.arrRoleType');
        $arrStudentOrigin = Config::get('constants.arrStudentOrigin');
        $studentRoleType = array_search('Student', $arrRoleType);
        $arrCourseStatus = Config::get('constants.arrCourseStatus');
        $objRtoStudentsLists = new Students;
        $arrCampusAll['all'] = 'All Campus';
        $objRtoCollegeCampus = new CollegeCampus;
        $arrCampus = $objRtoCollegeCampus->getCollegeCampusList($collegeId);
        $objCountry = new Country;
        $arrCountry = $objCountry->getCountryAbsList();

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseListV3($collegeId);
        $objRtoAgent = new Agent;
        $arrAgentData = $objRtoAgent->getAgentData($collegeId);
        $arrCampus = $arrCampusAll + $arrCampus;
        $arrCampus['notAll'] = 'Not To Any Campus';

        $data['header'] = [
            'title' => 'Search Students',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Students' => '',
            ]];
        $data['pagetitle'] = 'Search Student';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['applyonline.js'];
        $data['funinit'] = ['Applyonline.initStudentListing()'];
        $data['activateValue'] = 'Students';
        $data['arrCampus'] = $arrCampus;
        $data['arrCountry'] = $arrCountry;
        $data['arrStudentOrigin'] = $arrStudentOrigin;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrAgentData'] = $arrAgentData;
        $data['arrCourseStatus'] = $arrCourseStatus;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.search-students', $data);
    }

    public function searchStudents_old(Request $request)
    {
        $arrCampus = [];
        $perPage = Config::get('constants.pagination.perPage');
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = $sessionPermission['delete_student_account'];
        $sessionPermissionDataEdit = $sessionPermission['deny_staff_to_update_student_profile_info'];

        $collegeId = Auth::user()->college_id;
        $arrRoleType = Config::get('constants.arrRoleType');
        $arrStudentOrigin = Config::get('constants.arrStudentOrigin');
        $studentRoleType = array_search('Student', $arrRoleType);
        $arrCourseStatus = Config::get('constants.arrCourseStatus');
        $objRtoStudentsLists = new Students;
        $arrCampusAll['all'] = 'All Campus';
        $objRtoCollegeCampus = new CollegeCampus;
        $arrCampus = $objRtoCollegeCampus->getCollegeCampusList($collegeId);
        $objCountry = new Country;
        $arrCountry = $objCountry->getCountryAbsList();

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseListV3($collegeId);
        $objRtoAgent = new Agent;
        $arrAgentData = $objRtoAgent->getAgentData($collegeId);
        $arrCampus = $arrCampusAll + $arrCampus;
        $arrCampus['notAll'] = 'Not To Any Campus';

        if ($request->isMethod('get')) {
            $campusID = $request->input('campus_name');
            $searchBy = $request->input('search_by');

            if ($searchBy == 'nationality') {
                $searchString = $request->input('search_string_nation');
            }
            if ($searchBy == 'course_id') {
                $searchString = $request->input('student_course');
            } elseif ($searchBy == 'DOB' || $searchBy == 'start_date') {
                $searchString = date('Y-m-d', strtotime($request->input('search_string_date')));
            } elseif ($searchBy == 'student_type') {
                $searchString = $request->input('student_origin');
            } elseif ($searchBy == 'agent_id') {
                $searchString = $request->input('student_agent');
            } elseif ($searchBy == 'status') {
                $searchString = $request->input('student_status');
            } else {
                $searchString = $request->input('search_string');
            }
            if ($campusID != '' && $searchBy != '' && $searchString != '') {
                $findRtoStudentId = new StudentCourse;
                $findStudentId = $findRtoStudentId->searchStudentsWithCampus($campusID);
                $studentId = [];
                for ($i = 0; $i < count($findStudentId); $i++) {
                    $studentId[] = $findStudentId[$i]['student_id'];
                }
                $objRtoStudentsLists = new Students;
                $objStudentsLists = $objRtoStudentsLists->searchStudentsV2($searchBy, $searchString, $perPage, $studentId, $collegeId, $studentRoleType);
            } elseif ($campusID == '' && $searchBy != '' && $searchString != '') {
                $studentId = '';
                $objRtoStudentsLists = new Students;
                $objStudentsLists = $objRtoStudentsLists->searchStudentsV2($searchBy, $searchString, $perPage, $studentId, $collegeId, $studentRoleType);
            } else {
                $objStudentsLists = $objRtoStudentsLists->getStudentsList($perPage, $collegeId, $studentRoleType);
            }
        }

        $data['header'] = [
            'title' => 'Search Students',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Students' => '',
            ]];
        $data['pagetitle'] = 'Search Student';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['applyonline.js'];
        $data['funinit'] = ['Applyonline.initStudentListing()'];
        $data['activateValue'] = 'Students';

        $data['objStudentsLists'] = $objStudentsLists;
        $data['arrCampus'] = $arrCampus;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['sessionPermissionDataEdit'] = $sessionPermissionDataEdit;
        $data['arrCountry'] = $arrCountry;
        $data['arrStudentOrigin'] = $arrStudentOrigin;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrAgentData'] = $arrAgentData;
        $data['arrCourseStatus'] = $arrCourseStatus;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.search-students_old', $data);
    }

    public function deleteStudents($studentData, Request $request)
    {
        $sData = explode('-', $studentData);
        $studentId = $sData[0];
        $courseId = $sData[1];

        $objRtoStudentCourse = new StudentCourse;
        $objStudentCourses = $objRtoStudentCourse->deleteStudentCourseRecord($courseId, $studentId);

        $getStudentEnrollSubjectId = StudentSubjectEnrolment::where('college_id', '=', Auth::user()->college_id)
            ->where('student_id', $studentId)
            ->where('course_id', $courseId)
            ->get(['id'])->toArray();

        $objRtoStudentUnitEnrollment = new StudentUnitEnrollment;
        $deleteUnitEnrollment = $objRtoStudentUnitEnrollment->deleteStudentUnitEnrollmentInfo($getStudentEnrollSubjectId);

        $objRtoStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $objStudentSubjectEmployment = $objRtoStudentSubjectEnrolment->deleteStudentSubjectEmployment($studentId, $courseId);

        $objRtoAgentCommission = new StudentAgentCommission;
        $objAgentCommission = $objRtoAgentCommission->deleteStudentAgentCommission($studentId, $courseId);

        $objRtoStudentInitialPayment = new StudentInitialPayment;
        $objStudentInitialPayment = $objRtoStudentInitialPayment->deleteStudentInitialPayment($studentId, $courseId);

        $objStudentServicePayment = new StudentServicePayment;
        $arrStudentServicePayment = $objStudentServicePayment->deleteStudentServicePayment($studentId, $courseId);

        $objRtoStudentInitialPaymentDetails = new StudentInitialPaymentDetails;
        $objStudentInitialPaymentDetails = $objRtoStudentInitialPaymentDetails->deleteStudentInitialPaymentDetails($studentId, $courseId);

        $objRtoStudentInitialPaymentTransaction = new StudentInitialPaymentTransaction;
        $deleteStudentInitialPaymentTransaction = $objRtoStudentInitialPaymentTransaction->deleteStudentInitialPaymentTransaction($studentId, $courseId);

        $objMiscellaneous = new StudentMiscellaneousPayment;
        $arrMiscellaneousList = $objMiscellaneous->deleteStudentMiscellaneousPayment($studentId, $courseId);

        $objStudentAdditionalServiceRequest = new StudentAdditionalServiceRequest;
        $objStudentAdditionalServiceRequest->deleteStudentAdditionalServiceRequest($studentId, $courseId);

        $countStudentEnrolled = StudentCourse::where('student_id', $studentId)->where('offer_status', 'Enrolled')->get()->count();

        $objRtoDeleteStudent = new Students;
        $arrStudentEmailId = $objRtoDeleteStudent->getStudentEmailId($studentId);

        if ((count($arrStudentEmailId) > 0) && ($countStudentEnrolled == 0)) {
            $objRtoDeleteUser = new Users;
            $objRtoDeleteUser = $objRtoDeleteUser->deleteUserFromStudent($arrStudentEmailId[0]['email']);
        }

        $count = StudentCourse::where('student_id', $studentId)->get()->count();
        if ($count == 0) {

            $objDeleteStudents = $objRtoDeleteStudent->deleteStudents($studentId);

            $objRtoDeleteStudentDetails = new StudentDetails;
            $objDeleteStudentsDetails = $objRtoDeleteStudentDetails->deleteStudentsDetails($studentId);

            $objRtoStudentEducation = new StudentEducation;
            $objStudentEducation = $objRtoStudentEducation->deleteStudentEducation($studentId);

            $objRtoStudentEmployment = new StudentEmployment;
            $objStudentEmployment = $objRtoStudentEmployment->deleteStudentEmployment($studentId);

            if ($objDeleteStudents && $objDeleteStudentsDetails) {
                $request->session()->flash('session_success', 'Student Delete Successfull.');

                return Redirect::back();
            }
        }

        if ($objStudentCourses) {
            $request->session()->flash('session_success', 'Student Course Delete Successfull.');

            return Redirect::back();
        }

        return redirect(route('search-students'));
    }

    public function viewAcademicSummary($studentId, $courseId, Request $request)
    {
        $perPage = Config::get('constants.pagination.perPage');
        $arrAcademic = Config::get('constants.arrAcademic');

        $collegeId = Auth::user()->college_id;

        $objCourseSubject = new CourseSubject;
        $arrCourseSubjectId = $objCourseSubject->getCourseSubjectId($courseId);

        $objSubjectSummary = $objCourseSubject->getCourseSubjectSummary($collegeId, $courseId);

        $objRtoStudentSubjectEnrolment = new StudentSubjectEnrolment;

        $objEnrollSubject = $objRtoStudentSubjectEnrolment->getCourseSubjectSummary($collegeId, $studentId, $courseId);

        $arrSubjectSummary = [];
        for ($i = 0; $i < count($objSubjectSummary); $i++) {
            $arrSubjectSummary[$i]['subject_code'] = $objSubjectSummary[$i]->subject_code;
            $arrSubjectSummary[$i]['subject_name'] = $objSubjectSummary[$i]->subject_name;
            $arrSubjectSummary[$i]['first_name'] = $objSubjectSummary[$i]->first_name;
            $arrSubjectSummary[$i]['last_name'] = $objSubjectSummary[$i]->last_name;

            for ($j = 0; $j < count($objEnrollSubject); $j++) {
                if ($objSubjectSummary[$i]->subject_id == $objEnrollSubject[$j]['subject_id']) {
                    $arrSubjectSummary[$i]['activity_start_date'] = $objEnrollSubject[$j]->activity_start_date;
                    $arrSubjectSummary[$i]['activity_finish_date'] = $objEnrollSubject[$j]->activity_finish_date;
                    $arrSubjectSummary[$i]['final_outcome'] = $objEnrollSubject[$j]->final_outcome;
                    $arrSubjectSummary[$i]['batch'] = $objEnrollSubject[$j]->batch;
                    break;
                } else {
                    $arrSubjectSummary[$i]['activity_start_date'] = '';
                    $arrSubjectSummary[$i]['activity_finish_date'] = '';
                    $arrSubjectSummary[$i]['final_outcome'] = '';
                }
            }
        }

        $objCourseUnit = new UnitModule;
        $objUnitSummary = $objCourseUnit->getCourseUnitSummary($collegeId, $arrCourseSubjectId, $perPage, $studentId);

        $data['pagetitle'] = 'Student Profile';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['studentprofile.js'];
        $data['funinit'] = ['studentProfile.initAcademicSummary()'];
        $data['activateValue'] = 'Students';
        $data['arrAcademic'] = $arrAcademic;
        $data['objUnitSummary'] = $objUnitSummary;
        $data['arrSubjectSummary'] = $arrSubjectSummary;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.view-academic-summary', $data);
    }

    public function studentDataView($studentId)
    {
        $studentData = Students::where('id', $studentId)->get()->toArray();
        dd($studentData);
    }

    public function studentProfile($studentId, Request $request)
    {
        return redirect(route('student-profile-view', ['id' => encryptIt($studentId)]));

        $collegeId = Auth::user()->college_id;
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = (isset($sessionPermission['deny_staff_to_update_student_profile_info'])) ? $sessionPermission['deny_staff_to_update_student_profile_info'] : '';

        $perPage = Config::get('constants.pagination.perPage');
        $arrDays = Config::get('constants.arrDays');
        $arrTypeData = Config::get('constants.arrCourseDeferType');
        $arrReasonData = Config::get('constants.arrCourseDeferReason');
        $arrAccountManager = Config::get('constants.arrAccountManager');

        $objStudendProfile = new Students;
        // $studentProfile = $objStudendProfile->getStudentDetail($studentId)[0];
        $tempStudentProfile = $objStudendProfile->getStudentDetail($studentId);
        if (! isset($tempStudentProfile) || count($tempStudentProfile) == 0) {
            $request->session()->flash('session_error', 'Invalid student ID');

            return redirect(route('search-students'));
        }
        $studentProfile = $tempStudentProfile[0];

        $objDeferCourse = new StudentDeferCourse;
        $arrDeferCourseStudent = $objDeferCourse->getDeferCourseStudent($collegeId, $studentId, $perPage);

        $objStudentCourse = new StudentCourse;
        $studentCourses = $objStudentCourse->getStudentProfileCourses($studentId, $collegeId, $this->newLimit);

        $objStaff = new Staff;
        $arrStaff = $objStaff->getStaffNameListData($collegeId);

        $taskModel = new Task;
        $taskmodelRepository = new Repository($taskModel);

        $arrTimeTableData = [];
        $arrTimeTableDay = [];
        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrolment = $objStudentSubjectEnrolment->getStudentEnrolmentDetails($studentId);
        for ($i = 0; $i < count($arrStudentSubjectEnrolment); $i++) {
            $objTimetable = new Timetable;
            $arrTimetable = $objTimetable->getStudentTimeTable($arrStudentSubjectEnrolment[$i]);
            $arrTimeTableData[$i] = $arrTimetable;
        }
        for ($j = 0; $j < count($arrTimeTableData); $j++) {
            if (! empty($arrTimeTableData[$j])) {
                for ($z = 0; $z < count($arrTimeTableData[$j]); $z++) {
                    if ($arrTimeTableData[$j][$z]['day'] == 'Sunday') {
                        $arrTimeTableDay['Sunday'][] = $arrTimeTableData[$j][$z];
                    }
                    if ($arrTimeTableData[$j][$z]['day'] == 'Monday') {
                        $arrTimeTableDay['Monday'][] = $arrTimeTableData[$j][$z];
                    }
                    if ($arrTimeTableData[$j][$z]['day'] == 'Tuesday') {
                        $arrTimeTableDay['Tuesday'][] = $arrTimeTableData[$j][$z];
                    }
                    if ($arrTimeTableData[$j][$z]['day'] == 'Wednesday') {
                        $arrTimeTableDay['Wednesday'][] = $arrTimeTableData[$j][$z];
                    }
                    if ($arrTimeTableData[$j][$z]['day'] == 'Thursday') {
                        $arrTimeTableDay['Thursday'][] = $arrTimeTableData[$j][$z];
                    }
                    if ($arrTimeTableData[$j][$z]['day'] == 'Friday') {
                        $arrTimeTableDay['Friday'][] = $arrTimeTableData[$j][$z];
                    }
                    if ($arrTimeTableData[$j][$z]['day'] == 'Saturday') {
                        $arrTimeTableDay['Saturday'][] = $arrTimeTableData[$j][$z];
                    }
                }
            }
        }

        $objStudentDetails = new StudentDetails;
        $StudentDetails = $objStudentDetails->getStudentDetails($studentId);

        $sectionTypeId = '1'; // for Defer/Holiday/Suspension
        $objSetupSection = new SetupSection;
        $arrReasonList = $objSetupSection->getSectionTypeList($collegeId, $sectionTypeId);

        $arrCaseType = Config::get('constants.arrCaseType');
        $filePath = Config::get('constants.uploadFilePath.StudentPics');
        $destinationPath = Helpers::changeRootPath($filePath, $studentId);

        $profile_pic = $destinationPath['default'].$studentProfile->profile_picture;

        if (file_exists($profile_pic) && ! empty($studentProfile->profile_picture)) {
            $data['profile_pic'] = $destinationPath['view'].$studentProfile->profile_picture;
        } else {
            $data['profile_pic'] = 'icon/profile.png';
        }

        $objStudentCommunicationLog = new StudentCommunicationLog;
        $studentCommunicationLog = $objStudentCommunicationLog->getStudentCommunicationLog($studentId, $perPage);

        $arrModeQfDelivery = Config::get('constants.arrModeQfDelivery');
        $data['priorityColor'] = Config::get('constants.arrTaskPriorityColor');
        $data['taskColor'] = Config::get('constants.arrTaskStatusColor');

        $year = date_diff(date_create($studentProfile->DOB), date_create('today'))->y;
        $month = date_diff(date_create($studentProfile->DOB), date_create('today'))->m;
        $days = date_diff(date_create($studentProfile->DOB), date_create('today'))->d;
        $age = $year.' Year. '.$month.' Month. '.$days.' Days.';

        $data['pagetitle'] = 'Student Profile';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js', 'readmore.js'];
        $data['js'] = ['studentprofile.js', 'studentProfileTask.js'];
        $data['funinit'] = ['studentProfile.initHomePage()', 'studentProfile.initModuleList()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Students';

        $data['studentProfile'] = $studentProfile;
        $data['age'] = $age;
        $data['studentId'] = $studentId;
        $data['arrTypeData'] = $arrTypeData;
        $data['arrReasonData'] = $arrReasonData + $arrReasonList;
        $data['studentCourses'] = $studentCourses;
        $data['StudentDetails'] = $StudentDetails;
        $data['arrModeQfDelivery'] = $arrModeQfDelivery;
        $data['arrDeferCourseStudent'] = $arrDeferCourseStudent;

        $newArray = [];
        $abc = $studentCommunicationLog->toArray();
        if (! empty($abc)) {
            $findLast = end($abc);

            $data['lastId'] = $findLast['id'];
            foreach ($abc as $key => $value) {
                $newArray[$value['today_date']][] = $value;
            }
        }
        $data['studentCommunicationLog'] = $newArray;
        $data['arrTimeTableDay'] = $arrTimeTableDay;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['arrDays'] = $arrDays;
        $data['arrCaseType'] = $arrCaseType;
        $data['studentTasks'] = $this->getStudentTask($collegeId, $studentId);
        $arrSelectFinalOutcome = Config::get('constants.arrSelectFinalOutcome');
        $arrSelectMarksOutcome = (new ResultGrade)->getGradeResultList($collegeId);
        $arrSubjectUnitList = $objStudentSubjectEnrolment->listStudentSubjectEnrolmentV2($collegeId, $studentId, '', '');
        $arrSubjectList = [];
        $arrWeeklyAttendance = [];
        $arrTimetable = [];
        $subjectBatch = [];
        $arrSemestor = [];
        $arrTerm = [];
        if (! empty($arrSubjectUnitList)) {
            for ($i = 0; $i < count($arrSubjectUnitList); $i++) {
                $objStudentAttendance = new StudentAttendance;
                $arrWeeklyAttendance[] = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $arrSubjectUnitList[$i]['semester_id'], $arrSubjectUnitList[$i]['term'], $studentId);
                $subjectBatch[$arrSubjectUnitList[$i]['subject_code']] = $arrSubjectUnitList[$i]['batch'];
                $arrSemestor[$arrSubjectUnitList[$i]['semester_id']] = $arrSubjectUnitList[$i]['semester_name'];
                $arrTerm[$arrSubjectUnitList[$i]['term']] = $arrSubjectUnitList[$i]['term'];
                $arrSubjectList[$i] = $arrSubjectUnitList[$i]['subject_code'];
            }
            $objTimetable = new Timetable;
            $arrTimetable = $objTimetable->getWeeklyTimeTableBySubjectOnDeskboard($collegeId, $arrSubjectUnitList[0]['semester_id'], $arrSubjectUnitList[0]['term'], $subjectBatch);
        }
        $objRtoStudentCourse = new StudentCourse;
        $arrStudentCourse = $objRtoStudentCourse->getStudentAppliedOfferCourseName($studentId);

        $data['arrSemestor'] = array_unique($arrSemestor);
        $data['arrTerm'] = array_unique($arrTerm);
        $data['arrSubjectList'] = $arrSubjectList;
        $data['arrWeeklyAttendance'] = $arrWeeklyAttendance;
        $data['arrTimetable'] = $arrTimetable;

        $data['arrSubjectUnitList'] = $arrSubjectUnitList;
        $data['arrSelectFinalOutcome'] = $arrSelectFinalOutcome;
        $data['arrSelectMarksOutcome'] = $arrSelectMarksOutcome;
        $data['pagination'] = $studentCourses->links();
        $data['student_course_list'] = $studentCourses;
        $data['mainmenu'] = 'clients';

        if ($studentProfile->account_manager_id != '') {
            $data['accountManager'] = (array_key_exists($studentProfile->account_manager_id, $arrStaff)) ? $arrStaff[$studentProfile->account_manager_id] : '';
        } else {
            $data['accountManager'] = null;
        }
        $tenantPrefix = Config::get('tenancy.database.prefix');
        $filePath = Config::get('constants.uploadFilePath.TaskFile');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data['filepath'] = $destinationPath['view'];
        $currentUrl = explode('/', url()->current());
        $currentUrl = explode('.', $currentUrl[2]);
        $data['applicationurl'] = getStudentApplicationUrl().Auth::user()->id;

        $isAllowToSync = false;
        $xeroContact = false;
        if (Xero::isConnected()) {

            $hasExistCoe = StudentCourse::where('student_id', $studentId)
                ->where(function ($query) {
                    $query->where('coe_name', '!=', '')->orWhere('coe_image', '!=', '');
                })
                ->exists();

            if ($hasExistCoe) {
                $student = Student::with('xeroContact')->findOrFail($studentId);
                $xeroContact = $student->xeroContact;
                $outstandingBalance = 0;
                $unallocatedCredit = 0;

                if (! $xeroContact || $xeroContact->xero_failed_at) {
                    $isAllowToSync = true;
                } else {
                    if ($student->isXeroContactCreatated()) {
                        // dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($student->xeroContact));
                        $outstandingBalance = $student->xeroContact->fresh()->outstanding_balance ? $student->xeroContact->fresh()->outstanding_balance : 0;
                        $unallocatedCredit = $xeroContact->fresh()->unallocated_credit ?? 0;
                    }
                }
                // $data['xeroContact'] = $xeroContact;
                if ($xeroContact) {
                    $xeroContact->outstanding_balance = abs($outstandingBalance);
                    $xeroContact->unallocatedCredit = abs($unallocatedCredit);
                }
            }
        }
        $data['isAllowToSync'] = $isAllowToSync;
        $data['xeroContact'] = $xeroContact;

        return view('frontend.student.student-profile', $data);
    }

    public function getStudentTask($collegeId, $studentId)
    {
        $query = TaskNew::from('rto_task_new')
            ->leftjoin('rto_task_priority', 'rto_task_priority.id', '=', 'rto_task_new.priority_id')
            ->leftjoin('rto_task_status', 'rto_task_status.id', '=', 'rto_task_new.status_id')
            ->where('rto_task_new.college_id', $collegeId)
            ->where('rto_task_new.student_id', $studentId)
            ->where('rto_task_new.subtask_id', 0);

        $query->select(
            'rto_task_new.id as taskid',
            'rto_task_new.title as title',
            'rto_task_new.due_date as due_date',
            'rto_task_priority.priority_name as priority',
            'rto_task_priority.priority_color as priority_color',
            'rto_task_status.status_name as status',
            'rto_task_status.status_color'
        );
        $query->orderBy('taskid', 'DESC');
        $query->limit('5');

        return $query->get()->toArray();
    }

    public function _getAttendanceOverview($collegeId, $studentId, $semestorId, $term)
    {
        $arrSelectFinalOutcome = Config::get('constants.arrSelectFinalOutcome');
        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrSubjectUnitList = $objStudentSubjectEnrolment->listStudentSubjectEnrolmentSemesterTerm($collegeId, $studentId, '', $this->newLimit, $semestorId, $term);
        $arrSubjectList = [];
        $arrWeeklyAttendance = [];
        $arrTimetable = [];
        $subjectBatch = [];
        $arrSemestor = [];
        $arrTerm = [];
        $total_projected_attandance_rows = 0;
        if (! empty($arrSubjectUnitList)) {
            for ($i = 0; $i < count($arrSubjectUnitList); $i++) {
                if ($semestorId == $arrSubjectUnitList[$i]['semester_id']) {
                    $objStudentAttendance = new StudentAttendance;
                    $arrWeeklyAttendance[] = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $semestorId, $term, $studentId);
                    $subjectBatch[$arrSubjectUnitList[$i]['subject_code']] = $arrSubjectUnitList[$i]['batch'];

                    $arrSubjectList[$semestorId][] = $arrSubjectUnitList[$i]['subject_code'];
                    $arrSemestor[$semestorId] = $arrSubjectUnitList[$i]['semester_name'];

                    $objTimetable = new Timetable;
                    $arrTimetable[$semestorId] = $objTimetable->getWeeklyTimeTableBySubjectOnDeskboard($collegeId, $semestorId, $term, $subjectBatch);
                }
            }

            foreach ($arrTimetable[$semestorId] as $key => $row) {
                $subjectWeeklyHours = isset($row['subject']) ? array_sum($row['subject']) : 0;
                $total_projected_attandance_rows += $subjectWeeklyHours;
            }
        }

        $data['arrSemestor'] = $arrSemestor;
        $data['arrSubjectList'] = $arrSubjectList;
        $data['arrWeeklyAttendance'] = $arrWeeklyAttendance;
        $data['arrTimetable'] = $arrTimetable;
        $data['total_projected_attandance_rows'] = ($total_projected_attandance_rows) ? $total_projected_attandance_rows : 1;

        return $data;

    }

    public function _getUnitName($course_id, $subjectID, $studentId)
    {
        $collegeId = Auth::user()->college_id;
        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rsse.unit_id')
            ->leftjoin('rto_student_unit_enrollment', 'rto_student_unit_enrollment.student_subject_enrollment_id', '=', 'rsse.id')
            ->leftjoin('rto_timetable', function ($join) {
                $join->on('rto_timetable.subject_id', '=', 'rsse.subject_id');
                $join->on('rto_timetable.batch', '=', 'rsse.batch');
                $join->on('rto_timetable.semester_id', '=', 'rsse.semester_id');
            })
            ->where('rsse.college_id', '=', $collegeId)
            ->where('rsse.student_id', '=', $studentId)
            ->where('rsse.subject_id', '=', $subjectID)
            ->where('rsse.course_id', '=', $course_id)
            ->groupBy('rto_student_unit_enrollment.unit_id')
            ->orderBy('rto_student_unit_enrollment.study_from')
            ->get(['rsse.id as enrollID', 'rto_subject_unit.id', 'rto_subject_unit.unit_name',
                'rsse.mark_outcome',
                'rto_subject_unit.unit_code', 'rto_student_unit_enrollment.study_from', 'rto_student_unit_enrollment.id as studnet_unit_enrollment_id', 'rto_student_unit_enrollment.study_to', 'rsse.final_outcome', 'rto_student_unit_enrollment.compentency', 'rsse.updated_at', 'rsse.batch', 'rto_timetable.start_week', 'rto_timetable.end_week']);

        return $sql;
    }

    public function studentProfileSendMail($studentId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $arrRecipient = Config::get('constants.arrRecipient');
        unset($arrRecipient['3']);
        unset($arrRecipient['4']);
        unset($arrRecipient['5']);
        unset($arrRecipient['6']);

        $objCollegesMail = new Colleges;
        $arrFromMail = $objCollegesMail->getCollegeMail($collegeId);
        // $courseId = $request->input('enrolled_course_id'); // Remove by kartik 27-04-2018

        $studentCourseId = '';
        $courseId = $request->input('course_id');
        if (empty($courseId)) {
            $studentCourseId = $request->input('enrolled_course_id');
            $courseId = StudentCourse::where('id', $studentCourseId)->value('course_id');
        }

        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailTemplate = $objRtoEmailTemplate->getEmailTemplateType($collegeId);

        $objRtoStudentCourse = new StudentCourse;
        $arrStudentCourse = $objRtoStudentCourse->getStudentAppliedCourseNameV2($studentId);
        if ($studentCourseId == '') {
            $studentCourseIdArr = array_keys($arrStudentCourse);
            $studentCourseId = $studentCourseIdArr[0];
        }

        $objStdentAgentName = $objRtoStudentCourse->getStudentAgentNameV3($studentId, $studentCourseId);
        unset($objStdentAgentName['0']);
        $agentName = array_values($objStdentAgentName);
        $objRtoapplicationRefID = new Students;
        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($studentId, $collegeId);

        if (count($objStdentAgentName) > 0) {
            $objStdentAgentName = $objStdentAgentName;
        } else {
            $objStdentAgentName = [];
        }

        if ($request->isMethod('post')) {
            $errorArr = [];
            $fileForValidate = $request->file();

            if ($request->file()) {
                $isValidSize = true;
                $isValidType = true;
                foreach ($fileForValidate as $tmpFile) {
                    if ($isValidSize) {
                        $tmpSize = $tmpFile->getSize();
                        $tmpSize = number_format($tmpSize / 1048576, 2);
                        if ($tmpSize > 5) {
                            $isValidSize = false;
                        }
                    }
                    if ($isValidType) {
                        $arr = ['pdf', 'jpg', 'jpeg', 'gif', 'png'];
                        $tmpType = $tmpFile->guessClientExtension();
                        if (! in_array(strtolower($tmpType), $arr)) {
                            $isValidType = false;
                        }
                    }
                }

                if (! $isValidType || ! $isValidSize) {
                    (! $isValidType) ? $errorArr[] = 'Please Select Valid File ( .png , .pdf , .jpg ,.jpeg ,.gif)' : '';
                    (! $isValidSize) ? $errorArr[] = 'The maximum file upload size limit is 5MB.' : '';

                    return redirect(route('student-profile-send-mail', ['id' => $studentId]))->withErrors($errorArr)->withInput();
                }
            }

            $validations = [
                'email_subject' => 'required',
                'email_content' => 'required',
                'email_cc' => 'nullable',
            ];

            $validator = Validator::make($request->all(), $validations, $errorArr);
            if ($validator->fails()) {
                if (! empty($request->page_type)) {
                    return redirect(route('student-payment-summary', ['id' => $studentId]))
                        ->withErrors($validator)
                        ->withInput();
                } elseif (! empty($request->last_page)) {
                    return redirect(route($request->last_page, ['id' => $studentId]))
                        ->withErrors($validator)
                        ->withInput();
                } else {
                    return redirect(route('student-profile-send-mail', ['id' => $studentId]))
                        ->withErrors($validator)
                        ->withInput();
                }
            }

            $emailTemplateId = $request->input('email_template');
            $email_to = $request->email_to;
            $status = true;

            $getStudentCOEDetail = $objRtoStudentCourse->getStudentCoursesData($request->input('stud_id'));

            $is_payment_invoice = ($request->input('student_invoice')) ? $request->input('student_invoice') : '0';
            $is_offer_letter = ($request->input('offer_letter')) ? $request->input('offer_letter') : '0';
            $is_attach_coe = ($request->input('attach_coe')) ? $request->input('attach_coe') : '0';
            $tempFilePath = Config::get('constants.uploadFilePath.Templates');
            $templateFilePath = Helpers::changeRootPath($tempFilePath, $emailTemplateId);
            $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);
            $savedFileName = [];
            $invoicePdf = $offerLetterPdf = '';
            $download = 'save';
            if ($is_payment_invoice > 0) {
                // $courseId = $request->input('course_id');
                $this->studentInvoicePdf($courseId, $studentId, $download, $request);
                $invoicePdf = $destinationPath['default'].'invoice.pdf';
                $savedFileName[] = $invoicePdf;
            }
            //  offer letter pdf
            if ($is_offer_letter > 0) {
                // $courseId = $request->input('course_id');

                $arrStudentCourseDetails = $objRtoStudentCourse->getStudentCourseDetails($courseId, $studentId);

                $studentCourseID = $arrStudentCourseDetails->id;
                if (isset($studentCourseID)) {
                    $this->offerLetterPdf($courseId, $studentId, $studentCourseID, $download, $request);

                    $studentOfferLetterName = $this->studentOfferLetterName($studentId);
                    $offerLetterPdf = $destinationPath['default'].$studentOfferLetterName.'.pdf';
                    $savedFileName[] = $offerLetterPdf;
                }
            }

            if ($is_attach_coe > 0) {
                $filePath = Config::get('constants.uploadFilePath.StudentCOE');
                $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                foreach ($getStudentCOEDetail as $key => $value) {
                    if (isset($value->coe_image) && ! empty($value->coe_image)) {
                        if (file_exists($destinationPath['default'].$value->coe_image)) {
                            $COEImage = $destinationPath['default'].$value->coe_image;
                            $savedFileName[] = $COEImage;
                        }
                    }
                }
            }

            $invoiceNo = $request->input('invoice_number');
            if ($invoiceNo == '') {
                $invoiceNo = $request->input('studentInvoiceNo');
            }
            /* Start */
            $objRtoEmailTemplate = new EmailTemplate;
            $arrEmailTemplateInfo = $objRtoEmailTemplate->getEmailTemplateInfo($collegeId, $emailTemplateId);
            $replacedContent = $request->input('email_content');
            $objSendMail = new SendMail;
            /* End */

            $objRtoStudentEmail = new StudentEmail;
            $savedFileNameList = $objRtoStudentEmail->saveStudentMailForPayment($studentId, $request, $email_to, $status, $replacedContent, 'Invoice');

            // $courseId = $request->input('enrolled_course_id');
            $filePath1 = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath1 = Helpers::changeRootPath($filePath1);
            $logoPath = $destinationPath1['default'];
            $data['arrState'] = Config::get('constants.arrState');
            $agentAttachment = [];
            if ($request->input('invoice_sent') == '1') {
                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $objStudent = new Students;
                $arrStudentInfo = $objStudent->getStudentDetail($studentId);

                $objStudentDetailsGet = new StudentInitialPaymentDetails;
                $arrStudentDetailsGet = $objStudentDetailsGet->studentPaymentDetailsGet($collegeId, $courseId, $studentId, $invoiceNo);

                $objStudentCourse = new StudentCourse;
                $arrStudentCourse = $objStudentCourse->getStudentPdfCourseData($courseId, $studentId);
                $objMiscellaneousPayment = new StudentMiscellaneousPayment;
                $arrMiscellaneousPayment = $objMiscellaneousPayment->getMiscellaneousPayment($invoiceNo);
                $invoiceSent = $request->input('invoice_sent');
                if ($request->input('student_miscellaneous_payment_id')) {
                    $studentMiscellaneousPaymentId = $request->input('student_miscellaneous_payment_id');
                    $objMiscellaneousPayment->updatePaidMiscellaneousInvoiceStatus($invoiceSent, $studentMiscellaneousPaymentId);
                }

                $objStudentServicePayment = new StudentServicePayment;
                $arrStudentServicePayment = $objStudentServicePayment->getStudentServicePayment($invoiceNo);
                $objAgentCommission = new StudentAgentCommission;
                $arrAgentCommission = $objAgentCommission->getAgentCommissionValue($studentId, $courseId, $invoiceNo);
                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $data['objCollegeDetails'] = $objCollegeDetails[0];
                // $data['clg_logo'] = $logoPath . $arrStudentInfo[0]->college_logo;
                $data['clg_logo'] = str_replace('\\', '/', $destinationPath1['view']).$arrStudentInfo[0]->college_logo;
                $data['arrStudentDetailsGet'] = $arrStudentDetailsGet;
                $data['arrMiscellaneousPayment'] = $arrMiscellaneousPayment;
                $data['arrStudentServicePayment'] = $arrStudentServicePayment;
                $data['arrStudentCourse'] = $arrStudentCourse;
                $data['arrAgentCommission'] = $arrAgentCommission;

                $html = view('frontend.student_account.agent-pro-invoice-pdf', $data);
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadHTML($html);
                $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
                $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                if (! is_dir($destinationPath['default'])) {
                    mkdir($destinationPath['default'], 0777, true);
                }
                $pdf->save($destinationPath['default'].'AgentInvoice_'.$invoiceNo.'.pdf');
                $agentAttachment[] = $destinationPath['default'].'AgentInvoice_'.$invoiceNo.'.pdf';
            }

            if (! empty($emailTemplateId)) {

                $templateFileArr = EmailTemplateDocuments::where('email_template_id', $emailTemplateId)->get(['file'])->toArray();
                for ($i = 0; $i < count($templateFileArr); $i++) {
                    $savedFileNameList[] = $templateFilePath['default'].$templateFileArr[$i]['file'];
                }
            }
            $email_cc_array = ! empty($request->input('email_cc')) ? explode(',', $request->input('email_cc')) : '';
            $email_cc = is_array($email_cc_array) ? array_map('trim', $email_cc_array) : trim($email_cc_array);

            if ($request->input('invoice_sent') == '1') {
                $mailSendAddress = [$agentName[0]];
                $mailData = [
                    'from' => env('MAIL_USERNAME'),
                    'fromName' => env('MAIL_NAME'),
                    'to' => $mailSendAddress,
                    'page' => 'mail.student-offer-email',
                    'subject' => $request->input('email_subject'),
                    'cc' => $email_cc,
                    'attachFile' => $agentAttachment,
                    'data' => ['content' => $replacedContent],
                ];

                $objSendMail->sendSmtpMail($mailData);
            }
            if ($savedFileName) {
                $savedFileNameList = array_merge($savedFileNameList, $savedFileName);
            }

            if ($request->input('student_invoice') == '1') {
                $mailSendAddress = [$applicationRefID[0]['email']];
                $mailData = [
                    'from' => env('MAIL_USERNAME'),
                    'fromName' => env('MAIL_NAME'),
                    'to' => $mailSendAddress,
                    'page' => 'mail.student-offer-email',
                    'subject' => $request->input('email_subject'),
                    'cc' => $email_cc,
                    'attachFile' => $savedFileNameList,
                    'data' => ['content' => $replacedContent],
                ];
                $objSendMail->sendSmtpMail($mailData);
            }

            /* START (28-10-2020)only send this url  https://design.galaxy360.com.au/student-profile-send-mail/576 */
            $mailSendAddress = [$applicationRefID[0]['email']];
            $mailData = [
                'from' => env('MAIL_USERNAME'),
                'fromName' => env('MAIL_NAME'),
                'to' => $mailSendAddress,
                'page' => 'mail.student-offer-email',
                'subject' => $request->input('email_subject'),
                'cc' => $email_cc,
                'attachFile' => $savedFileNameList,
                'data' => ['content' => $replacedContent],
            ];

            $result = $objSendMail->sendSmtpMail($mailData);

            if ($result == 'success') {
                $request->session()->flash('session_success', 'Mail Sent Successfully.');
            } else {
                $request->session()->flash('session_error', 'Mail is not sent successfully. Please check Failed Email section for detail.');
            }
            /* END (28-10-2020) only uncomment code */

            if (! empty($request->page_type)) {
                return redirect(route('student-payment-summary', ['id' => $studentId]));
            } elseif (! empty($request->last_page)) {
                return redirect(route($request->last_page, ['id' => $studentId]));
            } else {
                return redirect(route('student-profile-send-mail', ['id' => $studentId]));
            }
        }
        $data['header'] = [
            'title' => 'Send Email Setup',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Students' => route('search-students'),
                'Send Mail' => '',
            ]];
        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [];
        $data['pluginjs'] = ['ckeditor/ckeditor.js', 'jQuery/jquery.validate.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['studentprofile.js', 'studentProfileTask.js'];
        $data['funinit'] = ['studentProfile.initProfileSendMail()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Students';
        $data['arrFromMail'] = $arrFromMail;
        $data['arrRecipient'] = $arrRecipient;
        $data['arrEmailTemplate'] = $arrEmailTemplate;
        $data['arrStudentCourse'] = $arrStudentCourse;
        $data['objStdentAgentName'] = $objStdentAgentName;
        $data['applicationRefID'] = $applicationRefID;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-profile-send-mail', $data);
    }

    public function studentProfileSendMailReceipt($studentId, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $arrRecipient = Config::get('constants.arrRecipient');
        unset($arrRecipient['3']);
        unset($arrRecipient['4']);
        unset($arrRecipient['5']);
        unset($arrRecipient['6']);
        $courseId = $request->input('enrolled_course_id');
        $objCollegesMail = new Colleges;
        $arrFromMail = $objCollegesMail->getCollegeMail($collegeId);
        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailTemplate = $objRtoEmailTemplate->getEmailTemplateType($collegeId);

        $objRtoStudentCourse = new StudentCourse;
        $arrStudentCourse = $objRtoStudentCourse->getStudentAppliedCourseNameV2($studentId);
        $objStdentAgentName = $objRtoStudentCourse->getStudentAgentNameV2($studentId, $courseId);
        unset($objStdentAgentName['0']);
        $agentName = array_values($objStdentAgentName);

        $objRtoapplicationRefID = new Students;
        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($studentId, $collegeId);
        if (count($objStdentAgentName) > 0) {
            $objStdentAgentName = $objStdentAgentName;
        } else {
            $objStdentAgentName = [];
        }

        if ($request->isMethod('post')) {
            $errorArr = [];
            $fileForValidate = $request->file();

            if ($request->file()) {
                $isValidSize = true;
                $isValidType = true;
                foreach ($fileForValidate as $tmpFile) {
                    if ($isValidSize) {
                        $tmpSize = $tmpFile->getSize();
                        $tmpSize = number_format($tmpSize / 1048576, 2);
                        if ($tmpSize > 5) {
                            $isValidSize = false;
                        }
                    }
                    if ($isValidType) {
                        $arr = ['pdf', 'jpg', 'jpeg', 'gif', 'png'];
                        $tmpType = $tmpFile->guessClientExtension();
                        if (! in_array(strtolower($tmpType), $arr)) {
                            $isValidType = false;
                        }
                    }
                }

                if (! $isValidType || ! $isValidSize) {
                    (! $isValidType) ? $errorArr[] = 'Please Select Valid File ( .png , .pdf , .jpg ,.jpeg ,.gif)' : '';
                    (! $isValidSize) ? $errorArr[] = 'The maximum file upload size limit is 5MB.' : '';

                    return redirect(route('student-profile-send-mail', ['id' => $studentId]))->withErrors($errorArr)->withInput();
                }
            }

            $validations = [
                'email_subject' => 'required',
                'email_content' => 'required',
            ];

            $validator = Validator::make($request->all(), $validations, $errorArr);
            if ($validator->fails()) {
                if (! empty($request->page_type)) {
                    return redirect(route('student-payment-summary', ['id' => $studentId]))
                        ->withErrors($validator)
                        ->withInput();
                } elseif (! empty($request->last_page)) {
                    return redirect(route($request->last_page, ['id' => $studentId]))
                        ->withErrors($validator)
                        ->withInput();
                } else {
                    return redirect(route('student-profile-send-mail', ['id' => $studentId]))
                        ->withErrors($validator)
                        ->withInput();
                }
            }

            $emailTemplateId = $request->input('email_template');
            $email_to = $request->email_to;
            $status = true;

            /* Start */
            $objRtoEmailTemplate = new EmailTemplate;
            $arrEmailTemplateInfo = $objRtoEmailTemplate->getEmailTemplateInfo($collegeId, $emailTemplateId);
            $content = (count($arrEmailTemplateInfo) > 0) ? $arrEmailTemplateInfo[0]->content : '';

            $objSendMail = new SendMail;
            $replacedContent = $objSendMail->setEmailBodyContent($studentId, $courseId, $content);
            /* End */

            $objRtoStudentEmail = new StudentEmail;
            $savedFileNameList = $objRtoStudentEmail->saveStudentMailForPayment($studentId, $request, $email_to, $status, $replacedContent, 'Receipt');

            $studentId = $request->input('enrolled_student_id');
            $student_course_id = $request->input('enrolled_course_id');
            $payment_id = $request->input('studentPaymentId');
            $data['arrState'] = Config::get('constants.arrState');
            if ($request->input('invoice_sent') == '1') {
                $objStudent = new Students;
                $collegeId = Auth::user()->college_id;
                $arrStudentInfo = $objStudent->getStudentDetail($studentId);
                $objStudentsLists = $objStudent->viewStudentPayment($studentId, $collegeId);

                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $objStudentDetailsGet = new StudentInitialPaymentDetails;
                $arrStudentDetailsGet = $objStudentDetailsGet->studentPaymentDetailsGetPrimary($payment_id);
                $objPaymentMode = new PaymentMode;
                $arrPaidMode = $objPaymentMode->getPaymentModeData($collegeId);
                for ($i = 0; $i < count($arrStudentDetailsGet); $i++) {
                    $arrStudentDetailsGet[$i]['payment_mode_value'] = $arrPaidMode[$arrStudentDetailsGet[0]['payment_mode']];
                }

                $objStudentCourse = new StudentCourse;
                $arrStudentCourse = $objStudentCourse->getStudentUpfrontFeeScheduleV2($studentId, $student_course_id);

                $courseId = $arrStudentCourse[0]->course_id;

                $objStudentCourse = new StudentCourse;
                $arrStudentCourse = $objStudentCourse->getStudentPdfCourseDataV2($courseId, $studentId, $student_course_id);

                $invoiceNumber = $arrStudentDetailsGet[0]['invoice_number'];
                $objMiscellaneousPayment = new StudentMiscellaneousPayment;
                $arrMiscellaneousPayment = $objMiscellaneousPayment->getMiscellaneousPayment($invoiceNumber);

                $objStudentServicePayment = new StudentServicePayment;
                $arrStudentServicePayment = $objStudentServicePayment->getStudentServicePayment($invoiceNumber);
                $objAgentCommission = new StudentAgentCommission;
                $arrAgentCommission = $objAgentCommission->getAgentCommissionValue($studentId, $courseId, $invoiceNumber);
                $transactionNo = $arrStudentDetailsGet[0]['transection_no'];

                // $arrAgentCommissionRefund = $objAgentCommission->getAgentPaidPaymentList($collegeId, $courseId, $studentId, $transactionNo);
                $arrAgentCommissionRefund = $objAgentCommission->getAgentPaidPaymentList($collegeId, $courseId, $studentId, $invoiceNumber);
                if ($arrAgentCommissionRefund->count() == 0) {
                    $arrAgentCommissionRefund[] = [
                        'comm_to_refund' => 0,
                        'GST_to_refund' => 0,
                    ];
                }

                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $logoPath = $destinationPath['default'];
                $clgLogo = str_replace('\\', '/', $destinationPath['view']).$arrStudentInfo[0]->college_logo;

                $data['college_signature'] = str_replace('\\', '/', $destinationPath['view']).$objCollegeDetails[0]->college_signature;

                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $data['objCollegeDetails'] = $objCollegeDetails[0];
                $data['clg_logo'] = $clgLogo; // $logoPath . $arrStudentInfo[0]->college_logo;
                $data['arrStudentDetailsGet'] = $arrStudentDetailsGet;
                $data['arrMiscellaneousPayment'] = $arrMiscellaneousPayment;
                $data['arrStudentServicePayment'] = $arrStudentServicePayment;
                $data['arrAgentCommissionRefund'] = $arrAgentCommissionRefund[0];
                $data['objStudentsLists'] = $objStudentsLists;
                $data['arrStudentCourse'] = $arrStudentCourse;
                $data['arrAgentCommission'] = $arrAgentCommission;
                $html = view('frontend.student_account.student-agent-receipt-pdf', $data);
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadHTML($html);

                $mailAttachFilePath = Config::get('constants.uploadFilePath.StudentMailAttach');
                $agentDestinationPath = Helpers::changeRootPath($mailAttachFilePath, $studentId);

                if (! is_dir($agentDestinationPath['default'])) {
                    mkdir($agentDestinationPath['default'], 0777, true);
                }

                $pdf->save($agentDestinationPath['default'].'Agent_Receipt_'.$invoiceNumber.'.pdf');
                $savedFileNameList[] = $agentDestinationPath['default'].'Agent_Receipt_'.$invoiceNumber.'.pdf';

                /*    GENERATE STUDENT RECEIPT */
                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $invoicePaymentInfo = $objStudentDetailsGet->getInvoicePaymentInfo($collegeId, $courseId, $studentId, $payment_id, $student_course_id);

                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $data['objCollegeDetails'] = $objCollegeDetails[0];
                $data['clg_logo'] = $clgLogo; // $logoPath . $arrStudentInfo[0]->college_logo;
                $data['arrStudentDetailsGet'] = $arrStudentDetailsGet;
                $data['arrMiscellaneousPayment'] = $arrMiscellaneousPayment;
                $data['arrStudentServicePayment'] = $arrStudentServicePayment;
                $data['objStudentsLists'] = $objStudentsLists;
                $data['arrStudentCourse'] = $arrStudentCourse;
                $data['arrAgentCommission'] = $arrAgentCommission;
                $data['invoicePaymentInfo'] = $invoicePaymentInfo;

                $html = view('frontend.student_account.student-tax-receipt-pdf', $data);
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadHTML($html);

                /*$filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
                $agentDestinationPath = Helpers::changeRootPath($filePath, $studentId);
                if (!is_dir($agentDestinationPath['default'])) {
                    mkdir($agentDestinationPath['default'], 0777, true);
                }*/

                $pdf->save($agentDestinationPath['default'].'Student_Receipt_'.$invoiceNumber.'.pdf');
                $savedFileNameList[] = $agentDestinationPath['default'].'Student_Receipt_'.$invoiceNumber.'.pdf';
            }

            if ($request->input('student_invoice') == '1') {
                $filePath1 = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath1 = Helpers::changeRootPath($filePath1);
                $logoPath1 = $destinationPath1['default'];

                $invoiceNo = $request->input('studentInvoiceNo');
                $student_course_id = $request->input('enrolled_course_id');

                $objStudentCourse = new StudentCourse;
                $arrStudentCourse = $objStudentCourse->getStudentUpfrontFeeScheduleV2($studentId, $student_course_id);

                $courseId = $arrStudentCourse[0]->course_id;

                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $objStudent = new Students;
                $arrStudentInfo = $objStudent->getStudentDetail($studentId);

                $objStudentCourse = new StudentCourse;
                $arrStudentCourse = $objStudentCourse->getStudentPdfCourseDataV2($courseId, $studentId, $student_course_id);

                $objStudentDetailsGet = new StudentInitialPaymentDetails;
                $arrStudentDetailsGet = $objStudentDetailsGet->studentPaymentDetailsGetV2($collegeId, $courseId, $studentId, $invoiceNo, $student_course_id);

                $objMiscellaneousPayment = new StudentMiscellaneousPayment;
                $arrMiscellaneousPayment = $objMiscellaneousPayment->getMiscellaneousPayment($invoiceNo);

                $objStudentServicePayment = new StudentServicePayment;
                $arrStudentServicePayment = $objStudentServicePayment->getStudentServicePayment($invoiceNo);

                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $data['objCollegeDetails'] = $objCollegeDetails[0];
                // $data['clg_logo'] = $logoPath1 . $arrStudentInfo[0]->college_logo;
                $data['clg_logo'] = str_replace('\\', '/', $destinationPath1['view']).$arrStudentInfo[0]->college_logo;
                $data['arrStudentCourse'] = [];
                if ($arrStudentCourse->count() > 0) {
                    $data['arrStudentCourse'] = $arrStudentCourse[0];
                }

                $data['arrStudentDetailsGet'] = $arrStudentDetailsGet;
                $data['arrMiscellaneousPayment'] = $arrMiscellaneousPayment;
                $data['arrStudentServicePayment'] = $arrStudentServicePayment;

                $html = view('frontend.student_account.student-schedule-invoice-pdf', $data);
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadHTML($html);
                $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
                $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                if (! is_dir($destinationPath['default'])) {
                    mkdir($destinationPath['default'], 0777, true);
                }
                $pdf->save($destinationPath['default'].'Schedule_Invoice_'.$invoiceNo.'.pdf');
                $savedFileNameList[] = $destinationPath['default'].'Schedule_Invoice_'.$invoiceNo.'.pdf';
            }
            // $mailSendAddress = array($applicationRefID[0]['email'], $agentName[0]);
            $mailSendAddress = [$applicationRefID[0]['email']];

            if (! empty($emailTemplateId)) {
                $templateFilePath = Config::get('constants.EmailTemplate.DocumentsPath');
                $templateFileArr = EmailTemplateDocuments::where('email_template_id', $emailTemplateId)->get(['file'])->toArray();
                for ($i = 0; $i < count($templateFileArr); $i++) {
                    $savedFileNameList[] = $templateFilePath.$templateFileArr[$i]['file'];
                }
            }
            $mailData = [
                'from' => env('MAIL_USERNAME'),
                'fromName' => env('MAIL_NAME'),
                'to' => $mailSendAddress,
                'page' => 'mail.student-offer-email',
                'subject' => $request->input('email_subject'),
                'cc' => (empty($request->input('email_cc'))) ? '' : $request->input('email_cc'),
                'attachFile' => $savedFileNameList,
                'data' => ['content' => $replacedContent],
            ];
            $objSendMail->sendSmtpMail($mailData);

            $request->session()->flash('session_success', 'Mail Sent Successfully.');
            if (! empty($request->page_type)) {
                return redirect(route('student-payment-summary', ['id' => $studentId]));
            } elseif (! empty($request->last_page)) {
                return redirect(route($request->last_page, ['id' => $studentId]));
            } else {
                return redirect(route('student-profile-send-mail', ['id' => $studentId]));
            }
        }

        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['ckeditor/ckeditor.js', 'jQuery/jquery.validate.min.js'];
        $data['js'] = ['studentprofile.js'];
        $data['funinit'] = ['studentProfile.initProfileSendMail()'];
        $data['activateValue'] = 'Students';
        $data['arrFromMail'] = $arrFromMail;
        $data['arrRecipient'] = $arrRecipient;
        $data['arrEmailTemplate'] = $arrEmailTemplate;
        $data['arrStudentCourse'] = $arrStudentCourse;
        $data['objStdentAgentName'] = $objStdentAgentName;
        $data['applicationRefID'] = $applicationRefID;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-profile-send-mail', $data);
    }

    public function studentSendLetter($studentId, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $fromMail = [Auth::user()->email => Auth::user()->email];

        $objStdentAgentNameArray = [];
        $templateType = [];

        $objCollegesMail = new Colleges;
        $arrFromMail = $objCollegesMail->getCollegeMail($collegeId);

        $templateType = Config::get('constants.pdfConstant');
        $objColleges = new Colleges;
        $arrColleges = $objColleges->collegeNameGet($collegeId);

        $reportId = 5; // 5 for application record

        $objReport = new Report;
        $arrReportName = $objReport->getReportListV2();

        $objReportCategory = new ReportCategory;
        $arrReportCategory = $objReportCategory->getReportCategory();
        unset($arrReportCategory['']);

        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailTemplate = $objRtoEmailTemplate->getEmailTemplateType($collegeId);

        $objRtoStudentCourse = new StudentCourse;
        $arrStudentCourse = $objRtoStudentCourse->getStudentAppliedCourseNameV3($studentId);
        $objStdentAgentName = $objRtoStudentCourse->getStudentAgentName($studentId);

        $objRtoapplicationRefID = new Students;
        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($studentId, $collegeId);

        $objStdentAgentNameArray[' '] = 'Select Agent Eamil';
        for ($i = 0; $i < count($objStdentAgentName); $i++) {
            if (! empty($objStdentAgentName[$i]['primary_email'])) {
                $objStdentAgentNameArray[$objStdentAgentName[$i]['primary_email']] = $objStdentAgentName[$i]['primary_email'];
            }
            if (! empty($objStdentAgentName[$i]['alertnet_email'])) {
                $objStdentAgentNameArray[$objStdentAgentName[$i]['alertnet_email']] = $objStdentAgentName[$i]['alertnet_email'];
            }
        }

        $arrRecipient = Config::get('constants.arrRecipient');

        if ($request->isMethod('post')) {
            $isAttachLetter = (isset($_POST['is_attach_letter']) && $_POST['is_attach_letter'] == '1') ? true : false;
            if (isset($_POST['dataSubmit'])) {
                $fileForValidate = $request->file();
                if ($request->file()) {
                    $isValidSize = true;
                    $isValidType = true;
                    foreach ($fileForValidate as $tmpFile) {
                        if ($isValidSize) {
                            $tmpSize = $tmpFile->getSize();
                            $tmpSize = number_format($tmpSize / 1048576, 2);
                            if ($tmpSize > 5) {
                                $isValidSize = false;
                            }
                        }
                        if ($isValidType) {
                            $arr = ['pdf', 'jpg', 'jpeg', 'gif', 'png'];
                            $tmpType = $tmpFile->guessClientExtension();
                            if (! in_array(strtolower($tmpType), $arr)) {
                                $isValidType = false;
                            }
                        }
                    }

                    if (! $isValidType || ! $isValidSize) {
                        $errorArr = [];
                        (! $isValidType) ? $errorArr[] = 'Please Select Valid File ( .png , .pdf , .jpg ,.jpeg ,.gif)' : '';
                        (! $isValidSize) ? $errorArr[] = 'The maximum file upload size limit is 5MB.' : '';

                        return redirect(route('student-send-letter', ['id' => $studentId]))
                            ->withErrors($errorArr)
                            ->withInput();
                    }
                }

                $validations = [
                    'email_subject' => 'required',
                    'email_content' => 'required',
                ];
                $validator = Validator::make($request->all(), $validations);
                if ($validator->fails()) {
                    return redirect(route('student-send-letter', ['id' => $studentId]))
                        ->withErrors($validator)
                        ->withInput();
                }

                $email_to = $request->email_to;
                $status = true;
                $objRtoStudentEmail = new StudentEmail;
                $objStudentEmail = $objRtoStudentEmail->saveStudentMail($studentId, $request, $email_to, $status);
                $arrFiles = [];
                if (count($request->file()) > 0) {
                    $arrFiles = $objStudentEmail;
                }
                if ($isAttachLetter) {
                    $arrFiles[] = $this->generateLetterPDF($request, true);
                }

                $mailData = [
                    'from' => env('MAIL_USERNAME'),
                    'fromName' => env('MAIL_NAME'),
                    'to' => $request->input('email_to'),
                    'page' => 'mail.student-offer-email',
                    'subject' => $request->input('email_subject'),
                    'cc' => (empty($request->input('email_cc'))) ? '' : $request->input('email_cc'),
                    'bcc' => '',
                    'attachFile' => $arrFiles,
                    'data' => ['content' => $request->input('email_content')],
                ];
                $mailStatus = (new SendMail)->sendSmtpMail($mailData);

                $request->session()->flash('session_success', 'Mail Sent Successfully.');

                return redirect(route('student-send-letter', ['id' => $studentId]));
            }
            /* Pdf Generate Code */

            if ($request->input('hiddenFlag') == 'pdf') {
                return $this->generateLetterPDF($request);
            }
        }

        $data['header'] = [
            'title' => 'Generate Letter/Send Letter',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Students' => route('search-students'),
                'Send Letter' => '',
            ]];
        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [];
        $data['pluginjs'] = ['ckeditor/ckeditor.js', 'jQuery/jquery.validate.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['studentprofile.js', 'studentProfileTask.js'];
        $data['funinit'] = ['studentProfile.initProfileSendLetter()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Students';

        $data['fromMail'] = $fromMail;
        $data['arrFromMail'] = $arrFromMail;
        $data['arrRecipient'] = $arrRecipient;
        $data['arrEmailTemplate'] = $arrEmailTemplate;
        $data['arrStudentCourse'] = $arrStudentCourse;
        $data['objStdentAgentName'] = $objStdentAgentName;
        $data['objStdentAgentNameArray'] = $objStdentAgentNameArray;
        $data['applicationRefID'] = $applicationRefID;
        $data['studentId'] = $studentId;
        $data['arrReportCategory'] = $arrReportCategory;
        $data['arrReportName'] = $arrReportName;
        $data['templateType'] = $templateType;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-send-letter', $data);
    }

    private function generateLetterPDF($request, $storage = false)
    {
        $studentId = $request->input('studentIdHidden');
        $collegeId = Auth::user()->college_id;

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $logoPath = $destinationPath['default'];

        $filePath1 = Config::get('constants.uploadFilePath.LetterSetting');
        $destinationLetterPath = Helpers::changeRootPath($filePath1);
        $watermarkPath = $destinationLetterPath['view'];

        $objCollegeDetails = (new CollegeDetails)->getCollegeDetails($collegeId);
        $arrStudentInfo = (new Students)->getStudentDetail($studentId);

        $letterSettingData = LetterSetting::find(1);
        if ($request->input('template_type') == 1) {
            if (! file_exists(public_path($watermarkPath.$letterSettingData->watermark))) {
                $request->session()->flash('session_error', 'Water Mark not found.');

                return redirect(route('student-send-letter', ['id' => $studentId]));
            }
        }

        $clgLogo = $logoPath.$arrStudentInfo[0]->college_logo;
        $arrState = Config::get('constants.arrState');

        $data = [
            'letterSettingData' => $letterSettingData,
            'watermarkPath' => $watermarkPath,
            'logoPath' => $logoPath,
            'arrStudentInfo' => $arrStudentInfo[0],
            'objCollegeDetails' => $objCollegeDetails[0],
            'clg_logo' => $clgLogo,
            'arrState' => $arrState,
            'latterHead' => $this->replaceContent($letterSettingData->header, $objCollegeDetails[0], $clgLogo, $arrState),
            'latterFooter' => $this->replaceContent($letterSettingData->footer, $objCollegeDetails[0], $clgLogo, $arrState),
            'letterBody' => $request->input('email_content'),
            'template_type' => $request->input('template_type'),
        ];

        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView('frontend.student.students-send-letter-pdf', $data);
        $pdf->output();
        $dom_pdf = $pdf->getDomPDF();
        $canvas = $dom_pdf->get_canvas();
        $canvas->page_text(500, 790, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);

        if ($storage) {
            $timestamp = date('YmdHis');
            $tempFilePath = Config::get('constants.uploadFilePath.TempMailAttachment');

            $tempDestinationPathDir = Helpers::changeRootPath($tempFilePath);
            if (! is_dir($tempDestinationPathDir['default'])) {
                mkdir($tempDestinationPathDir['default'], 0777, true);
            }

            $tempDestinationPath = Helpers::changeRootPath($tempFilePath, $studentId);
            if (! is_dir($tempDestinationPath['default'])) {
                mkdir($tempDestinationPath['default'], 0777, true);
            }
            $pdf->save($tempDestinationPath['default'].$timestamp.'-Letter.pdf');

            return $tempDestinationPath['default'].$timestamp.'-Letter.pdf';
        } else {
            return $pdf->download('Send_Letter'.'.pdf', $data);
        }
    }

    public function studentCourseInformation($studentId, Request $request)
    {
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = $sessionPermission['allow_to_update_student_course_info_(override)'];
        $sessionPermissionDataEdit = $sessionPermission['deny_staff_to_update_student_course_info'];

        $collegeId = Auth::user()->college_id;
        $objRtoStudentCourse = new StudentCourse;
        $arrStudentCourses = $objRtoStudentCourse->getStudentProfileCourses($studentId, $collegeId);
        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [''];
        $data['js'] = [''];
        $data['funinit'] = [''];
        $data['activateValue'] = 'Students';

        $data['arrStudentCourses'] = $arrStudentCourses;
        $data['studentId'] = $studentId;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['sessionPermissionDataEdit'] = $sessionPermissionDataEdit;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-course-information', $data);
    }

    public function replaceContent($content, $objCollegeDetails, $college_logo, $arrState)
    {
        if (! empty($objCollegeDetails)) {

            $dataArr = [
                '{college_name}' => empty($objCollegeDetails['college_name']) ? null :
                                    $objCollegeDetails['college_name'],
                '{college_legal_name}' => empty($objCollegeDetails['legal_name']) ? null :
                                    $objCollegeDetails['legal_name'],
                '{college_ABN}' => empty($objCollegeDetails['ABN']) ? null : $objCollegeDetails['college_name'],
                '{college_CRICOS_code}' => $objCollegeDetails['CRICOS_code'],
                '{college_RTO_code}' => $objCollegeDetails['RTO_code'],
                '{college_street_address}' => $objCollegeDetails['street_address'],
                '{college_street_suburb}' => $objCollegeDetails['street_suburb'],
                '{college_street_state}' => (array_key_exists($objCollegeDetails['street_state'], $arrState)) ?
                                            $arrState[$objCollegeDetails['street_state']] : '',
                '{college_street_postcode}' => $objCollegeDetails['street_postcode'],
                '{college_contact_phone}' => $objCollegeDetails['contact_phone'],
                '{college_contact_email}' => $objCollegeDetails['contact_email'],
                '{college_account_email}' => $objCollegeDetails['account_email'],
                '{college_logo}' => $college_logo,
                '{college_url}' => $objCollegeDetails['college_url'],

            ];

            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
            }

            return $content;
        } else {
            return false;
        }
    }

    public function studentSendSms($studentId, Request $request)
    {
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = (isset($sessionPermission['allow_to_send_sms'])) ? $sessionPermission['allow_to_send_sms'] : 'yes';

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);
        $objSMSTemplate = new SMSTemplate;
        $arrSmsTemplate = $objSMSTemplate->getAllSMSTemplate();
        unset($arrSmsTemplate['']);
        if ($request->isMethod('post')) {
            $validations = [
                'sms_text' => 'required',
                'mobile_no' => 'required',
            ];

            $validator = Validator::make($request->all(), $validations);
            if ($validator->fails()) {
                return redirect(route('student-send-sms', ['id' => $studentId]))
                    ->withErrors($validator);
            } else {
                if (empty($arrStudentDetail->current_mobile_phone)) {
                    $request->session()->flash('session_error', 'SMS can not be send without number.');
                } else {
                    $request->session()->flash('session_success', 'SMS Successfully Sent to Student');

                    return redirect(route('student-send-sms', ['id' => $studentId]))->withErrors($validator);
                }
            }
        }
        $data['header'] = [
            'title' => 'Send SMS to Student',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Student Search' => route('search-students'),
                'Send SMS to Student' => '',
            ]];
        $data['pagetitle'] = 'Send SMS';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js', 'bootstrap-wysihtml5/bootstrap3-wysihtml5.all.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['studentprofile.js', 'studentProfileTask.js'];
        $data['funinit'] = ['studentProfile.initProfileSendSms()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Students';

        $data['studentId'] = $studentId;
        $data['arrStudentDetail'] = $arrStudentDetail;
        $data['arrSmsTemplate'] = $arrSmsTemplate;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-send-sms', $data);
    }

    public function studentOfferChecklist(Request $request)
    {

        $studentId = $request->id;
        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');

        $objChecklist = new Checklist;
        $objChecklistData = $objChecklist->getCheckListWithType($perPage, $collegeId, $type = 'Offer');

        $objstudentOfferChecklist = new StudentOfferChecklist;
        $checklistDatas = $objstudentOfferChecklist->getStudentChecklistRecords($studentId);
        $newArray = [];

        foreach ($checklistDatas as $key => $checklistData) {
            $newArray[$checklistData['checklist_id']] = $checklistData;
        }

        if ($request->isMethod('post')) {
            if ($request->input('total_count') == 0) {
                $request->session()->flash('session_error', 'Student Offer Checklist Not Selected');

                return redirect(route('student-offer-checklist', ['id' => $studentId]));
            }
            $validations = [];
            $failedValidationsMsg = [];
            foreach ($request->input('mandatory') as $key => $value) {
                if ($request->input('mandatory.'.$key) == 'Yes') {
                    $request->input('comment'.$key);
                    $validations['comment'.$key] = 'required';
                    $failedValidationsMsg['comment'.$key.'.required'] = 'The comment field is required for '.$request->input('checklist_text_'.$key);
                }
            }
            $validator = Validator::make($request->all(), $validations, $failedValidationsMsg);
            if ($validator->fails()) {
                return redirect(route('student-offer-checklist', ['id' => $studentId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objRtoStudentOfferChecklist = new StudentOfferChecklist;
            $arrStudentChecklist = $objRtoStudentOfferChecklist->saveStudentOffersChecklist($objChecklistData, $studentId, $request);

            $request->session()->flash('session_success', 'Student Offer Checklist Update SuccessFully.');

            return redirect(route('offer-manage'));
        }
        $data['header'] = [
            'title' => 'Students Checklist',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Student Checklist' => '',
            ]];
        $data['pagetitle'] = 'Student Offer Checklist';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['studentprofile.js'];
        $data['funinit'] = ['studentProfile.initProfileSendSms()'];
        $data['activateValue'] = 'Offers';
        $data['objChecklistData'] = $objChecklistData;
        $data['checklistData'] = $newArray;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-offer-checklist', $data);
    }

    public function studentProfileChecklist(Request $request)
    {

        $studentId = $request->student_primary;
        $studentPrimary = $request->id;

        $perPage = Config::get('constants.pagination.perPage');
        $collegeId = Auth::user()->college_id;

        $objChecklist = new Checklist;
        $objChecklistData = $objChecklist->getCheckListWithType($perPage, $collegeId, $type = 'Student Course');

        $objstudentOfferChecklist = new StudentOfferChecklist;
        $checklistDatas = $objstudentOfferChecklist->getStudentChecklistRecords($studentId);
        $newArray = [];

        foreach ($checklistDatas as $key => $checklistData) {
            $newArray[$checklistData['checklist_id']] = $checklistData;
        }

        if ($request->isMethod('post')) {
            $validations = [];
            if ($request->input('mandatory')) {
                $objRtoStudentOfferChecklist = new StudentOfferChecklist;
                $arrStudentChecklist = $objRtoStudentOfferChecklist->saveStudentOffersChecklist($objChecklistData, $studentId, $request);

                $request->session()->flash('session_success', 'Student Profile Checklist Update SuccessFully.');

                return redirect(route('student-profile', ['id' => $studentId]));
            } else {
                return redirect(route('student-profile-checklist', ['id' => $studentPrimary, 'student_primary' => $studentId]))
                    ->withErrors('Something will be wrong.Please try again.')
                    ->withInput();
            }
        }
        $data['pagetitle'] = 'Student Offer Checklist';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['studentprofile.js'];
        $data['funinit'] = ['studentProfile.initProfileSendSms()'];
        $data['activateValue'] = 'Offers';
        $data['header'] = [
            'title' => 'Student Profile Checklist',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Student Profile' => route('student-profile', $studentId),
                'Student Profile Checklist' => '',
            ]];
        $data['objChecklistData'] = $objChecklistData;
        $data['checklistData'] = $newArray;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-profile-checklist', $data);
    }

    public function studentApprove(Request $request)
    {
        $studentId = $request->student_id;
        $courseId = $request->offer_id;

        $objStudents = new Students;
        $arrStaffList = $objStudents->approveDeapproveStaff($studentId);

        $getCurrentStatus = StudentCourse::where('id', $courseId)->select('offer_status')->get()->toArray();

        $objStudentCourse = new StudentCourse;
        $arrStaffList = $objStudentCourse->offerStatusChanged($courseId);

        $offeredStatus = 'Offered';
        $objStudentCommunicationLog = new StudentCommunicationLog;
        $saveLog = $objStudentCommunicationLog->saveApproveStatusLog($request, $studentId, $courseId, $getCurrentStatus[0]['offer_status'], $offeredStatus);

        if (galaxy_feature('galaxy_webhooks')) {
            $objStudentCourse = StudentCourses::find($courseId);
            \Webhooks\Facades\Webhook::dispatch(new \Webhooks\Events\Student\StudentOfferedEvent($objStudentCourse));
        }

        $request->session()->flash('session_success', 'Status Change Successfully.');

        return redirect(route('offer-manage'));
    }

    public function studentCOE(Request $request)
    {
        $studentId = $request->student_id;
        if (empty($studentId)) {
            $request->session()->flash('session_error', 'Student info not found. Please try again.');

            return redirect(route('offer-manage'));
        }

        $id = $request->id;
        $offerId = $request->offer_id;
        $userId = auth()->user()->id;
        $collegeId = auth()->user()->college_id;

        DB::beginTransaction();
        try {
            $objStudentCourse = new StudentCourse;
            $result = $objStudentCourse->updateCoeOfferManage($collegeId, $userId, $request, $studentId, $offerId, $id);
            if ($result) {
                $request->session()->flash('session_success', 'COE Detail Updated Successfully.');
                if ($this->isZohoConnected()) {
                    dispatch(new SyncStudentToZoho($studentId));
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $request->session()->flash('session_error', $e->getMessage());
        }

        if (isset($request->redirect)) {
            $this->generateStudentId($studentId, $id);

            return redirect(route('student-course-information', ['id' => $studentId]));
        }

        return redirect(route('offer-manage'));
    }

    public function studentCOE_OLD(Request $request)
    {

        $studentId = $request->student_id;

        if ($studentId == '') {
            $request->session()->flash('session_error', 'Student info not found. Please try again.');

            return redirect(route('offer-manage'));
        }

        $offerId = $request->offer_id;
        $id = $request->id;
        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $objStudentCourse = new StudentCourse;
        $result = $objStudentCourse->updateCoeOfferManage($collegeId, $userId, $request, $studentId, $offerId, $id);

        if ($result) {
            // TODO::GNG-3182 (Remove option for sync to xero from add COE)
            /*if (Xero::isConnected() && $request->is_sync == '1') {
                $student = Student::findOrFail($studentId);
                $student->asXeroContact();
            }*/
            $request->session()->flash('session_success', 'COE Detail Updated Successfully.');
        } else {
            $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
        }

        if (isset($request->redirect)) {
            $this->generateStudentId($studentId, $id);

            return redirect(route('student-course-information', ['id' => $studentId]));
        }

        return redirect(route('offer-manage'));
    }

    public function generateStudentId($studentId, $studentCourseId)
    {
        $generatedStudId = Students::select('id', 'generated_stud_id')->where('id', $studentId)->first()->toArray();
        if ($generatedStudId['generated_stud_id'] == '') {

            $collegeID = Auth::user()->college_id;

            $getGeneratedId = $this->_generateStudentNewID();

            $objStudent = Students::find($studentId);
            $objStudent->is_student = 1;
            $objStudent->generated_stud_id = $getGeneratedId;
            $objStudent->save();
            if (galaxy_feature('galaxy_webhooks')) {
                $objStudentCourse = StudentCourses::find($studentCourseId);
                \Webhooks\Facades\Webhook::dispatch(new \Webhooks\Events\Student\StudentEnrolledEvent($objStudentCourse));
            }
            $studIDFormate = StudentIdFormate::where('college_id', $collegeID)->get();
            $studIDFormate[0]->auto_increment = ($studIDFormate[0]->auto_increment + 1);
            $studIDFormate[0]->save();
        }

    }

    private function _generateStudentNewID()
    {

        $collegeID = Auth::user()->college_id;

        $studIDFormate = new StudentIdFormate;
        $studentIdFormate = $studIDFormate->getStudentIdFormate($collegeID);

        $p1 = $studentIdFormate[0]->position1;
        $p2 = $studentIdFormate[0]->position2;
        $p3 = $studentIdFormate[0]->position3;

        $alphabeat = $studentIdFormate[0]->alphabeat;
        $yeardigit = $studentIdFormate[0]->yeardigit;
        $auto_no = $studentIdFormate[0]->auto_increment;

        $str = '';
        if ($p1 == 'countrycode') {
            $str .= '061';
        } elseif ($p2 == 'countrycode') {
            $str .= '061';
        } elseif ($p3 == 'countrycode') {
            $str .= '061';
        }

        if ($p1 == 'alphabeat' && $alphabeat != '') {
            $str .= $alphabeat;
        } elseif ($p2 == 'alphabeat' && $alphabeat != '') {
            $str .= $alphabeat;
        } elseif ($p3 == 'alphabeat' && $alphabeat != '') {
            $str .= $alphabeat;
        }

        if ($p1 == 'yeardigit' && $yeardigit != '') {
            $str .= $yeardigit;
        } elseif ($p2 == 'yeardigit' && $yeardigit != '') {
            $str .= $yeardigit;
        } elseif ($p3 == 'yeardigit' && $yeardigit != '') {
            $str .= $yeardigit;
        }

        // $str .= $auto_no;
        $generateID = $str.$auto_no;

        return $generateID;

    }

    public function pendingOffer(Request $request)
    {
        $student_course_id = $request->student_id;

        $getCurrentStatus = StudentCourse::where('id', $student_course_id)->select('offer_status', 'student_id')->get()->toArray();

        $objStudentCourse = new StudentCourse;
        $arrStaffList = $objStudentCourse->offerStatusPending($student_course_id);

        $offeredStatus = 'Pending';
        $objStudentCommunicationLog = new StudentCommunicationLog;
        $saveLog = $objStudentCommunicationLog->saveApproveStatusLog($request, $getCurrentStatus[0]['student_id'], $student_course_id, $getCurrentStatus[0]['offer_status'], $offeredStatus);
        $request->session()->flash('session_success', 'Status Change Pending Successfully.');

        return redirect(route('offer-manage'));
    }

    public function reconsiderOffer(Request $request)
    {

        $student_course_id = $request->student_id;
        $getCurrentStatus = StudentCourse::where('id', $student_course_id)->select('offer_status', 'student_id')->get()->toArray();

        $objStudentCourse = new StudentCourse;
        $objStudentCourse->offerStatusReconsider($student_course_id);

        $offeredStatus = 'Reconsider';
        $objStudentCommunicationLog = new StudentCommunicationLog;
        $saveLog = $objStudentCommunicationLog->saveApproveStatusLog($request, $getCurrentStatus[0]['student_id'], $student_course_id, $getCurrentStatus[0]['offer_status'], $offeredStatus);

        $request->session()->flash('session_success', 'Status Change Reconsider Successfully.');

        return redirect(route('offer-manage'));
    }

    public function studentCommunicationDiary($student_id, Request $request)
    {

        $student_id = $request->id;
        $userId = $login_id = Auth::user()->id;
        $college_id = Auth::user()->college_id;
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionStaff = (isset($sessionPermission['activate/deactivate_student_comm_log'])) ? $sessionPermission['activate/deactivate_student_comm_log'] : 'yes';
        $sessionPermissionStaffModify = (isset($sessionPermission['modify_student_comm_log'])) ? $sessionPermission['modify_student_comm_log'] : 'yes';
        $perPage = Config::get('constants.pagination.perPage');

        $objStudentCourse = new StudentCourse;
        $arrCourseList = $objStudentCourse->getStudentAppliedCourseName($student_id);

        $objStudent = new Students;
        $arrStudent = $objStudent->getExistsReferenceId($student_id, $college_id);

        $logType = ['profile', 'Payment'];
        $objStudentCommunication = new StudentCommunicationLog;
        $arrStudCommList = $objStudentCommunication->getStudentCommunicationDiary($perPage, $student_id, $college_id, $logType);
        $typeId = '8'; // for Diary-Student Type List
        $statusId = '9'; // for Diary-Student Status List
        $objSetupSection = new SetupSection;
        $arrSectionTypeList = $objSetupSection->getSectionTypeList($college_id, $typeId);
        $arrSectionStatusList = $objSetupSection->getSectionTypeList($college_id, $statusId);
        $data['editpage'] = 'N';
        if ($request->isMethod('post')) {
            $objStudentCommunicationLog = new StudentCommunicationLog;
            $objStudentCommunicationLog->addStudentCommunicationLog($request, $college_id, $student_id, $login_id, $userId);

            $request->session()->flash('session_success', 'Student Communication Log Diary Save SuccessFully.');

            return redirect(route('student-communication-log', ['id' => $student_id]));
        }
        $data['header'] = [
            'title' => 'Student Communication Log/Diary',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Student Communication Log/Diary' => '',
            ]];
        $data['pagetitle'] = 'Student Offer Checklist';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['studentCommunicationDiary.js', 'studentProfileTask.js'];
        $data['funinit'] = ['studentCommunicationDiary.initCommunicationDiaryAdd()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Offers';
        $data['arrStudentCourse'] = $arrCourseList;
        $data['applicationRefID'] = $arrStudent;
        $data['diaryCategory'] = $arrSectionTypeList;
        $data['diaryStatus'] = $arrSectionStatusList;
        $data['studentId'] = $student_id;
        $data['arrStudCommList'] = $arrStudCommList;

        $data['sessionPermissionStaff'] = $sessionPermissionStaff;
        $data['sessionPermissionStaffModify'] = $sessionPermissionStaffModify;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-communication-diary', $data);

        $request->session()->flash('session_success', 'Status Change Reconsider Successfully.');

        return redirect(route('offer-manage'));
    }

    public function studenDiaryEdit($id, Request $request)
    {

        $userId = $login_id = Auth::user()->id;
        $college_id = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');

        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionStaff = $sessionPermission['activate/deactivate_student_comm_log'];
        $objStudentCommunicationLog = new StudentCommunicationLog;
        $arrEditCommunicationLog = $objStudentCommunicationLog->getSingleComunicationLog($id);
        $student_id = $arrEditCommunicationLog->student_id;
        $objStudentCourse = new StudentCourse;
        $arrCourseList = $objStudentCourse->getStudentAppliedCourseName($student_id);

        $objStudent = new Students;
        $arrStudent = $objStudent->getExistsReferenceId($student_id, $college_id);

        $logType = ['profile'];
        $objStudentCommunication = new StudentCommunicationLog;
        $arrStudCommList = $objStudentCommunication->getStudentCommunicationDiary($perPage, $student_id, $college_id, $logType);

        $typeId = '8'; // for Diary-Student Type List
        $statusId = '9'; // for Diary-Student Status List
        $objSetupSection = new SetupSection;
        $arrSectionTypeList = $objSetupSection->getSectionTypeList($college_id, $typeId);
        $arrSectionStatusList = $objSetupSection->getSectionTypeList($college_id, $statusId);

        if ($request->isMethod('post')) {
            $objStudentCommunicationLog->editCommunicationLogDiary($request, $id);

            $request->session()->flash('session_success', 'Student Communication Log Diary Update SuccessFully.');

            return redirect(route('student-communication-log', ['id' => $student_id]));
        }

        $data['pagetitle'] = 'Student Offer Checklist';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['studentCommunicationDiary.js'];
        $data['funinit'] = ['studentCommunicationDiary.initCommunicationDiaryAdd()'];
        $data['activateValue'] = 'Offers';
        $data['arrStudentCourse'] = $arrCourseList;
        $data['applicationRefID'] = $arrStudent;
        $data['diaryCategory'] = $arrSectionTypeList;
        $data['diaryStatus'] = $arrSectionStatusList;
        $data['studentId'] = $student_id;
        $data['arrStudCommList'] = $arrStudCommList;

        $data['editpage'] = 'Y';
        $data['arrEditCommunicationLog'] = $arrEditCommunicationLog;
        $data['sessionPermissionStaff'] = $sessionPermissionStaff;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-communication-diary', $data);
    }

    public function studenDiaryDelete($id, Request $request)
    {
        $result = StudentCommunicationLog::where('id', '=', $id)->delete();
        if ($result) {
            $data['status'] = 'success';
            $data['message'] = 'Deleted Successfully';
            $data['jscode'] = 'setTimeout( function(){ location.reload(true); },200);';
        } else {
            $data['status'] = 'error';
            $data['message'] = 'Student communication log not found.';
            $data['jscode'] = 'setTimeout( function(){ location.reload(true); },200);';
        }
        echo json_encode($data);
        exit();

    }

    public function studentCommunicationDiaryEdit($studentCommunicationId, $visiblity, $studentId, Request $request)
    {
        $objStudentCommunicationLog = new StudentCommunicationLog;
        $objStudentCommunicationLog->editStudentCommunicationLog($studentCommunicationId, $visiblity);

        $request->session()->flash('session_success', 'Student Communication Log Diary Edit SuccessFully.');

        return redirect(route('student-communication-log', ['id' => $studentId]));
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        $collegeId = Auth::user()->college_id;
        $arrRoleType = Config::get('constants.arrRoleType');

        switch ($action) {
            case 'getAttendanceOverview':
                $studentId = $request->input('data.studentId');
                $semestorId = $request->input('data.semestorId');
                $term = $request->input('data.term');
                $data = $this->_getAttendanceOverview($collegeId, $studentId, $semestorId, $term);

                return view('frontend.student.student-attendance-overview', $data);
                break;
            case 'GetUnitName':
                $course_id = $request->input('data.courseID');
                $subjectID = $request->input('data.subjectID');
                $studentId = $request->input('data.studentId');
                $isHigherEd = $request->input('data.isHigherEd');

                if ($isHigherEd) {
                    $arrSelectFinalOutcome = (new ResultGrade)->getGradeResultList($collegeId);
                } else {
                    $arrSelectFinalOutcome = Config::get('constants.arrSelectFinalOutcome');
                }

                $query = $this->_getUnitName($course_id, $subjectID, $studentId);
                $data['arrSelectFinalOutcome'] = $arrSelectFinalOutcome;
                $data['query'] = $query;
                echo json_encode($data);
                break;
            case 'getCourseDateRange':
                $course_id = $request->input('data.course_id');
                $student_id = $request->input('data.student_id');
                $this->_getCourseDateRange($course_id, $student_id);
                break;
            case 'getStudentPayment':
                $studentID = $request->input('data.studentID');
                $courseID = $request->input('data.courseID');
                $resultArr = $this->getStudentPaymentData($studentID, $courseID);
                echo json_encode($resultArr);
                break;
            case 'getStudentPaymentTab':
                $studentID = $request->input('data.studentID');
                $resultArr = $this->getStudentPaymentTabData($studentID);
                echo json_encode($resultArr);
                break;
            case 'searchClaimTrackData':
                $courseID = $request->input('data.courseID');
                $studentId = $request->input('data.studentId');
                $this->_searchTrackDetail($courseID, $studentId);
                break;
            case 'deleteClaimTrackData':
                $trackID = $request->input('data.id');
                $this->_getClaimTrackDelete($trackID);
                break;
            case 'getStatusHistory':
                $studentID = $request->input('data.studentID');
                $courseID = $request->input('data.courseID');
                $resultArr = $this->getStatusHistoryData($studentID, $courseID);
                echo json_encode($resultArr);
                break;
            case 'semesterGet':
                $course_id = $request->input('data.course_id');
                $this->_getSemester($course_id);
                break;
            case 'semesterGetByYear':
                $year = $request->input('data.year');
                $this->_getyear($year);
                break;
            case 'sendmailToNotice':
                $action_taken = $request->input('data.action_taken');
                $case_status = $request->input('data.case_status');
                $recorded_date = $request->input('data.recorded_date');
                $reoccurance = $request->input('data.reoccurance');
                $student_id = $request->input('data.student_id');
                $teacher_email = $request->input('data.teacher_email');
                $intervention_type = $request->input('data.intervention_type');
                $case_detail = $request->input('data.case_detail');
                $this->_sendMailToNotice($action_taken, $case_status, $recorded_date, $reoccurance, $teacher_email, $student_id, $intervention_type, $case_detail);
                break;
            case 'searchInterventionData':
                $filter_by = $request->input('data.filter_by');
                $search_string = $request->input('data.search_string');
                $outcome_date = $request->input('data.outcome_date');
                $case_status = $request->input('data.case_status');
                $due_date = $request->input('data.due_date');
                $intervention_type = $request->input('data.intervention_type');
                $semester = $request->input('data.semester');
                $course_id = $request->input('data.course_id');
                $this->_interventionListFilter($filter_by, $search_string, $request, $outcome_date, $case_status, $due_date, $intervention_type, $semester, $course_id);
                break;
            case 'showCreateUser':
                $objRole = new Roles;
                $studentRoleType = $objRole->getRoleId('Student');
                $studentId = $request->input('data.studentID');
                $objStudendProfile = new Students;
                $studentProfile = $objStudendProfile->getStudents($studentId);
                $data = [];
                $objSecurityQuestion = new SecurityQuestion;
                $objArrSecurityQuestion = $objSecurityQuestion->getSecurityQuestion();
                $data['arrSecurityQuestion'] = $objArrSecurityQuestion;
                $data['generated_stud_id'] = $studentProfile->generated_stud_id;
                $data['id'] = $studentId;
                $data['name'] = $studentProfile->first_name.' '.$studentProfile->family_name;
                $data['email'] = $studentProfile->email;
                $data['phone'] = $studentProfile->current_home_phone;
                $data['mobile'] = $studentProfile->current_mobile_phone;
                $data['role'] = $studentRoleType;

                return view('frontend.student.create-user', $data);
                break;
            case 'createUser':
                $formData = $request->input('data.form');
                $arrData = [];
                parse_str($formData, $arrData);
                $objUser = new Users;

                $arrUserData = $objUser->duplicateUsersEmailCheck($arrData['email']);

                if (count($arrUserData) == 0) {
                    if (strlen($arrData['password']) < 8 || strlen($arrData['password']) > 16) {
                        $return = ['status' => 'alert-danger', 'message' => 'The password must be between 8 and 16 digits.'];
                        echo json_encode($return);
                        break;
                    }
                    $userId = $objUser->addUserFromStudent($arrData);
                    $objUserRoleType = new UserRoleType;
                    $lastInsertId = $objUserRoleType->saveStudentRoleType($arrData['role'], $userId);
                    $return = ['status' => 'alert-success', 'message' => 'User Added successfully.'];
                } else {
                    $return = ['status' => 'alert-danger', 'message' => 'Email already exist.'];
                }
                echo json_encode($return);
                exit;
                break;
            case 'showReset':
                $studentId = $request->input('data.studentID');
                $objStudendProfile = new Students;
                $studentProfile = $objStudendProfile->getStudents($studentId);
                $data = [];
                $data['email'] = $studentProfile->email;

                return view('frontend.student.reset-password', $data);
                break;
            case 'resetStudentPassword':
                $formData = $request->input('data.form');
                $arrData = [];
                parse_str($formData, $arrData);

                if (strlen($arrData['password']) < 8 || strlen($arrData['password']) > 16) {
                    $studentData['type'] = 'alert-error';
                    $studentData['message'] = 'The password must be between 8 and 16 digits.';
                    echo json_encode($studentData);
                    break;
                }
                $objUser = new Users;
                $objUser->resetStudentPassword($arrData);
                $studentData['type'] = 'alert-success';
                $studentData['message'] = 'Student Password change successfully.';
                echo json_encode($studentData);
                break;
            case 'currentCourseAcademicSemester':
                $studentID = $request->input('data.studentID');
                $this->currentCourseAcademicSemester($studentID);
                break;
            case 'currentCourseAcademicTerm':
                $currentCourseSemester = $request->input('data.currentCourseSemester');
                $this->currentCourseAcademicTerm($currentCourseSemester);
                break;
            case 'currentCourseAcademicData':
                $studentId = $request->input('data.studentId');
                $semesterId = $request->input('data.semesterId');
                $termId = $request->input('data.termId');
                $sortField = $request->input('data.sortField');
                $sortBy = $request->input('data.sortBy');
                $this->currentCourseAcademicData($collegeId, $studentId, $semesterId, $termId, $sortField, $sortBy);
                break;
            case 'studentSearchById':
                $popupStudentId = $request->input('data.popupStudentId');
                $this->studentSearchById($popupStudentId);
                break;
            case 'getReportLetter':
                $reportId = $request->input('data.reportId');

                $recepient = 'Student';
                $objReportLetter = new ReportLetterSetup;
                $arrLetter = $objReportLetter->getLetterListByRecepient($collegeId, $reportId, $recepient);
                echo json_encode($arrLetter);
                exit;
            case 'getLetterListByCategory':
                $categoryId = $request->input('data.categoryId');
                $recepient = 'Student';
                $objReportLetter = new ReportLetterSetup;
                $arrLetter = $objReportLetter->getLetterListByCategory($collegeId, $categoryId, $recepient);
                echo json_encode($arrLetter);
                exit;
            case 'getLetterBodyDetail':
                $letterId = $request->input('data.letterId');
                $courseId = $request->input('data.courseId');
                $studentId = $request->input('data.studentId');

                $objReportLetter = new ReportLetterSetup;
                $content = $objReportLetter->getLetterDetail($collegeId, $letterId);

                $objSendMail = new SendMail;
                $replacedContent = $objSendMail->setLetterBodyContent($studentId, $courseId, $content);
                $arrParameterList = $objSendMail->getParameterList($replacedContent);
                $result = ['content' => $replacedContent, 'parameter' => $arrParameterList];
                echo json_encode($result);
                exit;
            case 'replaceParameterValue':
                $paramVal = $request->input('data.arr');
                $letterId = $request->input('data.letterId');
                $courseId = $request->input('data.courseId');
                $studentId = $request->input('data.studentId');

                $objReportLetter = new ReportLetterSetup;
                $content = $objReportLetter->getLetterDetail($collegeId, $letterId);

                $objSendMail = new SendMail;
                $replacedContent = $objSendMail->setLetterBodyContent($studentId, $courseId, $content);
                $finalContent = $objSendMail->assignParameterValue($replacedContent, $paramVal);
                $result = ['content' => $finalContent];
                echo json_encode($result);
                exit;
            case 'getEmailTemplate':
                $emailRecipient = $request->input('data.EmailTemplateType');
                $objRtoEmailTemplate = new EmailTemplate;
                $arrEmailTemplateList = $objRtoEmailTemplate->getEmailTemplate($emailRecipient, $collegeId);
                echo json_encode($arrEmailTemplateList);
                break;
            case 'getEmailContent':
                $courseId = $request->input('data.courseId');
                $studentId = $request->input('data.studentId');
                $emailContentId = $request->input('data.EmailContentId');

                $objRtoEmailTemplate = new EmailTemplate;
                $arrEmailTemplateInfo = $objRtoEmailTemplate->getEmailTemplateInfo($collegeId, $emailContentId);
                $content = (count($arrEmailTemplateInfo) > 0) ? $arrEmailTemplateInfo[0]->content : '';

                $objSendMail = new SendMail;
                $replacedContent = $objSendMail->setEmailBodyContent($studentId, $courseId, $content);

                $arrEmailTemplateInfo[0]->content = $replacedContent;
                echo json_encode($arrEmailTemplateInfo);
                break;
            case 'sendSms':
                $arrSMSFormData = $request->input('data.formData');
                $this->sendStudentSms($arrSMSFormData, $collegeId);
                break;
            case 'getStudentUploadedFileInfo':
                $primaryId = $request->input('data.primaryId');
                $resultArr = StudentUploadFile::find($primaryId);
                echo json_encode($resultArr);
                break;
            case 'getCourseList':
                $studentId = $request->input('data.studentId');
                $objStudentCourse = new StudentCourse;
                $studentCourses = $objStudentCourse->getStudentProfileCourses($studentId, $collegeId, $this->newLimit);
                $data['studentId'] = $studentId;
                $data['studentCourses'] = $studentCourses;
                $data['pagination'] = $studentCourses->links();

                return view('frontend.student.student-course-list', $data);
                break;
            case 'getAcademicSummary':
                $studentID = $request->input('data.studentID');
                $courseID = $request->input('data.courseID');
                $resultArr = $this->academicSummaryData($studentID, $courseID);
                echo json_encode($resultArr);
                break;
            case 'getCourseAcademicSummary':
                $studentId = $request->input('data.studentId');
                $resultArr = [
                    'title' => 'Current Course Academic Summary',
                    'body' => view('frontend.student.popup-course-academic-summary')->render(),
                ];
                echo json_encode($resultArr);
                break;
            case 'getStudentFullInfoList':
                $studentRoleType = array_search('Student', $arrRoleType);
                $objRtoStudentsLists = new Students;
                $resultArr = $objRtoStudentsLists->getStudentsList($collegeId, $studentRoleType, $request);
                $resultArr = $this->convertToUTF_1($resultArr);
                echo json_encode($resultArr);
                break;
            case 'exportStudentList':
                echo $studentRoleType = array_search('Student', $arrRoleType);
                echo $paramVal = $request->status;
                print_r($request->input('data.fild'));
                exit;
                $resultArr = $this->exportStudentList($request);
                echo json_encode($resultArr);
                break;
            case 'activityLoadMore':
                $studentId = $request->input('data.studentId');
                $lastId = $request->input('data.lastId');
                $loadMoreCommunicationLog = new StudentCommunicationLog;
                $loadCommunicationLog = $loadMoreCommunicationLog->loadMoreCommunicationLo($studentId, $lastId);
                echo json_encode($loadCommunicationLog);
                exit();
                break;
        }exit;
    }

    public function downloadUnitAcademicSummaryPDF($studentCourseID)
    {
        $collegeId = Auth::user()->college_id;
        $objStudentCourse = new StudentCourse;
        $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studentCourseID);

        $dataArr = [];
        if ($studentCourseInfo->count() > 0) {
            $studentCourseInfo = $studentCourseInfo->toArray();

            $studentId = $studentCourseInfo[0]['studentId'];
            $courseId = $studentCourseInfo[0]['courseId'];
            $objCourseSubject = new CourseSubject;
            $arrCourseSubjectId = $objCourseSubject->getCourseSubjectId($courseId);
            $postData['courseId'] = $courseId;

            $objCourseUnit = new UnitModule;
            $studentCourseInfo[0]['enrollUnitList'] = $objCourseUnit->getCourseUnitSummary($collegeId, $arrCourseSubjectId, $perPage = '', $studentId);

            $studentCourseInfo[0]['issueDate'] = date('d-m-Y');

            $dataArr = $studentCourseInfo[0];
        }
        $objRtoCollegeDetails = new CollegeDetails;
        $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId)[0];
        $data['objCollegeDetails'] = $objCollegeDetails;
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data['college_signature'] = str_replace('\\', '/', $destinationPath['default']).$objCollegeDetails->college_signature;

        $data['studentCourseInfo'] = $dataArr;

        $pdf = App::make('dompdf.wrapper');

        $pdf->loadView('frontend.student.unit-academic-summary-pdf', $data);

        return $pdf->download('unit-academic-summary.pdf');

    }

    public function studentSearchById($popupStudentId)
    {
        $collegeId = Auth::user()->college_id;
        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudentId($collegeId, $popupStudentId);
        echo json_encode($arrStudentDetail);
    }

    public function _sendMailToNotice($action_taken, $case_status, $recorded_date, $reoccurance, $teacher_email, $student_id, $intervention_type, $case_detail)
    {
        $collegeId = Auth::user()->college_id;
        $inter_typs = '';
        foreach ($intervention_type as $val) {
            $inter_typs .= $val.', ';
        }
        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($student_id);

        $dataArray['recorded_date'] = $recorded_date;
        $dataArray['user_name'] = Auth::user()->name;
        $dataArray['intervention_type'] = $inter_typs;
        $dataArray['case_detail'] = $case_detail;
        $dataArray['action_taken'] = $action_taken;
        $dataArray['reoccurance'] = $reoccurance;
        $dataArray['case_status'] = ($case_status == 1) ? 'Case Open' : 'Close Open';
        $dataArray['arrStudentDetail'] = $arrStudentDetail;

        $mailData = [
            'from' => env('MAIL_USERNAME'),
            'to' => trim($teacher_email),
            'fromName' => $arrStudentDetail->name_title.' '.$arrStudentDetail->first_name.' '.$arrStudentDetail->family_name,
            'page' => 'mail.intervantion-email',
            'subject' => 'Student Intervention',
            'attachFile' => [],
            'data' => ['content' => $dataArray],
        ];

        $sendMail = new SendMail;
        $arrCountry = $sendMail->sendSmtpMail($mailData);
        $studentData['type'] = 'alert-success';
        $studentData['message'] = 'Student Mail Send Successfully .';
        echo json_encode($studentData);
        exit;
    }

    public function currentCourseAcademicData_OLD($semesterId, $termId, $studentId)
    {

        $collegeId = Auth::user()->college_id;
        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrolment = $objStudentSubjectEnrolment->currentCourseAcademicData($collegeId, $semesterId, $termId, $studentId);
        echo json_encode($arrStudentSubjectEnrolment);
        exit;
    }

    public function currentCourseAcademicData($collegeId, $studentId, $semesterId, $termId, $sortField, $sortBy)
    {
        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrolment = $objStudentSubjectEnrolment->currentCourseAcademicDataWithSort($collegeId, $studentId, $semesterId, $termId, $sortField, $sortBy);
        echo json_encode($arrStudentSubjectEnrolment);
        exit;
    }

    public function currentCourseAcademicTerm($termId)
    {
        $collegeId = Auth::user()->college_id;
        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrolment = $objStudentSubjectEnrolment->currentCourseAcademicTerm($collegeId, $termId);
        echo json_encode($arrStudentSubjectEnrolment);
        exit;
    }

    public function currentCourseAcademicSemester($student_id)
    {
        $collegeId = Auth::user()->college_id;
        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrolment = $objStudentSubjectEnrolment->currentCourseAcademicSemester($collegeId, $student_id);
        echo json_encode($arrStudentSubjectEnrolment);
        exit;
    }

    public function _searchTrackDetail($courseID, $student_id)
    {
        $collegeId = Auth::user()->college_id;
        $login_id = Auth::user()->id;
        $perPage = Config::get('constants.pagination.perPage');
        $arrState = Config::get('constants.arrState');
        $arrClaimStageStatus = Config::get('constants.arrClaimStageStatus');
        $arrClaimStageYear = Config::get('constants.arrClaimStageYear');
        $objStudentCourse = new StudentCourse;
        $arrCourseList = $objStudentCourse->getStudentAppliedCourseName($student_id);

        $objStudent = new Students;
        $arrStudent = $objStudent->getExistsReferenceId($student_id, $collegeId);

        $objClaimTrack = new StudentClaimTracking;
        $arrClaimTrackList = $objClaimTrack->getClaimTrackEditData($courseID, $perPage, $student_id);

        $data['pagetitle'] = 'Student Claim Tracking';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['teacher.js'];
        $data['funinit'] = ['Teacher.initViewTeacher()'];
        $data['activateValue'] = 'Teacher';
        $data['arrStudentCourse'] = $arrCourseList;
        $data['applicationRefID'] = $arrStudent;
        $data['studentId'] = $student_id;
        $data['arrClaimTrackList'] = $arrClaimTrackList;
        $data['arrState'] = $arrState;
        $data['arrClaimStageStatus'] = $arrClaimStageStatus;
        $data['arrClaimStageYear'] = $arrClaimStageYear;

        $resultTable = view('frontend.student.search-claim-track', $data)->render();
        echo json_encode([$resultTable, count($arrClaimTrackList)]);
        exit;
    }

    public function _getClaimTrackDelete($courseId)
    {
        $objClaimTrack = new StudentClaimTracking;
        $objClaimTrack->getClaimTrackDelete($courseId);
        $result['status'] = 'success';
        echo json_encode([$result]);
        exit;
    }

    /* Start Student Course Deferral Module */

    public function addStCourseDeferral($studentId, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $arrTypeData = Config::get('constants.arrCourseDeferType');

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseSingleStudentArr($collegeId, $studentId);

        $objDeferCourse = new StudentDeferCourse;
        $arrDeferCourseStudent = $objDeferCourse->getDeferCourseStudent($collegeId, $studentId, $perPage);

        $sectionTypeId = '1'; // for Defer/Holiday/Suspension
        $objSetupSection = new SetupSection;
        $arrReasonList = $objSetupSection->getSectionTypeList($collegeId, $sectionTypeId);
        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'course_id' => 'required',
                'type' => 'required',
                'reason' => 'required',
                'request_date' => 'required',
                'start_date' => 'required',
                'from_date' => 'required',
                'to_date' => 'required',
                'comments' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('student-course-deferral', ['id' => $studentId]))
                    ->withErrors($validator)
                    ->withInput();
            }
            $objDeferCourse->saveStudentDeferCourse($collegeId, $request);

            $request->session()->flash('session_success', 'Course Deferral Add Successfully.');

            return redirect(route('student-course-deferral', ['id' => $studentId]));
        }
        $data['header'] = [
            'title' => 'Course Defferal, Holiday or Suspension ',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Add Defer Course ' => '',
            ]];
        $data['pagetitle'] = 'Add Course Deferral';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['courseDeferral.js', 'studentProfileTask.js'];
        $data['funinit'] = ['courseDeferral.initInfo()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Students';

        $data['arrDeferCourseStudent'] = $arrDeferCourseStudent;
        $data['studentDetail'] = $arrStudentDetail;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrReasonData'] = $arrReasonList;
        $data['arrTypeData'] = $arrTypeData;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-course-deferral', $data);
    }

    public function editStCourseDeferral($studentId, $courseDeferId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $arrReasonData = Config::get('constants.arrCourseDeferReason');
        $arrTypeData = Config::get('constants.arrCourseDeferType');

        $objDeferCourse = new StudentDeferCourse;
        $arrDeferCourseStudent = $objDeferCourse->getDeferCourseStudent($collegeId, $studentId, $perPage);
        $arrDeferCourseInfo = $objDeferCourse->getDeferCourse($collegeId, $studentId, $courseDeferId);

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseSingleStudentArr($collegeId, $studentId);

        $sectionTypeId = '1'; // for Defer/Holiday/Suspension
        $objSetupSection = new SetupSection;
        $arrReasonList = $objSetupSection->getSectionTypeList($collegeId, $sectionTypeId);

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'course_id' => 'required',
                'type' => 'required',
                'reason' => 'required',
                'request_date' => 'required',
                'start_date' => 'required',
                'from_date' => 'required',
                'to_date' => 'required',
                'comments' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('edit-student-course-deferral', ['student_id' => $studentId, 'id' => $courseDeferId]))
                    ->withErrors($validator)
                    ->withInput();
            }
            $objDeferCourse->editStudentDeferCourse($courseDeferId, $request);

            $request->session()->flash('session_success', 'Course Deferral Update Successfully.');

            return redirect(route('edit-student-course-deferral', ['student_id' => $studentId, 'id' => $courseDeferId]));
        }

        $data['pagetitle'] = 'Edit Course Deferral';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['courseDeferral.js'];
        $data['funinit'] = ['courseDeferral.initInfo()'];
        $data['activateValue'] = 'Students';
        $data['header'] = [
            'title' => 'Edit Defer Course Detail',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Defer Course' => route('student-course-deferral', $studentId),
                'Edit Defer Course Detail' => '',
            ]];

        $data['arrDeferCourseStudent'] = $arrDeferCourseStudent;
        $data['arrDeferCourseInfo'] = $arrDeferCourseInfo[0];
        $data['studentDetail'] = $arrStudentDetail;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrReasonData'] = $arrReasonData + $arrReasonList;
        $data['arrTypeData'] = $arrTypeData;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        if ($arrDeferCourseInfo[0]->college_id == $collegeId) {
            return view('frontend.student.student-course-deferral', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function _getCourseDateRange($course_id, $student_id)
    {

        $objStudentCourse = new StudentCourse;
        $courseData = $objStudentCourse->getCourseDateRange($course_id, $student_id);
        if ($courseData->count() > 0 || isset($courseData)) {
            if (! empty($courseData[0]->start_date) && (! empty($courseData[0]->finish_date))) {
                $dateRange = [
                    'dateRange' => date('d/m/Y', strtotime($courseData[0]->start_date)).' - '.date('d/m/Y', strtotime($courseData[0]->finish_date)),
                ];
            }
        } else {
            $dateRange = ['dateRange' => ''];
        }

        echo json_encode($dateRange);
        exit;
    }

    public function deleteCourseDeferDetail($studentId, $courseDeferId, Request $request)
    {

        $objDeferCourse = new StudentDeferCourse;
        $objDeferCourse->deleteCourseDeferDetail($courseDeferId);

        $request->session()->flash('session_success', 'Course Deferral Delete Successfully.');

        return redirect(route('student-course-deferral', ['student_id' => $studentId]));
    }

    public function pdfCourseDeferDetail($studentId, $courseDeferId)
    {

        if (($studentId > 0) && ($courseDeferId > 0)) {
            $collegeId = Auth::user()->college_id;
            $objDeferCourse = new StudentDeferCourse;
            $arrDeferCourseInfo = $objDeferCourse->getDeferCoursePDF($collegeId, $studentId, $courseDeferId);

            if (isset($arrDeferCourseInfo)) {
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $data['logoPath'] = $destinationPath['default'];
                $data['arrDeferCourseInfo'] = $arrDeferCourseInfo[0];
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('frontend.student.course-deferral-pdf-view', $data);
            }
        }

        return $pdf->download('invoice.pdf');
    }

    /* End Student Course Deferral Module */

    /* Start Student Intervention Module */

    public function addStudentIntervention($studentId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');

        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = (isset($sessionPermission['allow_staff_to_delete_record_in_student_intervention_register'])) ? $sessionPermission['allow_staff_to_delete_record_in_student_intervention_register'] : 'yes';
        $objIntervention = new StudentIntervention;
        $arrStudentInterventionList = $objIntervention->getStudentIntervention($collegeId, $perPage);

        $objInterventionCat = new InterventionStrategy;
        $arrStudentInterventionSubCategory = $objInterventionCat->getStudentInterventionSubCategory($collegeId);

        $objIntervationType = new InterventionStrategyType;
        $arrIntervationTypeList = $objIntervationType->getInterventionTypeListV2();
        $objCourses = new Courses;
        $arrCourses = $objCourses->getFullCoursesIntervention($collegeId);
        $arrCaseType = Config::get('constants.arrCaseType');
        $arrInterventionType = Config::get('constants.arrInterventionType');

        $objTeacher = new Teacher;
        $arrTeacher = $objTeacher->getTeacherNameList($collegeId);
        $objSemester = new Semester;

        $arrSemesterYear = $objSemester->_getYearByCollageId($collegeId);

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);
        $interventionId = null;
        $objStrategy = new StudentInterventionStrategy;
        $arrInterventionCategoty = $objStrategy->getInterventionSubCategoty($collegeId, $studentId, $interventionId);

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseSingleStudentArr($collegeId, $studentId);

        if ($request->isMethod('post')) {
            $validations = ['student_id' => 'required',
                'intervention_for' => 'required',
                'course_id' => 'required',
                'semester' => 'required',
                'case_detail' => 'required',
                'action_taken' => 'required',
                'reoccurance' => 'required',
                'case_status' => 'required',
                'due_date' => 'required',
                'outcome_date' => 'required',
                'teacher_id' => 'required'];
            if (empty($request->input('hiddenInterType'))) {
                $validations['intervention_type'] = 'required';
                $checkValidations = [
                    'intervention_type.required' => 'Please select at least One Intervention Type',
                ];
                $validator = Validator::make($request->all(), $validations, $checkValidations);
            }
            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('student-intervention', ['id' => $studentId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objSemester = new Semester;
            $arrSemester = $objSemester->_getYear($request->input('semester'));
            $objStrategy = new StudentInterventionStrategy;
            $resultIntervention = $objIntervention->saveStudentIntervention($collegeId, $request, $arrSemester[0]['year']);

            if (isset($resultIntervention) && $resultIntervention['success']) {
                $objStrategy = new StudentInterventionStrategy;
                $objStrategy->saveInterventionStrategy($resultIntervention['last_id'], $request);
            }

            $request->session()->flash('session_success', 'Student Intervention Add Successfully.');

            return redirect(route('student-intervention', ['id' => $studentId]));
        }

        $data['pagetitle'] = 'Add Student Intervention';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['intervention.js', 'studentProfileTask.js'];
        $data['funinit'] = ['intervention.initInfo()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Students';

        $data['arrStudentInterventionList'] = $arrStudentInterventionList;
        $data['arrStudentInterventionSubCategory'] = $arrStudentInterventionSubCategory;
        $data['arrInterventionType'] = $arrInterventionType;
        $data['arrIntervationTypeList'] = $arrIntervationTypeList;
        $data['arrInterventionCategoty'] = $arrInterventionCategoty;
        $data['studentDetail'] = $arrStudentDetail;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrCaseType'] = $arrCaseType;
        $data['arrTeacher'] = $arrTeacher;
        $data['studentId'] = $studentId;
        $data['arrSemesterYear'] = $arrSemesterYear;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['arrCourses'] = $arrCourses;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-intervention', $data);
    }

    public function editStudentIntervention($studentId, $interventionId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');

        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = $sessionPermission['allow_staff_to_delete_record_in_student_intervention_register'];

        $objIntervationType = new InterventionStrategyType;
        $arrIntervationTypeList = $objIntervationType->getInterventionTypeListV2();

        $objInterventionCat = new InterventionStrategy;
        $arrStudentInterventionSubCategory = $objInterventionCat->getStudentInterventionSubCategory($collegeId);

        $objIntervention = new StudentIntervention;
        $arrStudentInterventionList = $objIntervention->getStudentIntervention($collegeId, $perPage);
        $arrInterventionInfo = $objIntervention->getInterventionDetail($collegeId, $studentId, $interventionId);

        if (! empty($arrInterventionInfo[0]['file_name'])) {
            $filePath = Config::get('constants.uploadFilePath.Intervention');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);

            $arrInterventionInfo[0]['file_path'] = asset($destinationPath['view'].'/'.$arrInterventionInfo[0]['file_name']);
        }

        $objStrategy = new StudentInterventionStrategy;
        $arrInterventionCategoty = $objStrategy->getInterventionSubCategoty($collegeId, $studentId, $interventionId);

        $arrCaseType = Config::get('constants.arrCaseType');
        $arrInterventionType = Config::get('constants.arrInterventionType');

        $objTeacher = new Teacher;
        $arrTeacher = $objTeacher->getTeacherNameList($collegeId);

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseSingleStudentArr($collegeId, $studentId);
        $objCourses = new Courses;
        $arrCourses = $objCourses->getFullCoursesIntervention($collegeId);
        $objSemester = new Semester;

        $arrSemesterYear = $objSemester->_getYearByCollageId($collegeId);

        if ($request->isMethod('post')) {
            $validations = ['student_id' => 'required',
                'intervention_for' => 'required',
                'semester' => 'required',
                'action_taken' => 'required',
                'reoccurance' => 'required',
                'case_status' => 'required',
                'due_date' => 'required',
                'outcome_date' => 'required',
                'teacher_id' => 'required'];

            if (empty($request->input('hiddenInterType'))) {
                $validations['intervention_type'] = 'required';
                $checkValidations = [
                    'intervention_type.required' => 'Please select at least One Intervention Type',
                ];
                $validator = Validator::make($request->all(), $validations, $checkValidations);
            }
            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('edit-student-intervention', ['student_id' => $studentId, 'id' => $interventionId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objIntervention->editStudentIntervention($interventionId, $request);

            $objStrategy = new StudentInterventionStrategy;
            $objStrategy->editInterventionStrategy($interventionId, $request);
            $request->session()->flash('session_success', 'Student Intervention Update Successfully.');

            return redirect(route('edit-student-intervention', ['student_id' => $studentId, 'id' => $interventionId]));
        }

        $data['header'] = [
            'title' => 'Intervention Register - Student',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Edit Student Intervention' => '',
            ]];
        $data['pagetitle'] = 'Edit Student Intervention';
        $data['plugincss'] = [''];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['intervention.js'];
        $data['funinit'] = ['intervention.initInfo()'];
        $data['activateValue'] = 'Students';

        $data['arrStudentInterventionList'] = $arrStudentInterventionList;
        $data['arrStudentInterventionSubCategory'] = $arrStudentInterventionSubCategory;
        $data['arrIntervationTypeList'] = $arrIntervationTypeList;
        $data['arrInterventionCategoty'] = $arrInterventionCategoty;
        $data['arrInterventionInfo'] = $arrInterventionInfo[0];
        $data['arrInterventionType'] = $arrInterventionType;
        $data['studentDetail'] = $arrStudentDetail;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrCaseType'] = $arrCaseType;
        $data['arrTeacher'] = $arrTeacher;
        $data['studentId'] = $studentId;
        $data['arrSemesterYear'] = $arrSemesterYear;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['arrCourses'] = $arrCourses;
        $data['mainmenu'] = 'clients';
        if ($arrInterventionInfo[0]->college_id == $collegeId) {
            return view('frontend.student.student-intervention', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function studentInterventionCommunicationLog($studentId, $interventionId, Request $request)
    {

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objStudentCommunicationLog = new StudentCommunicationLog;
        $result = $objStudentCommunicationLog->interventionCommunicationAdd($studentId, $interventionId, $arrStudentDetail['email']);

        if ($result) {
            $request->session()->flash('session_success', 'Student Intervention Communication Log Add Successfully.');
        } else {
            $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');
        }

        return redirect(route('student-intervention', ['student_id' => $studentId]));
    }

    public function studentComplianceInterventionCommunicationLog($studentId, $interventionId, Request $request)
    {

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objStudentCommunicationLog = new StudentCommunicationLog;
        $result = $objStudentCommunicationLog->interventionCommunicationAdd($studentId, $interventionId, $arrStudentDetail['email']);

        if ($result) {
            $request->session()->flash('session_success', 'Email is sent successfully.');
        } else {
            $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');
        }

        return redirect(route('compliance-intervention'));
    }

    public function deleteStudentIntervention($studentId, $interventionId, Request $request)
    {

        $objIntervention = new StudentIntervention;
        $objIntervention->deleteStudentIntervention($interventionId);

        $request->session()->flash('session_success', 'Student Intervention Delete Successfully.');

        return redirect(route('student-intervention', ['student_id' => $studentId]));
    }

    public function _interventionListFilter($filter_by, $search_string, $request, $outcome_date, $case_status, $due_date, $intervention_type, $semester, $course_id)
    {

        $collegeId = Auth::user()->college_id;
        $objIntervention = new StudentIntervention;
        $arrStudentInterventionList = $objIntervention->getStudentInterventionFilter($collegeId, $filter_by, $search_string, $outcome_date, $case_status, $due_date, $intervention_type, $semester, $course_id);
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = $sessionPermission['allow_staff_to_delete_record_in_student_intervention_register'];

        $data['arrInterventionFilter'] = $arrStudentInterventionList;
        $data['arrCaseType'] = Config::get('constants.arrCaseType');
        $data['arrInterventionType'] = Config::get('constants.arrInterventionType');
        $data['sessionPermissionData'] = $sessionPermissionData;
        $resultList = view('frontend.student.search-student-intervention-list', $data)->render();

        $result = ['filterData' => $resultList, 'count' => count($arrStudentInterventionList)];
        echo json_encode($result);
        exit;
    }

    public function _getSemester($courseId)
    {

        $objCourseCalender = new CourseCalendar;
        $calendarType = $objCourseCalender->_getCalenderData($courseId);
        $courseTypeId = $objCourseCalender->getCourseTypeId($courseId);

        $objSemester = new Semester;
        $arrSemesterList = $objSemester->_getSemesterData($courseTypeId, $calendarType);

        echo json_encode($arrSemesterList);
        exit;
    }

    public function _getyear($year)
    {
        $collegeId = Auth::user()->college_id;
        $objSemester = new Semester;
        $arrSemester = $objSemester->getSemesterByInterventionSemid($collegeId, $year);
        echo json_encode($arrSemester);
        exit;
    }

    /* End Student Intervention Module */

    public function uploadStudentProfilePicture($studentId, Request $request)
    {
        $fileForValidate = $request->file();
        $img = 'icon/profile.png';
        if ($request->file()) {
            $isValidSize = true;
            $isValidType = true;
            foreach ($fileForValidate['images'] as $tmpFile) {
                if ($isValidSize) {
                    $tmpSize = $tmpFile->getSize();
                    $tmpSize = number_format($tmpSize / 1048576, 2);
                    if ($tmpSize > 5) {
                        $isValidSize = false;
                    }
                }
                if ($isValidType) {
                    $arr = ['jpg', 'jpeg', 'png', 'JPG', 'JPEG', 'PNG'];
                    $tmpType = $tmpFile->guessClientExtension();

                    if (! in_array(strtolower($tmpType), $arr)) {
                        $isValidType = false;
                    }
                }
            }

            if (! $isValidType || ! $isValidSize) {
                $status = 'error';
                $msg = (! $isValidType) ? 'Please Select Valid File ( .jpg ,.jpeg )' : '';
                $msg .= (! $isValidSize) ? 'The maximum file upload size limit is 5MB.' : '';
            } else {
                $objStudendProfile = new Students;
                $result = $objStudendProfile->saveStudentProfilePicture($studentId, $request);
                if ($result) {
                    $studentProfile = $objStudendProfile->getStudentDetail($studentId)[0];
                    $filePath = Config::get('constants.uploadFilePath.StudentPics');
                    $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                    $profile_pic = $destinationPath['default'].$studentProfile->profile_picture;
                    if (file_exists($profile_pic) && ! empty($studentProfile->profile_picture)) {
                        $img = $destinationPath['view'].$studentProfile->profile_picture;
                    }
                }

                $status = 'success';
                $msg = 'Student Profile Picture Upload Successfully.';
            }
        }

        if (empty($request->file())) {
            $request->session()->flash('session_error', 'Please Upload valid Image for Student Profile Picture.');
            $status = 'error';
            $msg = 'Please Upload valid Image for Student Profile Picture.';
        }
        $request->session()->flash('session_success', $msg);

        return redirect()->back();
    }

    public function uploadStudentProfilePictureOLD($studentId, Request $request)
    {
        $fileForValidate = $request->file();

        if ($request->file()) {
            $isValidSize = true;
            $isValidType = true;
            foreach ($fileForValidate['images'] as $tmpFile) {
                if ($isValidSize) {
                    $tmpSize = $tmpFile->getSize();
                    $tmpSize = number_format($tmpSize / 1048576, 2);
                    if ($tmpSize > 5) {
                        $isValidSize = false;
                    }
                }
                if ($isValidType) {
                    $arr = ['jpg', 'jpeg'];
                    $tmpType = $tmpFile->guessClientExtension();

                    if (! in_array(strtolower($tmpType), $arr)) {
                        $isValidType = false;
                    }
                }
            }

            if (! $isValidType || ! $isValidSize) {
                $errorArr = [];
                (! $isValidType) ? $request->session()->flash('session_error', 'Please Select Valid File ( .jpg ,.jpeg )') : '';
                (! $isValidSize) ? $request->session()->flash('session_error', 'The maximum file upload size limit is 5MB.') : '';
            } else {
                $objStudendProfile = new Students;
                $result = $objStudendProfile->saveStudentProfilePicture($studentId, $request);
                $request->session()->flash('session_success', 'Student Profile Picture Upload Successfully.');
            }
        }

        if (empty($request->file())) {
            $request->session()->flash('session_error', 'Please Upload valid Image for Student Profile Picture');
        }

        return redirect(route('student-profile', ['student_id' => $studentId]));
    }

    public function pdfStudentCard($studentId)
    {
        if (($studentId > 0)) {
            $objStudent = new Students;
            $arrStudentInfo = $objStudent->getStudentDetail($studentId);

            if (isset($arrStudentInfo)) {
                $profilePicPath = Config::get('constants.displayProfilePicture');
                $filePath = Config::get('constants.uploadFilePath.StudentPics');
                $profilePicDestinationPath = Helpers::changeRootPath($filePath, $studentId);
                $profilePicPath = $profilePicDestinationPath['default'];
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $logoPath = $destinationPath['default'];

                $data['clg_logo'] = $logoPath.$arrStudentInfo[0]->college_logo;
                $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('frontend.student.student-card-pdf', $data);
            }
        }

        return $pdf->download('student-card.pdf');
    }

    /* Start Student Exit Interview Module */

    public function addStudentInterview($studentId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $exitTypeArr = Config::get('constants.exitTypeArr');
        $newCollage = Config::get('constants.newCollage');

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseSingleStudentArr($collegeId, $studentId);

        $objInterview = new StudentInterview;
        $arrStudentInterviewList = $objInterview->getStudentInterviewArr($collegeId, $perPage);

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), ['exit_type' => 'required',
                'reason' => 'required',
                'comment' => 'required', ]);

            if ($validator->fails()) {
                return redirect(route('student-interview', ['id' => $studentId]))
                    ->withErrors($validator)
                    ->withInput();
            }
            $objInterview->saveStudentInterviewInfo($collegeId, $request);

            $request->session()->flash('session_success', 'Student Interview Details Add Successfully.');

            return redirect(route('student-interview', ['id' => $studentId]));
        }
        $data['header'] = [
            'title' => ' Record Student Exit Interview Details',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Add Student Exit Interview' => '',
            ]];
        $data['pagetitle'] = 'Add Student Interview';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['interview.js', 'studentProfileTask.js'];
        $data['funinit'] = ['interview.initInfo()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Students';

        $data['arrStudentInterviewList'] = $arrStudentInterviewList;
        $data['studentDetail'] = $arrStudentDetail;
        $data['arrCourseList'] = $arrCourseList;
        $data['exitTypeArr'] = $exitTypeArr;
        $data['newCollage'] = $newCollage;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-interview-detail', $data);
    }

    public function deleteStudentInterview($studentId, $interviewId, Request $request)
    {

        $objInterview = new StudentInterview;
        $objInterview->deleteStudentInterview($interviewId);

        $request->session()->flash('session_success', 'Student Interview Details Delete Successfully.');

        return redirect(route('student-interview', ['student_id' => $studentId]));
    }

    /* End Student Exit Interview Module */

    public function _getStudentpaymentDetail($studentID, $courseID)
    {

        $objCourse = new Courses;
        $courseCode = $objCourse->getCourseCode($courseID);

        $objStudentPayment = new StudentInitialPaymentDetails;
        $studentPaymentData = $objStudentPayment->getStudentPaymentList($studentID, $courseID);
        if ($studentPaymentData->count() > 0 || isset($studentPaymentData)) {
            $resultArr = [$studentPaymentData, $courseCode];
            echo json_encode($resultArr);
            exit;
        }
    }

    public function _getStatusHistoryDetail($studentID, $courseID)
    {

        $objStatusHistory = new StudentCourseStatusHistory;
        $statusHistoryArr = $objStatusHistory->getStatusHistoryArr($studentID, $courseID);

        if ($statusHistoryArr->count() > 0 || isset($statusHistoryArr)) {
            echo json_encode($statusHistoryArr);
            exit;
        }
    }

    public function studentClaimTrackingList($student_id, Request $request)
    {

        $student_id = $request->id;
        $login_id = Auth::user()->id;
        $college_id = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $arrState = Config::get('constants.arrState');
        $arrClaimStageStatus = Config::get('constants.arrClaimStageStatus');
        $arrClaimStageYear = Config::get('constants.arrClaimStageYear');
        $objStudentCourse = new StudentCourse;
        $arrCourseList = $objStudentCourse->getStudentAppliedCourseName($student_id);

        $objStudent = new Students;
        $arrStudent = $objStudent->getExistsReferenceId($student_id, $college_id);

        $objClaimTrack = new StudentClaimTracking;
        $arrClaimTrackList = $objClaimTrack->getClaimTrackList($perPage, $student_id);

        if ($request->isMethod('post')) {
            $validations = [
                'claim_status' => 'required',
                'invoice_number' => 'required',
                'payment_received_date' => 'required',
            ];
            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('student-claim-tracking-list', ['student_id' => $student_id]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objClaimTracking = new StudentClaimTracking;
            $result = $objClaimTracking->saveStudentClaimTracking($request);
            if (! empty($result)) {
                $request->session()->flash('session_success', 'Claim Tracking Add successfully!.');

                return redirect(route('student-claim-tracking-list', ['student_id' => $student_id]));
            } else {
                $request->session()->flash('session_warning', 'Claim Tracking Not added');

                return redirect(route('student-claim-tracking-list', ['student_id' => $student_id]));
            }
        }
        $data['header'] = [
            'title' => ' Student Claim Tracking List',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Student Claim Tracking ' => '',
            ]];
        $data['pagetitle'] = 'Student Claim Tracking';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'datepicker/bootstrap-datepicker.js', 'ckeditor/ckeditor.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['studentCliamTracking.js', 'studentProfileTask.js'];
        $data['funinit'] = ['studentCliamTracking.initStudentCliamTrackingList()', 'studentProfileTask.initTask()'];
        $data['activateValue'] = 'Offers';
        $data['arrStudentCourse'] = $arrCourseList;
        $data['applicationRefID'] = $arrStudent;
        $data['studentId'] = $student_id;
        $data['arrClaimTrackList'] = $arrClaimTrackList;
        $data['arrState'] = $arrState;
        $data['arrClaimStageStatus'] = $arrClaimStageStatus;
        $data['arrClaimStageYear'] = $arrClaimStageYear;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-claim-tracking-list', $data);
    }

    public function studentClaimTrackingAdd($student_id, Request $request)
    {

        $student_id = $request->id;
        $login_id = Auth::user()->id;
        $college_id = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $arrState = Config::get('constants.arrState');
        unset($arrState['09'], $arrState['00']);
        $arrClaimStageStatus = Config::get('constants.arrClaimStageStatus');
        $arrClaimStageYear = Config::get('constants.arrClaimStageYear');
        $objStudentCourse = new StudentCourse;
        $arrCourseList = $objStudentCourse->getStudentAppliedCourseName($student_id);

        $objStudent = new Students;
        $arrStudent = $objStudent->getExistsReferenceId($student_id, $college_id);

        $logType = ['profile'];
        $objStudentCommunication = new StudentCommunicationLog;
        $arrStudCommList = $objStudentCommunication->getStudentCommunicationDiary($perPage, $student_id, $college_id, $logType);

        if ($request->isMethod('post')) {
            $validations = [
                'claim_status' => 'required',
                'invoice_number' => 'required',
                'payment_received_date' => 'required',
            ];
            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('student-claim-tracking-list', ['student_id' => $student_id]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objClaimTracking = new StudentClaimTracking;
            $result = $objClaimTracking->saveStudentClaimTracking($request);
            if (! empty($result)) {
                $request->session()->flash('session_success', 'Claim Tracking Add successfully!.');

                return redirect(route('student-claim-tracking-list', ['id' => $student_id]));
            } else {
                $request->session()->flash('session_warning', 'Claim Tracking Not added');

                return redirect(route('student-claim-tracking-list', ['id' => $student_id]));

            }
        }

        $data['header'] = [
            'title' => 'Add Student Claim Tracking',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Add Student Claim Tracking ' => '',
            ]];
        $data['pagetitle'] = 'Student Claim Tracking';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'datepicker/bootstrap-datepicker.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['studentCliamTracking.js'];
        $data['funinit'] = ['studentCliamTracking.initStudentCliamTracking()'];
        $data['activateValue'] = 'Offers';
        $data['arrStudentCourse'] = $arrCourseList;
        $data['applicationRefID'] = $arrStudent;
        $data['arrClaimStageStatus'] = $arrClaimStageStatus;
        $data['studentId'] = $student_id;
        $data['arrStudCommList'] = $arrStudCommList;
        $data['arrState'] = $arrState;
        $data['arrClaimStageYear'] = $arrClaimStageYear;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-claim-tracking-add', $data);
    }

    public function studentClaimTrackingEdit($student_id, $track_id, Request $request)
    {

        $student_id = $student_id;
        $login_id = Auth::user()->id;
        $college_id = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $arrState = Config::get('constants.arrState');
        unset($arrState['09'], $arrState['00']);
        $arrClaimStageStatus = Config::get('constants.arrClaimStageStatus');
        $arrClaimStageYear = Config::get('constants.arrClaimStageYear');
        $objStudentCourse = new StudentCourse;
        $arrCourseList = $objStudentCourse->getStudentAppliedCourseName($student_id);

        $objStudent = new Students;
        $arrStudent = $objStudent->getExistsReferenceId($student_id, $college_id);

        $objClaimTracking = new StudentClaimTracking;
        $arrTrackEdit = StudentClaimTracking::find($track_id);

        if ($request->isMethod('post')) {
            $validations = [
                'claim_status' => 'required',
                'invoice_number' => 'required',
                'payment_received_date' => 'required',
            ];
            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('student-claim-tracking-list', ['student_id' => $student_id]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $result = $objClaimTracking->editStudentClaimTracking($track_id, $request);
            if (! empty($result)) {
                $request->session()->flash('session_success', 'Claim Tracking Edit  successfully!.');

                return redirect(route('student-claim-tracking-list', ['id' => $student_id]));
            } else {
                $request->session()->flash('session_warning', 'Claim Tracking Not added');

                return redirect(route('student-claim-tracking-list', ['id' => $student_id]));
            }
        }
        $data['header'] = [
            'title' => 'Edit Student Claim Tracking',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Edit Student Claim Tracking ' => '',
            ]];
        $data['pagetitle'] = 'Student Claim Tracking';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'datepicker/bootstrap-datepicker.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['studentCliamTracking.js'];
        $data['funinit'] = ['studentCliamTracking.initStudentCliamTracking()'];
        $data['activateValue'] = 'Offers';
        $data['arrStudentCourse'] = $arrCourseList;
        $data['applicationRefID'] = $arrStudent;
        $data['arrClaimStageStatus'] = $arrClaimStageStatus;
        $data['studentId'] = $student_id;
        $data['arrTrackEdit'] = $arrTrackEdit;
        $data['arrState'] = $arrState;
        $data['arrClaimStageYear'] = $arrClaimStageYear;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-claim-tracking-add', $data);
    }

    public function SearchStudentId($studentId, Request $request)
    {

        if ($request->input()) {
            print_r($request->input());
            exit;
            $college_id = Auth::user()->college_id;
            $objStudent = new Students;
            $arrStudent = $objStudent->getStudentId($college_id, $request->input('student_id'));
            if (empty($arrStudent)) {
                $request->session()->flash('session_error', 'Search Student id not found.');

                return redirect(route('student-profile', ['student_id' => $studentId]));
            } else {
                $request->session()->flash('session_success', 'Student id search successfully');

                return redirect(route('student-profile', ['student_id' => $arrStudent[0]['id']]));
            }
        }
    }

    public function studentInterventionExportdDocs1($studentId, $interventionId, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $data['studetId'] = $studentId;
        $data['interventionId'] = $interventionId;

        $arrIntervention = InterventionStrategy::where('college_id', $collegeId)
            ->pluck('strategy', 'id')
            ->toArray();

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objIntervationType = new InterventionStrategyType;
        $arrIntervationTypeList = $objIntervationType->getInterventionTypeListV2();

        $objIntervention = new StudentIntervention;
        $arrInterventionInfo = $objIntervention->getInterventionDetail($collegeId, $studentId, $interventionId);
        $objStrategy = new StudentInterventionStrategy;
        $arrInterventionCategoty = $objStrategy->getInterventionSubCategoty($collegeId, $studentId, $interventionId);
        $categoryName = '';
        $subCategoryName = '';
        if (isset($arrInterventionCategoty) && ! empty($arrInterventionCategoty)) {
            foreach ($arrInterventionCategoty['cat'] as $key => $value) {
                $categoryName .= array_key_exists($value, $arrIntervationTypeList) ? $arrIntervationTypeList[$value].', ' : '';
            }
        }
        if (isset($arrInterventionCategoty) && ! empty($arrInterventionCategoty)) {
            foreach ($arrInterventionCategoty['catVal'] as $key => $subCat) {
                $subCategoryName .= array_key_exists($subCat, $arrIntervention) ? $arrIntervention[$subCat].', ' : '';
            }
        }

        $name = $arrStudentDetail['first_name'].'_'.$arrStudentDetail['family_name'].'_'.$arrStudentDetail['generated_stud_id'];
        $data['studentId'] = $arrStudentDetail['generated_stud_id'];
        $data['studentName'] = $arrStudentDetail['first_name'].' '.$arrStudentDetail['family_name'];
        $data['coursename'] = $arrInterventionInfo[0]['course_name'];
        $data['recordedDate'] = $arrInterventionInfo[0]['recorded_date'];
        $data['semesterName'] = $arrInterventionInfo[0]['semester_name'];
        $data['categoryname'] = $categoryName;
        $data['subCategoryName'] = $subCategoryName;
        $data['casedetails'] = $arrInterventionInfo[0]['case_detail'];
        $data['case'] = ($arrInterventionInfo[0]['case_status'] == 1) ? 'Case Open' : 'Case Close';

        $data['due_date'] = date('d-m-Y', strtotime($arrInterventionInfo[0]['due_date']));
        $data['outcome_date'] = date('d-m-Y', strtotime($arrInterventionInfo[0]['outcome_date']));

        $results = view('frontend.student.student-intervention-docs', $data)->render();

        $phpWord = new \PhpOffice\PhpWord\PhpWord;
        $section = $phpWord->addSection();
        \PhpOffice\PhpWord\Shared\Html::addHtml($section, $results);

        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save("$name.docx");

        return response()->download(public_path("$name.docx"))->deleteFileAfterSend(true);
    }

    public function studentInterventionExportdDocs($studentId, $interventionId, Request $request)
    {
        $collegeId = Auth::user()->college_id;

        $objInterventionCat = new InterventionStrategy;
        $arrStudentInterventionSubCategory = $objInterventionCat->getStudentInterventionSubCategory($collegeId);
        $arrIntervention = InterventionStrategy::where('college_id', $collegeId)
            ->pluck('strategy', 'id')
            ->toArray();

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objIntervationType = new InterventionStrategyType;
        $arrIntervationTypeList = $objIntervationType->getInterventionTypeListV2();

        $objIntervention = new StudentIntervention;
        $arrInterventionInfo = $objIntervention->getInterventionDetail($collegeId, $studentId, $interventionId);
        $objStrategy = new StudentInterventionStrategy;
        $arrInterventionCategoty = $objStrategy->getInterventionSubCategoty($collegeId, $studentId, $interventionId);
        $categoryName = '';
        $subCategoryName = '';
        if (isset($arrInterventionCategoty) && ! empty($arrInterventionCategoty)) {
            foreach ($arrInterventionCategoty['cat'] as $key => $value) {
                $categoryName .= array_key_exists($value, $arrIntervationTypeList) ? $arrIntervationTypeList[$value].', ' : '';
            }
        }
        if (isset($arrInterventionCategoty) && ! empty($arrInterventionCategoty)) {
            foreach ($arrInterventionCategoty['catVal'] as $key => $subCat) {
                $subCategoryName .= array_key_exists($subCat, $arrIntervention) ? $arrIntervention[$subCat].', ' : '';
            }
        }

        $phpWord = new \PhpOffice\PhpWord\PhpWord;
        $section = $phpWord->addSection();

        $case = ($arrInterventionInfo[0]['case_status'] == 1) ? 'Case Open' : 'Case Close';

        $fontStyle = new \PhpOffice\PhpWord\Style\Font;
        $fontStyle->setBold(true);
        $fontStyle->setName('Tahoma');
        $fontStyle->setSize(12);
        $name = $arrStudentDetail['first_name'].'_'.$arrStudentDetail['family_name'].'_'.$arrStudentDetail['generated_stud_id'];
        $myTextElement = $section->addText('Student Name : '.$arrStudentDetail['first_name'].' '.$arrStudentDetail['family_name']);
        $myTextElement->setFontStyle($fontStyle);

        $section->addText('Student Id: '.$arrStudentDetail['generated_stud_id']);
        $section->addText('Course name: '.$arrInterventionInfo[0]['course_name']);
        $section->addText('Recorded Date: '.$arrInterventionInfo[0]['recorded_date']);
        $section->addText('Semester name: '.$arrInterventionInfo[0]['semester_name']);
        $section->addText('Intervention Type: '.$categoryName);
        $section->addText('Intervention Strategy: '.$subCategoryName);
        $section->addText('Details of Intervention case: '.$arrInterventionInfo[0]['case_detail']);
        $section->addText('Status: '.$case);
        $section->addText('Due date: '.date('d-m-Y', strtotime($arrInterventionInfo[0]['due_date'])));
        $section->addText('Outcome date: '.date('d-m-Y', strtotime($arrInterventionInfo[0]['outcome_date'])));

        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');

        $filePath = Config::get('constants.uploadFilePath.TempFiles');
        $destinationPath = Helpers::changeRootPath($filePath);

        // Ensure the TempFiles directory exists
        if (! is_dir($destinationPath['default'])) {
            mkdir($destinationPath['default'], 0777, true); // `true` ensures nested directories are created
        }
        $tempFile = $destinationPath['default']."/$name.docx";
        $objWriter->save($tempFile);

        return response()->download($tempFile)->deleteFileAfterSend(true);
    }

    public function studentInterventionExportdDocs_old($studentId, $interventionId, Request $request)
    {
        $phpWord = new \PhpOffice\PhpWord\PhpWord;

        $section = $phpWord->addSection();
        // Adding Text element to the Section having font styled by default...
        $section->addText('"Learn from yesterday, live for today, hope for tomorrow. The important thing is not to stop questioning." (Albert Einstein)');

        /*
         * Note: it's possible to customize font style of the Text element you add in three ways:
         * - inline;
         * - using named font style (new font style object will be implicitly created);
         * - using explicitly created font style object.
         */

        // Adding Text element with font customized inline...
        $section->addText('"Student intrvention files.', ['name' => 'Tahoma', 'size' => 10]);

        // Adding Text element with font customized using named font style...
        $fontStyleName = 'oneUserDefinedStyle';
        $phpWord->addFontStyle($fontStyleName, ['name' => 'Tahoma', 'size' => 10, 'color' => '1B2232', 'bold' => true]);
        $section->addText('"The greatest accomplishment is not in never falling, but in rising again after you fall." (Vince Lombardi)', $fontStyleName);

        // Adding Text element with font customized using explicitly created font style object...
        $fontStyle = new \PhpOffice\PhpWord\Style\Font;
        $fontStyle->setBold(true);
        $fontStyle->setName('Tahoma');
        $fontStyle->setSize(13);
        $myTextElement = $section->addText('"Believe you can and you\'re halfway there." (Theodor Roosevelt)');
        $myTextElement->setFontStyle($fontStyle);

        // Saving the document as OOXML file...
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save('StudentIntervention.docx');

        return response()->download(public_path('StudentIntervention.docx'))->deleteFileAfterSend(true);
    }

    public function interventionLetter($id, Request $request)
    {
        $teacherId = $id;
        $collegeId = Auth::user()->college_id;
        $id = $request->id;
        $limit = 15;
        $perPage = Config::get('constants.pagination.perPage');

        $objCommunication = new StaffCommunicationLog;
        $arrCommunicationList = $objCommunication->getStaffCommunicationData($perPage, $id, $collegeId, $limit);
        $objRtoTeacher = new Teacher;
        $objTeacherProfile = $objRtoTeacher->getTeacherProfile($teacherId);

        $data['pagetitle'] = 'Teacher Manage';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['teacher.js'];
        $data['funinit'] = ['Teacher.initAddTeacher()'];
        $data['activateValue'] = 'Teacher';

        $data['objTeacherProfile'] = $objTeacherProfile;
        $data['teacherId'] = $teacherId;
        $data['arrCommunicationList'] = $arrCommunicationList;

        return view('frontend.teacher.trainer-preview', $data);
    }

    private function getEmailContentData($content, $studentId, $courseId) {}

    public function sendStudentSms($arrSmsData, $collegeId)
    {
        $userId = Auth::user()->id;
        $arrResult = [];
        $arrResult['message'] = $arrSmsData['sms_text'];
        $arrResult['studentId'] = $arrSmsData['studentId'];
        $arrResult['userId'] = $userId;
        $arrResult['collegeId'] = $collegeId;
        $objStudentSmsLog = new StudentCommunicationLog;
        $result = $objStudentSmsLog->addSMSDetails($arrResult);
        $returnData['type'] = 'alert-success';
        $returnData['message'] = 'SMS successfully Send to student.';
        echo json_encode($returnData);
        exit;
    }

    // ------------------------ Student Upload Document -----------------------//
    public function studentDocumentUpload(Request $request)
    {

        $collegeId = $this->loginUser->college_id;
        $userId = $this->loginUser->id;
        $studentId = $request->student_id;
        $parentId = $request->parent_id;

        $objStudendProfile = new Students;
        $arrStudentData = $objStudendProfile->getExistsReferenceId($studentId, $collegeId);

        $objStudentUploadFile = new StudentUploadFile;
        $objStudentFile = new StudentUploadFile;
        $arrStudentFile = $objStudentFile->getStudentFileList($collegeId, $studentId, $parentId);

        $backMenuId = $objStudentUploadFile->getParentId($parentId, $collegeId);

        if ($request->isMethod('post')) {

            if (empty($request->input('folder_or_file')) && empty($request->file())) {
                $validations['folder_or_file'] = 'required';
                $checkValidations = [
                    'folder_or_file.required' => 'Please Create any folder OR Upload Document(s).',
                ];
                $validator = Validator::make($request->all(), $validations, $checkValidations);

                if ($validator->fails()) {
                    return redirect(route('student-document-upload', ['student_id' => $studentId, 'parent_id' => $parentId]))->withErrors($validator)->withInput();
                }
            }

            $fileForValidate = $request->file();

            if ($request->file()) {
                $isValidSize = true;
                $isValidType = true;
                $tmpFile = $fileForValidate['images'][0];
                if ($isValidSize) {
                    $tmpSize = $tmpFile->getSize();
                    $tmpSize = number_format($tmpSize / 1048576, 2);
                    if ($tmpSize > 5) {
                        $isValidSize = false;
                    }
                }

                if ($isValidType) {
                    $arr = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'gif'];
                    $tmpType = $tmpFile->guessClientExtension();

                    if (! in_array(strtolower($tmpType), $arr)) {
                        $isValidType = false;
                    }
                }
                if (! $isValidType || ! $isValidSize) {
                    $errorArr = [];
                    (! $isValidType) ? $errorArr[] = 'Please Select Valid File ( .pdf , .jpg ,.jpeg ,.gif)' : '';
                    (! $isValidSize) ? $errorArr[] = 'The maximum file upload size limit is 5MB.' : '';

                    return redirect(route('student-document-upload', ['student_id' => $studentId, 'parent_id' => $parentId]))->withErrors($errorArr)->withInput();
                }
            }

            $objStudentUploadFile = new StudentUploadFile;
            $result = $objStudentUploadFile->studentFileUpload($request, $collegeId, $studentId, $userId, $parentId);
            if ($result == 'file') {
                $request->session()->flash('session_success', 'Student Documents Upload Successfully.');
                $globalSearchData = [
                    'college_id' => $collegeId,
                    'tenant_id' => tenant('id'),
                ];
                $this->updateGlobalSearch($globalSearchData);
            } elseif ($result == 'folder') {
                $request->session()->flash('session_success', 'Root directory Add Successfully.');
            } else {
                $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
            }

            return redirect(route('student-document-upload', ['student_id' => $studentId, 'parent_id' => $parentId]));
        }

        $data['header'] = [
            'title' => 'Document Upload Page regarding'.$arrStudentData[0]->generated_stud_id.' ( '.$arrStudentData[0]->fullName.' )',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('student-course-information', ['id' => $studentId]),
                'Student document upload' => '',
            ]];
        $data['pagetitle'] = 'Student Document Upload';
        $data['plugincss'] = ['dropzone/dist/dropzone.css'];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'jQuery/jquery.form.js', 'dropzone/dist/dropzone.js'];
        $data['js'] = ['student.js', 'studentProfileTask.js'];
        $data['funinit'] = ['Student.initStudentDoc()', 'studentProfileTask.initTask()'];
        $data['userId'] = $userId;
        $data['studentId'] = $studentId;
        $data['backMenuId'] = $backMenuId;
        $data['studentData'] = $arrStudentData[0];
        $data['arrStudentUploadedFile'] = $arrStudentFile;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-document-upload', $data);
    }

    public function updateGlobalSearch($globalSearchData)
    {
        event(new \App\Events\GlobalSearchEvent($globalSearchData));
    }

    public function editStudentDocumentDirecory(Request $request)
    {
        $collegeId = $this->loginUser->college_id;
        $userId = $this->loginUser->id;

        if ($request->isMethod('post')) {
            $studentId = $request->input('student_id');
            $studentDocId = $request->input('primary_id');

            $objStudentUploadFile = new StudentUploadFile;
            $result = $objStudentUploadFile->editStudentFileDirecory($collegeId, $userId, $request);
            $parentId = $objStudentUploadFile->getParentId($studentDocId, $collegeId);

            if ($result) {
                $request->session()->flash('session_success', 'Renamed successfully.');
            } else {
                $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
            }

            return redirect(route('student-document-upload', ['student_id' => $studentId, 'parent_id' => $parentId]));
        }
    }

    public function downloadstudentFile(Request $request)
    {
        $studentId = base64_decode($request->studentId);
        $primaryId = base64_decode($request->primaryId);
        $filePath = Config::get('constants.uploadFilePath.StudentFiles');
        $destinationPath = Helpers::changeRootPath($filePath, $studentId);

        $objStudentUploadFile = StudentUploadFile::find($primaryId);
        $fileName = $objStudentUploadFile->folder_or_file;
        $originalName = $objStudentUploadFile->original_name;
        $path = $destinationPath['view'].$fileName;

        return redirect()->away(UploadService::download($path, $originalName));
    }

    public function deleteUploadedFile(Request $request)
    {
        $studentId = $request->student_id;
        $primaryId = $request->primary_id;
        $documentId = $request->document_id;
        $collegeId = $this->loginUser->college_id;
        if ($documentId == 0) {
            $objStudentUploadFile = new StudentUploadFile;
            $parentId = $objStudentUploadFile->getParentId($primaryId, $collegeId);
            $result = $objStudentUploadFile->deleteStudentUploadedFile($collegeId, $studentId, $primaryId);

            if ($result) {
                $request->session()->flash('session_success', 'Student File Delete Successfully.');
            } else {
                $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
            }

            return redirect(route('student-document-upload', ['student_id' => $studentId, 'parent_id' => $parentId]));
        } else {

            $collegeMaterialsIdDelete = CollegeMaterials::where('id', $documentId)->delete();
            $dataDelete = StudentOfferDocuments::where('document_material_id', $documentId)->where('rto_student_id', $studentId)->delete();
            if ($dataDelete) {
                $request->session()->flash('session_success', 'Document File Delete Successfully.');
            } else {
                $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
            }

            return redirect(route('student-document-upload', ['student_id' => $studentId, 'parent_id' => 0]));
        }
    }

    /* view student course list action on popup */

    public function academicSummaryData($studentID, $courseID)
    {

        $perPage = Config::get('constants.pagination.perPage');
        $arrAcademic = Config::get('constants.arrAcademic');

        $collegeId = Auth::user()->college_id;
        $objStudentCourse = new StudentCourse;
        $arrStudentCourse = $objStudentCourse->getStudentCourseDetails($courseID, $studentID);

        $objCourseSubject = new CourseSubject;
        $arrCourseSubjectId = $objCourseSubject->getCourseSubjectId($courseID);
        $objSubjectSummary = $objCourseSubject->getCourseSubjectSummary($collegeId, $courseID);

        $objRtoStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $objEnrollSubject = $objRtoStudentSubjectEnrolment->getCourseSubjectSummary($collegeId, $studentID, $courseID);

        $arrSubjectSummary = [];
        for ($i = 0; $i < count($objSubjectSummary); $i++) {
            $arrSubjectSummary[$i]['subject_code'] = $objSubjectSummary[$i]->subject_code;
            $arrSubjectSummary[$i]['subject_name'] = $objSubjectSummary[$i]->subject_name;
            $arrSubjectSummary[$i]['first_name'] = $objSubjectSummary[$i]->first_name;
            $arrSubjectSummary[$i]['last_name'] = $objSubjectSummary[$i]->last_name;

            for ($j = 0; $j < count($objEnrollSubject); $j++) {
                if ($objSubjectSummary[$i]->subject_id == $objEnrollSubject[$j]['subject_id']) {
                    $arrSubjectSummary[$i]['activity_start_date'] = $objEnrollSubject[$j]->activity_start_date;
                    $arrSubjectSummary[$i]['activity_finish_date'] = $objEnrollSubject[$j]->activity_finish_date;
                    $arrSubjectSummary[$i]['final_outcome'] = $objEnrollSubject[$j]->final_outcome;
                    $arrSubjectSummary[$i]['batch'] = $objEnrollSubject[$j]->batch;
                    break;
                } else {
                    $arrSubjectSummary[$i]['activity_start_date'] = '';
                    $arrSubjectSummary[$i]['activity_finish_date'] = '';
                    $arrSubjectSummary[$i]['final_outcome'] = '';
                }
            }
        }
        $perPage = '';
        $objCourseUnit = new UnitModule;
        $objUnitSummary = $objCourseUnit->getCourseUnitSummary($collegeId, $arrCourseSubjectId, $perPage, $studentID);

        $data['arrAcademic'] = $arrAcademic;
        $data['arrSubjectSummary'] = $arrSubjectSummary;
        $data['objUnitSummary'] = $objUnitSummary;
        $data['studentId'] = $studentID;
        $data['studentCourseId'] = $arrStudentCourse->id;
        $data['popup-title'] = 'Academic Summary List';
        $resultArr = [
            'title' => 'Academic Summary List',
            'body' => view('frontend.student.popup-academic-summary')->with($data)->render(),
        ];

        return $resultArr;
    }

    public function getStudentPaymentData($studentID, $courseID)
    {

        $objCourse = new Courses;
        $courseCode = $objCourse->getCourseCode($courseID);
        $courseName = 'N/A';
        if (isset($courseCode)) {
            $courseName = $courseCode[0]['course_code'].' : '.$courseCode[0]['course_name'];
        }

        $objStudentPayment = new StudentInitialPaymentDetails;
        $studentPaymentData = $objStudentPayment->getStudentPaymentList($studentID, $courseID);

        $data['arrStudentPaymentData'] = $studentPaymentData;
        $resultArr = [
            'title' => 'Student Payment Details for '.$courseName,
            'body' => view('frontend.student.popup-course-payment')->with($data)->render(),
        ];

        return $resultArr;
    }

    public function getStudentPaymentTabData($studentID)
    {

        $objStudentPaymentRecord = new StudentInitialPaymentDetails;
        $getPaymentTabRecord = $objStudentPaymentRecord->getPaymentTabRecord($studentID);
        $data['arrStudentPaymentData'] = $getPaymentTabRecord;

        $resultArr = [
            'body' => view('frontend.student.popup-course-payment')->with($data)->render(),
        ];

        return $resultArr;
    }

    public function getStatusHistoryData($studentID, $courseID)
    {

        $objStatusHistory = new StudentCourseStatusHistory;
        $statusHistoryArr = $objStatusHistory->getStatusHistoryArr($studentID, $courseID);

        $data['arrStatusHistoryData'] = $statusHistoryArr;
        $resultArr = [
            'title' => 'Student Course Status History',
            'body' => view('frontend.student.popup-course-history')->with($data)->render(),
        ];

        return $resultArr;
    }

    public function courseAcademicSummaryData($collegeId, $studentId, $semesterId, $termId)
    {

        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrAcademicData = $objStudentSubjectEnrolment->currentCourseAcademicData($collegeId, $studentId, $semesterId, $termId);
        $data['arrAcademicData'] = $arrAcademicData;
        $resultArr = [
            'title' => 'Current Course Academic Summary',
            'body' => view('frontend.student.popup-course-academic-summary')->render(),
        ];

        return $resultArr;
    }

    public function getAutocompleteData(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $objGetAutoComplete = new Students;
        $studentAutoComplete = $objGetAutoComplete->getAutocompleteData($request, $collegeId);
        echo json_encode($studentAutoComplete);
        exit;
    }

    public function getStudentAutocompleteData(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $objGetAutoComplete = new Students;
        $studentAutoComplete = $objGetAutoComplete->getStudentAutocompleteData($request, $collegeId);
        echo json_encode($studentAutoComplete);
        exit;
    }

    public function exportStudentList($courseStatus = null)
    {
        return Excel::download(new StudentsExport($courseStatus), 'Student_List.xlsx');
    }

    public function studentInvoicePdf($courseId, $studentId, $download, $request)
    {

        $data = [];
        $data['pagetitle'] = '';
        if (isset($studentId)) {

            $objRtoStudents = new Students;
            $objStudentDetails = $objRtoStudents->getStudentDetailsV2($studentId, $courseId);

            $collegeId = Auth::user()->college_id;
            if ($objStudentDetails[0]->college_id == $collegeId) {

                $objRtoCountry = new Country;
                $objCurrentCountry = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->current_country);
                $objNationality = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->nationality);

                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $objRtoStudentCourse = new StudentCourse;
                $objStudentCourse = $objRtoStudentCourse->getAppliedStudentCourse($courseId, $studentId);
            } else {
                return view('frontend.error_page.unauthorized', $data);
            }

            if ($objStudentCourse->count() > 0) {
                $objStudentCourse = $objStudentCourse;
            } else {
                $objStudentCourse = [];
            }

            $studentDetails = new StudentDetails;
            $getOHSCCourseId = $studentDetails->getStudentDetailsOfOHSCCourseId($studentId);
            if (empty($getOHSCCourseId[0])) {
                $firstStudentCourseId = StudentCourse::where('rto_student_courses.student_id', '=', $studentId)
                    ->select('rto_student_courses.course_id')
                    ->get();
                $firstStudentCourseId = $firstStudentCourseId[0]->course_id;
            } else {
                $firstStudentCourseId = $getOHSCCourseId[0];
            }
        }

        // $data['logoPath'] = Config::get('constants.displayCollegeLogoPDF');
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $data['arrState'] = Config::get('constants.arrState');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data['logoPath'] = $destinationPath['default'];

        $college_logo = '';
        if ($objCollegeDetails[0]['college_logo'] != '') {
            $college_logo_url = str_replace('\\', '/', $destinationPath['view']).$objCollegeDetails[0]['college_logo'];
            $college_logo = '<img src="data:image/png;base64,'.base64_encode(file_get_contents(public_path($college_logo_url))).'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
        }
        $data['objCollegeDetails'] = $objCollegeDetails;
        $data['objStudentCourse'] = $objStudentCourse;
        $data['objCurrentCountry'] = $objCurrentCountry;
        $data['objNationality'] = $objNationality;
        $data['objStudentDetails'] = $objStudentDetails;
        $data['firstStudentCourseId'] = $firstStudentCourseId;
        $data['college_logo'] = $college_logo;

        if ($download == 'save') {
            $html = view('frontend.offer_manage.student-invoice-pdf', $data);
            $pdf = App::make('dompdf.wrapper');

            $pdf->loadHTML($html);
            $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);
            if (! is_dir($destinationPath['default'])) {
                // mkdir($destinationPath['default'], 0777);
                File::makeDirectory($destinationPath['default'], 0777, true, true);
            }
            $pdf->save($destinationPath['default'].'invoice.pdf');
        } else {
            $pdf = App::make('dompdf.wrapper');

            // load from other pages use object or array by comma like (pdf-view,$user)
            $pdf->loadView('frontend.offer_manage.student-invoice-pdf', $data);

            // return $pdf->stream();exit;
            return $pdf->download('student-invoice.pdf', $data);
        }
    }

    public function offerLetterPdf($courseId, $studentId, $studentCourseID, $download, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoPdfTemplate = new PdfTemplate;
        $objRtoStudents = new Students;
        $getRtoStudents = $objRtoStudents->getStudents($studentId);
        if ($getRtoStudents->student_type == 'Domestic') {
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(2);
        } else {
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(1);
        }

        $content = $arrPdfTemplateContent[0]->pdf_template;
        $offerLetterContent = $objRtoPdfTemplate->setPdfTemplateBodyContentNew($collegeId, $studentId, $content);

        if (isset($offerLetterContent['status']) && $offerLetterContent['status'] == 'error') {
            $request->session()->flash('session_error', $offerLetterContent['msg']);

            return redirect(route('offer-manage'));
        }

        $pdf = App::make('dompdf.wrapper');
        $pdf->loadHTML($offerLetterContent);

        $studentOfferLetterName = $this->studentOfferLetterName($studentId);

        if ($download == 'save') {

            $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);
            if (! is_dir($destinationPath['default'])) {
                File::makeDirectory($destinationPath['default'], 0777, true, true);
            }

            $offerLetterPdf = $destinationPath['default'].$studentOfferLetterName.'.pdf';
            $pdf->save($offerLetterPdf);

        } else {

            $pdf->output();
            $dom_pdf = $pdf->getDomPDF();
            $canvas = $dom_pdf->get_canvas();
            $canvas->page_text(500, 750, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);
            // return $pdf->stream();exit;

            return $pdf->download($studentOfferLetterName.'.pdf');
        }

    }

    public function studentInterventionExportPdf($studentId, $interventionId, Request $request)
    {
        $collegeId = Auth::user()->college_id;

        $objInterventionCat = new InterventionStrategy;
        $arrStudentInterventionSubCategory = $objInterventionCat->getStudentInterventionSubCategory($collegeId);
        $arrIntervention = InterventionStrategy::where('college_id', $collegeId)
            ->pluck('strategy', 'id')
            ->toArray();

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objIntervationType = new InterventionStrategyType;
        $arrIntervationTypeList = $objIntervationType->getInterventionTypeListV2();

        $objIntervention = new StudentIntervention;
        $arrInterventionInfo = $objIntervention->getInterventionDetail($collegeId, $studentId, $interventionId);

        $objStrategy = new StudentInterventionStrategy;
        $arrInterventionCategoty = $objStrategy->getInterventionSubCategoty($collegeId, $studentId, $interventionId);

        $categoryName = '';
        $subCategoryName = '';
        if (! empty($arrInterventionCategoty['cat'])) {
            foreach ($arrInterventionCategoty['cat'] as $value) {
                $categoryName .= array_key_exists($value, $arrIntervationTypeList) ? $arrIntervationTypeList[$value].', ' : '';
            }
            $categoryName = rtrim($categoryName, ', '); // Remove trailing comma
        }

        if (! empty($arrInterventionCategoty['catVal'])) {
            foreach ($arrInterventionCategoty['catVal'] as $subCat) {
                $subCategoryName .= array_key_exists($subCat, $arrIntervention) ? $arrIntervention[$subCat].', ' : '';
            }
            $subCategoryName = rtrim($subCategoryName, ', '); // Remove trailing comma
        }

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $logoPath = $destinationPath['default'];

        $filePath1 = Config::get('constants.uploadFilePath.LetterSetting');
        $destinationLetterPath = Helpers::changeRootPath($filePath1);
        $watermarkPath = $destinationLetterPath['view'];

        $letterSettingData = LetterSetting::find(1);

        $data = [
            'studentDetail' => $arrStudentDetail,
            'interventionInfo' => $arrInterventionInfo[0],
            'categoryName' => $categoryName,
            'subCategoryName' => $subCategoryName,
            'caseStatus' => $arrInterventionInfo[0]['case_status'] == 1 ? 'Case Open' : 'Case Close',
            'dueDate' => date('d-m-Y', strtotime($arrInterventionInfo[0]['due_date'])),
            'outcomeDate' => date('d-m-Y', strtotime($arrInterventionInfo[0]['outcome_date'])),
            'letterSettingData' => $letterSettingData,
            'watermarkPath' => $watermarkPath,
        ];

        // Load PDF View
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView('frontend.offer_manage.student-invoice-pdf-v3', $data);

        // Download PDF with Dynamic Filename
        $name = $arrStudentDetail['first_name'].'_'.$arrStudentDetail['family_name'].'_'.$arrStudentDetail['generated_stud_id'];

        return $pdf->download("$name.pdf");
    }

    public function studentInterventionExportExcel($studentId, $interventionId, Request $request)
    {
        $collegeId = Auth::user()->college_id;

        $objInterventionCat = new InterventionStrategy;
        $arrStudentInterventionSubCategory = $objInterventionCat->getStudentInterventionSubCategory($collegeId);
        $arrIntervention = InterventionStrategy::where('college_id', $collegeId)
            ->pluck('strategy', 'id')
            ->toArray();

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objIntervationType = new InterventionStrategyType;
        $arrIntervationTypeList = $objIntervationType->getInterventionTypeListV2();

        $objIntervention = new StudentIntervention;
        $arrInterventionInfo = $objIntervention->getAllStudentInterventionDetail($collegeId, $studentId);

        $data = [];
        foreach ($arrInterventionInfo as $info) {

            $objStrategy = new StudentInterventionStrategy;
            $arrInterventionCategoty = $objStrategy->getInterventionSubCategoty($collegeId, $studentId, $info['id']);
            $categoryName = '';
            $subCategoryName = '';

            if (! empty($arrInterventionCategoty['cat'])) {
                foreach ($arrInterventionCategoty['cat'] as $value) {
                    $categoryName .= array_key_exists($value, $arrIntervationTypeList) ? $arrIntervationTypeList[$value].', ' : '';
                }
                $categoryName = rtrim($categoryName, ', ');
            }

            if (! empty($arrInterventionCategoty['catVal'])) {
                foreach ($arrInterventionCategoty['catVal'] as $subCat) {
                    $subCategoryName .= array_key_exists($subCat, $arrIntervention) ? $arrIntervention[$subCat].', ' : '';
                }
                $subCategoryName = rtrim($subCategoryName, ', ');
            }

            $data[] = [
                'Student Name' => $arrStudentDetail['first_name'].' '.$arrStudentDetail['family_name'],
                'Student ID' => $arrStudentDetail['generated_stud_id'],
                'Course Name' => $info['course_code'].'-'.$info['course_name'],
                'Recorded Date' => $info['recorded_date'],
                'Semester Name' => $info['semester_name'],
                'Intervention Type' => $categoryName,
                'Intervention Strategy' => $subCategoryName,
                'Details of Intervention Case' => $info['case_detail'],
                'Status' => $info['case_status'] == 1 ? 'Case Open' : 'Case Close',
                'Due Date' => date('d-m-Y', strtotime($info['due_date'])),
                'Outcome Date' => date('d-m-Y', strtotime($info['outcome_date'])),
            ];
        }
        $name = $arrStudentDetail['first_name'].'_'.$arrStudentDetail['family_name'].'_'.$arrStudentDetail['generated_stud_id'];

        return Excel::download(new SingleStudentInterventionExport($data), "$name.xlsx");
    }
}
