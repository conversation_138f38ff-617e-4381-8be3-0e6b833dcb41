<div class="tw-result-grid grid grid-cols-12 {{ isset($isAjax) && $isAjax ? '' : 'hidden'}}">
    <div class="bg-gray-100 col-span-12">
        <div class="grid grid-cols-1 md:flex items-center px-6 md:px-8 py-4 justify-between flex-wrap gap-2 lg:gap-0">
            <div class="course-list w-auto justify-start">
                <select class="header_course_list" style="display: none;"></select>
            </div>
            <x-v2.skeleton.buttongroup count="4" />
            <x-v2.templates.profile-buttons>
                <button type="button" class="generateCertificateResultTabBtn btn-secondary h-[1.875rem] btn-icon">
                    <svg width="13" height="16" viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M2.99963 5.50476C2.99963 5.22862 3.22349 5.00476 3.49963 5.00476H4.79033L5.01095 3.90339C5.06518 3.63263 5.32865 3.4571 5.59941 3.51133C5.87018 3.56557 6.04571 3.82904 5.99147 4.0998L5.8102 5.00476H6.79102L7.01075 3.9037C7.06479 3.6329 7.32813 3.45718 7.59893 3.51122C7.86974 3.56526 8.04545 3.8286 7.99141 4.0994L7.81074 5.00476H8.75176C9.0279 5.00476 9.25176 5.22862 9.25176 5.50476C9.25176 5.7809 9.0279 6.00476 8.75176 6.00476H7.61118L7.41161 7.00476H8.50133C8.77747 7.00476 9.00133 7.22862 9.00133 7.50476C9.00133 7.7809 8.77747 8.00476 8.50133 8.00476H7.21205L6.99392 9.09781C6.93988 9.36861 6.67654 9.54433 6.40574 9.49029C6.13494 9.43625 5.95922 9.17291 6.01326 8.90211L6.19233 8.00476H5.20927L4.99025 9.0982C4.93601 9.36897 4.67255 9.5445 4.40178 9.49026C4.13102 9.43603 3.95549 9.17256 4.00973 8.9018L4.18941 8.00476H3.25183C2.97569 8.00476 2.75183 7.7809 2.75183 7.50476C2.75183 7.22862 2.97569 7.00476 3.25183 7.00476H4.38972L4.59003 6.00476H3.49963C3.22349 6.00476 2.99963 5.7809 2.99963 5.50476ZM6.3919 7.00476L6.59146 6.00476H5.60989L5.40958 7.00476H6.3919ZM2 0H10.0036C11.1081 0 12.0036 0.895431 12.0036 2V13.5013C12.0036 13.7775 11.7797 14.0013 11.5036 14.0013H1C1.0007 14.553 1.44815 15 2 15H11.5036C11.7797 15 12.0036 15.2239 12.0036 15.5C12.0036 15.7762 11.7797 16 11.5036 16H2C0.895431 16 0 15.1046 0 14V2C0 0.895429 0.895431 0 2 0ZM1 13.0013H11.0036V2C11.0036 1.44772 10.5559 1 10.0036 1H2C1.44771 1 1 1.44771 1 2V13.0013Z"
                            fill="#9CA3AF" />
                    </svg>
                    <p class="text-xs leading-4 text-gray-700 truncate">Generate Certificate</p>
                </button>
            </x-v2.templates.profile-buttons>
        </div>
        <div id="studResultTab" class="px-8 pb-4">
            <div class="grid grid-cols-12 gap-4 w-full py-1">
                <div
                    class="flex col-span-6 md:col-span-2 p-4 bg-white rounded-md shadow justify-start items-center space-x-2">

                    <div class="w-12 h-12 p-3 bg-sky-100 rounded-md justify-center items-center inline-flex">
                        <img src="{{ asset('v2/img/result_assessment.svg') }}" class="w-5 h-5" alt="Result Assessment">
                    </div>
                    <div class="inline-flex flex-col items-start justify-start flex-1 space-y-1">
                        <p class="text-sm leading-5 text-gray-400">Completion</p>
                        <div class="text-gray-700 text-lg font-medium leading-7 tracking-tight totalCompletion">
                            <div class="w-12 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                        </div>
                    </div>
                </div>
                <div
                    class="flex col-span-6 md:col-span-2 p-4 bg-white rounded-md shadow justify-start items-center space-x-2">
                    <div class="w-12 h-12 p-3 bg-sky-100 rounded-md justify-center items-center inline-flex">
                        <img src="{{ asset('v2/img/result_assessment.svg') }}" class="w-5 h-5" alt="Result Assessment">
                    </div>
                    <div class="inline-flex flex-col items-start justify-start flex-1 space-y-1">
                        <p class="text-sm leading-5 text-gray-400">Assessments</p>
                        <div class="text-gray-700 text-lg font-medium leading-7 tracking-tight totalcourseAssesment">
                            <div class="w-12 h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col col-span-12 md:col-span-8 p-4 bg-white rounded-md shadow justify-start items-center space-x-2"
                    id="unitProgressbarInPercentageDiv">
                    <div class="w-full h-5 bg-gray-200 rounded-full animate-pulse mb-2"></div>
                </div>
            </div>
            <div class="flex flex-row space-x-0 md:space-x-2 py-4 flex-wrap gap-2 md:gap-0">
                <button type="button" class="bultkReportToTcsi btn-secondary" disabled>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M7 0.5C7 0.223858 7.22386 0 7.5 0H10.5C10.7761 0 11 0.223858 11 0.5V1.5C11 1.77614 10.7761 2 10.5 2H8V3C10.7614 3 13 5.23858 13 8V9H14C15.1046 9 16 9.89543 16 11V15C16 15.5523 15.5523 16 15 16H1C0.447716 16 0 15.5523 0 15V11C0 9.89543 0.895431 9 2 9H3V8C3 5.58104 4.71776 3.56329 7 3.10002V0.5ZM14 10H12.5C12.2239 10 12 9.77614 12 9.5V8C12 5.79086 10.2091 4 8 4C5.79086 4 4 5.79086 4 8V9.5C4 9.77614 3.77614 10 3.5 10H2C1.44772 10 1 10.4477 1 11V15H6V13C6 12.4477 6.44772 12 7 12H9C9.55228 12 10 12.4477 10 13V15H15V11C15 10.4477 14.5523 10 14 10ZM4 11.5C4 11.2239 3.77614 11 3.5 11C3.22386 11 3 11.2239 3 11.5V13.5C3 13.7761 3.22386 14 3.5 14C3.77614 14 4 13.7761 4 13.5V11.5ZM13 11.5C13 11.2239 12.7761 11 12.5 11C12.2239 11 12 11.2239 12 11.5V13.5C12 13.7761 12.2239 14 12.5 14C12.7761 14 13 13.7761 13 13.5V11.5ZM6.5 7C6.22386 7 6 7.22386 6 7.5V9.5C6 9.77614 6.22386 10 6.5 10C6.77614 10 7 9.77614 7 9.5V7.5C7 7.22386 6.77614 7 6.5 7ZM10 7.5C10 7.22386 9.77614 7 9.5 7C9.22386 7 9 7.22386 9 7.5V9.5C9 9.77614 9.22386 10 9.5 10C9.77614 10 10 9.77614 10 9.5V7.5ZM7 15H9V13H7V15Z"
                            fill="#9CA3AF" />
                    </svg>
                    <p class="text-xs leading-4 text-gray-700 truncate">Bulk Report To TCSI</p>
                </button>
                <button type="button" class="bulkAssignBatchBtn btn-secondary" disabled>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M14 6C14 5.44772 13.5523 5 13 5V11C13 12.6569 11.6569 14 10 14L5 14C5 14.5523 5.44772 15 6 15H10.2105C12.3034 15 14 13.3034 14 11.2105V6ZM4.99854 1C3.89397 1 2.99854 1.89543 2.99854 3V11C2.99854 12.1046 3.89397 13 4.99854 13L10 13C11.1046 13 12 12.1046 12 11V5.5H11.9985V4.41421C11.9985 4.01639 11.8405 3.63486 11.5592 3.35355L9.64498 1.43934C9.36368 1.15804 8.98215 1 8.58432 1H4.99854ZM10 12L4.99854 12C4.44625 12 3.99854 11.5523 3.99854 11V3C3.99854 2.44772 4.44625 2 4.99854 2H7.99854V3.5C7.99854 4.32843 8.67011 5 9.49854 5H10.9985V6.06135L11 6.06102V11C11 11.5523 10.5523 12 10 12ZM10.7914 4H9.49854C9.22239 4 8.99854 3.77614 8.99854 3.5V2.20711L10.7914 4Z"
                            fill="#9CA3AF" />
                    </svg>
                    <p class="text-xs leading-4 text-gray-700 truncate">Bulk Assign Batch </p>
                </button>
                <button type="button" class="bulkUpdateUnitOutcome btn-secondary" disabled>
                    <svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M1.75 7.4375C1.75 7.19588 1.94588 7 2.1875 7C2.42912 7 2.625 7.19588 2.625 7.4375C2.625 7.67912 2.42912 7.875 2.1875 7.875C1.94588 7.875 1.75 7.67912 1.75 7.4375ZM2.1875 8.75C1.94588 8.75 1.75 8.94588 1.75 9.1875C1.75 9.42912 1.94588 9.625 2.1875 9.625C2.42912 9.625 2.625 9.42912 2.625 9.1875C2.625 8.94588 2.42912 8.75 2.1875 8.75ZM1.75 10.9375C1.75 10.6959 1.94588 10.5 2.1875 10.5C2.42912 10.5 2.625 10.6959 2.625 10.9375C2.625 11.1791 2.42912 11.375 2.1875 11.375C1.94588 11.375 1.75 11.1791 1.75 10.9375ZM3.9375 7C3.69588 7 3.5 7.19588 3.5 7.4375C3.5 7.67912 3.69588 7.875 3.9375 7.875H8.3125C8.55412 7.875 8.75 7.67912 8.75 7.4375C8.75 7.19588 8.55412 7 8.3125 7H3.9375ZM3.5 9.1875C3.5 8.94588 3.69588 8.75 3.9375 8.75H8.3125C8.55412 8.75 8.75 8.94588 8.75 9.1875C8.75 9.42912 8.55412 9.625 8.3125 9.625H3.9375C3.69588 9.625 3.5 9.42912 3.5 9.1875ZM3.9375 10.5C3.69588 10.5 3.5 10.6959 3.5 10.9375C3.5 11.1791 3.69588 11.375 3.9375 11.375H8.3125C8.55412 11.375 8.75 11.1791 8.75 10.9375C8.75 10.6959 8.55412 10.5 8.3125 10.5H3.9375ZM1.75 0C0.783502 0 0 0.783502 0 1.75V12.25C0 13.2165 0.783502 14 1.75 14H8.75C9.7165 14 10.5 13.2165 10.5 12.25V4.73744C10.5 4.38934 10.3617 4.0555 10.1156 3.80936L6.69064 0.384422C6.4445 0.138281 6.11066 0 5.76256 0H1.75ZM0.875 1.75C0.875 1.26675 1.26675 0.875 1.75 0.875H5.25V3.9375C5.25 4.66237 5.83763 5.25 6.5625 5.25H9.625V12.25C9.625 12.7332 9.23325 13.125 8.75 13.125H1.75C1.26675 13.125 0.875 12.7332 0.875 12.25V1.75ZM9.44378 4.375H6.5625C6.32088 4.375 6.125 4.17912 6.125 3.9375V1.05622L9.44378 4.375Z"
                            fill="#9CA3AF" />
                    </svg>
                    <p class="text-xs leading-4 text-gray-700 truncate">Bulk Update Unit Outcome</p>
                </button>
                <button type="button" class="bulkUpdateUnitAvetmissOutcome btn-secondary" disabled>
                    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M4.99952 2C4.99952 0.89543 5.89495 0 6.99952 0C8.10403 0 8.99943 0.895343 8.99952 1.99984C8.99952 1.99979 8.99952 1.99989 8.99952 1.99984L10.5 2.00009C11.3284 2.00009 12 2.67165 12 3.50007L12 6.00016L11.0002 6.00048C10.4479 6.00048 10 6.4482 10 7.00048C10 7.55277 10.4477 8.00048 11 8.00048H12L12.0001 10.5001C12.0001 11.3285 11.3285 12.0001 10.5001 12.0001H9.00048C9.00043 13.1046 8.10502 14 7.00048 14C5.89597 14 5.00057 13.1047 5.00048 12.0002C5.00048 12.0001 5.00048 12.0002 5.00048 12.0002L3.50009 12.0001C2.67167 12.0001 2.00009 11.3285 2.00009 10.5001V9.00048C0.895523 9.00048 0 8.10505 0 7.00048C0 5.89597 0.895343 5.00057 1.99984 5.00048C1.99979 5.00048 1.99989 5.00048 1.99984 5.00048L2.00009 3.50009C2.00009 2.67167 2.67167 2.00009 3.50009 2.00009L4.99952 2ZM6.99952 1C6.44723 1 5.99952 1.44772 5.99952 2V3.00009H3.50009C3.22395 3.00009 3.00009 3.22395 3.00009 3.50009V6.00016L2.00016 6.00048C1.44788 6.00048 1 6.4482 1 7.00048C1 7.55277 1.44772 8.00048 2 8.00048H3.00009V10.5001C3.00009 10.7762 3.22395 11.0001 3.50009 11.0001H6.00016L6.00048 11.9998C6.00048 12.5521 6.4482 13 7.00048 13C7.55277 13 8.00048 12.5523 8.00048 12V11.0001H10.5001C10.7762 11.0001 11.0001 10.7762 11.0001 10.5001L11.0001 9.00048C9.89549 9.00048 9 8.10505 9 7.00048C9 5.89597 9.89534 5.00057 10.9998 5.00048C10.9998 5.00048 10.9999 5.00048 10.9998 5.00048L11 3.50009C11 3.22395 10.7761 3.00009 10.5 3.00009H7.99984L7.99952 2.00016C7.99952 1.44788 7.5518 1 6.99952 1Z"
                            fill="#9CA3AF" />
                    </svg>
                    <p class="text-xs leading-4 text-gray-700 truncate">Bulk Update Unit Avetmiss Value</p>
                </button>
                <button type="button" class="bulkUpdateResultCourse btn-secondary" disabled>
                    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M4.99952 2C4.99952 0.89543 5.89495 0 6.99952 0C8.10403 0 8.99943 0.895343 8.99952 1.99984C8.99952 1.99979 8.99952 1.99989 8.99952 1.99984L10.5 2.00009C11.3284 2.00009 12 2.67165 12 3.50007L12 6.00016L11.0002 6.00048C10.4479 6.00048 10 6.4482 10 7.00048C10 7.55277 10.4477 8.00048 11 8.00048H12L12.0001 10.5001C12.0001 11.3285 11.3285 12.0001 10.5001 12.0001H9.00048C9.00043 13.1046 8.10502 14 7.00048 14C5.89597 14 5.00057 13.1047 5.00048 12.0002C5.00048 12.0001 5.00048 12.0002 5.00048 12.0002L3.50009 12.0001C2.67167 12.0001 2.00009 11.3285 2.00009 10.5001V9.00048C0.895523 9.00048 0 8.10505 0 7.00048C0 5.89597 0.895343 5.00057 1.99984 5.00048C1.99979 5.00048 1.99989 5.00048 1.99984 5.00048L2.00009 3.50009C2.00009 2.67167 2.67167 2.00009 3.50009 2.00009L4.99952 2ZM6.99952 1C6.44723 1 5.99952 1.44772 5.99952 2V3.00009H3.50009C3.22395 3.00009 3.00009 3.22395 3.00009 3.50009V6.00016L2.00016 6.00048C1.44788 6.00048 1 6.4482 1 7.00048C1 7.55277 1.44772 8.00048 2 8.00048H3.00009V10.5001C3.00009 10.7762 3.22395 11.0001 3.50009 11.0001H6.00016L6.00048 11.9998C6.00048 12.5521 6.4482 13 7.00048 13C7.55277 13 8.00048 12.5523 8.00048 12V11.0001H10.5001C10.7762 11.0001 11.0001 10.7762 11.0001 10.5001L11.0001 9.00048C9.89549 9.00048 9 8.10505 9 7.00048C9 5.89597 9.89534 5.00057 10.9998 5.00048C10.9998 5.00048 10.9999 5.00048 10.9998 5.00048L11 3.50009C11 3.22395 10.7761 3.00009 10.5 3.00009H7.99984L7.99952 2.00016C7.99952 1.44788 7.5518 1 6.99952 1Z"
                            fill="#9CA3AF" />
                    </svg>
                    <p class="text-xs leading-4 text-gray-700 truncate">Bulk Move Of Course Results</p>
                </button>
            </div>
            <div
                class="flex p-4 pt-2 border border-gray-200 bg-white rounded-md shadow justify-start items-center w-full">
                <div class="w-full" id="resultViewTabStrip">
                    <ul class="flex flex-wrap pb-2 resultViewTabStrip !gap-4 lg:!gap-8 !pl-0" data-tabs-toggle=""
                        role="tablist">
                        <li class="k-state-active"><span
                                class="inline-block text-sm leading-5 px-1 py-4 text-gray-500 font-medium">Unit
                                View</span></li>
                        <li><span class="inline-block text-sm leading-5 px-1 py-4 text-gray-500 font-medium">Semester
                                View</span></li>
                    </ul>
                    <div style="">
                        <div class="pt-4 space-y-4">
                            <x-v2.search-input id="searchResultUnitList" class="searchInputField h-9.5"
                                style="width: 300px" autocomplete="off" data-grid-id="resultTabUnitList"
                                placeholder="Search Unit Code Or Name">
                                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                            </x-v2.search-input>
                            <div id="resultTabUnitList" class="tw-table tw-table__row--check tw-table__action--sticky">
                            </div>
                        </div>
                    </div>
                    <div style="">
                        <div
                            class="flex flex-col py-4 space-y-4 items-start justify-start w-full resultSemesterViewForTab">
                        </div>
                    </div>
                </div>
            </div>
            <x-v2.skeleton.templates.result-part class="tw-skeleton" style="display: none;" />
        </div>
    </div>
</div>

<div id="resultUnitDetailsModal" class="bg-gray-100" style="display: none">
    <div id="resultUnitDetailsData"></div>
</div>

<script id="unitProgressbarInPercentage" type="text/html">
    <div class="w-full">
        <div class="bg-gray-200 flex h-4 overflow-hidden rounded-full text-xs w-full">
            <div style="width: #: data.totalCompletion #" class="bg-green-500 transition-all duration-500 ease-out">
            </div>
            <div style="width: #: data.failedSubjects #" class="bg-yellow-500 transition-all duration-500 ease-out">
            </div>
            <div style="width: #: data.NotStartedSubjects #" class="bg-fff-500 transition-all duration-500 ease-out">
            </div>
        </div>

        <div class="flex h-3 mt-2 overflow-hidden rounded-full text-xs w-full">
            <div style="width: #: data.totalCompletion #"
                class="flex justify-start ml-4 transition-all duration-500 ease-out">
                <svg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3.83331 0L7.29741 6H0.369211L3.83331 0Z" fill="gray" />
                </svg>
            </div>
            <div style="width: #: data.failedSubjects #"
                class="flex justify-start ml-4 transition-all duration-500 ease-out">
                <svg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3.83331 0L7.29741 6H0.369211L3.83331 0Z" fill="gray" />
                </svg>
            </div>
            <div style="width: #: data.NotStartedSubjects #"
                class="flex justify-start ml-4 transition-all duration-500 ease-out">
                <svg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3.83331 0L7.29741 6H0.369211L3.83331 0Z" fill="gray" />
                </svg>
            </div>
        </div>
        <div class="flex h-4 overflow-hidden rounded-full text-xs w-full relative gap-4">
            <div style="width: #:data.totalCompletion#"
                class="flex justify-start transition-all duration-500 ease-out text-gray-700"
                title="#: data.passCount # unit passed">#: data.passCount # <span class="text-gray-500 ml-1 truncate">
                    unit passed</span></div>
            <div style="width: #: data.failedSubjects #"
                class="flex justify-start transition-all duration-500 ease-out text-gray-700"
                title="#: data.failedCount  # unit not qualified">#: data.failedCount # <span
                    class="text-gray-500 ml-1 truncate"> unit not qualified</span></div>
            <div style="width: #: data.NotStartedSubjects #"
                class="flex justify-start transition-all duration-500 ease-out text-gray-700"
                title="#: data.totalSubject - data.passCount - data.failedCount  # unit not started">#:
                data.totalSubject - data.passCount - data.failedCount # <span class="text-gray-500 ml-1 truncate">
                    unit not started</span></div>
            <div class="absolute bottom-0 right-0 text-gray-800 flex items-center justify-start bg-white">#:
                data.totalSubject # <span class="text-gray-800 ml-1"> total units</span>
                <div id="unitProgressHelp">
                    <x-v2.icons name="icon-help" width="14" height="14" viewBox="0 0 14 14"
                        class="text-gray-400 icon-help hover:text-primary-blue-500" />
                    <span style="display: none;" id="resultHelp"></span>
                </div>
            </div>
        </div>
    </div>
</script>
<script id="unitProgressbarTooltip" type="text/html">
    <div class="space-y-1 p-2 text-xs unit-progress-tooltip-content">
        <div class="flex gap-1 items-center text-gray-700">
            <span class="w-1.5 h-1.5 rounded-full bg-green-500"></span>
            #: data.passCount # <span class="text-gray-500"> unit passed</span>
        </div>
        <div class="flex gap-1 items-center text-gray-700">
            <span class="w-1.5 h-1.5 rounded-full bg-yellow-500"></span>
            #: data.failedCount # <span class="text-gray-500"> unit not qualified</span>
        </div>
        <div class="flex gap-1 items-center text-gray-700">
            <span class="w-1.5 h-1.5 rounded-full bg-gray-300"></span>
            #: data.totalSubject - data.passCount - data.failedCount # <span class="text-gray-500"> unit not
                started</span>
        </div>
    </div>
</script>

<script id="resultUnitDetailsTemplate" type="text/html">
    <div class="flex h-auto min-h-screen w-full flex-col gap-6 bg-gray-100 p-6">
        <div class="px-6 py-1 bg-white border rounded-lg border-gray-200">
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Student Name">
                <span class="text-gray-7000">#: data.student_name #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Unit Code">
                <span class="text-gray-7000">#: data.unit_code #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Unit Name">
                <span class="text-gray-7000">#: data.unit_name #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Batch">
                <span class="text-gray-7000">#: data.batch #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Semester/Term">
                <span class="text-gray-7000">#: data.semester_term #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Trainer Name">
                <span class="text-gray-7000">#: data.teacher_name #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Start Date">
                <span class="text-gray-7000">#: data.start_date_detail #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Finish Date">
                <span class="text-gray-7000">#: data.finish_date_detail #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Final Outcome">
                <div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'>
                    <span class='text-xs leading-5 font-normal text-green-800 action-div'>
                        # if(isHigherEd){ #
                        #: data.grade #
                        # } else { #
                        #: data.final_outcome #
                        # } #
                    </span>
                </div>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal" class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center"
                label="Last Updated By">
                <span class="text-gray-7000">#: data.user_name #</span>
            </x-v2.label-value>
            <x-v2.label-value class:value="font-normal"
                class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center shadow-none" label="Last Updated On">
                <span class="text-gray-7000">#: data.updated_at #</span>
            </x-v2.label-value>
        </div>
        <div class="space-y-4">
            <h2 class="font-medium text-base text-gray-800">Associated assessment</h2>
            # if(assessmentData.length > 0){ #
            <!-- <div class="inline-flex flex-col items-start justify-start px-3 py-2 bg-white border rounded-lg border-gray-200 w-full mt-4">
            <div class="text-gray-700 text-xl font-semibold leading-loose">Assessment</div> -->
            # for(let i=0; i < assessmentData.length; i++) { # <div class="transition space-y-1">
                <div class="accordion-header cursor-pointer transition flex space-x-2 items-center h-9">
                    <i class="fas fa-plus text-gray-400"></i>
                    <h3 class="text-gray-900">#= assessmentData[i]['task_name'] #</h3>
                </div>
                <div class="accordion-contentv2">
                    <div class="px-6 py-1 bg-white border rounded-lg border-gray-200">
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center" label="Outcome">
                            <div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'>
                                <span
                                    class='flex items-center text-xs leading-5 font-normal text-green-800 action-div'>#:
                                    assessmentData[i].competency #</span>
                            </div>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center" label="Assignment Due Date">
                            <span class="text-gray-7000">#: assessmentData[i].due_date #</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center" label="Batch">
                            <span class="text-gray-7000">#: assessmentData[i].batch #</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center shadow-none"
                            label="Last Modified By">
                            <span class="text-gray-7000">#: assessmentData[i].updated_by_name # at #:
                                assessmentData[i].uploaded_date #</span>
                        </x-v2.label-value>
                    </div>
                </div>
        </div>
        # } #
        <!-- </div> -->
        # } #
    </div>
    </div>
</script>

<script id="resultSemesterViewTemplate" type="text/html">
    # if(data.length > 0){ #
    # for(let i=0; i < data.length; i++) { # <div class="bg-white border rounded-md border-gray-200 w-full"
        data-grid-id="courseResult#= data[i]['id']#">
        <button class="accordionResult inline-flex items-start justify-start p-4 w-full"
            data-semester="#= data[i]['semester_id'] #" data-term="#= data[i]['term'] #"
            data-student-course-id="#= data[i]['student_course_id'] #" data-grid-id="courseResult#= data[i]['id']#">
            <div class="inline-flex flex-col space-y-4 items-center justify-center w-full">
                <div class="inline-flex items-center justify-between w-full">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-base font-medium leading-normal text-gray-900">#= data[i]['semester_term'] #</p>
                    </div>
                    <span class="k-icon k-i-arrow-60-down"></span>
                </div>
            </div>
        </button>
        <div class="accordion-contentv2">
            <div class="courseResult#= data[i]['id']#">

            </div>
        </div>
        </div>
        # } #
        # } #
</script>

<script id="assessmentsTemplate" type="text/html">
    <div class="resultUnitListExpandDetailDiv">

    </div>
</script>

<script id="semesterAssessmentsTemplate" type="text/html">
    <div class="resultSemesterListExpandDetailDiv">

    </div>
</script>

<script id="resultSemesterViewTemplateForTab" type="text/html">
    # if(data.length > 0){ #
    # for(let i=0; i < data.length; i++) { # <div class="bg-white rounded-md w-full"
        data-grid-id="courseResultForTab#= data[i]['id']#">
        <button class="accordionResultForTab p-4 w-full border rounded-t-md border-gray-200"
            data-semester="#= data[i]['semester_id'] #" data-term="#= data[i]['term'] #"
            data-student-course-id="#= data[i]['student_course_id'] #"
            data-grid-id="courseResultForTab#= data[i]['id']#">
            <div class="inline-flex flex-col space-y-4 items-center justify-center w-full">
                <div class="inline-flex items-center justify-between w-full">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-base font-medium leading-normal text-gray-900">#= data[i]['semester_term'] #</p>
                    </div>
                    <span class="k-icon k-i-arrow-60-down"></span>
                </div>
            </div>
        </button>
        <div class="accordion-contentv2">
            <div
                class="courseResultForTab#= data[i]['id']# tw-table tw-table__header--borderless tw-table__hierarchy--expand-hide">

            </div>
        </div>
        </div>
        # } #
        # } #
</script>

<script id="unitActionTemplate" type="text/html">
    <div class="action-menu z-10">
        <div class="space-y-1 px-1 py-2">
            <button type="button" data-id="#=id#" data-batch="#= batch #" data-unit_id="#= unit_id#"
                data-student_id="#= student_id#" data-subject_id="#=subject_id#" data-course_id="#=course_id#"
                class="viewUnitDetails flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <span class="k-icon k-i-eye k-icon-eye grow-1 shrink-0" style="color: gray; "></span>
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">View Detail</span>
            </button>
            # if (is_higher_ed) { #
            <button type="button" data-id="#=id#" data-subject_id="#=subject_id#" data-course_id="#=course_id#"
                data-unit-code="#=unit_code#" data-unit-name="#=unit_name#"
                class="reportToTcsi flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/Building_Government.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Report to TCSI</span>
            </button>
            # } #
            <button type="button" data-id="#=id#" data-semester_id="#=semester_id#" data-subject_id="#=subject_id#"
                data-course_id="#=course_id#" data-is_course_complete="#=is_course_complete#"
                class="updateAssignBatchBtn flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/update_subject_outcome.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Assign Batch</span>
            </button>
            <button type="button" data-id="#=id#" data-unit_code="#=unit_code#" data-semester_id="#=semester_id#"
                data-subject_id="#=subject_id#" data-course_id="#=course_id#"
                data-is_course_complete="#=is_course_complete#"
                class="updateUnitOutCome flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/upfront-fee.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Update Unit Outcome</span>
            </button>
            <button type="button" data-id="#=id#" data-semester_id="#=semester_id#" data-subject_id="#=subject_id#"
                data-course_id="#=course_id#" data-unit-code="#=unit_code#" data-unit-name="#=unit_name#"
                class="updateUnitAvetmiss flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/mail.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Update Unit AVETMISS
                    Value</span>
            </button>
            <button type="button" data-id="#=id#"
                class="generateSOAForUnit flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/offer-check.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Generate Statement of
                    Attainment (SOA)</span>
            </button>
            <button type="button" data-id="#=id#"
                class="updateResultCourse flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/offer-check.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Move Courses Result</span>
            </button>
        <button type="button" data-id="#=id#" data-semester_id="#=semester_id#" data-subject_id="#=subject_id#"  data-course_id="#=course_id#" data-is_course_complete="#=is_course_complete#"  class="transferToAnotherCourse flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
            <img src="{{ asset('v2/img/offer-check.svg') }}" class="w-4 h-4" alt=" ">
            <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Transfer To Another Course</span>
        </button>
        </div>
    </div>
</script>

<script id="semesterActionTemplate" type="text/html">
    <div class="action-menu z-10">
        <div class="space-y-1 px-1 py-2">
            # if (is_higher_ed) { #
            <button type="button" data-id="#=id#" data-subject_id="#=subject_id#" data-course_id="#=course_id#"
                class="reportToTcsi flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/Building_Government.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Report to TCSI</span>
            </button>
            #}#
            <button type="button" data-id="#=id#" data-semester_id="#=semester_id#" data-subject_id="#=subject_id#"
                data-course_id="#=course_id#" data-is_course_complete="#=is_course_complete#"
                class="updateAssignBatchBtn flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/update_subject_outcome.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Assign batch</span>
            </button>
            <button type="button" data-id="#=id#" data-unit_code="#=unit_code#" data-semester_id="#=semester_id#"
                data-subject_id="#=subject_id#" data-is_course_complete="#=is_course_complete#"
                data-course_id="#=course_id#"
                class="updateUnitOutCome flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/upfront-fee.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Update Unit Outcome</span>
            </button>
            <button type="button" data-id="#=id#" data-semester_id="#=semester_id#" data-subject_id="#=subject_id#"
                data-course_id="#=course_id#"
                class="updateUnitAvetmiss flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/mail.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Update Unit AVETMISS
                    Value</span>
            </button>
            <button type="button" data-id="#=id#"
                class=" flex w-full cursor-pointer items-center justify-start space-x-2 rounded px-2 py-1 text-left hover:bg-gray-100">
                <img src="{{ asset('v2/img/offer-check.svg') }}" class="w-4 h-4" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Setup Assessment Marking</span>
            </button>

        </div>
    </div>
</script>

<div id="updateHigherEdUnitOutComeModal" style="display: none;">
    <div class="items-start justify-start mb-10 mt-2 p-4 space-x-6 w-full" id="">
        <input id="finalOutcomeId" name="id" value="" hidden />
        <form id="updateHigherEdUnitOutComeForm"></form>
    </div>
</div>

<div id="updateResultCourseModal" class="p-0" style="display: none;">
    <div class="items-start justify-start space-x-6 w-full" id="">
        <input id="enrollmentId" name="id" value="" hidden />
        <form id="updateResultCourseForm"></form>
    </div>
</div>

<div id="reportToTCSIModal" style="display: none;">
    <div class="bg-gray-50 p-6 pb-20">
        <div class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full selectedUnitsDiv mb-5"
            style="display: none">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="text-gray-500 text-sm font-medium leading-snug tracking-tight w-48 whitespace-nowrap">
                        Selected Units for Update</div>
                    <div id="selectedUnitsList"></div>
                </div>
            </div>
        </div>
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Update TCSI info for selected unit</p>
                    </div>
                </div>
            </div>
            <form id="tcsiStudentUnitEnrollmentInformation" class="w-full h-full "></form>
        </div>
    </div>
</div>

<div id="assignBatchModal" style="display: none;">
    <div class="bg-gray-50 p-6 pb-20">
        <div class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full selectedUnitsDiv"
            style="display: none">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="text-gray-500 text-sm font-medium leading-snug tracking-tight">Selected
                    Units for Bulk Update</div>
                <div class="inline-flex items-center justify-between">
                    <div id="selectedUnitsListForSubjectOutCome"></div>
                </div>
            </div>
        </div>
        <div id="studentInfoSubjectOutcome"></div>
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full mb-10">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Assign Batch & Update AVETMISS
                            Information</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div class="flex">
                <span class="flex items-center justify-center px-2 py-1 ml-1 bg-green-100 rounded">
                    <p class="text-xs leading-4 text-center text-green-800">Subject</p>
                </span>
                <span id="subjectNameLable" class="ml-1"></span>
            </div>
            <div class="flex mb-2">
                <span class="w-16 flex items-center justify-center px-2 py-1 ml-1 bg-green-100 rounded">
                    <p class="text-xs leading-4 text-center text-green-800">Unit</p>
                </span>
                <span id="unitNameLable" class="ml-1"></span>
            </div>
            <input id="subject_id" hidden>
            <form id="assignBatchInformationForm" class="w-full h-full "></form>
        </div>
    </div>
</div>
<div id="transferToAnotherCourseModal" style="display: none;">
    <div class="bg-gray-50 p-6 pb-20">
        <div class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full selectedUnitsDiv"
            style="display: none">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="text-gray-500 text-sm font-medium leading-snug tracking-tight">Selected
                    Units for Bulk Update</div>
                <div class="inline-flex items-center justify-between">
                    <div id="selectedUnitsListForTransferToAnotherCourse"></div>
                </div>
            </div>
        </div>
        <div id="studentInfoTransferToAnotherCourse"></div>
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full mb-10">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Transfer unit result to another course</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div class="flex">
                <span class="flex items-center justify-center px-2 py-1 ml-1 bg-green-100 rounded">
                    <p class="text-xs leading-4 text-center text-green-800">Subject</p>
                </span>
                <span id="subjectNameLableTransferToAnotherCourse" class="ml-1"></span>
            </div>
            <div class="flex mb-2">
                <span class="w-16 flex items-center justify-center px-2 py-1 ml-1 bg-green-100 rounded">
                    <p class="text-xs leading-4 text-center text-green-800">Unit</p>
                </span>
                <span id="unitNameLableTransferToAnotherCourse" class="ml-1"></span>
            </div>
            <div class="flex mb-2">
                <span class="w-16 flex items-center justify-center px-2 py-1 ml-1 bg-green-100 rounded">
                    <p class="text-xs leading-4 text-center text-green-800">Attempt</p>
                </span>
                <span id="" class="ml-1">2</span>
            </div>
            <div class="flex mb-2">
                <span class="w-24 flex items-center justify-center px-2 py-1 ml-1 bg-green-100 rounded">
                    <p class="text-xs leading-4 text-center text-green-800">Final Outcome</p>
                </span>
                <span id="finalOutcomeLableTransferToAnotherCourse" class="ml-1"></span>
            </div>
            <input id="subject_id" hidden>
            <form id="transferToAnotherCourseInformationForm" class="w-full h-full "></form>
        </div>
    </div>
</div>
<div id="bulkTransferToAnotherCourseModal" style="display: none;">
    <div class="bg-gray-50 p-6 pb-20">
        <div class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full selectedUnitsDiv"
            style="display: none">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="text-gray-500 text-sm font-medium leading-snug tracking-tight">Selected
                    Units for Bulk Update</div>
                <div class="inline-flex items-center justify-between">
                    <div id="selectedUnitsListForTransferToAnotherCourse"></div>
                </div>
            </div>
        </div>
        <div id="studentInfoTransferToAnotherCourse"></div>
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full mb-10">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Transfer unit result to another course</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="text-gray-500 text-sm font-medium leading-snug tracking-tight w-48 whitespace-nowrap">
                        Selected Units for Update:-</div>
                   <div class="flex-col" id="unitList">
                </div>
            </div>


            </div>
           <div class="items-start justify-start space-x-6 w-full" id="">
                <input id="enrollmentId" name="id" value="" hidden />
                <form id="updateBulkResultCourseForm"></form>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="unitListTemplate">
    # console.log(data.selectedUnits) #
    # for (let i = 0; i < data.selectedUnits.length; i++) { #
    #console.log('here'); #
        # let unitCode = data.selectedUnits[i]['unitCode']; #
        # let unitName = data.selectedUnits[i]['unitName']; #
        # let unitId = data.selectedUnits[i]['id']; #
        # let unitLabel = getUnitLabelTextFormat(unitCode, unitName); #
        # console.log(unitLabel) #

                <div class="inline-flex items-center justify-center space-x-2 px-2 py-1 m-1 bg-gray-100 rounded-lg">
                    <span class="text-xs leading-none text-center text-gray-800">${unitLabel}</span>
                </div>
        # } #


</script>

<div id="unitOutComeModal" style="display: none;">
    <div class="bg-gray-50 p-6 pb-20">
        <div id="studentInfoUnitOutcome">

        </div>
        <div class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full selectedUnitsDiv"
            style="display: none">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="text-gray-500 text-sm font-medium leading-snug tracking-tight">Selected
                    Units for Bulk Update</div>
                <div class="inline-flex items-center justify-between">
                    <div id="selectedUnitsListForUnitOutCome"></div>
                </div>
            </div>
        </div>
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Update Unit : <span
                                id="unit_code"></span></p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <form id="unitOutComeInformation" class="w-full h-full "></form>
        </div>
    </div>
</div>

<div id="unitAvetmissModal" style="display: none;">
    <div class="bg-gray-50 p-6 pb-20">
        <div class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full selectedAvetmissUnitsDiv mb-5"
            style="display: none">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="text-gray-500 text-sm font-medium leading-snug tracking-tight w-48 whitespace-nowrap">
                        Selected Units for Update</div>
                    <div id="selectedUnitsListForAvetmiss"></div>
                </div>
            </div>
        </div>
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full">
            <form id="unitAvetmissInformation" class="w-full h-full "></form>
        </div>
    </div>
</div>

<div id="bulkAssignBatchModal">
    <div id="studentInfoBulkUpdateAssignBatch"></div>
    <div
        class="inline-flex flex-col space-y-6 items-start justify-start p-5 bg-white border rounded-lg border-gray-200 w-full mb-10">
        <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
            <div class="inline-flex items-center justify-between">
                <div class="flex space-x-2 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-900">Units</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                </div>
            </div>
        </div>
        <div id="selectedUnitSubjectGrid" class="w-full h-full "></div>
    </div>
</div>

<div id="bulkUpdateUnitOutcomeModal">
    <div id="studentInfoBulkUpdateUnitOutcome"></div>
    <div
        class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 bg-white border rounded-lg border-gray-200 w-full mb-10">
        <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
            <div class="inline-flex items-center justify-between">
                <div class="flex space-x-2 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-900">Units</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                </div>
            </div>
        </div>
        <div id="selectedUnitSubjectGrid" class="w-full h-full "></div>
    </div>
</div>

<script id="commonDivForStudentInfo" type="text/html">
    <div
        class="inline-flex space-x-6 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full mt-2 mb-10">
        <div class="flex space-x-6 items-start justify-start w-full">
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Student Name</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: data.full_name #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">STATUS</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: data.status #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Student ID</p>
                <p class="w-full text-sm leading-5 text-gray-700">#: data.generated_stud_id #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Start Date</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: data.start_date #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Finish Date</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: data.finish_date #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Campus</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: data.campus_name #</p>
            </div>
        </div>
    </div>
</script>

<style>
    .resultSemesterListExpandDetailDiv .k-grid-content {
        height: auto !important;
    }
</style>

<div id="deleteSemesterUnitModal"></div>

<div id="assessmentSyncToMoodleFromResultModal"></div>

<div id="unitGradeSyncFromMoodleModal"></div>

@include('v2.sadmin.student.templates.course.current-course-summary')