/* Kendo Grid header*/
.k-grid {
    border: none;
}

.k-grid-header .k-grid-filter.k-state-active,
.k-grid-header .k-header-column-menu.k-state-active,
.k-grid-header .k-hierarchy-cell .k-icon.k-state-active {
    background-color: transparent !important;
}

/*checkbox */

.k-checkbox {
    border-radius: 4px;
}

.k-grid td > .k-checkbox {
    vertical-align: inherit;
}
/* 
.k-grid-header th .k-checkbox {
    margin-left: 3px !important;
} */

.k-checkbox:checked {
    border-color: var(--color-primary-blue-500);
    color: #fff;
    background-color: var(--color-primary-blue-500);
}

/* td */

.k-grid tr.k-state-selected > td {
    background-color: #e6f7ff;
}

.demo-section .tabstrip-container {
    width: 500px;
}

#resultTabUnitList {
    &.k-grid tr > td:nth-child(2) {
        // border-width: 0px 0 1px 4px !important;
        border-left-color: transparent;
    }

    &.k-grid tr.k-state-selected > td:nth-child(2) {
        border-left-color: var(--color-primary-blue-500);
    }

    .k-grid-header th.k-header:nth-child(2) {
        padding-left: 0.7rem !important;
    }
}

#studentList,
#orientationList,
#studentPlacementList {
    &.k-grid tr > td:nth-child(1) {
        .k-icon {
            display: none;
        }
    }
    &.k-grid tr > td:nth-child(1) {
        border-width: 0px 0 1px 4px !important;
        border-left-color: transparent;
    }

    &.k-grid tr.k-state-selected > td:nth-child(1) {
        border-left-color: var(--color-primary-blue-500);
    }

    .k-grid-header th.k-header:nth-child(1) {
        padding-left: calc(0.7rem + 4px) !important;
    }

    &.k-grid .k-grid-header .k-grid-filter {
        top: 50%;
        transform: translateY(-23%);
    }

    &.k-grid .k-grid-content tr td.tw-sticky-cell {
        position: sticky;
        right: 0;
    }

    &.k-grid .k-grid-header tr th.tw-sticky-header {
        border-left: 0;
    }
}

#resultTabUnitList {
    &.k-grid tr > td:nth-child(5) {
        padding-inline: 0.25rem;
        img {
            width: 0.625rem;
            height: 0.625rem;
            margin-inline: auto;
        }
    }

    .k-grid-header th.k-header:nth-child(2) {
        padding-inline: 0.25rem;
    }

    .k-datepicker:has(
            > .k-picker-wrap.k-state-focused,
            > .k-picker-wrap.k-state-focus,
            > .k-picker-wrap:focus
        ) {
        box-shadow:
            0px -2px 2px 2px rgba(24, 144, 255, 0.1),
            0px 2px 2px 2px rgba(24, 144, 255, 0.1);
        border-color: var(--color-primary-blue-500);
        border-radius: 0.5rem;
    }

    .k-dropdown .k-dropdown-wrap {
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        height: 36px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        background-color: white;

        &.k-state-focused,
        &.k-state-focus,
        &:focus {
            box-shadow:
                0px -2px 2px 2px rgba(24, 144, 255, 0.1),
                0px 2px 2px 2px rgba(24, 144, 255, 0.1);
            border-color: var(--color-primary-blue-500);
        }

        & .k-select {
            & > .k-i-arrow-60-down::before {
                margin-top: 0 !important;
            }
        }
    }
}

// #resultUnitList,
// #resultTabUnitList {
//     &.k-grid .k-command-cell {
//         .k-button {
//             min-width: 16px !important;
//             padding: 0.25rem 0 0.25rem 0 !important;
//             border: none !important;
//         }
//     }
// }

.k-scheduler-monthview .k-scheduler-content .k-scheduler-table tr {
    height: 130px !important;
}

.tw-table-alternate {
    &__row {
        &:nth-child(even) {
            background-color: white;
        }
    }
}

.tw-table {
    &__empty {
        // .k-grid-content {
        //     overflow: hidden;
        // }
    }
    &__empty-card {
        max-width: calc(100vw - 3.75rem);
        @media (screen(md)) {
            max-width: calc(100vw - 21.5rem);
        }
    }
    .k-grid .k-table-tbody > .k-table-row:not(.k-grid-norecords):hover td {
        background-color: var(--color-bluegray-100);
    }
    &__hierarchy {
        &--expand-hide {
            &.k-grid {
                .k-hierarchy-cell {
                    .k-icon::before {
                        display: none;
                    }
                }
            }
        }
    }

    &__row-hover {
        .k-grid tr:hover td.hover-visible {
            opacity: 0.7;
        }
    }

    &__overlay {
        position: relative;
        &::after {
            content: '';
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(24, 144, 255, 0.1);
        }
    }

    &__compact {
        &.k-grid td,
        &.k-grid td {
            padding-block: 0.25rem;
        }
    }

    &__rows {
        &--striped {
            & .k-grid.k-grid-md {
                .k-table .k-table-tbody .k-master-row.k-table-row {
                    &.k-alt.k-table-alt-row {
                        background-color: var(--color-gray-50);
                        & > td {
                            background-color: var(--color-gray-50);
                        }
                        &:hover {
                            & > td {
                                background-color: var(--color-bluegray-100);
                            }
                        }
                    }

                    &.k-selected,
                    &.k-selected:hover {
                        background-color: #e6f7ff !important;
                        & > td {
                            background-color: #e6f7ff !important;
                        }
                    }
                }
            }
        }

        &--hoverable {
        }

        &--check {
            &,
            &.k-grid {
                tr > td:nth-child(1) {
                    // border-width: 0px 0 1px 4px !important;
                    border-left-color: transparent;
                    .k-icon {
                        display: none;
                    }
                }
                tr.k-state-selected,
                tr.k-selected {
                    & > td:nth-child(1) {
                        border-left-color: var(--color-primary-blue-500);
                    }
                }
            }

            .k-grid-header th.k-header:nth-child(1) {
                padding-left: 0.7rem !important;
                font-size: 1rem !important;
                line-height: 1.4;
                justify-content: center;
            }

            .k-grid.k-grid-md .k-cell-inner > .k-link {
                justify-content: flex-start;
            }

            .k-grid.k-grid-md .k-grid-header th.k-header:nth-child(1) {
                padding-left: 1rem !important;
            }

            &--gap {
                .k-grid.k-grid-md .k-grid-header th.k-header:nth-child(1) {
                    padding-left: 1.8rem !important;
                }

                .k-grid td:nth-child(1) {
                    padding-left: calc(2rem - 4px);
                }
            }
        }
    }

    &__bordered {
        &--rounded {
            border: 1px solid var(--color-gray-200);
            border-radius: 0.5rem;
            overflow: hidden;
            .k-grid-header table,
            .k-grid-content table {
                border-inline: 0;
            }
            .k-grid-header th.k-header:last-child {
                border-right-width: 0;
            }
            .k-grid-header-wrap {
                border-inline-end-width: 0;
            }
            .k-grid-header th.k-header {
                border-width: 0 1px 1px 0;
            }
        }
    }

    &__relaxed {
    }

    &__header {
        &--bordered {
        }
        &--borderless {
            .k-filter-row th,
            .k-grid-header th.k-header {
                border-width: 1px 0 1px 0;
            }
        }
    }

    &__cell {
        &--center {
        }

        &--bordered {
            .k-grid td {
                border: 0 0 1px 1px !important;
            }
        }
    }

    &__header-cell {
        display: flex !important;
        justify-content: center;
        align-items: center;
    }

    &__borderless {
        .k-grid-header table,
        .k-grid-content table {
            border-inline: 0;
        }
        .k-grid-header th.k-header:last-child {
            border-right-width: 0;
        }
        .k-grid-header-wrap {
            border-inline-end-width: 0;
        }

        .k-filter-row th,
        .k-grid-header th.k-header {
            border-width: 0 0 1px 0;
        }
    }

    &__pager {
        .k-grid-pager.k-pager-md {
            .k-pager-sizes {
                visibility: hidden;
                .k-dropdownlist {
                    visibility: visible;
                    height: 2.25rem;
                }
            }
            .k-pager-info {
                flex: 0 1 auto;
                margin-right: auto;
                display: none;
            }
        }
        &--nospace {
            .k-pager-wrap {
                padding: 0.75rem 0 0;
            }
        }
    }

    .custom-select {
        .k-grid-pager {
            justify-content: space-between !important;
        }
    }

    &__action {
        &--sticky {
            .k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row,
            &.k-grid .k-master-row {
                &:hover {
                    td.k-command-cell,
                    td:last-child {
                        position: sticky;
                        right: 0;
                    }
                }
            }
            &.k-grid .k-grid-content tr td.tw-sticky-cell {
                position: sticky;
                right: 0;
            }

            &.k-grid .k-grid-header tr th.tw-sticky-header {
                border-left: 0;
            }

            // .k-grid-header .k-table-th {
            //     &:has(+ .k-grid-header-sticky) {
            //         box-shadow: inset -4px 0 4px -4px rgba(0, 0, 0, 0.2);
            //     }
            // }
            // .k-grid-content .k-table-td {
            //     &:has(+ .k-grid-content-sticky) {
            //         box-shadow: inset -4px 0 4px -4px rgba(0, 0, 0, 0.2);
            //     }
            // }
            .k-grid-header .k-table-th.k-grid-header-sticky,
            .k-grid-header .k-filter-row .k-grid-header-sticky,
            .k-grid .k-grid-content-sticky,
            .k-grid .k-grid-row-sticky,
            .k-grid .k-grid-footer-sticky {
                right: 0;

                &::before {
                    content: '';
                    position: absolute;
                    left: -0.75rem;
                    top: 0;
                    height: 100%;
                    width: 1rem;
                    box-shadow: inset -4px 0 4px -4px rgba(0, 0, 0, 0.2);
                }
            }
        }
    }

    &__sortable {
        .k-grid .k-cell-inner > .k-link > .k-sort-icon {
            display: none;
        }
    }
}

.tw-action {
    width: fit-content;
    margin: auto;
}

// .tw-action--autohide,
// .k-command-cell > div {
//     opacity: 0;
//     // visibility: hidden;
// }

// .k-grid tr:hover td {
//     .tw-action--autohide,
//     &.k-command-cell > div {
//         opacity: 1;
//         // visibility: visible;
//     }
// }

.k-grid {
    .tw-btn-action {
        color: var(--color-gray-400);
    }
}

.tw-action > a {
    &:is(:hover, :focus) {
        background-color: white;
    }
}

#markAttendanceGrid {
    .k-column-title {
        width: 100%;
    }
}

.k-grid {
    .k-grid-header {
        .k-header {
            .k-link.tw-sort-icon {
                &::after {
                    content: '';
                    background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.14645 6.35355C4.34171 6.54882 4.65829 6.54882 4.85355 6.35355L8 3.20711L11.1464 6.35355C11.3417 6.54882 11.6583 6.54882 11.8536 6.35355C12.0488 6.15829 12.0488 5.84171 11.8536 5.64645L8.35355 2.14645C8.15829 1.95118 7.84171 1.95118 7.64645 2.14645L4.14645 5.64645C3.95118 5.84171 3.95118 6.15829 4.14645 6.35355ZM4.14645 9.64645C4.34171 9.45118 4.65829 9.45118 4.85355 9.64645L8 12.7929L11.1464 9.64645C11.3417 9.45118 11.6583 9.45118 11.8536 9.64645C12.0488 9.84171 12.0488 10.1583 11.8536 10.3536L8.35355 13.8536C8.15829 14.0488 7.84171 14.0488 7.64645 13.8536L4.14645 10.3536C3.95118 10.1583 3.95118 9.84171 4.14645 9.64645Z' fill='%239CA3AF'/%3E%3C/svg%3E");
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: 1rem;
                    width: 1rem;
                    height: 1rem;
                    margin-left: 0.25rem;
                }
                .k-icon {
                    &::before {
                        background-position: center;
                        background-repeat: no-repeat;
                        background-size: contain;
                        width: 0.75rem;
                        height: 0.75rem;
                    }
                    // display: none;
                    &.k-i-sort-asc-sm::before {
                        content: '';
                        background-image: url("data:image/svg+xml,%3Csvg width='8' height='12' viewBox='0 0 8 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.146447 7.64645C0.341709 7.45118 0.658291 7.45118 0.853553 7.64645L4 10.7929L7.14645 7.64645C7.34171 7.45118 7.65829 7.45118 7.85355 7.64645C8.04882 7.84171 8.04882 8.15829 7.85355 8.35355L4.35355 11.8536C4.15829 12.0488 3.84171 12.0488 3.64645 11.8536L0.146447 8.35355C-0.0488155 8.15829 -0.0488155 7.84171 0.146447 7.64645Z' fill='%239CA3AF'/%3E%3Cpath d='M0.146447 4.35355C0.341709 4.54882 0.658291 4.54882 0.853553 4.35355L4 1.20711L7.14645 4.35355C7.34171 4.54882 7.65829 4.54882 7.85355 4.35355C8.04882 4.15829 8.04882 3.84171 7.85355 3.64645L4.35355 0.146447C4.15829 -0.0488155 3.84171 -0.0488155 3.64645 0.146447L0.146447 3.64645C-0.0488155 3.84171 -0.0488155 4.15829 0.146447 4.35355Z' fill='%231890FF'/%3E%3C/svg%3E");
                    }

                    &.k-i-sort-desc-sm::before {
                        content: '';
                        background-image: url("data:image/svg+xml,%3Csvg width='8' height='12' viewBox='0 0 8 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.146447 7.64645C0.341709 7.45118 0.658291 7.45118 0.853553 7.64645L4 10.7929L7.14645 7.64645C7.34171 7.45118 7.65829 7.45118 7.85355 7.64645C8.04882 7.84171 8.04882 8.15829 7.85355 8.35355L4.35355 11.8536C4.15829 12.0488 3.84171 12.0488 3.64645 11.8536L0.146447 8.35355C-0.0488155 8.15829 -0.0488155 7.84171 0.146447 7.64645Z' fill='%231890FF'/%3E%3Cpath d='M0.146447 4.35355C0.341709 4.54882 0.658291 4.54882 0.853553 4.35355L4 1.20711L7.14645 4.35355C7.34171 4.54882 7.65829 4.54882 7.85355 4.35355C8.04882 4.15829 8.04882 3.84171 7.85355 3.64645L4.35355 0.146447C4.15829 -0.0488155 3.84171 -0.0488155 3.64645 0.146447L0.146447 3.64645C-0.0488155 3.84171 -0.0488155 4.15829 0.146447 4.35355Z' fill='%239CA3AF'/%3E%3C/svg%3E");
                    }
                }
            }
            &.k-sorted {
                .k-link.tw-sort-icon {
                    &::after {
                        background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.14645 6.35355C4.34171 6.54882 4.65829 6.54882 4.85355 6.35355L8 3.20711L11.1464 6.35355C11.3417 6.54882 11.6583 6.54882 11.8536 6.35355C12.0488 6.15829 12.0488 5.84171 11.8536 5.64645L8.35355 2.14645C8.15829 1.95118 7.84171 1.95118 7.64645 2.14645L4.14645 5.64645C3.95118 5.84171 3.95118 6.15829 4.14645 6.35355ZM4.14645 9.64645C4.34171 9.45118 4.65829 9.45118 4.85355 9.64645L8 12.7929L11.1464 9.64645C11.3417 9.45118 11.6583 9.45118 11.8536 9.64645C12.0488 9.84171 12.0488 10.1583 11.8536 10.3536L8.35355 13.8536C8.15829 14.0488 7.84171 14.0488 7.64645 13.8536L4.14645 10.3536C3.95118 10.1583 3.95118 9.84171 4.14645 9.64645Z' fill='%231890FF'/%3E%3C/svg%3E");
                    }
                }
            }
        }
    }
}

.k-grid .k-grid-header .k-header a.k-link {
    // margin-top: 1px;
}

.grid-animation {
    // display: none;
    animation: slideDown 300ms ease-in-out;
    // .k-grid-content {
    //     min-height: 120px;
    // }
    .k-grid-norecords {
        .k-font-icon {
            font-size: 3rem;
        }
        h3 {
            font-size: 1.25rem;
        }
        .space-y-6 > :not([hidden]) ~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(1rem * var(--tw-space-y-reverse));
        }
    }
}

.tw-action-button {
    .k-button.k-button-solid-base {
        border-width: 1;
        border-color: var(--color-gray-200);
        padding: 0.25rem;
        background-color: transparent;
    }
    &.k-dropdown-button {
        .k-button.k-focus,
        .k-button.k-state-focus,
        .k-button.k-state-focused,
        .k-button:focus,
        .k-button:hover {
            border-width: 1px;
            padding: 0.25rem;
            border-color: var(--color-gray-200);
            background-color: var(--color-gray-50);
        }
    }

    .k-button-text {
        display: none;
    }
}

.tw-default-table {
    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        // overflow: hidden;
        border-top: 1px solid var(--color-gray-300);
        border-left: 1px solid var(--color-gray-300);
    }
    th,
    td {
        border-right: 1px solid var(--color-gray-300);
        border-bottom: 1px solid var(--color-gray-300);
        padding: 10px 12px;
        text-align: left;
        min-height: 40px;
        line-height: 20px;
        font-weight: normal;
    }
    th:first-child {
        border-top-left-radius: 6px;
    }
    th:last-child {
        border-top-right-radius: 6px;
    }
    tr:last-child td:first-child {
        border-bottom-left-radius: 6px;
    }
    tr:last-child td:last-child {
        border-bottom-right-radius: 6px;
    }
}

.tw-parameter-modal {
    table {
        thead {
            tr {
                th {
                    min-width: 150px;
                    padding: 14px 10px;
                    text-align-last: left;
                    &:first-child {
                        min-width: 407px;
                    }
                }
            }
        }
        td {
            padding: 5px 10px;
        }
        tbody {
            tr:nth-child(even) {
                background-color: rgba(243, 244, 246, 1);
            }
        }
    }
}

#studentList,
#classNeedMarkGrid {
    &.k-grid .k-grid-header .k-header a {
        text-transform: capitalize;
        font-weight: 400;
        font-size: 0.75rem;
    }

    &.k-grid td,
    &.k-grid th,
    &.k-grid-md .k-table-th {
        padding-block: 0.25rem;
    }

    &.k-grid tr td .action-only.k-state-border-down {
        display: block;
    }

    &.k-grid .k-grid-header .k-header a.k-link {
        // margin-top: 1px;
        margin-bottom: 1px;
    }
}

.k-grid-filter svg {
    width: 12px;
    height: 12px;
}

#classNeedMarkGrid {
    .k-grid .k-grid-header .k-header a,
    .k-grid .k-grid-header .k-header {
        text-transform: capitalize;
        font-weight: 400;
        font-size: 0.75rem;
    }

    .k-grid .k-grid-header .k-header {
        &:nth-child(2) {
            border-left: 0;
        }
    }
}

#markAttendanceGrid {
    .k-grid .k-grid-header .k-header {
        min-height: 40px;
        &,
        & a {
            text-transform: capitalize;
            font-weight: 400;
            font-size: 0.75rem;
        }
    }
    .k-table-md .k-table-td {
        padding-block: 0.125rem;
    }

    .k-grid-md .k-table-th > .k-cell-inner > .k-link {
        padding-block: 0.125rem;
    }
}

#batchGrid {
    .k-grid-md .k-table-th > .k-cell-inner > .k-link {
        padding-block: 1px;
        min-height: 38px;
    }

    .k-grid-md td {
        padding-block: 0.5rem;
        &:hover {
            background-color: #f3f4f6;
        }
    }
}

.k-grid .k-grid-header .k-header.tw-grid-header__blue {
    background: var(--color-primary-blue-500);
}

.k-grid.k-grid-md .k-header.tw-center-header .k-cell-inner > .k-link {
    justify-content: center !important;
}

.k-master-row .k-grid-content-sticky.tw-grid-cell__left-border::before {
    border-left: 1px solid var(--color-gray-200);
}

.tw-table__bordered--rounded .k-grid-header th.k-header.tw-border-r-none {
    border-right: none;
}

.k-grid .k-button.k-pager-nav,
.k-grid .k-pager-numbers .k-button {
    margin: 0;
}

table.tw-custom-table {
    width: 100%;
    background-color: white;
    thead {
        tr {
            th {
                border: 1px solid var(--color-gray-200);
                min-width: 150px;
                padding: 8px;
                text-align-last: left;
            }
        }
    }
    td {
        padding: 8px;
        border: 1px solid var(--color-gray-200);
    }
    tbody {
        tr:nth-child(even) {
            background-color: rgba(243, 244, 246, 1);
        }
    }
}

.queueProcessBar .k-progress-status {
    line-height: 1;
    margin-top: 4px;
}

#defaultTimetableList,
#viewAllTimetableList,
#studentAttendanceList {
    &.k-grid td {
        padding-block: 0.25rem;
        .action-div {
            padding-left: 0;
        }
    }
}

.tw-table--tableCellHeight {
    table {
        tbody {
            tr {
                td {
                    height: 51.21px;
                    padding-block: 0 !important;
                    .k-checkbox-wrap {
                        display: block !important;
                    }
                }
            }
        }
    }
}
.tw-table--tableHeadHeight {
    table {
        thead {
            tr {
                height: 41px;
                th {
                    padding: 0.5rem 0.75rem 0.47rem !important;
                    .k-link {
                        padding-block: 11.24px !important;
                        .k-checkbox {
                            margin-bottom: 0 !important;
                        }
                    }
                }
            }
        }
    }
}

.hide-scrollbar {
    .k-grid-content {
        overflow: hidden;
        overflow-x: hidden;
    }
}

#manageSectionList,
#getLanguageList,
#offerLetterList,
#failedEmailList,
#manageReportList,
#collegeEnrollmentFeesList,
#placementProviderList,
#courseTypeList,
#courseTemplateList,
#courseUpfrontFeeList,
#coursePromotionPriceList,
#globalQueueProgressList {
    .k-pager-wrap {
        padding: 0.75rem 0 0.75rem 0.75rem !important;
    }
    .gridPagination {
        padding: 0 0.75rem 0 !important;
    }
}

.accordion-content .k-grid {
    th.k-header:last-child {
        display: block !important;
    }
}

.k-filter-menu {
    .k-action-buttons {
        justify-content: flex-start;
    }

    .k-filter-menu-container {
        .k-item {
            padding-block: 0.25rem !important;
        }

        .k-multicheck-wrap {
            max-height: 300px;
            overflow-y: auto;
        }
    }
}

#timetable-import-grid {
    .k-grid-header th.k-header > .k-link {
        text-transform: uppercase;
    }
}

.v-popper__popper {
    z-index: 1000 !important;
}
