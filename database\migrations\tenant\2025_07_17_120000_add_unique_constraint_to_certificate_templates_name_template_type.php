<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('certificate_templates', function (Blueprint $table) {
            // Add unique constraint on name and template_type combination
            $table->unique(['name', 'template_type'], 'certificate_templates_name_template_type_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('certificate_templates', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('certificate_templates_name_template_type_unique');
        });
    }
};
