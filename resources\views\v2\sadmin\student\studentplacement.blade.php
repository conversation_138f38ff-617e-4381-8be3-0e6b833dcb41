<x-v2.layouts.default :page="'page-template-placement'">

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/student-placement.css') }}">

    </x-slot>
    <style>

    </style>

    {{-- Filter panelbar template --}}
    <script id="filterPanelBarTemplate" type="text/kendo-ui-template">

        <div class="flex flex-col items-start justify-start w-full #: item.value # custom-li">

            # if (item.id != 0 && typeof item.text != 'undefined') { #
            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-#: item.id #">
                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.text #</span>
            </div>

            # } #

            # if (item.type == 'switch') { #
            <div class="flex items-center justify-center">
                <label for="switch_#: item.value.toLowerCase() #_course" class="flex items-center cursor-pointer">
                    <div class="relative">
                        <input type="checkbox" class="sr-only external-filter" role="switch" id="switch_#: item.value.toLowerCase() #_course" data-category="#: item.field #" value="#: item.value #" data-val="#: item.original #" checked/>
                        <div class="w-10 h-5 bg-gray-200 rounded-full shadow-inner outer-dot"></div>
                        <div class="dot absolute w-5 h-5 bg-white rounded-full shadow left-0 top-0 transition"></div>
                    </div>
                    <div class="ml-3 text-gray-700 font-medium">
                        #: item.value #
                    </div>
                </label>
            </div>
            # } #

            # if (item.type == 'input') { #
            <div class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
                <img src="{{ asset('v2/img/search.png') }}" class="h-4 w-4" alt="searchIcon" />
                # if(item.field == 'course'){ #
                    <input type="text" data-value="#: item.value #" class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } else { #
                    <input type="text" data-value="#: item.value #" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } #

            </div>
            # } #

            # if (item.type == 'checkbox') { #
            <div class="inline-flex space-x-2 items-center justify-start">
                <div class="form-check flex items-center">
                    <input class="k-checkbox form-check-input external-filter mt-0 f-checkbox appearance-none h-4 w-4 border border-gray-300 rounded-sm bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200  align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer" type="checkbox" value="#: item.value #" data-category="#: item.field #" data-val="#: item.original #" id="#: item.category_id #_checkbox_#: item.id #">
                    <label class="text-sm leading-5 text-gray-700 h-full ml-2" for="#: item.category_id #_checkbox_#: item.id #" data-val="#: item.original #" >
                        #: item.subtext #
                    </label>
                </div>
            </div>
            # } #
 
            # if (item.type == 'dropdown') { #
            <select class="external-filter inline-flex space-x-2 items-center w-full justify-start inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 text-gray-500 custom-option leading-6 text-sm #: item.field #" id="#: item.value #">
                # if(item.field == 'Course') { #
                    <option value="">Select Course</option>
                # } #

                # for(var i=0; i < item.arr.length; i++){ #
                    <option value="#: item.arr[i].id #" title="#: item.arr[i].text #">#: ((item.arr[i].text.length > 30) ? (item.arr[i].text.substring(0,30) + '...') : item.arr[i].text) #</option>
                # } #
            </select>
            # } #

            # if (item.type == 'button') { #
            <button class="inline-flex space-x-2 items-center justify-center w-full h-10 py-1.5 bg-white border rounded-lg border-gray-300 hover:bg-primary-blue-500 hover:text-white" id="#: item.value #">
                <span class="text-sm font-medium leading-none text-blue-500 hover:text-white">
                    <span class="k-icon k-i-plus text-blue-500 hover:text-white"></span>
                    Add Course
                </span>
            </button>
            # } #

        </div>

    </script>

    <x-v2.splitter id="studentPlacementSplitter">
        <x-slot:leftPanel>
            <div class="px-4">
                <x-v2.search-input class="sidebarTopSearch w-full h-8" placeholder="Search keywords" data-field="panelBar">
                    <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                </x-v2.search-input>
            </div>
            <ul id="panelBar" class="w-full flex items-start justify-start !mt-4 custom-student-placement">
            </ul>
            {{-- <div
                class="filterFooterBox flex flex-col items-center justify-start w-full py-3.5 bg-white border absolute inset-x-0 bottom-0">
                <div class="flex space-x-4 items-center justify-end">
                    <x-v2.button type="button" id="clearFilter" variant="secondary" size="xs" class="active w-24">
                        Clear
                    </x-v2.button>
                    <x-v2.button type="button" id="applyFilter" variant="primary" size="xs" class="active w-28">
                        Apply
                    </x-v2.button>
                </div>
            </div> --}}
        </x-slot:leftPanel>
        <x-slot:rightPanel>
            <div class="flex flex-col justify-center pt-6 pb-4 pl-8 pr-6 w-full">
                <div class="searchdata flex gap-2 items-center justify-between w-full">
                    <div class="flex gap-2 items-center">
                        <x-v2.button type="button" id="filterBtn" variant="secondary" size="xs"
                            class="active btn-secondary btn-icon border-gray-300">
                            <img src="http://local.galaxy360.test/v2/img/f_icon.svg" class="w-[14px] h-[14px]"
                                alt="">
                            <span class="text-sm leading-5 font-normal text-gray-700">Filters</span>
                            <div
                                class="flex items-center justify-center px-3 py-0.5 bg-primary-blue-100 rounded-full filterCountDiv hidden">
                                <span class="text-sm leading-5 text-primary-blue-800 filterCount">1</span>
                            </div>
                        </x-v2.button>
                        <p class="text-base font-medium leading-tight text-gray-700 filter_title"></p>
                    </div>
                    <div class="flex space-x-2 items-center justify-end">
                        <x-v2.search-input data-grid-id="studentPlacementList" class="searchInputField w-full h-8"
                            placeholder="Search">
                            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                        </x-v2.search-input>
                        <x-v2.button type="button" id="exportData" variant="secondary" size="xs" class="px-4">
                            <span class="text-sm font-normal leading-tight text-gray-900 k-grid-excel">Export</span>
                        </x-v2.button>
                        <x-v2.templates.manage-columns>
                            <x-slot:itemsSlot>
                                @foreach ($column_arr as $column)
                                    <div class="flex space-x-2 items-center justify-start">
                                        <input type="checkbox" class="k-checkbox cursor-pointer rounded"
                                            id="fc_{{ $column['id'] }}" value="{{ $column['id'] }}"
                                            {{ $column['default'] }} />
                                        <label for="fc_{{ $column['id'] }}"
                                            class="text-sm leading-none text-gray-700">{{ $column['title'] }}</label>
                                    </div>
                                @endforeach
                            </x-slot:itemsSlot>
                        </x-v2.templates.manage-columns>
                    </div>
                </div>
                <div id="appliedFilterList"></div>
            </div>
            <div class="bg-gray-100">
                <div id="progressbar"></div>
                <div id="studentPlacementList" class="tw-table tw-table__rows--check"></div>
            </div>
        </x-slot:rightPanel>
    </x-v2.splitter>

    {{-- VPMS Modal --}}
    <div id="assignVpmsProviderModal" class="wizardModal" style="display: none;">
        <form id="assignVpmsProviderForm">
            <input type="hidden" class="studentIds" name="student_id" id="studentIds" value="" />
            <input type="hidden" class="studentCourseIds" name="student_course_id" id="studentCourseIds"
                value="" />
        </form>
    </div>

    <div id="confirmUnAssignVpmsModal"></div>

    {{-- List of action in bottom using Modal --}}
    <div style="transition: height 0.2s;" id="action"
        class="relative overflow-visible bottomaction heightzero grid grid-cols-3 items-center gap-4 px-8 bg-white shadow border-gray-300 h-16">
        <div class="flex space-x-2 items-center justify-start">
            <input id="selectedStudents" name="Selected" type="checkbox"
                class="focus:ring-geekBlue-500 h-4 w-4 rounded-sm text-geekBlue-600 cursor-pointer border-gray-300">
            <label id="selected_title" for="selectedStudents"
                class="text-sm leading-5 font-normal text-gray-800 w-48 cursor-pointer"></label>
        </div>

        <!-- Middle column centered -->
        <div class="flex items-center justify-center gap-3">
            <button
                class="assignVPMS space-x-2 items-center justify-center h-full py-2 px-4 bg-white hover:bg-primary-blue-500 hover:text-white group flex border rounded-xl border-gray-300">
                <span class="text-sm leading-5 font-normal text-gray-500 group-hover:text-white">Assign Provider</span>
            </button>
            <button
                class="unAssignVPMS space-x-2 items-center justify-center h-full py-2 px-4 bg-white hover:bg-primary-blue-500 hover:text-white group flex border rounded-xl border-gray-300">
                <span class="text-sm leading-5 font-normal text-gray-500 group-hover:text-white">Unassign
                    Provider</span>
            </button>
        </div>

        <div class="flex justify-end">
            <button type="button" title="Cancel" class="closeAction">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 13L13 1M1 1L13 13" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </button>
        </div>
    </div>


    {{-- Notification Templates --}}
    @include('v2.sadmin.notification')

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/jszip.min.js') }}"></script>
        <script src="{{ asset('plugins/ckeditor5/ckeditor.js') }}"></script>
        <script src="{{ asset('v2/js/sadmin/vpms/student-placement.js') }}"></script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ isset($api_token) ? "Bearer $api_token" : '' }}";
    </x-slot>

</x-v2.layouts.default>
