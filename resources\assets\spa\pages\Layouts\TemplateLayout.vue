<script setup>
import { Link } from '@inertiajs/vue3';

import { onMounted } from 'vue';
onMounted(() => {
    console.log('layout loaded');
});
</script>

<template>
    <div class="gradientbackground flex justify-between p-4 px-6">
        <div class="flex items-center justify-start space-x-2">
            <a href="#" class="cursor-pointer text-xl leading-6 text-white hover:underline"
                >Courses</a
            >
        </div>
        <div class="flex items-center justify-end space-x-2"></div>

        <header>
            <Link class="p-4 font-bold text-white" :href="appUrl('spa/courses')">Courses</Link>
            <Link class="p-4 font-bold text-white" :href="appUrl('spa/course-types')"
                >Course Types</Link
            >
            <Link class="p-4 font-bold text-white" :href="appUrl('spa/course-templates')"
                >Course Template</Link
            >
            <a class="p-4 font-bold text-white" :href="route('user-onboard')"
                >Laravel Route Example</a
            >
        </header>
    </div>
    <main class="w-full px-6">
        <article class="mt-4">
            <slot />
        </article>
    </main>
</template>
<style></style>
