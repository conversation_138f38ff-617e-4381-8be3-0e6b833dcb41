{{-- Transfer Payments Modal --}}
<div id="transferPaymentsModal" style="display: none;">
    <div class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-gray-100 w-full">
        <div
            class="flex flex-col space-y-4 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="transferPaymentsHtml w-full"></div>
        </div>
        <div class="flex flex-col items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full">
                <div class="inline-flex space-x-4 items-center justify-between w-full">
                    <div class="flex space-x-1 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Payment Schedule For Student</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                    </div>
                    <div class="flex space-x-2 items-start justify-start">
                        <button type="button" id="exportTrasactionToTransfer"
                            class="inline-flex space-x-2 items-center justify-center h-8 pl-2 pr-2.5 bg-white border rounded-lg border-gray-300 hover:shadow">
                            <svg width="10" height="13" viewBox="0 0 10 13" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M0.5 12H9.5C9.77614 12 10 12.2239 10 12.5C10 12.7455 9.82312 12.9496 9.58988 12.9919L9.5 13H0.5C0.223858 13 0 12.7761 0 12.5C0 12.2545 0.176875 12.0504 0.410124 12.0081L0.5 12H9.5H0.5ZM4.91012 0.00805569L5 0C5.24546 0 5.44961 0.176875 5.49194 0.410124L5.5 0.5V9.292L8.18198 6.61091C8.35555 6.43735 8.62497 6.41806 8.81984 6.55306L8.88909 6.61091C9.06265 6.78448 9.08194 7.0539 8.94694 7.24877L8.88909 7.31802L5.35355 10.8536C5.17999 11.0271 4.91056 11.0464 4.71569 10.9114L4.64645 10.8536L1.11091 7.31802C0.915651 7.12276 0.915651 6.80617 1.11091 6.61091C1.28448 6.43735 1.5539 6.41806 1.74877 6.55306L1.81802 6.61091L4.5 9.292V0.5C4.5 0.25454 4.67688 0.0503916 4.91012 0.00805569L5 0L4.91012 0.00805569Z"
                                    fill="#9CA3AF" />
                            </svg>
                            <p class="text-xs leading-4 text-gray-700 uppercase">export</p>
                        </button>
                    </div>
                </div>
                <div class="inline-flex items-start justify-start w-full">
                    <div id="paidPaymentTransferList" class="w-full"></div>
                </div>
            </div>
        </div>

        <div class="flex flex-col items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full">
                <div class="inline-flex space-x-4 items-center justify-between w-full">
                    <div class="flex space-x-1 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">Select Transaction to Transfer</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                    </div>
                    <div class="flex space-x-2 items-start justify-start">
                        <button type="button" id="exportTrasactionToTransfer"
                            class="inline-flex space-x-2 items-center justify-center h-8 pl-2 pr-2.5 bg-white border rounded-lg border-gray-300 hover:shadow">
                            <svg width="10" height="13" viewBox="0 0 10 13" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M0.5 12H9.5C9.77614 12 10 12.2239 10 12.5C10 12.7455 9.82312 12.9496 9.58988 12.9919L9.5 13H0.5C0.223858 13 0 12.7761 0 12.5C0 12.2545 0.176875 12.0504 0.410124 12.0081L0.5 12H9.5H0.5ZM4.91012 0.00805569L5 0C5.24546 0 5.44961 0.176875 5.49194 0.410124L5.5 0.5V9.292L8.18198 6.61091C8.35555 6.43735 8.62497 6.41806 8.81984 6.55306L8.88909 6.61091C9.06265 6.78448 9.08194 7.0539 8.94694 7.24877L8.88909 7.31802L5.35355 10.8536C5.17999 11.0271 4.91056 11.0464 4.71569 10.9114L4.64645 10.8536L1.11091 7.31802C0.915651 7.12276 0.915651 6.80617 1.11091 6.61091C1.28448 6.43735 1.5539 6.41806 1.74877 6.55306L1.81802 6.61091L4.5 9.292V0.5C4.5 0.25454 4.67688 0.0503916 4.91012 0.00805569L5 0L4.91012 0.00805569Z"
                                    fill="#9CA3AF" />
                            </svg>
                            <p class="text-xs leading-4 text-gray-700 uppercase">export</p>
                        </button>
                    </div>
                </div>

                <div class="inline-flex items-start justify-start w-full">
                    <div id="paidPaymentList" class="w-full"></div>
                </div>
                <div class="justify-start items-center inline-flex ">
                    <div class="text-gray-500 text-sm font-normal leading-tight tracking-wide">Total Amount to Transfer
                    </div>
                    <p class="px-6 text-gray-700 text-sm font-normal leading-tight tracking-wide totalAmountToTransfer">
                        $0</p>
                </div>
                <div class="justify-start items-center inline-flex ">
                    <div class="text-gray-500 text-sm font-normal leading-tight tracking-wide">Total Commission Paid
                    </div>
                    <p class="px-6 text-gray-700 text-sm font-normal leading-tight tracking-wide totalCommissionPaid">$0
                    </p>
                </div>
            </div>
        </div>

        <div
            class="inline-flex flex-col space-y-6 items-start justify-start px-5 pt-5 pb-20 bg-white border rounded-lg border-gray-200 w-full">
            <div class="inline-flex space-x-1 items-center justify-start">
                <p class="text-base font-medium leading-normal text-gray-700">Agent Commission Information</p>
                <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
            </div>
            <input name="transaction_number" id="transaction_number" type="hidden" />
            <input name="invoice_number" id="invoice_number" type="hidden" />
            <form id="agentCommissionInfoForm" method="POST" accept-charset="UTF-8" enctype="multipart/form-data"
                class="w-full">

            </form>
        </div>
    </div>
</div>

<script id="transferPaymentsTemplate" type="text/html">
    <div class="flex flex-col space-y-4 items-start justify-start bg-white w-full">
        <div class="inline-flex space-x-1 items-center justify-between w-full">
            <div class="flex space-x-2 items-center justify-start">
                # if (profile_pic == '') { let name = full_name.toUpperCase().split(/\s+/); let shortName =
                name[0].charAt(0) + name[1].charAt(0); #
                <div class="rounded-full">
                    <div class='flex user-profile-pic w-12 h-12 rounded-full bg-primary-blue-500 items-center'>
                        <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                            #</span>
                    </div>
                </div>
                # } else { #
                <div class="w-12 h-12 rounded-full">
                    <img class="w-12 h-12 flex-1 rounded-full" src="#= profile_pic #" />
                </div>
                # } #
                <div class="inline-flex flex-col items-start justify-end">
                    <p class="text-sm font-bold leading-5 text-gray-900">#: full_name #</p>
                    <p class="text-xs leading-5 text-gray-400">#: generated_stud_id #</p>
                </div>
            </div>
            <div class="flex space-x-2 items-center justify-start">
                <p class="text-xs leading-5 text-gray-700">#:course_name #</p>
                <div class="flex items-center justify-center px-2.5 py-0.5 bg-primary-blue-100 rounded">
                    <p class="text-xs leading-5 text-center text-primary-blue-800">#: status #</p>
                </div>
            </div>
        </div>
    </div>
    <div
        class="inline-flex space-x-6 items-start justify-start p-4 bg-gray-50 border rounded-lg border-gray-200 w-full mt-2">
        <div class="flex space-x-6 items-start justify-start w-full">
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Length</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: course_duration #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Duration</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#:total_weeks#</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Agent</p>
                <p class="w-full text-sm leading-5 text-gray-700">#:agency_name#</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Total Course Fee</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: kendo.toString(course_fee, "c") #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Monthly Fee</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: kendo.toString(0, "c") #</p>
            </div>
            <div class="inline-flex flex-col space-y-0.5 items-start justify-center flex-1">
                <p class="w-full text-xs text-gray-500 uppercase truncate">Subject Enrolled</p>
                <p class="w-full text-sm leading-5 text-gray-700 truncate">#: noOfStudentSubjectEnrolment #</p>
            </div>
        </div>
    </div>
</script>