<?php

namespace App\Http\Requests\api\v3;

use App\Classes\SiteConstants;
use App\Model\v2\Student;
use App\Model\v2\Tenant;
use App\Traits\CommonRequestMethodsTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class StudentCourseCheckoutRequest extends FormRequest
{
    use CommonRequestMethodsTrait;

    private $methodName;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        try {
            $user = auth()->user();
            $student = null;
            if ($user->role_id == SiteConstants::STUDENT_ROLE_ID) {
                $studentId = Student::GetLoggedInStudentId($user);
                $student = Student::find($studentId);
            } else {
                $student = @$user->associatedStudent;
                $studentId = $student ? $student->id : null;
                if (! $student) {
                    $studentId = $this->route()->parameter('studentId') ?? $this->student ?? $this->student_id ?? null;
                    $studentId = ($studentId) ? decryptIt($studentId) : null;
                    $student = Student::find($studentId);
                }
            }
            /*
            old code
            if($user->role_id && $user->role_id == SiteConstants::STUDENT_ROLE_ID) {
                $studentId = Student::GetLoggedInStudentId($user);
            }else{
                $studentId = $this->route()->parameter('studentId') ?? $this->student ?? $this->student_id ?? null;
                $studentId = ($studentId) ? decryptIt($studentId) : null;
            }
            $student = Student::find($studentId);
            */
            if (empty($student) || ($student && ! $student->isApiAccessible(auth()->user()))) {
                throw new \Exception;
            }
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'student_id' => 'The student ID is invalid.',
            ]);
        }

        try {
            $id = $this->enrollment_id ?? null;
            $id = ($id) ? decryptIt($id) : null;
        } catch (\Exception $e) {
            $id = null;
        }

        try {
            $rawHost = $hostUrl = $this->redirect_uri ?? '';

            if (! preg_match('/^https?:\/\//', $rawHost)) {
                $hostUrl = 'http://'.$rawHost;
            }

            $parsedUrl = parse_url($hostUrl);

            $host = $parsedUrl['host'] ?? null;

            $tenant = ($host) ? Tenant::GetTenantInfoByAllowedDomain($host) : null;
            $url = ($tenant) ? $tenant->GetApiBaseurl() : null;

            if (! $url) {
                throw ValidationException::withMessages([
                    'redirect_uri' => 'This Domain is not a registered as allowed domains.',
                ]);
            }

        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'redirect_uri' => $e->getMessage(),
            ]);
        }

        $this->merge([
            'student' => $studentId,
            'redirect_uri' => $hostUrl,
            'enrollment_id' => $id,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {

        return [
            /**
             * Hashed key of student Id (If the current user is the student, no need to pass the student Id)
             */
            'student' => ['required'],
            /**
             * Enrollment ID of the pending enrollment to get checkout link to
             */
            'enrollment_id' => [
                'required',
                Rule::exists('App\Model\v2\StudentCourses', 'id')->where(function ($query) {
                    $query->where('student_id', $this->student);
                }),
            ],
            /**
             * Provide the url where user should come back to after the payment process
             *
             * @example scheme://domain/endpoint
             *
             * Example host: https://example.com/shourtcourse/coursecode
             */
            'redirect_uri' => [
                'required',
                'url',
            ],

        ];
    }
}
