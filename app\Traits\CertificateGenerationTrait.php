<?php

namespace App\Traits;

use App;
use App\Helpers\Helpers;
use App\Model\v2\ResultGrade;
use App\Model\v2\StudentCertificateRegister;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use App\Services\CertificateContentReplacer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Support\Services\UploadService;

trait CertificateGenerationTrait
{
    private function getStudentData($studentId, $courseId)
    {
        return StudentCourses::with(['student', 'course'])
            ->where('student_id', $studentId)
            ->where('course_id', $courseId)
            ->first();
    }

    private function getGradeData($collegeId)
    {
        $whereArr = ['college_id' => $collegeId];
        $gradeDatas = ResultGrade::Where($whereArr)->select('*')->get()->toArray();

        return [
            'gradePointData' => collect($gradeDatas)->pluck('grade_point', 'id')->toArray(),
            'gradeData' => collect($gradeDatas)->pluck('grade', 'id')->toArray(),
        ];
    }

    private function getEnrolments($studentId, $courseId)
    {
        return StudentSubjectEnrolment::with(['semester', 'unit', 'subject'])
            ->where('student_id', $studentId)
            ->where('course_id', $courseId)
            ->get();
    }

    private function generateQrCode($data)
    {
        $qrCode = QrCode::size(200)
            ->format('png')
            // ->generate(json_encode($data));
            ->generate($data['verification_url']);

        return 'data:image/png;base64,'.base64_encode($qrCode);
    }

    private function prepareCertificateData($student, $uuid, $otherData, $enrolments, $qrCodeBase64)
    {
        return [
            'student' => [
                'fullname' => $student->student->name_title.' '.$student->student->first_name.' '.$student->student->family_name,
                'firstname' => $student->student->first_name,
                'lastname' => $student->student->family_name,
                'id' => $student->student->generated_stud_id,
            ],
            'course' => [
                'code' => $student->course->course_code ?? '',
                'name' => $student->course->course_name ?? '',
            ],
            'certificate' => [
                'uuid' => $uuid ?? '',
                'issued_on' => date('d M Y'),
                'expired_on' => $student->certificate_expired_on,
            ],
            'studentcourse' => [
                'course_startdate' => date('d M Y', strtotime($student->start_date)),
                'course_enddate' => date('d M Y', strtotime($student->finish_date)),
            ],
            'qr_code' => '<img src="'.$qrCodeBase64.'" alt="QR Code" />',
            'qr_code_src' => $qrCodeBase64,
            'unit' => [
                'otherData' => $otherData,
                'list' => $enrolments->map(function ($enrolment) {
                    if ($enrolment->unit && in_array($enrolment->final_outcome, ['C', 'CT', 'NYC', 'WD'])) {
                        return [
                            'code' => $enrolment->unit->unit_code,
                            'name' => $enrolment->unit->unit_name,
                            'year' => ($enrolment->activity_finish_date != '' &&
                                     $enrolment->activity_finish_date != '0000-00-00' &&
                                     strtotime($enrolment->activity_finish_date) !== false) ?
                                     date('Y', strtotime($enrolment->activity_finish_date)) : '-',
                            'result' => $enrolment->final_outcome,
                        ];
                    }

                    return null;
                })->filter()->values()->chunk(10)->toArray(),
                'trascript' => collect($enrolments)->groupBy('semester_id')->map(function ($items, $semester) {
                    return [
                        'semester_name' => optional($items->first()->semester)->semester_name ?? 'N/A',
                        'data' => $items->filter(function ($item) {
                            return ! empty($item->mark_outcome);
                        }),
                    ];
                })->values()->chunk(2)
                    ->map(function ($chunk) {
                        return [
                            'total_count' => $chunk->sum(fn ($item) => count($item['data'])),
                            'data' => $chunk->toArray(),
                        ];
                    })
                    ->toArray(),
            ],
            'cricos' => [
                'course' => [
                    'code' => $student->course->cricos_code ?? '',
                ],
            ],
        ];
    }

    private function generatePdf($templates, $data, $fileName = null, $isDownload = false)
    {
        $replacer = new CertificateContentReplacer($data);
        $processedHtml = $replacer->replace($templates->html_data);
        $extractCSS = $replacer->extractStyles($templates->html_data);

        $width_px = 707;
        $height_px = 1000;
        $width_pt = $width_px * 0.75;
        $height_pt = $height_px * 0.75;

        if (! is_dir(Config::get('constants.certificateTempFontPath'))) {
            mkdir(Config::get('constants.certificateTempFontPath'), 0777);
        }

        $pdf = App::make('dompdf.wrapper');
        $pdf->setPaper([0, 0, $width_pt, $height_pt]);
        $pdf->setOptions([
            'isRemoteEnabled' => true,
            'fontDir' => Config::get('constants.certificateTempFontPath'),
        ]);

        $pdf->loadView('certificates.pdf', compact('processedHtml', 'extractCSS'));
        $pdf->output();
        $dom_pdf = $pdf->getDomPDF();
        $canvas = $dom_pdf->get_canvas();
        $canvas->page_text(280, 800, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);

        if ($isDownload) {
            $filePath = Config::get('constants.uploadFilePath.StudentCertificate');
            $destinationPath = Helpers::changeRootPath($filePath);

            $pdfContent = $pdf->output();
            // $upload_success = UploadService::uploadAs($destinationPath['view'], $pdfContent, $fileName . '.pdf');

            $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tmpPath, $pdfContent);
            $upload_success = UploadService::uploadAs($destinationPath['view'], new \Illuminate\Http\File($tmpPath), $fileName.'.pdf');
            info('file uploaded form generatePdf', [$upload_success]);
            @unlink($tmpPath);

            return UploadService::download($destinationPath['view'].$fileName.'.pdf');
        }

        return $pdf->stream();
    }

    public function handleCertificateGeneration($params)
    {
        if ($params['isDownload']) {
            $fileName = str_replace(' ', '_', $params['templates']->name).'_'.time().'_'.$params['studentId'];
            $params['dataValue']['certificate_number_formate_id'] = $params['templates']->certificate_number_formate_id;
            $params['dataValue']['issueDate'] = date('Y-m-d');
            $recoredId = StudentCertificateRegister::saveCertificateRegisterFromPdfBeta($params['collegeId'], $params['dataValue'], $params['studentId'], $fileName);

            $qrCodeData = [
                'certificate_id' => $params['uuid'],
                'student_id' => $params['studentId'],
                'course_id' => $params['student']->course_id,
                'issue_date' => date('Y-m-d'),
                'verification_url' => route('verify.certificate', ['id' => encryptIt($recoredId)]),
            ];

            $qrCodeBase64 = $this->generateQrCode($qrCodeData);
            $data = $this->prepareCertificateData($params['student'], $params['uuid'], $params['otherData'], $params['enrolments'], $qrCodeBase64);

            return [
                'pdf' => $this->generatePdf($params['templates'], $data, $fileName, true),
                'fileName' => $fileName,
                'studentId' => $params['studentId'],
            ];
        }

        $dummyData = [
            'certificate_id' => 'PREVIEW-'.$params['uuid'],
            'student_id' => 'PREVIEW-'.$params['student']->student_id,
            'course_id' => 'PREVIEW-'.$params['student']->course_id,
            'issue_date' => date('Y-m-d'),
            'verification_url' => 'https://example.com/preview',
        ];

        $qrCodeBase64 = $this->generateQrCode($dummyData);
        $data = $this->prepareCertificateData($params['student'], $params['uuid'], $params['otherData'], $params['enrolments'], $qrCodeBase64);

        return [
            'pdf' => $this->generatePdf($params['templates'], $data),
            'studentId' => $params['studentId'],
        ];
    }

    public function handleBulkDownload($results)
    {
        $collegeId = Auth::user()->college_id;
        $filePath = config('constants.uploadFilePath.TempFiles');
        $destination = Helpers::changeRootPath($filePath, null, $collegeId);

        File::ensureDirectoryExists($destination['default'], 0777, true);

        $zipFileName = 'certificates_'.time().'.zip';
        $zipPath = $destination['default'].$zipFileName;

        $zip = new \ZipArchive;

        if ($zip->open($zipPath, \ZipArchive::CREATE) === true) {
            foreach ($results as $result) {
                if (! empty($result['pdf']) && ! empty($result['fileName'])) {
                    $parsedPath = parse_url($result['pdf'], PHP_URL_PATH);
                    $s3Key = ltrim(strstr($parsedPath, 'uploads/'), '/');

                    if (Storage::disk(config('filesystems.upload_disk'))->exists($s3Key)) {
                        $pdfContent = Storage::disk(config('filesystems.upload_disk'))->get($s3Key);
                        $zip->addFromString($result['fileName'].'.pdf', $pdfContent);
                    }
                }
            }
            $zip->close();
        }

        return response()->download($zipPath)->deleteFileAfterSend(true);
    }

    public function handleStudentCardGeneration($params)
    {
        $data = $this->prepareStudentCardData($params['student']);

        if ($params['isDownload']) {
            $fileName = str_replace(' ', '_', $params['templates']->name).'_'.time().'_'.$params['studentId'];

            $params['width_px'] = 324;      // 243;  // 3.375 inches * 72 DPI
            $params['height_px'] = 204;     // 153; // 2.125 inches * 72 DPI

            return [
                'pdf' => $this->generateStudentCardPdf($params, $data, $fileName, true),
                'fileName' => $fileName,
                'studentId' => $params['studentId'],
            ];
        }

        return [
            'pdf' => $this->generateStudentCardPdf($params, $data),
            'studentId' => $params['studentId'],
        ];
    }

    private function prepareStudentCardData($student)
    {
        $profilePicPath = asset('dist/img/avatar6.png');
        if ($student->profile_picture) {
            $profilePicPath = $this->getStudentProfilePicPath($student->id, $student->profile_picture);
        }

        $collegeLogoPath = '';
        if ($student->college->college_logo) {
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath);
            $collegeLogoPath = UploadService::imageEmbed($destinationPath['view'].$student->college->college_logo);
            // $collegeLogoPath = asset($destinationPath['view'].$student->college_logo);
        }

        return [
            'full_name' => collect([
                $student->name_title ?? null,
                $student->first_name ?? null,
                $student->family_name ?? null,
            ])->filter()->implode(' '),
            'first_name' => $student->first_name,
            'last_name' => $student->family_name,
            'student_id' => $student->generated_stud_id,
            'email' => $student->email,
            'date_of_birth' => date('d M Y', strtotime($student->DOB)),
            'profile_picture' => $profilePicPath,
            'college_name' => $student->college->college_name,
            'college_logo' => $collegeLogoPath,
        ];
    }

    private function generateStudentCardPdf($params, $data, $fileName = null, $isDownload = false)
    {
        $templates = $params['templates'];
        $replacer = new CertificateContentReplacer($data);
        $processedHtml = $replacer->replace($templates->html_data);
        $extractCSS = $replacer->extractStyles($templates->html_data);

        $width_px = $params['width_px'] ?? 324;
        $height_px = $params['height_px'] ?? 204;
        $width_pt = $width_px * 0.75;
        $height_pt = $height_px * 0.75;

        /*if (! is_dir(Config::get('constants.certificateTempFontPath'))) {
            mkdir(Config::get('constants.certificateTempFontPath'), 0777);
        }*/

        $pdf = App::make('dompdf.wrapper');
        $pdf->setPaper([0, 0, $width_pt, $height_pt]);
        /*$pdf->setOptions([
            'isRemoteEnabled' => true,
            'fontDir' => Config::get('constants.certificateTempFontPath'),
        ]);*/

        $pdf->loadView('certificates.pdf', compact('processedHtml', 'extractCSS'));
        $pdf->output();
        $dom_pdf = $pdf->getDomPDF();
        $canvas = $dom_pdf->get_canvas();
        $canvas->page_text(280, 800, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);

        if ($isDownload) {
            $filePath = Config::get('constants.uploadFilePath.StudentCard');
            $destinationPath = Helpers::changeRootPath($filePath);
            $pdfContent = $pdf->output();
            // $upload_success = UploadService::uploadAs($destinationPath['view'], $pdfContent, $fileName . '.pdf');

            $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tmpPath, $pdfContent);
            $upload_success = UploadService::uploadAs($destinationPath['view'], new \Illuminate\Http\File($tmpPath), $fileName.'.pdf');
            info('file uploaded form generatePdf', [$upload_success]);
            @unlink($tmpPath);

            return UploadService::download($destinationPath['view'].$fileName.'.pdf');
        }

        return $pdf->stream();
    }

    public function handleBulkStudentCardGeneration($params)
    {
        $students = $params['students'];
        $template = $params['template'];
        $fileName = $params['fileName'] ?? 'bulk_student_cards_'.time();
        $isDownload = $params['isDownload'] ?? true;

        $allCardsHtml = '';
        foreach ($students as $index => $studData) {
            $data = $this->prepareStudentCardData($studData);

            // Use existing content replacer
            $replacer = new CertificateContentReplacer($data);
            $processedHtml = $replacer->replace($template->html_data);

            // Add page break after each card except the last one
            $pageBreak = ($index < count($students) - 1) ? '<div style="page-break-after: always;"></div>' : '';

            // Wrap each card with proper dimensions
            $allCardsHtml .= '<div style="width: 324px; height: 204px; margin: 0 auto; page-break-inside: avoid;">'
                           .$processedHtml
                           .'</div>'
                           .$pageBreak;
        }

        // Generate PDF using similar approach as individual cards
        return $this->generateBulkStudentCardPdf($allCardsHtml, $fileName, $isDownload);
    }

    private function generateBulkStudentCardPdf($htmlContent, $fileName, $isDownload = false)
    {
        $width_px = 324;
        $height_px = 204;
        $width_pt = $width_px * 0.75;
        $height_pt = $height_px * 0.75;

        $pdf = App::make('dompdf.wrapper');
        $pdf->getDomPDF()->set_option('isHtml5ParserEnabled', true);
        $pdf->getDomPDF()->set_option('isRemoteEnabled', true);

        // Set paper size for ID cards
        $pdf->setPaper([0, 0, $width_pt, $height_pt], 'portrait');
        $pdf->loadHTML($htmlContent);

        if ($isDownload) {
            $filePath = Config::get('constants.uploadFilePath.StudentCard');
            $destinationPath = Helpers::changeRootPath($filePath);
            $pdfContent = $pdf->output();
            // $upload_success = UploadService::uploadAs($destinationPath['view'], $pdfContent, $fileName . '.pdf');

            $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tmpPath, $pdfContent);
            $upload_success = UploadService::uploadAs(
                $destinationPath['view'],
                new \Illuminate\Http\File($tmpPath),
                $fileName.'.pdf'
            );
            info('file uploaded form generatePdf', [$upload_success]);
            @unlink($tmpPath);

            return $destinationPath['view'].'/'.$fileName.'.pdf';

            return UploadService::download($destinationPath['view'].$fileName.'.pdf');
        }

        return $pdf->stream($fileName.'.pdf');
    }
}
