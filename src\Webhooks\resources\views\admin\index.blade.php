@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Webhooks</h1>
        <a href="{{ route('admin.webhooks.create') }}"
            class="bg-primary-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Add New Webhook
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Events
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach($webhooks as $webhook)
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {{ $webhook->name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span class="truncate max-w-xs block">{{ $webhook->url }}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        @foreach($webhook->events as $event)
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                            {{ $event }}
                        </span>
                        @endforeach
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $webhook->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $webhook->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.webhooks.edit', $webhook) }}"
                                class="text-indigo-600 hover:text-indigo-900">Edit</a>
                            <button onclick="testWebhook({{ $webhook->id }})"
                                class="text-green-600 hover:text-green-900">Test</button>
                            <button onclick="toggleWebhook({{ $webhook->id }})"
                                class="text-yellow-600 hover:text-yellow-900">
                                {{ $webhook->is_active ? 'Disable' : 'Enable' }}
                            </button>
                            <button onclick="deleteWebhook({{ $webhook->id }})"
                                class="text-red-600 hover:text-red-900">Delete</button>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="mt-4">
        {{ $webhooks->links() }}
    </div>
</div>

<script>
    function testWebhook(webhookId) {
    fetch(`/admin/webhooks/${webhookId}/test`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Webhook test successful!');
        } else {
            alert('Webhook test failed: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        alert('Error testing webhook: ' + error.message);
    });
}

function toggleWebhook(webhookId) {
    fetch(`/admin/webhooks/${webhookId}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error toggling webhook status');
        }
    })
    .catch(error => {
        alert('Error toggling webhook: ' + error.message);
    });
}

function deleteWebhook(webhookId) {
    if (confirm('Are you sure you want to delete this webhook?')) {
        fetch(`/admin/webhooks/${webhookId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting webhook');
            }
        })
        .catch(error => {
            alert('Error deleting webhook: ' + error.message);
        });
    }
}
</script>
@endsection