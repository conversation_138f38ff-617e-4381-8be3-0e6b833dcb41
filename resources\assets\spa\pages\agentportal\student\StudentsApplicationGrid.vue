<template>
    <Layout :noSpacing="true" :loading="false" :actionSticky="true">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Student Applications - Requested'" :back="false" />
        </template>
        <template #tabs>
            <OfferHeaderTabs :currentTab="data.currentActiveTab" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <HeaderTab
                :gridData="gridData"
                :action="data.action"
                :agentData="this.data.agentData"
                :filters="getFilters"
                :hastextsearch="true"
                :actions="getActionBtns"
                @export="handleExport"
                @filter="updateFilter"
                :width="'w-[300px]'"
                :dateRangeWidth="'w-[300px]'"
                :variant="'primary'"
            />
            <div
                v-if="data.currentActiveTab == 'continue'"
                class="inline-flex h-11 w-full flex-col items-end justify-start gap-2.5"
            >
                <div
                    class="inline-flex items-center justify-start gap-6 rounded-md border border-amber-400 bg-amber-50 p-3"
                >
                    <div class="flex items-center justify-start gap-3">
                        <div class="flex h-5 w-5 items-center justify-center px-0.5 pb-1 pt-0.5">
                            <svg
                                width="16"
                                height="14"
                                viewBox="0 0 16 14"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M6.25706 1.09858C7.02167 -0.260724 8.97875 -0.260725 9.74336 1.09858L15.3237 11.0191C16.0736 12.3523 15.1102 13.9996 13.5805 13.9996H2.4199C0.890251 13.9996 -0.0731769 12.3523 0.676753 11.0191L6.25706 1.09858ZM9.00012 10.9998C9.00012 11.552 8.55241 11.9998 8.00012 11.9998C7.44784 11.9998 7.00012 11.552 7.00012 10.9998C7.00012 10.4475 7.44784 9.99976 8.00012 9.99976C8.55241 9.99976 9.00012 10.4475 9.00012 10.9998ZM8.00012 2.99976C7.44784 2.99976 7.00012 3.44747 7.00012 3.99976V6.99976C7.00012 7.55204 7.44784 7.99976 8.00012 7.99976C8.55241 7.99976 9.00012 7.55204 9.00012 6.99976V3.99976C9.00012 3.44747 8.55241 2.99976 8.00012 2.99976Z"
                                    fill="#FBBF24"
                                />
                            </svg>
                        </div>
                        <div class="inline-flex flex-col items-start justify-start gap-0.5">
                            <div
                                class="text-sm font-normal leading-tight tracking-tight text-amber-800"
                            >
                                Please note: Applications will be automatically deleted after 90
                                days.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <PreviewPane
                :show="showPreview"
                @close-preview="handlePreviewClose"
                @show-preview="handleViewDetail"
                :documentName="documentName"
                :files="gridData"
                v-model:documentName="documentName"
                @next="handleNextClick"
                @prev="handlePrevClick"
            >
                <template #content>
                    <StudentOfferGrid
                        :data="gridData"
                        :type="'application'"
                        :columns="getColumns"
                        :currentTab="data.currentActiveTab"
                        :pagination="resource.state.pageable"
                        :sort="getSortInfo"
                        :hasSelect="hasSelectColumn"
                        @sort="sortDataHandler"
                        @changepage="changePageHandler"
                        :actions="getActionBtns"
                        @offer_letter_preview="handleViewDetail"
                        @offer_letter="downloadOfferLetter"
                        v-if="loadGrid"
                        :actionSticky="true"
                        :isTableCellHeight="true"
                        :isTableHeadHeight="true"
                        :class="'h-full'"
                        :isRowClick="false"
                    />
                </template>
                <template #preview>
                    <div class="custom-pdf p-8 px-0 pb-0 pt-0">
                        <div class="tw-filemanager tw-media-manager !h-full" ref="previewPdf"></div>
                    </div>
                    <div class="block md:hidden">
                        <PreviewDocumentModal
                            :file="file"
                            :isUrl="true"
                            :visible="showPreview"
                            :width="'90%'"
                            @cancel="handlePreviewClose"
                        />
                    </div>
                </template>
            </PreviewPane>
        </div>
    </Layout>
</template>

<script>
import { watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import OfferHeaderTabs from '@agentportal/student/partials/OfferHeaderTabs.vue';
import HeaderTab from '@agentportal/payments/partials/HeaderTab.vue';
//import StudentOfferGrid from "@agentportal/student/partials/StudentOfferGrid.vue";
import { IconArrowDownload24Regular } from '@iconify-prerendered/vue-fluent';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import {
    agentsResource,
    setPagination,
    prepareStudentOfferGridData,
} from '@spa/services/agent/agentsResource.js';
import { routeHistory } from '@spa/helpers/routeHistory';
import PreviewPane from '@spa/components/KendoGrid/PreviewPane.vue';
import PreviewDocumentModal from '../gteprocess/partials/PreviewDocumentModal.vue';
import StudentOfferGrid from '@agentportal/common/CommonGrid.vue';
import { url } from '@vuelidate/validators';
import { useDocumentStore } from '@spa/stores/modules/document.store';

export default {
    setup(props) {
        const resource = agentsResource({
            filters: props.query || {},
            only: ['grid', 'filters', 'data'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        data: { type: Object, default: {} },
        grid: { type: Object, default: {} },
        filters: { type: Object, default: {} },
        query: Object,
    },
    components: {
        Layout,
        PageTitleContent,
        OfferHeaderTabs,
        StudentOfferGrid,
        'search-input': IconInput,
        Button,
        'icon-download': IconArrowDownload24Regular,
        HeaderTab,
        PreviewPane,
        PreviewDocumentModal,
    },
    data() {
        return {
            gridData: [],
            loadGrid: false,
            allColumns: [],
            dataItem: null,
            meta: {
                total: 7,
                current_page: 1,
                per_page: 5,
            },
            headerActions: [
                {
                    icon: 'downloadIcon',
                    action: 'export',
                    value: 'excel',
                    title: 'Export (xls)',
                },
            ],
            showPreview: false,
            visible: false,
            isUrl: true,
            file: null,
            documentName: '',
        };
    },
    created() {
        routeHistory.previousRoute = window.location.pathname;
    },
    mounted() {
        this.gridData = this.prepareStudentOfferGridData(this.grid.students.data);
        this.setPagination(this.resource, this.grid.students.meta);
        this.allColumns = this.grid?.columns || [];
        this.loadGrid = true;
    },
    computed: {
        getFilters() {
            return this.filters || {};
        },
        getActionBtns() {
            return this.headerActions;
        },
        getColumns() {
            return this.allColumns;
        },
        hasSelectColumn() {
            return this.grid?.selectable || false;
        },
        getSortInfo() {
            return [
                {
                    field: this.resource.state.filters.sort,
                    dir: this.resource.state.filters.dir,
                },
            ];
        },
        documentStore() {
            return useDocumentStore();
        },
        files() {
            return this.documentStore.getDocuments;
        },
    },
    methods: {
        prepareStudentOfferGridData,
        setPagination,
        sortDataHandler(sort) {
            this.resource.state.filters.sort = sort[0]?.field || null;
            this.resource.state.filters.dir = sort[0]?.dir || null;
        },
        changePageHandler(page, take) {
            this.resource.state.filters.page = page;
            this.resource.state.filters.take = take;
        },
        updateFilter(filters) {
            Object.keys(filters).reduce((acc, key) => {
                const value = filters[key];
                this.resource.state.filters[key] = Array.isArray(value) ? value.join(',') : value;
                return acc;
            }, {});
            this.resource.state.filters.page = 1;
        },
        handleExport($e) {
            const selectedIds = this.gridData
                .filter((item) => item.selected)
                .map((item) => item.student_course_id);
            this.resource.exportdata({
                export: $e,
                records: selectedIds.length > 0 ? selectedIds : 'all',
            });
        },
        handleToggle() {
            this.visible = !this.visible;
        },
        handleClose() {
            this.visible = false;
        },
        getIdentifier(item) {
            return (this.documentName = item?.fullname + '-' + item?.student_id);
        },
        handleViewDetail(item) {
            this.getIdentifier(item);
            this.dataItem = item;
            this.showPreview = true;
            const urlPreview = route('agent_preview_offer_letter_pdf_new', [
                item.course_id,
                item.student_id,
                item.student_course_id,
            ]);
            this.previewDocument(urlPreview);
        },
        handlePreviewClose() {
            this.showPreview = false;
        },
        previewDocument(item) {
            this.$nextTick(() => {
                const componentRef = this.$refs.previewPdf;

                if (!componentRef) {
                    console.error('studentDocument ref is undefined.');
                    return;
                }

                const fileUrl = this.isUrl ? encodeURI(item) : encodeURI(window[item]);

                this.file = fileUrl;

                const existingPdfViewer = $(componentRef).data('kendoPDFViewer');
                console.log('Exi', existingPdfViewer);
                if (existingPdfViewer) {
                    existingPdfViewer.destroy();
                    $(componentRef).empty(); // Clear DOM to prevent duplicate instances
                }
                console.log('FileUrl', fileUrl);
                $.when(
                    $.getScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js'),
                    $.getScript(
                        'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js'
                    ),
                    $.getScript('https://kendo.cdn.telerik.com/2024.3.806/js/kendo.all.min.js')
                )
                    .done(() => {
                        window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                            'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js';
                        $.getScript(`${window['APP_URL']}/v2/js/kendo-ui-license.js`);
                    })
                    .then(() => {
                        $(componentRef)
                            .kendoPDFViewer({
                                pdfjsProcessing: {
                                    file: fileUrl,
                                },
                                width: '100%',
                                height: '100%',
                            })
                            .data('kendoPDFViewer');
                    });
            });
        },
        downloadOfferLetter(item) {
            const url = route('agent_offer_letter_pdf_new', [
                item.course_id,
                item.student_id,
                item.student_course_id,
            ]);
            window.open(url, '_blank');
            return;
        },
        handlePreviewChange(item) {
            setTimeout(() => {
                this.previewDocument(
                    route('agent_preview_offer_letter_pdf_new', [
                        item.course_id,
                        item.student_id,
                        item.student_course_id,
                    ])
                );
            });
        },
        handleNextClick(item) {
            this.handlePreviewChange(item);
        },
        handlePrevClick(item) {
            this.handlePreviewChange(item);
        },
    },
    watch: {
        grid: {
            handler(newVal) {
                this.gridData = this.prepareStudentOfferGridData(newVal.students.data);
                this.setPagination(this.resource, newVal.students.meta);
            },
            deep: true,
        },
    },
};
</script>

<style scoped>
/* Add your styles here */
</style>
