.toast-container-success,
.toast-container.toaster-success {
    &,
    & > .k-notification {
        background-color: var(--color-green-50);
        color: var(--color-green-800);
        border-radius: 0.5rem;
    }
    & > .k-notification {
        border: 1px solid var(--color-green-500);
    }

    .toast-message a,
    .toast-message label {
        &,
        &:hover {
            color: var(--color-green-800);
        }
    }
}
.toast-container-error,
.toast-container.toaster-error {
    &,
    & > .k-notification {
        background-color: var(--color-red-50);
        color: var(--color-red-700);
        border-radius: 0.5rem;
    }

    & > .k-notification {
        border: 1px solid var(--color-red-500);
    }
    .toast-message a,
    .toast-message label {
        &,
        &:hover {
            color: var(--color-red-700);
        }
    }
}
.toast-container-warning,
.toast-container.toaster-warning {
    &,
    & > .k-notification {
        background-color: var(--color-yellow-50);
        color: var(--color-yellow-700);
        border-radius: 0.5rem;
    }
    & > .k-notification {
        border: 1px solid var(--color-yellow-500);
    }
    .toast-message a,
    .toast-message label {
        &,
        &:hover {
            color: var(--color-yellow-700);
        }
    }
}

.k-notification-actions {
    padding-inline: 1rem;
}

.k-notification {
    white-space: normal;
}
