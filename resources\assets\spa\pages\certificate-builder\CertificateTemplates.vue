<template>
    <Layout :noSpacing="true" :loading="true" :pt="{ wrapper: 'h-full' }">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Certificate Templates'" :back="false" />
        </template>
        <!-- <HeaderTabs
            :tabs="tabs"
            :current="'templates'"
            :pt="{ root: 'pt-4' }"
        /> -->
        <div class="space-y-6 px-8 py-6">
            <div class="flex items-center justify-between">
                <IconInput
                    v-model.lazy="search"
                    :pt="{ root: 'w-64 md:w-80 h-9' }"
                    placeholder="Enter keyword"
                    :debounce="300"
                    :autocomplete="'off'"
                />
                <div :class="'flex items-center justify-end gap-2'">
                    <!-- <Link :href="route('spa.certificate-builder')"> -->
                    <Button :variant="'primary'" size="base" @click="createNew = true">
                        <span :class="'text-white'">
                            <icon :name="'add'" :fill="'#ffffff'" />
                        </span>
                        <span>Create New Template</span></Button
                    >
                    <!-- </Link> -->
                </div>
            </div>
            <Card :pt="{ content: 'space-y-6', root: 'p-6 md:p-6' }">
                <template #content>
                    <h2 class="text-2xl font-bold text-gray-800 md:text-2xl lg:text-3xl">
                        Templates
                    </h2>
                    <!-- <div class="space-y-4">
                        <h3
                            class="text-xl font-medium text-gray-700 lg:text-xl"
                        >
                            Recent Templates
                        </h3>
                        <div class="grid max-w-5xl grid-cols-4 gap-6 md:gap-8">
                            <template
                                v-for="(template, index) in templates1"
                                :key="template.id"
                            >
                                <TemplateCard
                                    :data="template"
                                    @edit="handleEdit($event, template)"
                                    @preview="handlePreview($event, template)"
                                    @delete="handleDelete($event, template)"
                                />
                            </template>
                        </div>
                    </div> -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-700 lg:text-xl">
                            Start Exploring
                        </h3>
                        <div class="grid max-w-5xl grid-cols-4 gap-6 md:gap-8">
                            <template v-for="(template, index) in templates" :key="template.id">
                                <TemplateCard
                                    :data="template"
                                    @edit="handleEdit($event, template)"
                                    @preview="handlePreview($event, template)"
                                    @delete="handleDelete($event, template)"
                                />
                            </template>
                        </div>
                    </div>
                </template>
            </Card>
        </div>
        <PreviewPopupWithData
            :visible="openPreviewPopup"
            :data="previewJson"
            :certificateEmbedeUrl="certificateEmbedeUrl"
            @close="closePreviewPopup"
            @stopLoading="isIframeLoad"
            type="image"
            :previewItem="selectedTemplate"
        />
        <LoadingPopup :visible="isLoading" @close="isLoading = false" />
        <CreateNewPopup
            :visible="createNew"
            @close="createNew = false"
            :certificateIdFormate="certificateIdFormate"
        />
    </Layout>
</template>
<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import SearchWithButtons from '@spa/modules/common/SearchWithButtons.vue';
import Card from '@spa/components/Card/Card.vue';
import TemplateCard from '@spa/modules/certificate-builder/TemplateCard.vue';
import data from './certificateProps.json';
import PreviewPopup from '@spa/modules/certificate-builder/PreviewPopup.vue';
import PreviewPopupWithData from '@spa/modules/certificate-builder/PreviewPopupWithData.vue';
import HeaderTabs from '@spa/modules/common/HeaderTabs.vue';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { Link } from '@inertiajs/vue3';
import useCertificateResource from '@spa/services/certificates/certificateResource';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import LoadingPopup from '@spa/modules/certificate-builder/LoadingPopup.vue';
import { debounce } from 'lodash';
import CreateNewPopup from '@spa/modules/certificate-builder/CreateNewPopup.vue';
import { router } from '@inertiajs/vue3';

const props = defineProps({
    templates: {
        type: Array,
        default: () => [],
    },
    certificateIdFormate: {
        type: Object,
        default: () => [],
    },
});

const loaderStore = useLoaderStore();

const isLoading = ref(false);
const createNew = ref(false);
const payload = ref(null);

const $certificate = useCertificateResource('spa/certificates');

const templates = computed(() => props.templates);
const certificateEmbedeUrl = ref('');
const selectedTemplate = ref({});
const previewJson = ref({});
const openPreviewPopup = ref(false);

const actionButtons = [
    {
        label: 'Create Blank Template',
        slug: 'create_blank',
        variant: 'primary',
        icon: 'add',
    },
];

const tabs = [
    {
        name: 'Templates',
        slug: 'templates',
        route: route('spa.certificate.templates'),
    },
    {
        name: 'Attributes',
        slug: 'attributes',
        route: route('spa.certificate.attributes'),
    },
];

const search = ref('');

const handleEdit = (e, item) => {
    // window.location.href = `/spa/certificate-builder?certificateId=${item.secureId}`;
    router.visit(route('spa.certificate-builder'), {
        preserveState: true,
        preserveScroll: true,
        only: ['certificate'],
        data: {
            certificateId: item.secureId,
            type: 'certificate',
        },
    });
};
const isIframeLoad = () => {
    if (certificateEmbedeUrl.value != '') {
        isLoading.value = false;
    }
};
const handlePreview = async (e, item) => {
    openPreviewPopup.value = true;
    await $certificate.getPreviewUrl(
        {
            id: item.id,
        },
        {
            handlePreview: (response) => {
                selectedTemplate.value = item;
                previewJson.value = item.json_data;
                certificateEmbedeUrl.value = response.embedeUrl;
            },
        }
    );
};
const closePreviewPopup = () => {
    openPreviewPopup.value = false;
};

const handleDelete = (e, item) => {
    $certificate.deleteTemplate({
        id: item.id,
    });
};

watch(
    () => loaderStore.contextLoaders['generate'] && openPreviewPopup.value,
    (newVal) => {
        if (newVal) {
            isLoading.value = true;
        } else {
            isLoading.value = false;
        }
    }
);

const filterTemplates = debounce((newVal) => {
    console.log('newVal', newVal);

    if (newVal) {
        templates1.value = props.templates.filter((item) =>
            item.name.toLowerCase().includes(newVal.toLowerCase())
        );
    } else {
        templates1.value = props.templates;
    }
}, 300);

watch(
    () => search.value,
    (newVal) => {
        filterTemplates(newVal);
    }
);

const handleCreateTemplate = () => {};
</script>
<style lang=""></style>
