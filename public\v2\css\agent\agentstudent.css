.widthzero {
    width: 0px;
}
.heightzero {
    overflow: hidden;
    height: 0px;
}

/* Start manage column bar CSS */
.manageColumnBox {
    z-index: 1;
    position: absolute;
    display: none;
}
.active .manageColumnBox {
    z-index: 9999;
    position: absolute;
    display: block;
}
.manageColumnBox.active .w-full .absolute {
    margin-right: -35px;
}

/* panelbar  */

.k-panelbar > .k-item > .k-link,
.k-panelbar > .k-panelbar-header > .k-link {
    color: #bbbffc !important;
    background-color: transparent !important;
    padding: 8px 0px !important;
    cursor: pointer;
}

.k-panelbar .k-group > .k-item > .k-link.k-state-selected.k-state-hover,
.k-panelbar .k-group > .k-panelbar-item > .k-link.k-state-selected.k-state-hover,
.k-panelbar .k-panelbar-group > .k-item > .k-link.k-state-selected.k-state-hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link.k-state-selected .k-state-hover {
    color: #fff;
    background-color: transparent !important;
}

.k-panelbar .k-group > .k-item > .k-link.k-state-selected,
.k-panelbar .k-group > .k-panelbar-item > .k-link.k-state-selected,
.k-panelbar .k-panelbar-group > .k-item > .k-link.k-state-selected,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link.k-state-selected {
    color: #fff;
    background-color: transparent !important;
    box-shadow: none;
}

.k-panelbar .k-group > .k-item.k-level-1 .k-link,
.k-panelbar .k-panelbar-group > .k-panelbar-item.k-level-1 .k-link {
    padding: 5px 4px !important;
}

.k-panelbar {
    border-color: transparent;
    color: inherit;
    background-color: transparent;
}

.k-panelbar-group > .k-panelbar-item > .k-link label {
    color: #374151 !important;
    height: 20px !important;
    font-size: 13px !important;
    cursor: pointer;
}

.k-panelbar > .k-item,
.k-panelbar > .k-item + .k-item,
.k-panelbar > .k-panelbar-header,
.k-panelbar > .k-panelbar-header + .k-panelbar-header {
    border-bottom: 1px solid #e5e7eb;
    padding-left: 16px;
    padding-right: 16px;
}

.k-panelbar > .k-item > .k-link.k-state-selected .k-icon,
.k-panelbar > .k-item > .k-link.k-state-selected .k-panelbar-item-icon,
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected .k-icon,
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected .k-panelbar-item-icon {
    color: var(--color-primary-blue-500);
}

.k-panelbar > .k-item > .k-link .k-icon,
.k-panelbar > .k-item > .k-link .k-panelbar-item-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-panelbar-item-icon {
    color: var(--color-primary-blue-500);
}

.k-checkbox:checked {
    border-color: var(--color-primary-blue-500);
    color: #fff;
    background-color: var(--color-primary-blue-500);
}

.k-panelbar > .k-item > .k-link.k-state-focus,
.k-panelbar > .k-item > .k-link.k-state-focused,
.k-panelbar > .k-item > .k-link:focus,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focus,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focused,
.k-panelbar > .k-panelbar-header > .k-link:focus {
    box-shadow: none;
}

.k-panelbar-group {
    max-height: 125px !important;
    overflow-y: auto !important;
}

.k-panelbar-group.custom-panel-size {
    max-height: 230px !important;
}

.filterFooterBox {
    position: sticky;
}

/* //action */
.sortable.open .dropdown-action.dm-toggle {
    top: inherit !important;
    left: inherit !important;
    display: block;
}

.dropdown-action {
    position: absolute;
    top: 100%;
    left: -85px;
    z-index: 1000;
    display: none;
    float: right;
    min-width: 230px;
    padding: 5px 0;
    margin: 0px -240px 0px;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    /* border: 1px solid #ccc; */
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
    box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
}

/* filter dropdown*/

.k-filter-menu .k-dropdown,
.k-action-buttons .k-button {
    border: 1px solid #b6b6b6;
    background-color: #e5e7eb;
}
.k-filter-menu .k-textbox,
.k-filter-menu .k-picker-wrap {
    border: 1px solid #b6b6b6;
}
.k-fieldselector .k-list .k-item,
.k-list-optionlabel.k-state-focused,
.k-list-optionlabel.k-state-selected,
.k-listbox .k-item,
.k-popup .k-list .k-state-focused,
.k-popup .k-list .k-state-hover,
.k-popup .k-list .k-state-selected,
.k-action-buttons .k-primary {
    background-color: #007fff;
    cursor: pointer;
    color: white;
}

#agentDocumentModal {
    padding: 0px;
}

.k-widget * {
    box-sizing: border-box !important;
}

/* upload */

#agentDocumentModal .k-upload {
    width: 100%;
}
#agentDocumentModal .k-dropzone {
    background-color: #f9fafb;
    height: 150px;
    cursor: pointer;
}
#agentDocumentModal .k-upload-button {
    position: absolute;
    top: 0;
    left: 0;
    border: 0;
    background: transparent;
    cursor: pointer;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 22;
    border: 2px dashed #eaeaea;
}

#agentDocumentModal .k-dropzone-hint,
#agentDocumentModal .k-upload-status-total {
    text-align: center;
    display: block;
    width: 100%;
}

#agentDocumentModal .k-upload-sync {
    border: none;
}

/* drop-down */

.k-dropdown-wrap .k-input {
    font-size: 14px !important;
    line-height: 30px !important;
    border-radius: 0;
    border: 1px solid black !important;
    box-shadow: none;
    font-weight: 400 !important;
    border-color: #dfe3e9 !important;
    color: #374151 !important;
    height: 36px !important;
    border-radius: 7px !important;
    margin-bottom: 0px !important;
}

.k-dropdown-wrap .k-select {
    display: none !important;
}

.k-dropdown {
    width: 100%;
}

.k-dropdown-wrap {
    padding: 0px;
}
.k-dropdown-wrap .k-input::before {
    content: '\f078';
    display: inline-block;
    position: absolute;
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    right: 40px;
    color: #9ca3af;
}

#agentDocumentManagement {
    width: 100%;
}
