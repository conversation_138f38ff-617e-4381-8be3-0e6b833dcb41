@section('title', @$title)
@section('keywords', @$keywords)
@section('description', @$description)
@section('mainmenu', @$mainmenu)
<x-slot name="cssHeader"></x-slot>
<div>
    <div class="grid w-full grid-cols-10 flex-row gap-6 p-6">
        <div class="col-span-7 flex w-full">
            <div class="w-full space-y-6">
                @if ($config->isFilled())
                    @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.connect')
                @endif
                @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.config-form')

                @if (@$xero_connected)
                    <div class="flex flex-col items-start justify-start space-y-6 rounded-lg bg-white p-6 shadow">
                        <div class="flex items-center justify-start space-x-1">
                            <p class="text-base font-medium leading-5 text-gray-900">Xero Advance Details</p>
                            {{--<livewire:docs path=".docs/xero.md"/>--}}
                        </div>

                        @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.account-form')
                        @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.agent-form')
                        {{-- @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.organization-info') --}}
                    </div>
                @endif

            </div>
        </div>
        <div class="col-span-3 space-y-6">
            @if (@$xero_connected)
                <div class="inline-flex w-full flex-col items-start justify-center gap-6 rounded-lg bg-white p-6 shadow">
                    <div class="grid w-full grid-cols-1 gap-3 p-1 max-w-64">
                        <button type="button" class="viewAccountsBtn btn-secondary py-2 pl-2.5 pr-3">
                            <img src="{{ asset('v2/img/view-account.svg') }}" class="h-4 w-4" alt="View Accounts">
                            <p class="text-sm font-medium leading-none text-gray-700">View Accounts</p>
                        </button>
                        <button type="button" class="viewTaxRatesBtn btn-secondary py-2 pl-2.5 pr-3">
                            <img src="{{ asset('v2/img/view-tax-rates.svg') }}" class="h-4 w-4" alt="View tax rates">
                            <p class="text-sm font-medium leading-none text-gray-700">View Tax Rates</p>
                        </button>
                        <x-button type="button" target="syncContacts" loading='Syncing...' wire:click.prevent="syncContacts"
                                  title="Sync Contacts From Xero" class="py-2 pl-2.5 pr-3 text-white">
                            <span class="k-icon k-i-refresh k-icon-refresh mb-1 mr-2"></span>
                            <span class="text-sm font-medium leading-none">Sync Contacts</span>
                        </x-button>
                    </div>
                    @if (@$config->organization_data['total_students_synced'] > 0)
                        <p class="font-bold text-green-500">Total
                            <span class="text-xl text-blue-500">{{ $config->organization_data['total_students_synced'] }}</span>
                            contacts synced from xero.
                        </p>
                    @endif
                </div>
                @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.tracking-categories-form')
                {{-- @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.organization-info') --}}
            @endif

            <div class="flex h-40 w-full flex-col items-start justify-start space-y-2 rounded-lg bg-white p-6 shadow">
                <p class="text-lg font-normal leading-7 text-primary-blue-500">Need Help?</p>
                <p class="text-sm font-normal leading-5 text-gray-500">
                    View this article to help you setup xero connection.
                </p>
                <livewire:docs title="Setup Xero Configuration for Galaxy" path=".docs/xero.md" :data="[
                    'redirectUrl' => route('xero-connect'),
                    'webhookUrl' => route('xero-webhook')
                ]"/>

                {{-- <div class="xeroSetupInfo cursor-pointer text-sm font-normal text-primary-blue-500 underline">
                    Setup Xero Configuration for Galaxy
                </div> --}}
            </div>
            @if (@$xero_connected)
                @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.agent-sync-option')
            @endif
        </div>
    </div>

    <div id="xeroSetupInfoModal" style="display:none;">
        @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.setup-info-modal')
    </div>

    <div id="viewAccountModal" style="display: none">
        @if (@$xero_connected)
            @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.accounts')
        @endif
    </div>
    <div id="viewTaxRatesModal" style="display: none">
        @if (@$xero_connected)
            @include('v2.sadmin.onboardsetting.xero-setup.livewire.partials.tax-rates')
        @endif
    </div>
</div>
