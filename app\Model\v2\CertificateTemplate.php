<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;

class CertificateTemplate extends Model
{
    protected $table = 'certificate_templates';

    protected $fillable = [
        'name',
        'json_data',
        'html_data',
        'paper_size',
        'orientation',
        'thumbnail',
        'is_default',
        'metadata',
        'certificate_number_formate_id',
        'type',
        'template_type',
    ];

    protected $casts = [
        'json_data' => 'array',
        // 'metadata' => 'array',
        'is_default' => 'boolean',
    ];

    const TEMPLATE_TYPE_CERTIFICATE = 'certificate';

    const TEMPLATE_TYPE_STUDENT_CARD = 'student-id';
}
