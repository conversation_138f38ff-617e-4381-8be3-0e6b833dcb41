<template>
    <div class="group flex flex-col gap-3">
        <div
            class="relative overflow-hidden rounded-md bg-gray-100"
            :class="type === 'student-id' ? 'aspect-[320/204]' : 'aspect-[3/4]'"
        >
            <img
                :src="data?.thumbnail"
                class="image-item h-full w-full object-contain transition-all duration-300 group-hover:scale-105"
                alt="Certificate Image"
            />
            <div
                class="invisible absolute inset-0 flex h-full w-full items-center justify-center gap-4 bg-gray-800/40 opacity-0 group-hover:visible group-hover:opacity-100"
            >
                <Button :size="'sm'" @click="handleEdit">Edit</Button>
                <Button variant="secondary" :size="'sm'" @click="handlePreview($event, data)"
                    >Preview</Button
                >
            </div>
        </div>
        <div class="flex items-start justify-between">
            <h4 class="text-base font-medium">
                {{ data?.name.split('_').join(' ') }}
            </h4>
            <Popover
                :open="openIndex === index"
                @toggle="openIndex = openIndex === index ? null : index"
                @close="openIndex = null"
            >
                <template #picker>
                    <Button variant="icon" class="k-link h-auto" @click="openMenu = !openMenu"
                        ><IconMoreHorizontal24Regular class="h-4 w-4"
                    /></Button>
                </template>
                <template #popup>
                    <ul class="min-w-[120px] py-1">
                        <li>
                            <button
                                class="inline-flex w-full items-center gap-2 px-3 py-1 text-left hover:bg-gray-100"
                                @click="handleEdit"
                            >
                                <IconPen24Regular class="h-4 w-4" />
                                <span>Edit</span>
                            </button>
                        </li>
                        <li>
                            <button
                                class="inline-flex w-full items-center gap-2 px-3 py-1 text-left hover:bg-gray-100"
                                @click="handlePreview"
                            >
                                <IconEye24Regular class="h-4 w-4" />
                                <span>Preview</span>
                            </button>
                        </li>
                        <li>
                            <button
                                class="inline-flex w-full items-center gap-2 px-3 py-1 text-left hover:bg-gray-100"
                                @click="handleDelete"
                            >
                                <IconDelete24Regular class="h-4 w-4" />
                                <span>Delete</span>
                            </button>
                        </li>
                    </ul>
                </template>
            </Popover>
        </div>
    </div>
</template>
<script setup>
import { ref } from 'vue';
import Button from '@spa/components/Buttons/Button.vue';
import Popover from '@spa/components/Popover/Popover.vue';
import {
    IconMoreHorizontal24Regular,
    IconEye24Regular,
    IconPen24Regular,
    IconDelete24Regular,
} from '@iconify-prerendered/vue-fluent';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    type: {
        type: String,
        default: 'certificate',
    },
});
const emit = defineEmits(['delete', 'edit', 'preview']);
const openPreviewPopup = ref(false);
const openMenu = ref(false);
const openIndex = ref(null);

const handlePreview = (e, item) => {
    emit('preview');
};

const closePreviewPopup = () => {
    openPreviewPopup.value = false;
};

const handleEdit = () => {
    emit('edit');
};

const handleDelete = () => {
    emit('delete');
};

const handleClose = (event) => {
    console.log('close event', event);
    openMenu.value = false;
};
</script>
<style lang=""></style>
