@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')


@if ( $errors->count() > 0 )
<section class="content server-side-validation">
    <div class="row">
        <div class="col-md-12">
            <p>The following errors have occurred:</p>
            <ul class="error-list">
                @foreach( $errors->all() as $message )
                <li>{{ $message }}</li>
                @endforeach
            </ul>
        </div>
    </div>
</section>
@endif

<!-- Main content -->
<div class="main-conntent">
    <section class="content">

        <!-- Info boxes -->
        {{ Form::open( array('class' => 'form-horizontal vertical-add-form','method' => 'post')) }}
        <div class="row">

            <div class="col-md-12">
                <div class="box box-info">
                    <!--                    <div class="box-header with-border">
                        <h3 class="box-title">Student Online Application</h3>
                    </div>-->


                    @if($accessRequest != 'new-application')
                    <!--<form class="form-horizontal vertical-add-form">-->
                    <!--                    <div class="box-body">
                        <div class="col-md-12">
                            <div class="form-group no-margin">
                                <div class="col-sm-12 label-value-view">
                                    Please enter your saved application ID to retrieve your application details.
                                </div>
                                <div class="col-sm-3">
                                    <label for="terms">Saved Application Id:</label>
                                    {{ Form::text('application_id',null,array('class' => 'form-control','placeholder'=>'Application Id')) }}
                                </div>
                                <div class="col-sm-3">
                                    <label for="search" class=" control-label blank-label">-</label>
                                    <input name="next" class="btn btn-info" value="Next" type="submit">
                                </div>
                            </div>
                        </div>
                    </div>-->

                </div>
                <div class="box-info">

                    <div class="row">
                        <div class="col-md-12">
                            <div class="navbar-custom-menu data-setting margin-t-30 column-navbar"
                                style="display: block;">
                                <ul class="nav navbar-nav">
                                    <li class="dropdown">
                                        <a href="javascript:;"
                                            class="dropdown-toggle link-black dropdown-toggle-column">
                                            Columns <i class="fa fa-angle-down"></i>
                                            <!--<i class="fa fa-cog light-gray-color"></i>-->
                                        </a>
                                        <ul class="dropdown-menu" style="background: #eee;">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <div class="col-md-12 toggle-label"><label
                                                            class="control-label">Toggle Column</label></div>
                                                    <div class="col-md-12">
                                                        <div class="form-group"><input type='checkbox' name='column_0'
                                                                value='0' class='custom-column'
                                                                data-default-status='true' data-column="0" id="ref_id"
                                                                checked /> <label for="ref_id">Application ref.
                                                                Id</label> </div>
                                                        <div class="form-group"><input type='checkbox' name='column_1'
                                                                value='1' class='custom-column'
                                                                data-default-status='true' data-column="1"
                                                                id="date_applied" checked /> <label
                                                                for="date_applied">Date Applied&nbsp;</label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_2'
                                                                value='2' class='custom-column'
                                                                data-default-status='true' data-column="2"
                                                                id="applicant_name" checked /> <label
                                                                for="applicant_name">Applicant Name&nbsp;&nbsp;</label>
                                                        </div>
                                                        <div class="form-group"><input type='checkbox' name='column_3'
                                                                value='3' class='custom-column'
                                                                data-default-status='true' data-column="3" id="agent"
                                                                checked /> <label for="agent">Agent&nbsp;&nbsp;</label>
                                                        </div>
                                                        <div class="form-group"><input type='checkbox' name='column_4'
                                                                value='4' class='custom-column'
                                                                data-default-status='true' data-column="4"
                                                                id="campus_applied" checked /> <label
                                                                for="campus_applied">Campus Name&nbsp;&nbsp;</label>
                                                        </div>
                                                        <div class="form-group"><input type='checkbox' name='column_5'
                                                                value='5' class='custom-column'
                                                                data-default-status='true' data-column="5"
                                                                id="course_applied" checked /> <label
                                                                for="course_applied">Course Applied </label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_6'
                                                                value='6' class='custom-column'
                                                                data-default-status='true' data-column="6" id="status"
                                                                checked /> <label for="status">Status </label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_7'
                                                                value='7' class='custom-column'
                                                                data-default-status='true' data-column="7"
                                                                id="study_period" /> <label for="study_period">Study
                                                                Period </label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_8'
                                                                value='8' class='custom-column'
                                                                data-default-status='true' data-column="8"
                                                                id="student_id" /> <label for="student_id">Student ID
                                                            </label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_9'
                                                                value='9' class='custom-column'
                                                                data-default-status='false' data-column="9"
                                                                id="reserved_id" /> <label for="reserved_id">Reserve Id
                                                            </label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_10'
                                                                value='10' class='custom-column'
                                                                data-default-status='false' data-column="10"
                                                                id="coe_name" /> <label for="coe_name">Coe Name </label>
                                                        </div>
                                                        <div class="form-group"><input type='checkbox' name='column_11'
                                                                value='11' class='custom-column'
                                                                data-default-status='false' data-column="11" id="usi" />
                                                            <label for="usi">USI</label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_12'
                                                                value='12' class='custom-column'
                                                                data-default-status='false' data-column="12" id="ID" />
                                                            <label for="ID">ID&nbsp;</label></div>
                                                        <div class="form-group"><input type='checkbox' name='column_13'
                                                                value='13' class='custom-column'
                                                                data-default-status='true' data-column="13"
                                                                id="created_by_user" checked /> <label
                                                                for="created_by_user">Created By</label></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                            <div class="table-responsive no-padding margin-t-30">
                                <table id="continueappication" class="table table-hover table-custom">
                                    <thead>
                                        <tr style="white-space:nowrap;">
                                            <th>Application Ref ID</th>
                                            <th>Date Applied</th>
                                            <th>Applicant Name</th>
                                            <th>Agent</th>
                                            <th>Campus Name</th>
                                            <th>Course Applied</th>
                                            <th>Status
                                                <!--<i class="fas fa-filter blue-font showFooter padding-t-7" style="margin-left: 80px;" data-toggle="collapse" data-target="#animation-dropdown" data-original-title="Show Status"></i>-->
                                                <!--                                    <div class="custom-animation-dropdown">
                                        <ul class="dropdown-menu" id="animation-dropdown" style="background: #eee;">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <div class="col-md-12 toggle-label"><label class="control-label">Toggle Column</label></div>
                                                    <div class="col-md-12">
                                                        <div class="form-group">
                                                            <input type="checkbox" value="In Application" class="custom-column-filter statusf"  name="status" id="new-application-request">
                                                            <label for="new-application-request">New Application Request</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Reconsider" class="custom-column-filter statusf"  name="status" id="reconsider">
                                                            <label for="reconsider">Reconsider</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Rejected" class="custom-column-filter statusf"  name="status" id="rejected">
                                                            <label for="rejected">Rejected</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Offered" class="custom-column-filter statusf"  name="status" id="offered">
                                                            <label for="offered">Offered</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Pending" class="custom-column-filter statusf"  name="status" id="pending">
                                                            <label for="pending">Pending</label>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                    </div>-->
                                            </th>
                                            <th>Study Period</th>
                                            <th>Student ID</th>
                                            <th>Reserve Id</th>
                                            <th>Coe Name</th>
                                            <th>USI</th>
                                            <th>ID</th>
                                            <th>Created By</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!--</form>-->
                @else
                <input type="hidden" name="isNewApplication" id="isNewApplication" value="<?= $accessRequest; ?>">
                <!--                        <div class="col-md-12">
                                            <div class="form-group">
                                                <div class="col-sm-6">
                                                    <label for="terms">Terms and condition of acceptance</label>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-sm-6">
                                                    <span data-toggle="modal" data-target="#viewTerms"> <a href="javascript:;"> View terms and Conditions </a> </span>
                                                    <span data-toggle="modal" data-target="#agreeModel"> <a href="javascript:;"> Model </a> </span>
                                                </div>
                                            </div>
                                            <div class="form-group no-margin">
                                                <div class="col-sm-6">
                                                    <input name="new_application" class="btn btn-info" value="I agree to the terms and conditions" type="submit">
                                                    <input name="goNext" id="GoNext" class="btn btn-info" value="Cancel" type="submit">
                                                </div>
                                            </div>
                                        </div>-->
                @endif

            </div>
        </div>
    </section>

</div>
{{ Form::close() }}

<div class="modal fade modal-show-center" id="viewTerms" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title">Terms and Condition</h4>
            </div>
            <div class="modal-body">
                <div class="box box-info">
                    <div class="box-body">
                        <p>Here is Terms and Condition</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-success yes-sure closeTerm" type="button">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="deleteModal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title">Delete Record</h4>
            </div>
            <div class="modal-body">
                <div class="box box-info">
                    <div class="box-body">
                        <p>Are you sure?</p>
                        <p> You want to delete Application?
                        <p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                <button class="btn btn-success yes-sure" type="button">Yes</button>
            </div>
        </div>
    </div>
</div>

<div class="relative z-10 modal fade" role="dialog" aria-modal="true" id="agreeModel">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
    <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div
                class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <div>
                    <div
                        class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 modal-header">
                        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-5">
                        <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Terms and condition
                            of acceptance</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Please accept term and condition</p>
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-6 flex justify-center space-x-2">
                    <a name="new_application" id="newApplication"
                        class="flex justify-center rounded-md bg-primary-blue-500 px-3 py-2 text-sm font-semibold text-white hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-blue-500"
                        target="_blank" href="{{ $applicationurl }}">Agree</a>
                    <a name="goNext" href='{{ route("student-continue-online-application") }}' id="front"
                        class="flex justify-center rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-red-500">Cancel</a>
                </div>
            </div>
        </div>
    </div>
</div>
</section>
</div>
<style>
    .modal-show-center {
        text-align: center;
        padding: 0 !important;
    }

    .modal-show-center:before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        margin-right: -4px;
    }

    .modal-show-center .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    a:active,
    a:focus,
    a:hover {
        outline: 0;
        text-decoration: none;
        color: white;
    }
</style>

@endsection