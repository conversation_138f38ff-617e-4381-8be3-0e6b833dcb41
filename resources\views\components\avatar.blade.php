@props(['url' => '', 'name' => 'User', 'subtitle' => 'N/A'])
<div class="flex items-center space-x-3">
    <div {{ $attributes->merge(['class' => 'flex items-center justify-center h-6 w-6 rounded-full bg-primary-blue-500
        overflow-hidden']) }}>
        @if($url)
        <img src="{{$url}}" alt="{{$name}}" class="object-top" />
        @else
        <p class="text-sm text-white uppercase leading-none">{{ initialLetters(@$name)}}</p>
        @endif
    </div>
    <span class="k-state-default">
        <p class="text-sm text-gray-800 group-hover:text-primary-blue-500 leading-5">{{
            @$name }}</p>
        <p class="text-xs leading-5 text-gray-400">{{ @$subtitle }}</p>
    </span>
</div>