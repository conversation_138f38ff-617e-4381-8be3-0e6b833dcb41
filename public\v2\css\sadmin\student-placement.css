/* Start Loader for Grid */
.k-loading-mask .k-loading-image {
    background-image: none !important;
}

div.k-loading-image {
    display: none;
}

span.k-loading-text {
    text-indent: 0;
    top: 50%;
    left: 50%;
    z-index: 9999;
}

.k-loading-mask {
    z-index: 99999;
}

.k-widget.k-window.k-display-inline-flex.blur-modal {
    z-index: 9999;
}

.k-loading-color {
    z-index: 99;
}

.k-grid-content {
    /*min-height: 460px;*/
    /*overflow: hidden;*/
}

/* End Loader for Grid */

/* Start manage column bar CSS */
.manageColumnBox {
    z-index: 1;
    position: absolute;
    display: none;
}

.active .manageColumnBox {
    z-index: 9999;
    position: absolute;
    display: block;
}

.manageColumnBox.active .w-full .absolute {
    margin-right: -35px;
}

/* End manage column bar CSS */

/* Start first column hide */
/* th:nth-child(1) {width: 0px;} */
/* td:nth-child(1) {width: 0px;} */
.k-grid .k-hierarchy-col {
    width: 0px;
}

/* End first column hide */

/* Start arrow-down OR selected-progress can use for triangle*/
.selected-progress {
    /*position: absolute;*/
    width: 5px;
    height: 5px;
    margin-top: -8px !important;
    margin-right: -8px !important;
    background: var(--color-primary-blue-500);
    border-radius: 0.3px;
    transform: rotate(-180deg);
}

.arrow-down {
    width: 0;
    height: 0;
    position: relative;
    margin-left: -2px !important;
    margin-top: -32px !important;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #3b82f6;
}

.k-grid tr td .arrow-down.represent {
    margin-top: -6px !important;
}

/* End arrow-down OR selected-progress can use for triangle*/

/* Start bottom popup open */
.bottomaction {
    position: sticky;
    bottom: 0px;
    z-index: 1000;
    /* overflow-y: auto; */
    width: 100%;
    left: 0;
    box-shadow:
        0px -4px 6px -1px rgba(0, 0, 0, 0.1),
        0px -2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* End bottom popup open */

/* Start custom tooltip */
.k-widget.k-tooltip.k-popup.k-group.k-reset {
    padding: 0px;
    background-color: transparent;
}

/* End custom tooltip */

/* Start action list */
.action-menu {
    /*position: absolute;*/
}

/* End action list */

/* Start sidebar filter hide-show*/
.widthzero {
    width: 0px;
}

.heightzero {
    overflow: hidden;
    height: 0px;
}

/* End Sidebar filter hide-show */

/* Start Sidebar filter */
.k-panelbar .k-group > .k-item > .k-link.k-state-selected.k-state-hover,
.k-panelbar .k-group > .k-panelbar-item > .k-link.k-state-selected.k-state-hover,
.k-panelbar .k-panelbar-group > .k-item > .k-link.k-state-selected.k-state-hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link.k-state-selected .k-state-hover {
    color: #fff;
    background-color: transparent !important;
}

.k-panelbar .k-group > .k-item > .k-link.k-state-selected,
.k-panelbar .k-group > .k-panelbar-item > .k-link.k-state-selected,
.k-panelbar .k-panelbar-group > .k-item > .k-link.k-state-selected,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link.k-state-selected {
    color: #fff;
    background-color: transparent !important;
    box-shadow: none;
}

.k-panelbar {
    border-color: transparent;
    color: inherit;
    background-color: transparent;
}

.k-panelbar-group > .k-panelbar-item > .k-link label {
    /* color: #374151 !important; */
    /* height: 20px !important; */
    /* font-size: 13px !important; */
    cursor: pointer;
}

.k-panelbar-group {
    max-height: 500px !important;
    overflow-y: auto !important;
    padding-left: 10px;
}

.k-panelbar-group.custom-panel-size {
    max-height: 230px !important;
}

.k-panelbar-group.custom-intake-size {
    max-height: 380px !important;
}

.k-panelbar .k-panelbar-group > .k-item > .k-link:hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link:hover {
    cursor: pointer;
}

.student-details .grid {
    min-height: 350px;
}

.k-panelbar > .k-item > .k-link .k-icon {
    color: red;
}

.k-panelbar > .k-item > .k-link .k-icon,
.k-panelbar > .k-item > .k-link .k-panelbar-item-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-icon,
.k-panelbar > .k-panelbar-header > .k-link .k-panelbar-item-icon {
    color: var(--color-primary-blue-500);
}

.k-panelbar > .k-item > .k-link.k-state-selected,
.k-panelbar > .k-panelbar-header > .k-link.k-state-selected {
    /* background-color: #fff !important; */
    color: var(--color-primary-blue-500);
}

k-panelbar > .k-item > .k-link.k-state-focused,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focused {
    box-shadow: none;
}

.k-panelbar .k-group > .k-item > .k-link,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link {
    padding: 4px 16px !important;
}

/* End Sidebar filter */

/* Start tooltip popup manage */
.k-widget.k-tooltip.k-popup {
    box-shadow: none !important;
    border: 0px !important;
}

.custom-option option {
    padding: 15px 0px !important;
}

.k-link.k-state-hover button span {
    color: #fff;
}

/* End tooltip popup manage */

/* Start switch as toggle */
input:checked ~ .dot {
    transform: translateX(100%);
}

input:checked ~ .outer-dot {
    background: var(--color-primary-blue-500) !important;
}

/* End switch as toggle */

#sendMailStudentModal,
#sendSmsStudentModal {
    padding: 0px;
}

.k-widget * {
    box-sizing: border-box !important;
}

/* table check box */
.k-checkbox:checked {
    background-color: var(--color-primary-blue-500) !important;
    border-color: var(--color-primary-blue-500) !important;
}

.k-checkbox.k-checked,
.k-checkbox:checked {
    background-image: none !important;
}

.k-checkbox:checked:focus {
    box-shadow: none;
}

/* radio button color*/
[type='radio'] {
    color: var(--color-primary-blue-500);
}

/*  */
.k-panelbar > .k-item > .k-link,
.k-panelbar > .k-panelbar-header > .k-link:hover {
    background-color: transparent !important;
    cursor: pointer;
}

#panelbar li.k-level-0 {
    position: relative;
    border: 0px !important;
}

#panelbar li.k-level-0::after {
    content: '';
    position: absolute;
    width: 90%;
    height: 1px;
    background-color: #e5e7eb;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0 auto;
}

#panelbar li.k-level-0:last-child:after {
    display: none;
}

#appliedFilterList .k-i-close::before {
    color: #9ca3af !important;
}

/* icon for kendo */
.copy-icon {
    font-size: 17px;
    font-weight: bold;
    color: #9ca3af;
}

/* on hover row effect */
.k-grid tr:hover td {
    background-color: rgba(249, 250, 251, 1);
}

.k-grid tr td .action-only {
    display: none;
}

.k-grid tr:hover td .action-only {
    display: block;
}

input[type='text']:focus {
    box-shadow: none;
}

/* tooltip shadow */

.k-progressbar .k-state-selected {
    border-color: var(--color-primary-blue-500) !important;
    color: #fff;
    background-color: var(--color-primary-blue-500) !important;
}

.k-stepper .k-step-done .k-step-indicator {
    border-color: var(--color-primary-blue-500) !important;
    color: #fff;
    background-color: var(--color-primary-blue-500) !important;
}

.k-stepper .k-step-current .k-step-indicator {
    border-color: var(--color-primary-blue-500);
    color: #fff;
    background-color: var(--color-primary-blue-500);
}

.k-step-current:hover .k-step-indicator {
    background-color: var(--color-primary-blue-500) !important;
    background: var(--color-primary-blue-500) !important;
}

.k-stepper .k-step-done.k-step-hover .k-step-indicator,
.k-stepper .k-step-done:hover .k-step-indicator {
    background-color: var(--color-primary-blue-500);
}

.k-notification {
    padding: 0px !important;
    border-radius: 8px !important;
}

.k-notification-success,
.k-notification-error {
    border-color: #fff !important;
    background-color: #fff !important;
}

/* DropDown  */

#assignVpmsProviderModal .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 5px 0px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#assignVpmsProviderModal .k-list-optionlabel.k-state-selected,
.k-list-optionlabel {
    display: none;
}

.k-listbox .k-item,
.k-popup .k-list .k-item {
    border-radius: 0.5rem;
}

.k-popup .k-list .k-state-hover {
    background-color: var(--color-gray-200) !important;
    cursor: pointer !important;
}

#assignVpmsProviderModal .k-dropdown-wrap .k-select {
    line-height: 35px !important;
    width: 35px !important;
}

#assignVpmsProviderModal .k-widget .k-invalid-msg {
    display: none;
}

/* DatePicker */

#assignVpmsProviderModal .k-form-field-wrap .k-datepicker {
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#assignVpmsProviderModal .k-picker-wrap .k-input[type='text'] {
    border-radius: 0.5rem;
    height: 36px !important;
}

#assignVpmsProviderModal .k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

.k-label.k-form-label {
    color: #374151;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 5px;
}

#panelbar li ul {
    max-height: 220px !important;
    overflow-y: auto !important;
}

/* multiselect */
.k-multiselect {
    border-width: 1px !important;
}

.k-multiselect-wrap {
    flex-direction: column !important;
}

.k-multiselect-wrap .k-input {
    width: 100% !important;
}

.k-multiselect-wrap .k-input:focus-within {
    box-shadow: none !important;
}

.action-div {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

/* radio  select filter */
.k-filter-menu-container .k-checkbox-label {
    padding: 5px 0px !important;
    width: 100%;
}

.k-filter-menu-container .k-checkbox-label span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.k-filter-menu-container .k-checkbox-label .k-checkbox {
    margin-left: 4px !important;
    margin-right: 0px !important;
}

.k-filter-menu-container .k-checkbox-label:hover {
    background-color: #e5e7eb !important;
    border-radius: 4px;
}

.k-filter-menu.k-popup .k-multicheck-wrap {
    padding: 0px !important;
}

.k-popup .k-multicheck-wrap {
    scrollbar-width: none;
}

#globalSearchText[type='text']:focus,
[type='password']:focus {
    border-color: #ffffff !important;
}

.custom-student-placement li {
    position: relative;
}

.custom-student-placement li ul {
    padding: 4px !important;
}

.custom-student-placement > li::after {
    position: absolute;
    content: '';
    width: calc(100% - 2rem);
    height: 1px;
    background-color: var(--color-gray-200);
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.custom-student-placement > li .k-link {
    padding-block: 16px !important;
}

.custom-input-border .k-dropdown-wrap {
    border-radius: 0.5rem !important;
    padding: 0.201rem 0 !important;
}
