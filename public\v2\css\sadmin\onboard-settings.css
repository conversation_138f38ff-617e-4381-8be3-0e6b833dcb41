/* 
#addSMSTemplateModal, #addEmailTemplateModal, #addOfferDocumentModal, #editOfferDocumentModal, #addAgentDocumentModal, #editAgentDocumentModal,
#addOfferTrackingStatusModal, #editOfferTrackingStatusModal, #addCustomChecklistModal, #editCustomChecklistModal, #addGteDocumentModal, 
#editGteDocumentModal, #addLetterTemplateModal{
    padding: 0px;
} */

.gridInfo .k-pager-sizes {
    border: none !important;
}

.k-notification {
    border-radius: 8px !important;
    padding: 0px 0px !important;
    border-width: 0px !important;
}

#addLetterTemplateModal {
    max-height: 550px !important;
}

/* wizard form */

.k-wizard .k-wizard-step {
    padding: 0px;
}

.k-wizard-horizontal .k-wizard-content,
.k-wizard-horizontal .k-wizard-steps {
    margin: 0px;
}

.k-wizard .k-wizard-step:focus {
    outline: none;
}

/* Stepper Css */

#smsDocumentStepper ol li .k-step-label,
#emailDocumentStepper ol li .k-step-label,
#checklistStepper ol li .k-step-label,
#letterDocumentStepper ol li .k-step-label {
    display: none;
}

#smsDocumentStepper ol li .k-step-indicator,
#emailDocumentStepper ol li .k-step-indicator,
#checklistStepper ol li .k-step-indicator,
#letterDocumentStepper ol li .k-step-indicator {
    width: 100% !important;
    height: auto;
    margin: 0px !important;
    display: flex !important;
    justify-content: flex-start !important;
    padding: 12px 16px !important;
    border-left: 0px !important;
    border-top: 0px !important;
    border-color: #e5e7eb !important;
}

#smsDocumentStepper.k-stepper .k-step-indicator,
#emailDocumentStepper.k-stepper .k-step-indicator,
#checklistStepper.k-stepper .k-step-indicator,
#letterDocumentStepper.k-stepper .k-step-indicator {
    border-radius: 0%;
}

#smsDocumentStepper.k-stepper .k-step-indicator::after,
#emailDocumentStepper.k-stepper .k-step-indicator::after,
#checklistStepper.k-stepper .k-step-indicator::after,
#letterDocumentStepper.k-stepper .k-step-indicator::after {
    border-radius: 0%;
}

#smsDocumentStepper.k-stepper .k-step-done .k-step-indicator,
.k-stepper .k-step-indicator,
#emailDocumentStepper.k-stepper .k-step-done .k-step-indicator,
#checklistStepper.k-stepper .k-step-done .k-step-indicator,
#letterDocumentStepper.k-stepper .k-step-done .k-step-indicator {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

#smsDocumentStepper .k-step-list-vertical .k-step,
#emailDocumentStepper .k-step-list-vertical .k-step,
#checklistStepper .k-step-list-vertical .k-step,
#letterDocumentStepper .k-step-list-vertical .k-step {
    min-height: auto !important;
}

#smsDocumentStepper .k-widget.k-progressbar.k-progressbar-vertical,
#emailDocumentStepper .k-widget.k-progressbar.k-progressbar-vertical,
#checklistStepper .k-widget.k-progressbar.k-progressbar-vertical,
#letterDocumentStepper .k-widget.k-progressbar.k-progressbar-vertical {
    display: none !important;
}

.k-stepper.smsDocumentStepper .k-step-indicator,
.k-stepper.emailDocumentStepper .k-step-indicator,
.k-stepper.checklistStepper .k-step-indicator,
.k-stepper.letterDocumentStepper .k-step-indicator {
    color: #1f2937 !important;
}

.k-stepper.smsDocumentStepper .k-step-current .k-step-indicator,
.k-stepper.emailDocumentStepper .k-step-current .k-step-indicator,
.k-stepper.checklistStepper .k-step-current .k-step-indicator,
.k-stepper.letterDocumentStepper .k-step-current .k-step-indicator {
    color: var(--color-primary-blue-500) !important;
}

.k-stepper.smsDocumentStepper .k-step-current .k-step-indicator,
.k-stepper.emailDocumentStepper .k-step-current .k-step-indicator,
.k-stepper.checklistStepper .k-step-current .k-step-indicator,
.k-stepper.letterDocumentStepper .k-step-current .k-step-indicator {
    border-color: #e5e7eb;
    color: var(--color-primary-blue-500);
    background-color: #e6f7ff;
}

/* form */
.k-popup .k-list .k-state-hover {
    background-color: #e5e7eb;
    cursor: pointer;
}

.k-popup .k-list .k-item {
    line-height: 30px !important;
    min-height: 1.8em;
}

.k-label.k-form-label {
    color: #374151;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 5px;
}

.k-widget.k-dropdown .k-dropdown-wrap {
    padding: 5px 4px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
    height: 100%;
}

.k-form-field-wrap .k-textbox {
    border-width: 1px;
    border-radius: 0.5rem;
    height: 38px;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.k-form-field-wrap .k-textbox.k-invalid {
    border: 1px solid red !important;
}

#editLetterTemplateForm .k-textbox > input {
    padding: 6px !important;
}

.k-form-field-wrap .k-textbox:hover,
.k-form-field-wrap .k-textbox:focus,
.k-form-field-wrap .k-dropdown-wrap:hover,
.k-form-field-wrap .k-dropdown-wrap:focus {
    border: 1px solid var(--color-primary-blue-500);
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

/* kendo switch */
.k-switch {
    width: 56px;
    border: 0px;
    padding: 6px 0px;
    background: #fff;
    display: flex;
    align-items: center;
}

.k-switch-handle {
    background: #fff;
    width: 25px;
    height: 27px;
    position: relative;
    box-shadow:
        0px 1px 3px rgba(0, 0, 0, 0.1),
        0px 1px 2px rgba(0, 0, 0, 0.06);
    top: -3px;
}

.k-switch-off .k-switch-handle {
    left: -1px;
}

.k-switch .k-switch-container {
    padding: 0;
    width: 0;
    height: 25px;
}

.k-switch.k-switch-on .k-switch-container {
    background-color: var(--color-primary-blue-500) !important;
}

.k-switch-label-on,
.k-switch-label-off {
    display: none;
}

/*  upload documents */
.k-upload {
    width: 100%;
}

.k-dropzone {
    background-color: #f9fafb !important;
    height: 60px;
    cursor: pointer;
}

.k-upload-button {
    position: absolute;
    top: 0;
    left: 0;
    color: transparent;
    border: 0;
    background: transparent;
    cursor: pointer;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 22;
    border: 2px dashed #d1d5db;
}

.k-dropzone-hint,
.k-upload-status-total {
    text-align: center;
    display: block;
    width: 100%;
}

.k-upload {
    border: 0px;
    border-radius: 0.5rem;
}

.k-upload-button {
    border-radius: 0.5rem !important;
}

/* radio button */

.k-radio-item .k-radio[type='radio']:checked {
    border-color: rgba(24, 144, 255, 1) !important;
    border-radius: 0.5rem !important;
    background-image: none !important;
    background-color: #2563eb !important;
}

.k-radio-item .k-radio[type='radio']:checked:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

.k-radio.k-checked.k-state-focus,
.k-radio:checked:focus {
    box-shadow: none;
}

.k-radio-item .k-radio {
    margin-right: 5px !important;
}

/*  other */

.maincontainer {
    position: relative;
    /* z-index: 8; */
}

span.k-state-active .k-i-arrow-60-down::before {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-i-arrow-60-down::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
    margin-top: 3px;
}

.k-dialog .k-dialog-titlebar {
    background: var(--color-primary-blue-500) !important;
}

.action-div {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

[type='checkbox']:checked:hover,
[type='checkbox']:checked:focus,
[type='radio']:checked:hover,
[type='radio']:checked:focus {
    background-color: var(--color-primary-blue-500) !important;
    box-shadow: none;
    outline: 2px solid var(--color-primary-blue-500);
    outline-offset: 2px;
}

.k-fieldselector .k-list .k-item,
.k-list-optionlabel.k-state-focused,
.k-list-optionlabel.k-state-selected,
.k-listbox .k-item,
.k-popup .k-list .k-state-focused,
.k-popup .k-list .k-state-selected,
.k-action-buttons .k-primary {
    background-color: var(--color-primary-blue-500) !important;
    cursor: pointer !important;
    color: white !important;
}

.k-form-error.k-invalid-msg {
    display: none;
}

.titlebar-sms-modal .k-window-action {
    color: #fff;
    opacity: 1;
}

.titlebar-sms-modal .k-window-action .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}

.titlebar-sms-modal {
    background: linear-gradient(270deg, #06b6d4 20.35%, #1e93ff 75.64%);
}

.k-icon-gray {
    color: #9ca3af;
}

.k-i-delete::before {
    content: close-quote !important;
    background-image: url('../../img/delete-new-gray.svg') !important;
    background-position: center;
    background-repeat: no-repeat;
}

.k-i-edit::before {
    content: close-quote !important;
    background-image: url('../../img/edit-gray-pencil.svg') !important;
    background-position: center;
    background-repeat: no-repeat;
}

.k-calendar-range .k-content .k-calendar-td.k-range-mid {
    background-color: #f3f4f6 !important;
}

tr:hover {
    cursor: pointer;
}

.k-textbox-container {
    padding: 0 !important;
}

.k-textbox-container > .k-label {
    display: none;
}

.k-icon.failed-sync::before {
    content: close-quote !important;
    background-image: url(../../img/failed-sync.svg);
    background-repeat: no-repeat;
}

.k-icon.k-sync-white::before {
    content: close-quote !important;
    background-image: url('../../img/sync-white.svg');
    background-repeat: no-repeat;
}

.k-daterangepicker {
    width: 400px !important;
}

.k-daterangepicker input.k-textbox {
    border-radius: 0.5rem;
    padding: 5px 4px;
    border: 1px solid #d1d5db;
}

.k-calendar-range .k-content td.k-range-split-end::after,
.k-calendar-range .k-content td.k-range-split-start::after {
    display: none;
}

#reSyncTransactionDetailModal {
    padding: 0px;
}
