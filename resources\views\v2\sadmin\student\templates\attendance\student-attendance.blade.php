<script id="studAttendanceTemplate" type="text/html">
    <div class="px-6 md:px-8 bg-gray-100">
        <input type="hidden" id="courseStartDate" name="courseStartDate" value="#: arr['start_date']  #" />
        <x-v2.animation.slide
            class="inline-flex w-full p-4 bg-white rounded-md border border-gray-200 flex-col justify-start items-start gap-4">
            <div class="self-stretch justify-between items-center inline-flex">
                <div class="justify-start items-center gap-2 flex">
                    <div class="w-10 h-10 rounded-2xl flex-col justify-center items-center inline-flex">
                        @if (empty($studentDetails[0]->profile_pic))
                        <div class="rounded-full">
                            <div class="flex user-profile-pic w-10 h-10 !rounded-md bg-primary-blue-500 items-center">
                                <span class="text-xl flex justify-center items-center leading-6 px-1 w-full">
                                    # let name = arr["student_name"].toUpperCase().split(/\s+/); let shortName =
                                    name[0].charAt(0) + name[1].charAt(0); #
                                    #= shortName #
                                </span>
                            </div>
                        </div>
                        @else
                        <div class="w-10 h-10 rounded-full">
                            <img class="w-10 h-10 flex-1 !rounded-md" src="{{ $studentDetails[0]->profile_pic }}"
                                alt="{{ $studentDetails[0]->first_name }}" />
                        </div>
                        @endif
                    </div>
                    <div class="inline-flex flex-col items-start justify-center h-full">
                        <p class="text-gray-700 text-2xl font-normal leading-loose">#= arr["student_name"] #</p>
                    </div>
                </div>
                <!--<div class="w-24 p-2 bg-primary-blue-100 rounded-lg justify-center items-center gap-2 flex"> <div class="text-primary-blue-500 text-xs font-normal uppercase">View Batch</div> </div>-->
            </div>
            <div class="justify-start items-start gap-8 inline-flex w-full md:w-1/2">
                <div class="w-full flex-col justify-start items-start gap-1 inline-flex">
                    <div class="text-gray-900 text-xs font-medium leading-none">Course</div>
                    <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">#= arr["course_name"] #
                    </div>
                </div>
                <div class="w-full flex-col justify-start items-start gap-1 inline-flex">
                    <div class="text-gray-900 text-xs font-medium leading-none">Course Duration</div>
                    <div class="text-gray-700 text-xs font-normal leading-tight tracking-wide">#= arr["course_duration"]
                        #</div>
                </div>
            </div>
            <div class="self-stretch h-px bg-gray-100"></div>
            <div class="self-stretch justify-start items-start gap-4 md:gap-6 grid grid-cols-2 md:grid-cols-5">
                <div
                    class="h-full grow shrink basis-0 self-stretch lg:h-28 p-4 bg-gray-50 rounded-md border border-gray-200 flex-col justify-start items-start gap-3 inline-flex">
                    <div class="text-gray-700 text-sm font-normal leading-tight tracking-tight pb-1">Present</div>
                    <div class="justify-start items-center gap-4 inline-flex">
                        <div class="text-gray-800 text-3xl font-normal leading-9">#= arr["present_day"] #</div>
                        <div class="justify-start items-center gap-1 flex lastMonthPresent">
                            <div class="relative">
                                <img src="{{ asset('v2/img/arrow_down.svg') }}" class="" alt="arrowDownIcon" />
                            </div>
                            <div class="text-green-500 text-xs font-normal leading-tight tracking-wide">
                                <span class="last_month_present">0</span>% last month
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="h-full grow shrink basis-0 self-stretch lg:h-28 p-4 bg-gray-50 rounded-md border border-gray-200 flex-col justify-start items-start gap-3 inline-flex">
                    <div class="text-red-500 text-sm font-normal leading-tight tracking-tight pb-1">Absent</div>
                    <div class="justify-start items-center gap-4 inline-flex">
                        <div class="text-red-500 text-3xl font-normal leading-9">#= (parseInt(arr["total_day"]) -
                            parseInt(arr["present_day"])) #</div>
                        <div class="justify-start items-center gap-1 flex lastMonthAbsent">
                            <div class="relative">
                                <img src="{{ asset('v2/img/arrow_down.svg') }}" class="" alt="arrowDownIcon" />
                            </div>
                            <div class="text-green-500 text-xs font-normal leading-tight tracking-wide">
                                <span class="last_month_absent">0</span>% last month
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="h-full grow shrink basis-0 self-stretch lg:h-28 p-4 bg-gray-50 rounded-md border border-gray-200 flex-col justify-start items-start gap-3 inline-flex">
                    <div class="text-gray-700 text-sm font-normal leading-tight tracking-tight pb-1">Warnings</div>
                    <div class="justify-start items-center gap-4 inline-flex">
                        <div class="text-gray-800 text-3xl font-normal leading-9">#= arr["warning_count"] #</div>
                    </div>
                </div>
                <div
                    class="h-full grow shrink basis-0 self-stretch p-4 bg-gray-50 rounded-md border border-gray-200 flex-col justify-start items-start gap-3 inline-flex">
                    <div class="self-stretch justify-start items-center gap-5 inline-flex">
                        <div class="grow shrink basis-0 flex-col justify-start items-start gap-3 inline-flex">
                            <div class="text-gray-700 text-sm font-normal leading-tight tracking-tight pb-1">Attendance
                                Percentage</div>
                            <div class="justify-start items-center gap-4 inline-flex">
                                # let overallAttd = ((arr["overall_attd"]) ? ((arr["overall_attd"] == 100) ?
                                arr["overall_attd"] : parseFloat(arr["overall_attd"]).toFixed(1)) : 0); #
                                <div class="text-gray-800 text-3xl font-normal leading-9">#= overallAttd #%</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="h-full grow shrink basis-0 self-stretch p-4 bg-gray-50 rounded-md border border-gray-200 flex-col justify-start items-start gap-3 inline-flex">
                    <div class="self-stretch justify-start items-center gap-5 inline-flex">
                        <div class="grow shrink basis-0 flex-col justify-start items-start gap-3 inline-flex">
                            <div class="text-gray-700 text-sm font-normal leading-tight tracking-tight pb-1">Projected
                                Attendance</div>
                            <div class="justify-start items-center gap-2 inline-flex">
                                # let projectedAttd = ((arr["projected_attd"]) ? ((arr["projected_attd"] == 100) ?
                                arr["projected_attd"] : parseFloat(arr["projected_attd"]).toFixed(1)) : 0); #
                                <div class="text-gray-800 text-3xl font-normal leading-9">#= projectedAttd #%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </x-v2.animation.slide>
    </div>
    <x-v2.skeleton.templates.attendance-part class="tw-skeleton" style="display: none;" />
</script>