<template>
    <Layout :noSpacing="true" :loading="true" :pt="{ wrapper: 'h-full' }">
        <template v-slot:pageTitleContent>
            <PageTitleContent
                :title="isStudentId ? 'Student ID Builder' : 'Certificate Builder'"
                :linkurl="
                    route(isStudentId ? 'spa.student-id-builder.list' : 'spa.certificate.templates')
                "
                :back="true"
            />
        </template>
        <Toolbar
            :selectedTextNode="selectedNode"
            :updateTextProperties="updateTextProperties"
            :deleteSelectedText="handleDelete"
            :updateShapeProperties="updateShapeProperties"
            :selectedNode="selectedNode"
            :certificateEmbedeUrl="certificateEmbedeUrl"
            @save="storeData({}, false)"
            @saveAs="openSavePopup = true"
            @preview="handleOpenPreview"
            @undo="handleUndo"
            @redo="handleRedo"
            @toggle="handleToggleSidebar"
            @exportPng="exportAsPng"
            @exportHtml="exportAsHtml"
            v-model:certificateName="certificateName"
            :disabled="!enableSave"
            :canUndo="canUndo"
            :canRedo="canRedo"
            :templateName="selectedCertificate.name"
        />
        <div class="flex h-fit">
            <div
                class="absolute left-1 top-1/2 z-10 col-span-1 flex h-fit -translate-y-1/2 rounded-lg border border-gray-200 bg-white shadow"
                ref="positionRef"
            >
                <div class="border-r">
                    <ul>
                        <template v-for="(menu, index) in menus">
                            <li
                                class="flex cursor-pointer flex-col items-center justify-center gap-y-1 text-black/85 transition-colors hover:text-blue-600"
                                :class="
                                    menu.slug === currentMenu
                                        ? 'border-r-2 border-primary-blue-500 bg-primary-blue-50 text-primary-blue-500'
                                        : ''
                                "
                            >
                                <button
                                    class="flex h-[68px] w-[80px] flex-col items-center justify-center"
                                    @click="handleMenuClick($event, menu)"
                                    @mouseenter="handleMenuChange($event, menu)"
                                    @mouseleave="onSidebarLeave"
                                    draggable="true"
                                    @dragstart="onDragStart($event, menu?.slug)"
                                >
                                    <IconDocumentRibbon24Regular
                                        class="h-5 w-5"
                                        v-if="menu.slug === 'templates'"
                                    />
                                    <IconResizeImage24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'background'"
                                    />
                                    <IconImageCopy24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'images'"
                                    />
                                    <IconTextT24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'texts'"
                                    />
                                    <IconBraces24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'attributes'"
                                    />
                                    <IconShapes24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'shapes'"
                                    />
                                    <IconScanQrCode24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'qr_codes'"
                                    />
                                    <IconPersonSquare24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'profile'"
                                    />
                                    <span class="text-xs">{{ menu.label }}</span>
                                </button>
                            </li>
                        </template>
                    </ul>
                </div>
                <div
                    class="flex-1 space-y-4 p-4"
                    v-show="sidebar"
                    @mouseenter="onPopupEnter"
                    @mouseleave="onPopupLeave"
                    :class="'absolute left-[84px] top-1/2 z-50 max-h-[550px] min-h-[500px] w-[300px] -translate-y-1/2 overflow-y-scroll rounded border border-gray-200 bg-white shadow-xl'"
                >
                    <div
                        class="flex flex-col gap-3"
                        v-if="currentMenu === 'templates'"
                        @mouseenter="onPopupEnter"
                        @mouseleave="onPopupLeave"
                    >
                        <p class="text-sm font-medium">Default Templates</p>
                        <hr />
                        <div class="space-y-4">
                            <div class="grid grid-cols-2 gap-3">
                                <!-- <button
                                    class="group relative aspect-square cursor-pointer overflow-hidden border bg-gray-800/30 hover:border-primary-blue-300 focus:border-primary-blue-500"
                                    @click="handleCreateTemplate"
                                >
                                    <span
                                        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                                    >
                                        <IconAdd24Regular
                                            class="h-8 w-8 font-bold text-white"
                                      d  />
                                    </span>
                                </button> -->
                                <template v-for="(template, index) in templates" :key="template.id">
                                    <button
                                        class="group relative aspect-[707/1000] cursor-pointer overflow-hidden border hover:border-primary-blue-300 hover:bg-gray-800/30 focus:border-primary-blue-500"
                                        @click="handleLoadTemplate(template, 'default')"
                                        draggable="true"
                                        @dragstart="onTemplateDragStart($event, template)"
                                    >
                                        <img
                                            v-if="template.name"
                                            :src="template.thumbnail"
                                            class="image-item h-full w-full object-contain"
                                            alt="Certificate Image"
                                        />
                                        <span
                                            class="invisible absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:visible group-hover:opacity-100"
                                        >
                                            <IconAdd24Regular
                                                class="h-8 w-8 font-bold text-white"
                                            />
                                        </span>
                                    </button>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div
                        class="flex flex-col gap-3"
                        v-if="currentMenu === 'texts'"
                        @mouseenter="onPopupEnter"
                        @mouseleave="onPopupLeave"
                    >
                        <p class="text-sm font-medium">Texts</p>
                        <hr />
                        <ul class="space-y-4">
                            <Button
                                variant="secondary"
                                size="xs"
                                class="w-full"
                                @click="addText(null)"
                                ><IconAdd24Regular /> Add Text</Button
                            >
                            <li
                                class="border-b px-2 py-3 text-xs text-gray-800"
                                v-for="(textItem, index) in textLists"
                            >
                                <span>{{ textItem.text }}</span>
                            </li>
                        </ul>
                    </div>
                    <div
                        class="flex flex-col gap-3"
                        v-if="currentMenu === 'background'"
                        @mouseenter="onPopupEnter"
                        @mouseleave="onPopupLeave"
                    >
                        <p class="text-sm font-medium">Custom Background</p>
                        <hr />
                        <input
                            type="file"
                            ref="bgFileInput"
                            @change="handleImageUpload($event, 'background')"
                            accept="image/*"
                            class="hidden"
                        />
                        <Button
                            variant="secondary"
                            size="xs"
                            class="w-full"
                            @click="triggerBackgroundUpload"
                            ><IconAdd24Regular /> Add Custom Background</Button
                        >
                        <Button
                            variant="danger"
                            size="xs"
                            class="w-full"
                            @click="removeBackground"
                            v-if="backgroundImage && backgroundImageSrc"
                            ><IconAdd24Regular /> Remove Background</Button
                        >
                        <hr />
                        <div class="space-y-4">
                            <p class="text-sm font-medium">Format</p>
                            <RadioGroup :data-items="format" v-model="selectedFormat" />
                            <div class="flex items-center gap-4" v-if="showCustomFormat">
                                <div class="space-y-1">
                                    <p class="text-xs">Width</p>
                                    <NumericTextBox
                                        :style="{ width: '100' }"
                                        :placeholder="'width'"
                                        v-model="customWidth"
                                    >
                                    </NumericTextBox>
                                </div>
                                <div class="space-y-1">
                                    <p class="text-xs">height</p>
                                    <NumericTextBox
                                        :style="{ width: '100' }"
                                        :placeholder="'height'"
                                        v-model="customHeight"
                                    >
                                    </NumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <p class="text-sm font-medium">Orientation</p>
                            <RadioGroup :data-items="orientation" v-model="selectedOrientation" />
                        </div>
                    </div>
                    <div
                        class="flex flex-col gap-3"
                        v-if="currentMenu === 'attributes'"
                        @mouseenter="onPopupEnter"
                        @mouseleave="onPopupLeave"
                    >
                        <p class="text-sm font-medium">Attributes</p>
                        <hr />

                        <!-- <Button
                            variant="secondary"
                            size="xs"
                            class="w-full"
                            @click="addAttributePopup"
                            ><IconAdd24Regular /> Add Attributes</Button
                        > -->
                        <ul class="h-[calc(100vh-200px)] space-y-4 overflow-y-auto">
                            <li
                                v-for="(group, parent) in defaultAttrs"
                                :key="parent"
                                class="space-y-3 border-b pb-4"
                            >
                                <p class="text-sm capitalize text-gray-800">
                                    {{ parent }}
                                </p>
                                <template v-for="(attrs, index) in group" :key="index">
                                    <button
                                        class="w-full space-y-3 rounded p-1 hover:bg-primary-blue-50"
                                        @click="addText(`[${attrs.tag}]`)"
                                        draggable="true"
                                        @dragstart="onAttributesDragStart($event, attrs)"
                                    >
                                        <div class="space-y-1 text-left">
                                            <p class="text-xs text-gray-700">
                                                {{ attrs.name }}
                                            </p>
                                            <p class="text-xs text-gray-400">[{{ attrs.tag }}]</p>
                                        </div>
                                    </button>
                                </template>
                            </li>

                            <!-- <li
                                class="border-b px-2 py-3 text-xs text-gray-800"
                                v-for="(attrsItem, index) in attrsLists"
                            >
                                <span>{{ attrsItem.text }}</span>
                            </li> -->
                        </ul>
                    </div>
                    <div
                        class="flex flex-col gap-3"
                        v-if="currentMenu === 'images'"
                        @mouseenter="onPopupEnter"
                        @mouseleave="onPopupLeave"
                    >
                        <p class="text-sm font-medium">Images</p>
                        <input
                            type="file"
                            ref="fileInput"
                            @change="handleImageUpload"
                            accept="image/*"
                            class="hidden"
                        />
                        <Button
                            variant="secondary"
                            size="xs"
                            class="w-full"
                            @click="triggerFileUpload"
                            ><IconAdd24Regular /> Add Images</Button
                        >
                        <p class="text-xs text-gray-500">Supports SVG, PNG, and JPG. Up to 2MB.</p>
                        <hr />
                        <div class="space-y-4">
                            <div class="grid grid-cols-2 gap-3">
                                <template v-for="(image, index) in imageElements">
                                    <button
                                        class="group relative cursor-pointer overflow-hidden border p-6 hover:border-primary-blue-300 focus:border-primary-blue-500"
                                        @click="handleSelectNode(image)"
                                    >
                                        <img
                                            v-if="image.name"
                                            :src="image.name"
                                            class="image-item h-full w-full object-contain"
                                            alt="Certificate Image"
                                        />
                                        <!-- <span
                                            class="invisible absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:visible group-hover:opacity-100"
                                        >
                                            <IconAdd24Regular
                                                class="h-8 w-8 font-bold text-white"
                                            />
                                        </span> -->
                                    </button>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div
                        class="flex flex-col gap-3"
                        v-if="currentMenu === 'shapes'"
                        @mouseenter="onPopupEnter"
                        @mouseleave="onPopupLeave"
                    >
                        <p class="text-sm font-medium">Shapes</p>
                        <hr />
                        <div class="grid grid-cols-3 gap-3">
                            <template v-for="(shape, index) in shapes" :key="shape.value">
                                <div class="flex flex-col gap-1">
                                    <button
                                        class="group relative cursor-pointer overflow-hidden border p-6 hover:border-primary-blue-300 hover:bg-gray-800/30 focus:border-primary-blue-500"
                                        @click="handleLoadShape($event, shape)"
                                        draggable="true"
                                        @dragstart="onShapesDragStart($event, shape)"
                                    >
                                        <icon :name="shape.value" />
                                        <span
                                            class="invisible absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:visible group-hover:opacity-100"
                                        >
                                            <IconAdd24Regular
                                                class="h-8 w-8 font-bold text-white"
                                            />
                                        </span>
                                    </button>
                                    <p class="text-center text-xs">
                                        {{ shape.label }}
                                    </p>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div
                        class="flex flex-col gap-3"
                        v-if="currentMenu === 'qr_codes'"
                        @mouseenter="onPopupEnter"
                        @mouseleave="onPopupLeave"
                    >
                        <Button variant="secondary" size="xs" class="w-full"
                            ><IconAdd24Regular />Verification Page</Button
                        >
                        <p class="text-xs text-gray-500">
                            Redirects to the digital version of the certificate.
                        </p>
                        <hr />
                        <Button
                            variant="secondary"
                            size="xs"
                            class="w-full"
                            @click="triggerFileUpload"
                            ><IconAdd24Regular />Custom Url</Button
                        >
                        <p class="text-xs text-gray-500">Redirects to custom url.</p>
                    </div>
                </div>
            </div>
            <div
                class="relative z-0 col-span-3 flex flex-1 flex-col items-center justify-center overflow-y-auto"
                :style="{ height: 'fit-content' }"
            >
                <div class="my-8 space-y-4">
                    <div
                        class="relative border border-gray-200 bg-white"
                        @dragover.prevent
                        @drop="onDrop"
                    >
                        <v-stage ref="stageRef" :config="stageConfig" @click="handleStageClick">
                            <v-layer ref="layerRef">
                                <v-image
                                    v-if="backgroundImage"
                                    :config="{
                                        x: 0,
                                        y: 0,
                                        image: backgroundImage,
                                        width: stageConfig.width,
                                        height: stageConfig.height,
                                        name: backgroundImageSrc,
                                        type: 'BACKGROUND',
                                    }"
                                />
                                <DraggableShapes
                                    v-for="(shapeItem, index) in shapeElements"
                                    :key="'shape-' + shapeItem.id"
                                    ref="shapeNodes"
                                    :shapeItem="shapeItem"
                                    :updateSize="updateShapeSize"
                                    :onSelect="handleSelectNode"
                                    @dragstart="hideToolbar"
                                    @dragend="handleDragEnd"
                                    @dragmove="onDragMove"
                                    :isSelected="isSelected"
                                />
                                <EditableText
                                    v-for="(textItem, index) in textElements"
                                    :key="textItem.id"
                                    ref="textNodes"
                                    :textItem="textItem"
                                    :updateText="updateText"
                                    :updateSize="updateSize"
                                    :onSelect="handleSelectNode"
                                    :onHoverSelect="handleHoverSelectNode"
                                    @dragstart="hideToolbar"
                                    @dragend="handleDragEnd"
                                    @dragmove="onDragMove"
                                    :selectedNode="selectedNode"
                                    :isSelected="isSelected"
                                />
                                <DraggableImage
                                    v-for="(imageItem, index) in imageElements"
                                    :key="imageItem.id"
                                    ref="imageNodes"
                                    :imageItem="imageItem"
                                    :updateSize="updateImageSize"
                                    :onSelect="handleSelectNode"
                                    @dragstart="hideToolbar"
                                    @dragend="handleDragEnd"
                                    @dragmove="onDragMove"
                                    :isSelected="isSelected"
                                />
                                <v-image
                                    v-if="qrImage"
                                    ref="qrNode"
                                    :config="{
                                        id: `qr-${Date.now()}`,
                                        type: 'QR',
                                        x: qrConfig.x,
                                        y: qrConfig.y,
                                        width: qrConfig.width,
                                        height: qrConfig.height,
                                        image: qrImage || null,
                                        name: qrSrc || null,
                                        draggable: true,
                                    }"
                                    @dblclick="handleQrDblClick"
                                    @dbltap="handleQrDblClick"
                                    @click="handleSelectNode(qrNode)"
                                    @tap="handleSelectNode(qrNode)"
                                    @transform="handleQrTransform"
                                    @dragend="handleDragEnd($event, qrNode)"
                                    @dragmove="onDragMove"
                                />

                                <Transformer
                                    :selectedNode="selectedNode"
                                    :stageRef="stageRef"
                                    ref="transformerRef"
                                />

                                <HoverTransformer
                                    :selectedNode="hoveredNode"
                                    ref="hoveredTransformerRef"
                                />

                                <v-line
                                    v-for="(guide, index) in guidelines"
                                    :key="'guide-' + index"
                                    :config="guide"
                                />
                                <QuickActionBar
                                    ref="actionBarRef"
                                    :selectedNode="selectedNode"
                                    @deleteNode="handleDelete"
                                    @duplicateNode="handleDuplicate"
                                    @mouseover="handleActionMouseOver"
                                />
                            </v-layer>
                        </v-stage>
                    </div>
                </div>
            </div>
        </div>
        <AttributePopup :visible="openAttributePopup" @submit="handleAttributeSubmit" />
        <PreviewPopupWithData
            :visible="openPreviewPopup"
            @save="handleToolbarSave"
            :data="savedObj"
            @stopLoading="isIframeLoad"
            :certificateEmbedeUrl="certificateEmbedeUrl"
            @close="closePreviewPopup"
            type="html"
        />
        <SaveTemplatePopup
            :dataItem="selectedCertificate"
            :certificateIdFormate="certificateIdFormate"
            :visible="openSavePopup"
            @submit="handleSaveAs"
            @close="openSavePopup = false"
        />
        <LoadingPopup :visible="loaderStore.contextLoaders['generate']" />
        <div
            class="fixed bottom-3 right-9 flex w-24 items-center gap-2 rounded-md bg-white p-2 shadow"
            v-if="showStatus"
        >
            <template v-if="isSaving">
                <!-- Spinner -->
                <svg class="h-6 w-6 animate-spin text-blue-500" fill="none" viewBox="0 0 24 24">
                    <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                    ></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
                </svg>
                <span class="font-medium text-gray-600">Saving...</span>
            </template>

            <template v-else>
                <!-- Checkmark -->
                <IconCheckmarkCircle24Filled class="h-6 w-6 text-green-500" />
                <span class="font-medium text-gray-600">Saved</span>
            </template>
        </div>
        <div class="fixed bottom-4 right-3">
            <Popover :open="openInfo" @toggle="openInfo = !openInfo" @close="openInfo = false">
                <template #picker>
                    <Button variant="icon" class="h-6 w-6 p-0">
                        <IconInfo24Regular class="size-5 text-gray-500" />
                    </Button>
                </template>
                <template #popup>
                    <div class="w-52 space-y-2 p-4 text-xs">
                        <div class="text-sm font-medium text-gray-800">Info</div>
                        <LabelValuePair
                            :pt="{ root: 'flex gap-2' }"
                            label="Name:"
                            :value="selectedCertificate.name"
                        />
                        <LabelValuePair
                            :pt="{ root: 'flex gap-2' }"
                            label="Format:"
                            :value="certificateFormate"
                        />
                        <LabelValuePair
                            :pt="{ root: 'flex gap-2', value: 'capitalize' }"
                            label="Orientation:"
                            :value="selectedCertificate.orientation"
                        />
                    </div>
                </template>
            </Popover>
        </div>
    </Layout>
</template>
<script setup>
import {
    IconAdd24Regular,
    IconBraces24Regular,
    IconDocumentRibbon24Regular,
    IconImageCopy24Regular,
    IconResizeImage24Regular,
    IconScanQrCode24Regular,
    IconShapes24Regular,
    IconTextT24Regular,
    IconCheckmarkCircle24Filled,
    IconInfo24Regular,
    IconPersonSquare24Regular,
} from '@iconify-prerendered/vue-fluent';
import { NumericTextBox, RadioGroup } from '@progress/kendo-vue-inputs';
import Button from '@spa/components/Buttons/Button.vue';
import AttributePopup from '@spa/modules/certificate-builder/AttributePopup.vue';
import DraggableImage from '@spa/modules/certificate-builder/DraggableImage.vue';
import DraggableShapes from '@spa/modules/certificate-builder/DraggableShapes.vue';
import EditableText from '@spa/modules/certificate-builder/EditableText.vue';
import PreviewPopup from '@spa/modules/certificate-builder/PreviewPopup.vue';
import PreviewPopupWithData from '@spa/modules/certificate-builder/PreviewPopupWithData.vue';
import QuickActionBar from '@spa/modules/certificate-builder/QuickActionBar.vue';
import Transformer from '@spa/modules/certificate-builder/Transformer.vue';
import Toolbar from '@spa/modules/certificate-builder/Toolbar.vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import globalHelper from '@spa/plugins/global-helper';
import useCertificateResource from '@spa/services/certificates/certificateResource';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import { useCertificateStore } from '@spa/stores/modules/certificate.store';
import Konva from 'konva';
import {
    computed,
    nextTick,
    onBeforeUnmount,
    onMounted,
    onUnmounted,
    ref,
    watch,
    onBeforeMount,
} from 'vue';
import { useImage } from 'vue-konva';
import data from './certificateProps.json';
import SaveTemplatePopup from '@spa/modules/certificate-builder/SaveTemplatePopup.vue';
import HoverTransformer from '@spa/modules/certificate-builder/HoverTransformer.vue';
import LoadingPopup from '@spa/modules/certificate-builder/LoadingPopup.vue';
import { router } from '@inertiajs/vue3';
import { debounce } from 'lodash';
import Popover from '@spa/components/Popover/Popover.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';

Konva._fixTextRendering = true;

const loaderStore = useLoaderStore();

const sampleQR = window['APP_URL'] + '/v2/img/sample-qr.png';
const sampleProfile = window['APP_URL'] + '/v2/img/sample-profile.png';

// Guidelines
const GUIDELINE_OFFSET = 5;
const guidelines = ref([]);
const certificateStore = useCertificateStore();
const props = defineProps({
    templates: {
        type: Array,
        default: () => [],
    },
    attributes: {
        type: Array,
        default: () => [],
    },
    selectedCertificate: {
        type: Object,
        default: () => [],
    },
    certificateIdFormate: {
        type: Object,
        default: () => [],
    },
    embedeUrl: {
        type: String,
        default: () => '',
    },
});
const openInfo = ref(false);
const actionBarRef = ref(null);
const templates = computed(() => data.defaultTemplates);
const templates1 = computed(() => props.templates);
const certificateEmbedeUrl = ref(props.embedeUrl);
const defaultAttrs = computed(() => {
    const items = props.attributes;
    return items.reduce((acc, item) => {
        if (!acc[item.parent]) {
            acc[item.parent] = [];
        }
        acc[item.parent].push(item);
        return acc;
    }, {});
});

const certificateName = ref('');

// CONSTANTS
const fixedImageWidth = 200;

const menus = computed(() => {
    const commonMenus = [
        {
            label: 'Background',
            slug: 'background',
            showPopover: true,
        },
        {
            label: 'Images',
            slug: 'images',
            showPopover: false,
        },
        {
            label: 'Texts',
            slug: 'texts',
            showPopover: false,
        },
        {
            label: 'Attributes',
            slug: 'attributes',
            showPopover: true,
        },
        {
            label: 'Shapes',
            slug: 'shapes',
            showPopover: true,
        },
    ];

    if (isStudentId.value) {
        return [
            ...commonMenus,
            {
                label: 'Profile',
                slug: 'profile',
                showPopover: false,
            },
        ];
    } else {
        return [
            ...commonMenus,
            {
                label: 'QR Codes',
                slug: 'qr_codes',
                showPopover: false,
            },
        ];
    }
});

const orientation = [
    {
        label: 'Portrait',
        value: 'portrait',
    },
    {
        label: 'Horizontal',
        value: 'horizontal',
    },
];

const format = computed(() => {
    if (isStudentId.value) {
        return [
            {
                label: 'CR80',
                value: 'cr80',
            },
            {
                label: 'Custom',
                value: 'custom',
            },
        ];
    }
    return [
        {
            label: 'A4',
            value: 'a4',
        },
        {
            label: 'US Letter',
            value: 'us-letter',
        },
        {
            label: 'Custom',
            value: 'custom',
        },
    ];
});

const shapes = [
    {
        label: 'Vertical Line',
        value: 'line-vert',
    },
    {
        label: 'Horizontal Line',
        value: 'line',
    },
    {
        label: 'Rectangle',
        value: 'rect',
    },
    {
        label: 'Circle',
        value: 'circle',
    },
    {
        label: 'Ellipse',
        value: 'ellipse',
    },
    {
        label: 'Rectangle Fill',
        value: 'rect-fill',
    },
    {
        label: 'Circle Fill',
        value: 'circle-fill',
    },
    {
        label: 'Ellipse Fill',
        value: 'ellipse-fill',
    },
];

const canvasSize = ref({
    width: window.innerWidth,
    height: window.innerHeight,
    fill: '#D1D5DB',
});

const stageConfig = ref({
    width: 707,
    height: 1000,
    fill: 'white',
    stroke: '#D1D5DB',
    strokeWidth: 1,
});

// REACTIVE STATES
const currentMenu = ref('templates');
const selectedFormat = ref('a4');
const showCustomFormat = ref(false);
const selectedOrientation = ref('portrait');
const customWidth = ref(700);
const customHeight = ref(1000);
const textElements = ref([]);
const selectedNode = ref(null);
const shapeElements = ref([]);
const transformerRef = ref(null);
const hoveredNode = ref(null);
const hoveredTransformerRef = ref(null);

const layerRef = ref(null);
const stageRef = ref(null);
const fileInput = ref(null);
const backgroundImage = ref(null);
const backgroundImageSrc = ref(null);
const bgFileInput = ref(null);

const imageElements = ref([]);

// Undo/Redo History
const history = ref([{ text: [], images: [], shapes: [], selectedNodeId: null }]);
const historyStep = ref(0);

const openAttributePopup = ref(false);
const openPreviewPopup = ref(false);
const savedObj = ref({});
const isDataSaved = ref(true);
const savedDataUrl = ref(null);
const savedHTML = ref('');
const openSavePopup = ref(false);

const isNewTemplate = ref(true);
const selectedTemplate = ref(null);
// COMPUTED VALUES
const attrsLists = computed(() => {
    return textElements.value.filter((item) => item.type === 'ATTRS');
});

const textLists = computed(() => {
    return textElements.value.filter((item) => item.type === 'TEXT');
});

const enableSave = computed(() => {
    return historyStep.value > 0;
});

const imagePosition = ref({
    x: null,
    y: null,
});

const positionRef = ref(null);
const templateType = ref('certificate');

onMounted(() => {
    const url = new URL(window.location.href);
    const certificateId = url.searchParams.get('certificateId');
    const type = url.searchParams.get('type');
    if (type) {
        templateType.value = type;
        qrSrc.value = sampleProfile;
        selectedFormat.value = 'cr80';
    }

    if (!certificateId) {
        router.visit(route('spa.certificate.templates'));
        globalHelper.methods.showPopupError('Missing certificate ID. Redirecting...', 'Error');
    }
});

const isStudentId = computed(() => {
    return templateType.value === 'student-id';
});

const getDefaultY = () => {
    const button = positionRef?.value?.$el || positionRef?.value;
    const stage = stageRef?.value?.getNode();

    if (!button || !stage) return;

    const { top: buttonTop } = button.getBoundingClientRect();
    const { top: canvasTop } = stage.container().getBoundingClientRect();

    const stageHeight = stageConfig.value.height;

    const relativeY = buttonTop - canvasTop;
    const defaultY = Math.max(0, Math.min(relativeY, stageHeight - 50)) + 200;
    return defaultY;
};

const qrImage = ref(null);
const qrSrc = ref(sampleQR);
const qrNode = ref(null);

const qrConfig = ref({
    id: `qr-${Date.now()}`,
    type: 'QR',
    x: 100,
    y: getDefaultY(),
    width: 100,
    height: 100,
    image: null,
    name: null,
    draggable: true,
});

watch(qrSrc, (newSrc) => {
    qrConfig.value.name = newSrc || sampleQR;
});

// IMAGES CANVAS
const addImage = (image, imageSrc, height) => {
    imageElements.value.push({
        id: `image-${Date.now()}`,
        type: 'IMAGE',
        x: imagePosition.value.x !== null ? imagePosition.value.x : 100,
        y: imagePosition.value.y !== null ? imagePosition.value.y : getDefaultY(),
        width: fixedImageWidth,
        height: height,
        isEditing: false,
        image: image,
        name: imageSrc,
    });
    saveState();
};

const handleSelectNode = (node) => {
    if (!node) return;
    selectedNode.value = node;
    if (selectedNode.value) {
        transformerRef.value?.attachTransformer(selectedNode.value);
        actionBarRef.value.attachToolbar(selectedNode.value);
        hoveredNode.value = null;
        hoveredTransformerRef.value.detachTransformer();
    }
};

const handleHoverSelectNode = (node) => {
    if (!node) return;
    hoveredNode.value = node;
    hoveredTransformerRef.value?.attachTransformer(hoveredNode.value);
    // const getHoveredNode = node.getNode();
    // const getSelectedNode = selectedNode.value?.getNode();
    // if (getSelectedNode && getHoveredNode._id !== getSelectedNode._id) {
    // } else {
    //     hoveredNode.value = null;
    //     hoveredTransformerRef.value.detachTransformer();
    // }
};

const updateShapeSize = (id, size) => {
    // const shape = shapeElements.value.find((t) => t.id === id);
    // if (shape) {
    //     shape.width = size.width;
    //     shape.height = size.height;
    // }
    // saveState();
    const index = shapeElements.value.findIndex((t) => t.id === id);
    if (index !== -1) {
        shapeElements.value[index] = {
            ...shapeElements.value[index],
            width: size.width,
            height: size.height,
        };
        saveState();
    }
};

const updateImageSize = (id, size) => {
    const image = imageElements.value.find((t) => t.id === id);
    if (image) {
        image.width = size.width;
        image.height = size.height;
    }
    saveState();
};

const handleFormatChange = (e) => {
    const value = e.value;
    selectedFormat.value = value;
};

const triggerFileUpload = () => {
    fileInput.value.click();
};

const triggerBackgroundUpload = () => {
    bgFileInput.value.click();
};

const removeBackground = () => {
    backgroundImage.value = null;
    backgroundImageSrc.value = null;
};

const handleImageUpload = (event, type) => {
    const file = event.target.files[0];

    if (!file) return;

    const reader = new FileReader();

    reader.onload = () => {
        const image = new Image();

        image.src = reader.result;

        image.onload = () => {
            const layer = stageRef.value.getStage().getLayers();

            if (type === 'background') {
                backgroundImageSrc.value = reader.result;

                backgroundImage.value = image;
            } else {
                const originalWidth = image.width;

                const originalHeight = image.height;

                const imageSrc = reader.result;

                // Maintain aspect ratio

                const aspectRatio = originalHeight / originalWidth;

                const calculatedHeight = fixedImageWidth * aspectRatio;

                addImage(image, imageSrc, calculatedHeight);
            }

            // layer.batchDraw();

            nextTick(() => {
                layerRef.value.getNode().batchDraw();
            });
        };
    };

    reader.readAsDataURL(file);

    saveState();
};

const handleImageUpload1 = (event, type) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
        const image = new Image();
        image.src = reader.result;

        image.onload = () => {
            const layer = stageRef.value.getStage().getLayers();

            if (type === 'background') {
                backgroundImageSrc.value = reader.result;
                backgroundImage.value = image;
                const layer = stageRef.value.getStage().getLayers()[0];

                // Remove existing background image if any
                const existingBackground = layer.find('.background');
                if (existingBackground) {
                    existingBackground.destroy();
                }

                const backgroundNode = new Konva.Image({
                    image: image,
                    width: stageConfig.value.width,
                    height: stageConfig.value.height,
                    name: reader.result,
                    className: 'background',
                    listening: false, // Disable mouse events on background
                });

                // Add background to the beginning of the layer (behind everything)
                layer.add(backgroundNode);
                backgroundNode.moveToBottom();
                layer.batchDraw();
            } else {
                const originalWidth = image.width;
                const originalHeight = image.height;
                const imageSrc = reader.result;

                // Maintain aspect ratio
                const aspectRatio = originalHeight / originalWidth;
                const calculatedHeight = fixedImageWidth * aspectRatio;
                addImage(image, imageSrc, calculatedHeight);
            }
            nextTick(() => {
                layerRef.value.getNode().batchDraw();
            });
        };
    };
    reader.readAsDataURL(file);
    saveState();
};

// TEXT AND ATTRS CANVAS
const addText = async (text = null, position = { x: null, y: null }) => {
    const stageWidth = stageConfig.value.width;
    const defaultY = getDefaultY();
    const newElement = {
        id: Date.now(),
        type: text ? 'ATTRS' : 'TEXT',
        text: text || 'Double Click To Edit',
        x: position.x !== null ? position.x - stageWidth / 4 : 100,
        y: position.y !== null ? position.y : defaultY,
        width: stageConfig.value.width / 2,
        isEditing: false,
        fontSize: 16,
        fontFamily: 'Arial',
        fill: 'black',
        align: 'center',
        textDecoration: '',
        fontStyle: 'normal',
        padding: 10,
        strokeWidth: 1,
        lineHeight: 1.5,
        stroke: 'red',
        strokeWidth: 1,
    };
    textElements.value.push(newElement);
    sidebar.value = false;
    saveState();
};

const updateText = (id, newText) => {
    const text = textElements.value.find((t) => t.id === id);
    if (text) text.text = newText;
    saveState();
};

const updateSize = (id, newWidth, newHeight) => {
    const element = textElements.value.find((el) => el.id === id);
    if (element) {
        element.width = newWidth;
        element.height = newHeight;
    }

    saveState();
};

const updateTextProperties = (properties) => {
    if (selectedNode.value) {
        const node = selectedNode.value.getNode();
        const existingAttrs = node.attrs;
        node.setAttrs({ ...existingAttrs, ...properties });
        const nodeId = existingAttrs.id; // fallback handling
        const index = textElements.value.findIndex((el) => el.id === nodeId);

        if (index !== -1) {
            // Merge existing properties with updated ones
            textElements.value[index] = {
                ...textElements.value[index],
                ...properties,
            };
        }
        node.getLayer().batchDraw();
        saveState();
    }
};

const updateShapeProperties = (properties) => {
    if (selectedNode.value) {
        const node = selectedNode.value.getNode();
        const existingAttrs = node.attrs;

        node.setAttrs({ ...existingAttrs, ...properties });
        node.getLayer().batchDraw();

        const nodeId = node._id || node._id(); // You might just use `node._id()` here safely
        const targetElement = textElements.value.find((el) => el.id === nodeId);

        if (targetElement && properties.fontFamily) {
            targetElement.fontFamily = properties.fontFamily;
        }
    }
    saveState();
};

const handleDelete = () => {
    if (!selectedNode.value) return;
    const node = selectedNode.value.getNode();
    if (node.attrs.type === 'TEXT' || node.attrs.type === 'ATTRS') {
        textElements.value = textElements.value.filter(
            (item) => item.id !== selectedNode.value.getNode().attrs.id
        );
    } else if (node.attrs.type === 'IMAGE') {
        imageElements.value = imageElements.value.filter(
            (item) => item.id !== selectedNode.value.getNode().attrs.id
        );
    } else if (node.attrs.type === 'SHAPE') {
        shapeElements.value = shapeElements.value.filter(
            (item) => item.id !== selectedNode.value.getNode().attrs.id
        );
    } else if (node.attrs.type === 'QR') {
        qrImage.value = null;
        qrNode.value = null;
    }

    handleDetach();
    saveState();
};

const addAttributePopup = () => {
    openAttributePopup.value = true;
};

const closeAttributePopup = () => {
    openAttributePopup.value = false;
};

const handleAttributeSubmit = (values) => {
    addText(values.attribute_name);
    closeAttributePopup();
};

const $certificate = useCertificateResource('spa/certificates');

const saveStageState = (data = {}, autoSave = true) => {
    const isNonEmptyObject = data && Object.keys(data).length > 0;

    if (stageRef.value) {
        const stageRefNode = stageRef.value.getNode();
        let stageData = stageRef.value.getNode().toJSON();
        const json = stageRefNode.toJSON();
        const dataURL = stageRefNode.toDataURL({
            pixelRatio: 2,
        });

        savedObj.value = JSON.parse(json);

        const htmlContent = generateHTML();

        const blob = new Blob([htmlContent], { type: 'text/html' });

        savedDataUrl.value = dataURL;

        savedHTML.value = blob;

        localStorage.setItem('stageState', json);

        isDataSaved.value = true;

        const templateData = {
            json_data: stageData,
            html_data: htmlContent,
            paper_size: stageData.attrs?.width > stageData.attrs?.height ? 'landscape' : 'portrait',
            orientation:
                stageData.attrs?.width > stageData.attrs?.height ? 'landscape' : 'portrait',
            thumbnail: savedDataUrl.value,
            fonts_used: getFonts.value,
        };
        payload.value = {
            ...payload.value,
            ...templateData,
            ...(isNonEmptyObject ? { ...data, id: null } : {}),
        };
    }
};

const storeData = async (data = {}, autoSave = true) => {
    const isNonEmptyObject = data && Object.keys(data).length > 0;
    if (!autoSave) {
        handleDetach();
        await nextTick();
    }
    saveStageState(data, autoSave);
    if (isNonEmptyObject) {
        await $certificate.saveTemplate(payload.value);
    } else {
        if (selectedTemplate.value) {
            await $certificate.updateTemplate(
                payload.value,
                {
                    autoSave: autoSave,
                },
                isStudentId.value ? 'student-id' : 'certificate'
            );
            // globalHelper.methods.showPopupSuccess(
            //     "Template updated successfully",
            //     "Success",
            // );
        } else {
            globalHelper.methods.showPopupError('Template is not loaded', 'Error');
        }
    }
};

const isSavePreview = ref(false);

const closePreviewPopup = () => {
    openPreviewPopup.value = false;
    // if (isSavePreview.value) {
    //     router.visit(route("spa.certificate.templates"));
    // }
};

const handleOpenPreview = () => {
    saveStageState();

    openPreviewPopup.value = true;
    // openPreviewPopup.value = true;
    loaderStore.startContextLoading('generate');
    onEmbedLoad();
};

const handleDuplicate = (node) => {
    if (node.type === 'TEXT' || node.type === 'ATTRS') {
        textElements.value.push(node);
    } else if (node.type === 'IMAGE') {
        imageElements.value.push(node);
    } else if (node.type === 'SHAPE') {
        shapeElements.value.push(node);
    }
    saveState();
};

const isSaving = ref(false);
const showStatus = ref(false);

const performAutoSave = () => {
    isSaving.value = true;
    showStatus.value = true;

    storeData({});

    setTimeout(() => {
        isSaving.value = false;
        setTimeout(() => {
            showStatus.value = false;
        }, 1000);
    }, 1500);
};

const debounceStoreData = debounce(() => {
    performAutoSave();
}, 3000);

const saveState = () => {
    history.value = history.value.slice(0, historyStep.value + 1);
    history.value.push({
        text: JSON.parse(JSON.stringify(textElements.value)),
        images: JSON.parse(JSON.stringify(imageElements.value)),
        shapes: JSON.parse(JSON.stringify(shapeElements.value)),
        qr: JSON.parse(JSON.stringify(qrConfig.value)),
        selectedNodeId: selectedNode.value?.id || null,
    });
    historyStep.value++;
    debounceStoreData();
};

const canUndo = computed(() => historyStep.value > 1);
const canRedo = computed(() => historyStep.value < history.value.length - 1);

const restoreState = (state) => {
    textElements.value.splice(
        0,
        textElements.value.length,
        ...JSON.parse(JSON.stringify(state.text))
    );
    imageElements.value = state.images.map((image) => {
        const [imageObj] = useImage(image.name);
        return {
            ...image,
            image: imageObj,
        };
    });

    shapeElements.value.splice(
        0,
        shapeElements.value.length,
        ...JSON.parse(JSON.stringify(state.shapes))
    );

    if (state.qr) {
        qrConfig.value = { ...state.qr };
    }
    // if (selectedNode.value) {
    //     actionBarRef.value.attachToolbar(selectedNode.value);
    // }
};

const handleUndo = () => {
    if (historyStep.value === 0) return;
    historyStep.value--;
    const state = history.value[historyStep.value];
    restoreState(state);

    const selectedId = selectedNode.value?.getNode().attrs.id;
    const stillExists =
        textElements.value.some((el) => el.id === selectedId) ||
        shapeElements.value.some((el) => el.id === selectedId) ||
        imageElements.value.some((el) => el.id === selectedId);

    if (!stillExists) {
        handleDetach();
    }
    if (hoveredTransformerRef.value) {
        hoveredTransformerRef.value.detachTransformer();
    }
};

const handleRedo = () => {
    if (historyStep.value >= history.value.length - 1) return;
    historyStep.value++;
    restoreState(history.value[historyStep.value]);
    handleDetach();
};

const handleKeydown = (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
        e.preventDefault();
        handleUndo();
    } else if ((e.ctrlKey || e.metaKey) && e.key === 'y') {
        e.preventDefault();
        handleRedo();
    }
};

onMounted(() => {
    window.addEventListener('keydown', handleKeydown);
    // saveState(); // Save initial state
});

onUnmounted(() => {
    window.removeEventListener('keydown', handleKeydown);
});

const hideToolbar = () => {
    // Hide QuickActionBar (implement logic as needed)
};

const handleDragEnd = (e, nodeRef) => {
    if (!nodeRef) return;
    const node = nodeRef.getNode();
    guidelines.value = [];
    const elements = [...textElements.value, ...imageElements.value, ...shapeElements.value];
    const itemIndex = elements.findIndex((item) => item.id === node.attrs.id);

    if (itemIndex !== -1) {
        elements[itemIndex].x = e.target.x();
        elements[itemIndex].y = e.target.y();
    }

    if (node.attrs.type === 'QR') {
        qrConfig.value.x = e.target.x();
        qrConfig.value.y = e.target.y();
    }

    selectedNode.value = nodeRef;
    if (selectedNode.value) {
        transformerRef.value?.attachTransformer(selectedNode.value, true);
        actionBarRef.value.attachToolbar(selectedNode.value);
    }

    saveState();
};

const getImageStyle = (attrs) => {
    return {
        position: 'absolute',
        left: attrs.x + 'px',
        top: attrs.y + 'px',
        width: attrs.width + 'px',
        height: attrs.height + 'px',
    };
};

const handleWheel = (e) => {
    e.evt.preventDefault();

    const stage = stageRef.value.getNode();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    const mousePointTo = {
        x: (pointer.x - stage.x()) / oldScale,
        y: (pointer.y - stage.y()) / oldScale,
    };

    // how to scale? Zoom in? Or zoom out?
    let direction = e.evt.deltaY > 0 ? 1 : -1;

    // when we zoom on trackpad, e.evt.ctrlKey is true
    // in that case lets revert direction
    if (e.evt.ctrlKey) {
        direction = -direction;
    }

    const scaleBy = 1.01;
    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale,
    };
    stage.position(newPos);
};

const sidebar = ref(false);

const handleToggleSidebar = () => {
    sidebar.value = !sidebar.value;
};

const handleLoadTemplate = (json, type = '') => {
    let jsonData = json.json_data;
    if (type !== 'default') {
        selectedTemplate.value = json.id;
    }

    if (jsonData) {
        const children = jsonData?.children?.[0]?.children ?? [];

        const textItems =
            children.filter((item) => item.attrs.type === 'TEXT' || item.attrs.type === 'ATTRS') ||
            [];

        const allImages = children.filter((item) => item.attrs.type === 'IMAGE');

        const background = children.find((item) => item.attrs.type === 'BACKGROUND');

        const qr = children.find((item) => item.attrs.type === 'QR');
        if (qr) {
            // const [image] = useImage(sampleQR);
            const [image] = useImage(qrSrc.value);
            qrConfig.value = {
                ...qr.attrs,
            };
            watch(image, (newImage) => {
                if (newImage) {
                    qrImage.value = newImage;
                }
            });
            qrSrc.value = isStudentId ? sampleProfile : sampleQR;
        }

        if (background) {
            const [bgImage] = useImage(background.attrs.name);

            watch(bgImage, (newImage) => {
                if (newImage) {
                    backgroundImage.value = newImage;
                }
            });

            backgroundImageSrc.value = background.attrs.name;
        }

        const shapeItems = children.filter((item) => item.attrs.type === 'SHAPE');
        if (allImages.length > 0) {
            const imageItems = allImages || [];
            imageElements.value = imageItems.map((image) => {
                const [imageObj] = useImage(image.attrs.name);

                return {
                    ...image.attrs,

                    image: imageObj,
                };
            });
        }
        textElements.value = textItems.map((text) => ({
            ...text.attrs,
            fontSize: text.attrs.fontSize || 12,
        }));
        shapeElements.value = shapeItems.map((item) => item.attrs);

        const fonts = children
            .filter((item) => item.className === 'Text' || item.className === 'ATTRS')
            .map((item) => item.attrs.fontFamily)
            .filter((value, index, self) => value && self.indexOf(value) === index);
        if (fonts && fonts.length > 0) {
            fonts.forEach((font) => {
                const fontName = font.replace(/\s+/g, '+'); // Replace spaces with '+'
                const linkId = `google-font-${fontName}`;

                // Avoid adding the same link multiple times
                if (!document.getElementById(linkId)) {
                    const link = document.createElement('link');
                    link.id = linkId;
                    link.rel = 'stylesheet';
                    link.href = `https://fonts.googleapis.com/css2?family=${fontName}&display=swap`;
                    document.head.appendChild(link);
                }
            });
        }
    }
};

const handleLoadTemplate1 = (json) => {
    imageElements.value = [];
    textElements.value = [];
    backgroundImage.value = null;
    isNewTemplate.value = false;
    let jsonData = json.json_data;
    selectedTemplate.value = json.id;
    if (jsonData) {
        const textItems =
            jsonData.children[0].children.filter(
                (item) => item.attrs.type === 'TEXT' || item.attrs.type === 'ATTRS'
            ) || [];

        const allImages = jsonData.children[0].children.filter(
            (item) => item.className === 'Image'
        );

        if (allImages.length > 0) {
            // Find background image
            const backgroundImageNode = allImages.find((item) => item.className === 'background');
            if (backgroundImageNode) {
                const [bgImage] = useImage(backgroundImageNode.attrs.name);
                watch(bgImage, (newImage) => {
                    if (newImage) {
                        backgroundImage.value = newImage;
                    }
                });
                backgroundImageSrc.value = backgroundImageNode.attrs.name;
            }

            // Filter out background image and get other images
            const imageItems = allImages.filter((item) => item.className !== 'background');
            textElements.value = textItems.map((text) => text.attrs);
            imageElements.value = imageItems.map((image) => {
                const [imageObj] = useImage(image.attrs.name);
                return {
                    ...image.attrs,
                    image: imageObj,
                };
            });
        }
        textElements.value = textItems.map((text) => text.attrs);
    }
};

const extractFontStyle = (styleString = '') => {
    const style = styleString.toLowerCase().trim();

    const fontStyle = style.includes('italic') ? 'italic' : 'normal';

    return fontStyle;
};

const extractFontWeight = (styleString = '') => {
    const style = styleString.toLowerCase().trim();

    const fontWeight = style.includes('bold') ? 'bold' : 'normal';

    return fontWeight;
};

const generateHTML = () => {
    const children = savedObj?.value?.children?.[0]?.children ?? [];
    const bg = children.find((item) => item.attrs.type === 'BACKGROUND');
    const scale = isStudentId.value ? 0.5 : 1;

    const width = savedObj.value.attrs.width * scale;
    const height = savedObj.value.attrs.height * scale;
    const fonts = children
        .filter((item) => item.className === 'Text')
        .map((item) => item.attrs.fontFamily)
        .filter((value, index, self) => value && self.indexOf(value) === index);
    const htmlContent = `
             <div id="watermark" style="position:fixed; bottom:0px; left:0px; top:0px; right:0px; width:100%; height:100%; z-index:-1000;">
            ${
                bg
                    ? `<img src="${bg.attrs.name}" class="background-image" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;" alt="Background">`
                    : ''
            }
            </div>
            <div class="certificate-container" style="width:${width}px; height: ${height}px; position: relative;
                margin: auto;
                overflow: hidden;">
                ${children
                    .slice()
                    .sort((a, b) => parseFloat(a.attrs.y) - parseFloat(b.attrs.y))
                    .map((item) => {
                        const fontFamily = item.attrs.fontFamily || 'sans-serif';
                        if (item.className === 'Text') {
                            return `<div class="text-item" style="position:absolute; font-family: ${fontFamily}; left:${item.attrs.x ? item.attrs.x : 0}px; top:${item.attrs.y}px; font-size:${item.attrs.fontSize || 12}px; color:${item.attrs.fill}; font-weight:${extractFontWeight(item.attrs.fontStyle)}; font-style: ${extractFontStyle(item.attrs.fontStyle)}; text-align:${item.attrs.align ? item.attrs.align : 'left'}; text-decoration:${item.attrs.textDecoration}; width:${item.attrs.width}px;">
                                  ${item.attrs.text.replace(/\n/g, '<br />')}
                               </div>`;
                        } else if (
                            item.className === 'Image' &&
                            item.attrs.name &&
                            item.attrs.type !== 'BACKGROUND' &&
                            item.attrs.type !== 'QR'
                        ) {
                            return `<img src="${item.attrs.name}" class="image-item" style="position: absolute; left:${item.attrs.x}px; top:${item.attrs.y}px; width:${item.attrs.width}px; height:${item.attrs.height}px;" alt="Certificate Image">`;
                        } else if (item.className === 'Circle') {
                            return `<div class="circle-item" style="position: absolute; left:${item.attrs.x}px; top:${item.attrs.y}px; width:${item.attrs.width ? item.attrs.width : 100}px; height:${item.attrs.height ? item.attrs.height : 100}px; border-width:${item.attrs.strokeWidth}px; border-color:${item.attrs.stroke}; border-radius:${item.attrs.radius}%; background:${item.attrs.fill};"></div>`;
                        } else if (item.className === 'Rect') {
                            return `<div class="rect-item" style="position: absolute; left:${item.attrs.x}px; top:${item.attrs.y}px; width:${item.attrs.width}px; height:${item.attrs.height}px; border-width:${item.attrs.strokeWidth}px; border-color:${item.attrs.stroke}; background:${item.attrs.fill};"></div>`;
                        } else if (item.className === 'Ellipse') {
                            return `<div class="ellipse-item" style="position: absolute; left:${item.attrs.x}px; top:${item.attrs.y}px; width:${item.attrs.width ? item.attrs.width : 100}px; height:${item.attrs.height ? item.attrs.height : 50}px; border-width:${item.attrs.strokeWidth}px; border-color:${item.attrs.stroke}; background:${item.attrs.fill}; border-radius:50%;"></div>`;
                        } else if (item.className === 'Line') {
                            const [x1, y1, x2, y2] = item.attrs.points;
                            const vertHeight = y2 - y1;
                            const hortWidth = x2 - x1;
                            return `<div class="line-item" style="position: absolute; left:${item.attrs.x}px; top:${item.attrs.y}px; width:${item.attrs.variant === 'line-vert' ? 1 : hortWidth * item.attrs.scaleX}px; height:${item.attrs.variant === 'line-vert' ? vertHeight * item.attrs.scaleY : 1}px; background:${item.attrs.stroke};"></div>`;
                        } else if (
                            item.className === 'Image' &&
                            item.attrs.name &&
                            item.attrs.type === 'QR'
                        ) {
                            return `<div id="qrImage" style="position: absolute; left:${item.attrs.x}px; top:${item.attrs.y}px; width:${item.attrs.width}px; height:${item.attrs.height}px;"><img src="${item.attrs.name}" class="qr-item" id="qrItem" style="width:100%; height:100%;" alt="Certificate QR Code"></div>`;
                        }
                        return '';
                    })
                    .join('')}
            </div>`;
    return htmlContent;
};

const exportAsHtml = () => {
    const blob = new Blob([savedHTML.value], { type: 'text/html' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'certificate.html';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const exportAsPng = () => {
    const dataURL = stageRef.value.getNode().toDataURL({
        pixelRatio: 2, // double resolution
    });

    const link = document.createElement('a');
    link.download = 'stage.png';
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// Guidelines Functions
const getLineGuideStops = (skipShape) => {
    const stage = stageConfig.value;
    let vertical = [0, stage.width / 2, stage.width];
    let horizontal = [0, stage.height / 2, stage.height];
    const elements = [...textElements.value, ...imageElements.value];

    elements.forEach((text) => {
        if (text === skipShape) return;
        let box = text;
        vertical.push(box.x, box.x + box.width, box.x + box.width / 2);
        horizontal.push(box.y, box.y + box.height, box.y + box.height / 2);
    });

    return {
        vertical,
        horizontal,
    };
};

const getObjectSnappingEdges = (node) => {
    let box = node;
    return {
        vertical: [
            { guide: box.x, offset: 0 },
            { guide: box.x + box.width / 2, offset: -box.width / 2 },
            { guide: box.x + box.width, offset: -box.width },
        ],
        horizontal: [
            { guide: box.y, offset: 0 },
            { guide: box.y + box.height / 2, offset: -box.height / 2 },
            { guide: box.y + box.height, offset: -box.height },
        ],
    };
};

const getGuides = (lineGuideStops, itemBounds) => {
    let guides = [];
    lineGuideStops.vertical.forEach((lineGuide) => {
        itemBounds.vertical.forEach((itemBound) => {
            if (Math.abs(lineGuide - itemBound.guide) < GUIDELINE_OFFSET) {
                guides.push({
                    lineGuide,
                    offset: itemBound.offset,
                    orientation: 'V',
                });
            }
        });
    });
    lineGuideStops.horizontal.forEach((lineGuide) => {
        itemBounds.horizontal.forEach((itemBound) => {
            if (Math.abs(lineGuide - itemBound.guide) < GUIDELINE_OFFSET) {
                guides.push({
                    lineGuide,
                    offset: itemBound.offset,
                    orientation: 'H',
                });
            }
        });
    });
    return guides;
};

const drawGuides = (guides) => {
    guidelines.value = guides.map((lg) => ({
        points: lg.orientation === 'H' ? [-6000, 0, 6000, 0] : [0, -6000, 0, 6000],
        stroke: lg.orientation === 'V' ? '#F11169' : '#C40CE5',
        strokeWidth: 0.5,
        dash: [4, 6],
        x: lg.orientation === 'V' ? lg.lineGuide : 0,
        y: lg.orientation === 'H' ? lg.lineGuide : 0,
    }));
};

const onDragMove = (e) => {
    guidelines.value = [];

    const node = e.target;
    const stage = node.getStage();

    // Get original attributes
    const attrs = { ...node.attrs };

    // Snap logic
    const lineGuideStops = getLineGuideStops(attrs);
    const itemBounds = getObjectSnappingEdges(attrs);
    const guides = getGuides(lineGuideStops, itemBounds);

    // Apply snapping if guides exist
    if (guides.length) {
        drawGuides(guides);

        guides.forEach((lg) => {
            if (lg.orientation === 'V') attrs.x = lg.lineGuide + lg.offset;
            if (lg.orientation === 'H') attrs.y = lg.lineGuide + lg.offset;
        });
    }

    // Temporarily apply attrs to calculate box properly
    node.setAttrs(attrs);

    // Get the actual bounding box (transformed)
    const box = node.getClientRect();
    const nodeWidth = box.width;
    const nodeHeight = box.height;

    // Bounds calculation
    const maxX = stage.width() - nodeWidth;
    const maxY = stage.height() - nodeHeight;

    const clampedX = Math.max(0, Math.min(attrs.x, maxX));
    const clampedY = Math.max(0, Math.min(attrs.y, maxY));

    // Finally set the bounded position
    node.setAttrs({ x: clampedX, y: clampedY });
};

const handleCreateTemplate = () => {
    stageRef.value.getNode().clear();
};

const beforeUnloadHandler = (event) => {
    if (!isDataSaved.value) {
        event.preventDefault();
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
    }
};

onMounted(() => {
    window.addEventListener('beforeunload', beforeUnloadHandler);
});

onUnmounted(() => {
    window.removeEventListener('beforeunload', beforeUnloadHandler);
});

const addShape = (type) => {
    let defaultConfig = {
        id: `${type}-${Date.now()}`,
        x: imagePosition.value.x !== null ? imagePosition.value.x : 100,
        y: imagePosition.value.y !== null ? imagePosition.value.y : getDefaultY(),
        strokeWidth: 0,
        draggable: true,
        type: 'SHAPE',
        variant: type,
    };
    let configMapping = {
        circle: {
            radius: 70,
            fill: 'transparent',
            strokeWidth: 1,
            stroke: 'black',
            width: 100,
            height: 100,
        },
        'circle-fill': {
            radius: 70,
            fill: 'black',
            width: 100,
            height: 100,
        },
        ellipse: {
            radiusX: 100,
            radiusY: 50,
            fill: 'transparent',
            strokeWidth: 1,
            stroke: 'black',
            width: 100,
            height: 50,
        },
        'ellipse-fill': {
            radiusX: 100,
            radiusY: 50,
            fill: 'black',
            width: 100,
            height: 50,
        },
        line: {
            points: [0, 0, 200, 0],
            strokeWidth: 1,
            stroke: 'black',
            hitStrokeWidth: 10,
        },
        'line-vert': {
            points: [0, 0, 0, 200],
            height: 200,
            strokeWidth: 1,
            stroke: 'black',
            hitStrokeWidth: 10,
        },
        rect: {
            width: 100,
            height: 50,
            fill: 'transparent',
            strokeWidth: 1,
            stroke: 'black',
        },
        'rect-fill': {
            width: 100,
            height: 50,
            fill: 'black',
        },
    };
    let typeConfig = configMapping[type];
    shapeElements.value.push({
        ...defaultConfig,
        ...typeConfig,
    });
    sidebar.value = false;
    saveState();
};

const handleLoadShape = (e, shape) => {
    addShape(shape.value);
};

const onDragStart = (event, menu) => {
    event.dataTransfer.setData('type', menu);
};

const isSelected = ref(false);

const onDrop = (event) => {
    isSelected.value = true;
    const type = event.dataTransfer.getData('type');
    const stage = stageRef.value.getNode();
    const template = event.dataTransfer.getData('template');
    const attribute = event.dataTransfer.getData('attribute');
    const shape = event.dataTransfer.getData('shape');
    const stageContainer = stage.container();
    const rect = stageContainer.getBoundingClientRect();

    // Calculate mouse position relative to stage
    const position = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
    };

    imagePosition.value = position;

    if (type === 'texts') {
        addText(null, position);
    } else if (type === 'images') {
        triggerFileUpload();
    } else if (type === 'qr_codes' || type === 'profile') {
        qrConfig.value = {
            ...qrConfig.value,
            x: position.x,
            y: position.y,
        };
        addQRCode();
    }

    if (template) {
        handleLoadTemplate(JSON.parse(template));
    }

    if (attribute) {
        addText(`[${JSON.parse(attribute).tag}]`, position);
    }

    if (shape) {
        handleLoadShape(event, JSON.parse(shape));
    }
};

const payload = ref({});

onMounted(() => {
    if (props.selectedCertificate && Object.keys(props.selectedCertificate).length > 0) {
        const data = props.selectedCertificate;
        handleLoadTemplate(data);
        isNewTemplate.value = false;
        const initial = {
            name: data.name || '',
            certificate_type: parseInt(data.type) || null,
            is_design_template: parseInt(data.certificate_number_formate_id || 0),
        };
        const templateData = {
            // name: certificateName.value || "Untitled Template",
            name: data.name || 'Untitled Template',
            json_data: data.json_data,
            html_data: data.html_data,
            paper_size: data.paper_size,
            orientation: data.orientation,
            thumbnail: data.thumbnail,
            is_default: data.is_default,
            id: data.id || '',
            // type: "certificate",
            type: data.certificate_type,
            certificate_number_formate_id: data.certificate_number_formate_id,
            fonts_used: data.font_used,
        };
        payload.value = { ...templateData };
        certificateStore.setInitialData(initial);
    }
});

const certificateFormate = computed(() => {
    if (!props.certificateIdFormate) {
        return null;
    }
    const selectedFormat = props.certificateIdFormate.find(
        (item) => item.id == props.selectedCertificate.certificate_number_formate_id
    );

    return selectedFormat?.name || 'N/A';
});

const onTemplateDragStart = (event, template) => {
    event.dataTransfer.setData('template', JSON.stringify(template));
};

const onAttributesDragStart = (event, template) => {
    event.dataTransfer.setData('attribute', JSON.stringify(template));
};

const onShapesDragStart = (event, template) => {
    event.dataTransfer.setData('shape', JSON.stringify(template));
};

const hoverTimeout = ref(null);

const handleMenuChange = (e, item) => {
    sidebar.value = false;
    clearTimeout(hoverTimeout.value);
    currentMenu.value = item.slug;
    if (item.showPopover) {
        sidebar.value = true;
    }
};

const handleMenuClick = (e, item) => {
    if (item.slug === 'texts') {
        addText(null);
    } else if (item.slug === 'images') {
        triggerFileUpload();
    } else if (item.slug === 'background') {
        triggerBackgroundUpload();
    } else if (item.slug === 'qr_codes' || item.slug === 'profile') {
        addQRCode();
    }
};

const onSidebarLeave = () => {
    hoverTimeout.value = setTimeout(() => {
        sidebar.value = false;
    }, 100);
};

const onPopupEnter = () => {
    clearTimeout(hoverTimeout.value);
    sidebar.value = true;
};

const onPopupLeave = () => {
    sidebar.value = false;
};

const handleStageClick = (e) => {
    const clickedNode = e.target;
    const defaults = [
        'top-left',
        'top-right',
        'bottom-left',
        'bottom-right',
        'middle-left',
        'middle-right',
        'bottom-center',
        'top-center',
    ];

    const currentNode = selectedNode.value?.getNode();
    const isNewNode = !selectedNode.value || clickedNode !== currentNode;
    const isNotQuickAction = clickedNode.attrs.type !== 'QUICKACTION';
    const isNotDefault = !defaults.some((prefix) => clickedNode?.attrs?.name?.startsWith(prefix));
    const isDifferentType = clickedNode.className !== currentNode?.className;
    if (isNewNode && isNotQuickAction && isNotDefault && isDifferentType) {
        handleDetach();
    }
};

const getFonts = computed(() => {
    const children = savedObj?.value?.children?.[0]?.children ?? [];
    const fonts = children
        .filter((item) => item.className === 'Text' || item.className === 'ATTRS')
        .map((item) => item.attrs.fontFamily)
        .filter((value, index, self) => value && self.indexOf(value) === index);
    return fonts;
});

const addQRCode = (position = null) => {
    const [image] = useImage(isStudentId ? sampleProfile : sampleQR);
    watch(image, (newImage) => {
        if (newImage) {
            qrImage.value = newImage;
        }
    });
    saveState();
};

onMounted(() => {
    stageRef.value?.getStage().on('mousedown', handleStageClick);
});

onBeforeUnmount(() => {
    stageRef.value?.getStage().off('mousedown', handleStageClick);
});

const handleSaveAs = (values) => {
    storeData(values);
};

const handleSaveAndPreview = async (data) => {
    saveStageState(data);
    // if (isNewTemplate.value) {
    //     await $certificate.saveTemplate(payload.value, {
    //         isPreview: true,
    //         handleAdd: (response) => {
    //             $certificate.getPreviewUrl({ id: response.template.id });
    //             openPreviewPopup.value = true;
    //         },
    //     });
    //     isNewTemplate.value = false;
    // } else {
    if (selectedTemplate.value) {
        await $certificate.updateTemplate(
            payload.value,
            {
                isPreview: true,
                handleEdit: (response) => {
                    $certificate.getPreviewUrl({ id: response.template.id });
                    openPreviewPopup.value = true;
                },
            },
            isStudentId.value ? 'student-id' : 'certificate'
        );
        globalHelper.methods.showPopupSuccess('Template updated successfully', 'Success');
    } else {
        globalHelper.methods.showPopupError('Template is not loaded', 'Error');
    }
    // }
    setTimeout(() => {
        openSavePopup.value = false;
    }, 400);
    isSavePreview.value = true;
};
const isIframeLoad = () => {
    if (certificateEmbedeUrl.value != '') {
        isLoading.value = false;
        loaderStore.stopContextLoading('generate');
    }
};

const handleToolbarSave = () => {
    openSavePopup.value = true;
};

const handleActionMouseOver = () => {
    hoveredNode.value = null;
    hoveredTransformerRef.value.detachTransformer();
};

const handleQrTransform = () => {
    const node = qrNode.value?.getNode();
    if (!node) return;

    // Apply scale to dimensions
    const newWidth = node.width() * node.scaleX();
    const newHeight = node.height() * node.scaleY();

    // Update attributes
    node.setAttrs({
        width: newWidth,
        height: newHeight,
        scaleX: 1,
        scaleY: 1,
    });
};

watch(
    [textElements, imageElements, backgroundImage],
    () => {
        isDataSaved.value = false;
    },
    { deep: true }
);

watch(
    () => customWidth.value,
    (newWidth) => {
        if (newWidth) {
            stageConfig.value.width = newWidth;
        }
    }
);
watch(
    () => certificateStore.getTemplates,
    (newTemplates) => {
        if (newTemplates) {
            selectedTemplate.value = newTemplates.id;
        }
    }
);

watch(
    () => customHeight.value,
    (newHeight) => {
        if (newHeight) {
            stageConfig.value.height = newHeight;
        }
    }
);

watch(
    () => selectedFormat.value,
    (newFormat) => {
        if (newFormat === 'us-letter') {
            stageConfig.value = {
                width: 816,
                height: 1056,
            };
        } else if (newFormat === 'a4') {
            stageConfig.value = {
                width: 707,
                height: 1000,
            };
        } else if (newFormat === 'cr80') {
            stageConfig.value = {
                width: 648,
                height: 408,
            };
        }

        if (newFormat === 'custom') {
            showCustomFormat.value = true;
        } else {
            showCustomFormat.value = false;
        }
    }
);

watch(
    () => selectedOrientation.value,
    (newOrientation) => {
        if (newOrientation === 'horizontal') {
            stageConfig.value = {
                width: stageConfig.value.height,
                height: stageConfig.value.width,
            };
        } else {
            stageConfig.value = {
                width: 700,
                height: 1000,
            };
        }
    }
);

const onEmbedLoad = () => {
    setTimeout(() => {
        loaderStore.stopContextLoading('generate');
    }, 2000);
};

const isLoading = ref(false);

const handleDetach = () => {
    selectedNode.value = null;
    transformerRef.value.detachTransformer();
    console.log('actionBarRef.value', actionBarRef.value);
    if (actionBarRef.value) {
        actionBarRef.value.detachToolbar();
    }
    if (hoveredTransformerRef.value) {
        hoveredNode.value = null;
        hoveredTransformerRef.value.detachTransformer();
    }
};

watch(
    () => loaderStore.contextLoaders['generate'] && isSavePreview.value,
    (newVal) => {
        if (newVal) {
            isLoading.value = true;
        } else {
            isLoading.value = false;
            openPreviewPopup.value = true;
        }
    }
);

watch(
    () => certificateStore.getTemplates,
    (newVal) => {
        if (newVal) {
            //   $certificate.getPreviewUrl({ id: newVal.id });
        }
    },
    {
        deep: true,
    }
);

watch(
    () => $certificate.state.previewUrl,
    (val) => {
        if (val) {
            certificateEmbedeUrl.value = val;
        }
    }
);
</script>
<style lang=""></style>
