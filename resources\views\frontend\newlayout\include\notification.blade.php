<button id="feedback-button" title="Give your feedback"
    class="updateNotificationCount rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
    <svg width="24" height="24" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M10.75 14A2.25 2.25 0 0 1 13 16.25v1.502l-.008.108c-.31 2.127-2.22 3.149-5.425 3.149-3.193 0-5.134-1.01-5.553-3.112L2 17.75v-1.5A2.25 2.25 0 0 1 4.25 14h6.5Zm0 1.5h-6.5a.75.75 0 0 0-.75.75v1.42c.28 1.2 1.55 1.839 4.067 1.839 2.516 0 3.73-.631 3.933-1.816V16.25a.75.75 0 0 0-.75-.75ZM7.5 6a3.5 3.5 0 1 1 0 7 3.5 3.5 0 0 1 0-7Zm12.25-4A2.25 2.25 0 0 1 22 4.25v3.5A2.25 2.25 0 0 1 19.75 10h-1.455l-2.166 2.141A1.25 1.25 0 0 1 14 11.253V9.986a2.25 2.25 0 0 1-2-2.236v-3.5A2.25 2.25 0 0 1 14.25 2h5.5ZM7.5 7.5a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm12.25-4h-5.5a.75.75 0 0 0-.75.75v3.5c0 .414.336.75.75.75h1.249v2.154L17.68 8.5h2.071a.75.75 0 0 0 .75-.75v-3.5a.75.75 0 0 0-.75-.75Z"
            stroke-width="2" fill="#9CA3AF" />
    </svg>
</button>
<button data-toggle="dropdown"
    class="updateNotificationCount rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    data-id="{{ Auth::user()->id }}" data-count="{{ Auth::user()->notification_unread_count }}">
    <span class="sr-only">View notifications</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path stroke="#9CA3AF" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M15 17h5l-1.405-1.405c-.38-.38-.595-.898-.595-1.437V11c0-2.612-1.67-4.835-4-5.659V5c0-1.105-.895-2-2-2s-2 .895-2 2v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1c0 1.657-1.343 3-3 3s-3-1.343-3-3v-1m6 0H9" />
        @if (Auth::user()->notification_unread_count > 0)
        <g filter="url(#filter0_d)">
            <path fill="#F87171" d="M11 8c0-2.761 2.239-5 5-5s5 2.239 5 5-2.239 5-5 5-5-2.239-5-5z" />
        </g>
        @endif
        <defs>

            <filter id="filter0_d" width="14" height="14" x="9" y="1" color-interpolation-filters="sRGB"
                filterUnits="userSpaceOnUse">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
                <feMorphology in="SourceAlpha" operator="dilate" radius="2" result="effect1_dropShadow" />
                <feOffset />
                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
                <feBlend in2="BackgroundImageFix" result="effect1_dropShadow" />
                <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
            </filter>
        </defs>
    </svg>
    <!-- <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
        </svg> -->
</button>
<div class="isShowNotification animate__animated animate__fadeIn absolute right-0 z-50 mt-1 w-96 overflow-auto rounded-md bg-white p-2 text-base shadow-lg ring-1 ring-black ring-opacity-5 transition duration-500 ease-in-out focus:outline-none sm:text-sm"
    tabindex="-1" role="listbox" aria-labelledby="listbox-label" aria-activedescendant="listbox-option-3">
    <div class="z-30 w-full overflow-hidden rounded-lg bg-white p-2">
        @if (Auth::user()->latest_notification != '' && Auth::user()->latest_notification != null)
        @php
        $finalArray = [];
        $latestNotificationArr = json_decode(Auth::user()->latest_notification);
        foreach ($latestNotificationArr as $key => $value) {
        $finalArray[date('Y-m-d', strtotime($value->created_at))][] = $value;
        }
        $currentDate = date('Y-m-d');
        $previuosDate = date('Y-m-d', strtotime('-1 days'));

        @endphp

        @foreach ($finalArray as $key => $values)
        <p class="mb-4 mt-0.5 text-sm text-gray-500">
            @if ($key == $currentDate)
            Today
            @elseif($key == $previuosDate)
            Yesterday
            @else
            {{ date('d M Y', strtotime($key)) }}
            @endif
        </p>
        <div class="flow-root">

            <ul class="">
                @foreach ($values as $value)
                <li class="">
                    <a href="{{ @$value->notification_link != '' ? $value->notification_link : '#' }}">
                        <div class="relative mb-2 border-b pb-2">
                            <div class="relative flex items-start space-x-3">
                                <div
                                    class="{{ Config::get('constants.notificationConfig.' . $value->notification_type . '.bgColorClass') }} flex h-8 w-8 items-center justify-center rounded-full">
                                    {!! Config::get('constants.notificationConfig.' . $value->notification_type .
                                    '.svgicon') !!}
                                </div>
                                <div class="min-w-0 flex-1">
                                    <div>
                                        <div class="text-sm text-gray-500">
                                            {!! $value->notification_value !!}
                                            <!-- <span class="font-medium text-gray-900">Hari Subedi</span> assigned to you a task<span class="font-medium text-gray-900"> Upload Documents </span> -->
                                        </div>
                                        <p class="mt-0.5 text-xs text-gray-500">
                                            {{ date('h:i A', strtotime($value->created_at)) }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                </li>
                @endforeach

            </ul>


        </div>
        @endforeach

        <a href="{{ route('notification-list') }}"
            class="mt-4 inline-flex w-full items-center justify-center rounded-md bg-primary-blue-500 px-4 py-1 text-base text-xs font-normal text-blue-500 text-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            View All Notifications
        </a>
        @else
        No Notification Found
        @endif
    </div>
</div>